2025-07-16 10:51:18,201 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250716_105118.log
2025-07-16 10:51:18,203 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 10:51:18,204 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 10:51:18,418 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 10:51:18,448 - batch_executor - INFO - 加载了 35 个任务状态
2025-07-16 10:51:25,175 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 10:51:25,175 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 10:51:25,199 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 10:51:25,219 - root - INFO - === GeoServer REST API服务 ===
2025-07-16 10:51:25,220 - root - INFO - 主机: 0.0.0.0
2025-07-16 10:51:25,220 - root - INFO - 端口: 5083
2025-07-16 10:51:25,222 - root - INFO - 调试模式: 禁用
2025-07-16 10:51:25,223 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-16 10:51:25,250 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-16 10:51:25,251 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-16 10:53:17,221 - tif_executor - INFO - 初始化任务日志: 1f61d08d-d04c-4654-9432-d0372d4487c9, 类型: process
2025-07-16 10:53:17,229 - tif_executor - INFO - 任务 1f61d08d-d04c-4654-9432-d0372d4487c9 进度: 5% - 开始处理TIF文件
2025-07-16 10:53:17,230 - tif_api - INFO - 启动异步任务 1f61d08d-d04c-4654-9432-d0372d4487c9 - 输入: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto.tif
2025-07-16 10:53:17,235 - tif_executor - INFO - 任务 1f61d08d-d04c-4654-9432-d0372d4487c9 状态更新: running, 开始处理TIF文件, 进度: 5%
2025-07-16 10:53:17,236 - tif_executor - INFO - 任务 1f61d08d-d04c-4654-9432-d0372d4487c9 状态更新: pending, 任务已提交，等待处理, 进度: 0%
2025-07-16 10:53:17,238 - tif_executor - ERROR - 更新任务状态失败: 'gbk' codec can't decode byte 0xa1 in position 717: illegal multibyte sequence
2025-07-16 10:53:17,241 - tif_executor - ERROR - 更新任务状态失败: 'gbk' codec can't decode byte 0xa1 in position 717: illegal multibyte sequence
2025-07-16 10:53:17,273 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:53:17] "GET /api/tif/process/async?input_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif&output_shp=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-16 10:53:17,258 - tif_executor - INFO - 任务 1f61d08d-d04c-4654-9432-d0372d4487c9 进度: 10% - 读取输入文件
2025-07-16 10:53:17,356 - tif_executor - INFO - 任务 1f61d08d-d04c-4654-9432-d0372d4487c9 状态更新: running, 读取输入文件, 进度: 10%
2025-07-16 10:53:17,364 - tif_executor - ERROR - 更新任务状态失败: 'gbk' codec can't decode byte 0xa1 in position 717: illegal multibyte sequence
2025-07-16 10:53:17,366 - tif_process - INFO - 进度: 5% - 开始处理影像: D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif
2025-07-16 10:53:17,380 - tif_process - INFO - 开始处理影像: D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif
2025-07-16 10:53:17,389 - tif_process - INFO - 可用GPU数量: 0, 使用GPU: False
2025-07-16 10:53:17,392 - tif_process - INFO - 处理模式: 全图检测无效区域
2025-07-16 10:53:17,395 - tif_process - INFO - 保护内部区域: False
2025-07-16 10:53:17,412 - tif_process - INFO - 保留Alpha通道: False
2025-07-16 10:53:17,421 - tif_process - INFO - 进度: 10% - 读取输入影像...
2025-07-16 10:55:36,210 - tif_process - INFO - 检测到4波段影像(可能包含Alpha通道)
2025-07-16 10:56:14,167 - tif_process - INFO - 将移除Alpha通道，只保留RGB三波段
2025-07-16 10:56:14,169 - tif_process - INFO - 进度: 12% - 移除Alpha通道，保留RGB三波段
2025-07-16 10:56:14,171 - tif_process - INFO - 处理后波段数: 3
2025-07-16 10:56:14,174 - tif_process - INFO - 影像尺寸: 38177x48241x3
2025-07-16 10:56:14,175 - tif_process - INFO - 估计内存使用: 总计 27.44 GB, 单个处理块 582.53 MB
2025-07-16 10:56:14,175 - tif_process - INFO - 进度: 15% - 影像尺寸: 38177x48241x3
2025-07-16 10:56:14,176 - tif_process - INFO - 进度: 20% - 开始创建掩码...
2025-07-16 10:56:14,178 - tif_process - INFO - 开始创建掩码... (10:56:14)
2025-07-16 10:56:14,180 - tif_process - INFO - 使用全图检测无效区域模式...
2025-07-16 10:56:14,183 - tif_process - INFO - 进度: 25% - 使用全图检测无效区域模式...
2025-07-16 10:56:39,001 - tif_process - INFO - 进度: 40% - 全图掩码创建完成
2025-07-16 10:56:39,017 - tif_process - INFO - 掩码创建完成，耗时: 24.84 秒 (10:56:39)
2025-07-16 10:56:39,018 - tif_process - INFO - 预计总处理时间: 约 74.52 秒
2025-07-16 10:56:39,019 - tif_process - INFO - 开始设置nodata值并保存到: D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif (10:56:39)
2025-07-16 10:56:39,021 - tif_process - INFO - 进度: 50% - 设置NoData值并保存到: D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif
2025-07-16 10:56:45,953 - tif_executor - ERROR - 获取任务状态失败: 'gbk' codec can't decode byte 0xa1 in position 717: illegal multibyte sequence
2025-07-16 10:56:45,969 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 10:56:45] "[33mGET /api/tif/status?task_id=1f61d08d-d04c-4654-9432-d0372d4487c9 HTTP/1.1[0m" 404 -
