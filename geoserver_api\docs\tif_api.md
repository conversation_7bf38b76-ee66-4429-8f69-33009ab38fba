# TIF处理API文档

TIF处理API模块提供TIF/GeoTIFF文件的处理和转换功能，支持同步和异步处理模式，适用于大型栅格数据的批量处理。

## 模块概述

TIF处理模块是系统的栅格数据处理核心，提供：
- **同步处理**：小文件的快速处理和转换
- **异步处理**：大文件的后台异步处理
- **格式转换**：TIF文件格式转换和优化
- **数据处理**：NoData值处理、重投影、重采样等
- **任务管理**：处理任务的状态监控和日志记录

## API端点列表

### 1. 同步处理TIF文件

**端点**: `GET /api/tif/process/`

**描述**: 同步处理TIF文件，适用于小文件的快速处理

**参数**:
- `input_tif` (必选): 输入TIF文件路径
- `output_tif` (必选): 输出TIF文件路径
- `nodata_value` (可选): NoData值，默认为-9999
- `compress` (可选): 压缩方式，默认为LZW
- `tiled` (可选): 是否创建瓦片，默认为true
- `overviews` (可选): 是否生成概览，默认为true

**请求示例**:
```bash
# 基本处理
curl "http://127.0.0.1:5000/api/tif/process/?input_tif=D:/data/input.tif&output_tif=D:/data/output.tif"

# 指定NoData值和压缩方式
curl "http://127.0.0.1:5000/api/tif/process/?input_tif=D:/data/dem.tif&output_tif=D:/data/dem_processed.tif&nodata_value=-32768&compress=DEFLATE"

# 完整参数处理
curl "http://127.0.0.1:5000/api/tif/process/?input_tif=D:/data/ortho.tif&output_tif=D:/data/ortho_optimized.tif&nodata_value=0&compress=JPEG&tiled=true&overviews=true"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "TIF文件处理完成",
  "input_file": "D:/data/input.tif",
  "output_file": "D:/data/output.tif",
  "processing_time": "2.34秒",
  "file_size": {
    "input": "125.6 MB",
    "output": "89.2 MB"
  },
  "compression_ratio": "28.9%"
}
```

### 2. 异步处理TIF文件

**端点**: `GET /api/tif/execute/`

**描述**: 异步处理TIF文件，适用于大文件的长时间处理

**参数**:
- `input_tif` (必选): 输入TIF文件路径
- `output_tif` (必选): 输出TIF文件路径
- `nodata_value` (可选): NoData值，默认为-9999
- `compress` (可选): 压缩方式，默认为LZW
- `tiled` (可选): 是否创建瓦片，默认为true
- `overviews` (可选): 是否生成概览，默认为true
- `reproject` (可选): 目标投影，如EPSG:4326
- `resample` (可选): 重采样方法，如bilinear、cubic

**请求示例**:
```bash
# 基本异步处理
curl "http://127.0.0.1:5000/api/tif/execute/?input_tif=D:/data/large_dem.tif&output_tif=D:/data/processed_dem.tif"

# 带重投影的处理
curl "http://127.0.0.1:5000/api/tif/execute/?input_tif=D:/data/utm_image.tif&output_tif=D:/data/wgs84_image.tif&reproject=EPSG:4326&resample=bilinear"

# 完整参数异步处理
curl "http://127.0.0.1:5000/api/tif/execute/?input_tif=D:/data/raw_ortho.tif&output_tif=D:/data/final_ortho.tif&nodata_value=255&compress=JPEG&tiled=true&overviews=true&reproject=EPSG:3857"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "TIF处理任务已启动",
  "task_id": "770fa622-04bd-63f6-c938-668877662222"
}
```

### 3. 查询处理任务状态

**端点**: `GET /api/tif/status/`

**描述**: 查询TIF处理任务的执行状态和进度

**参数**:
- `task_id` (必选): 任务ID

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/tif/status/?task_id=770fa622-04bd-63f6-c938-668877662222"
```

**成功响应**:
```json
{
  "status": "success",
  "task": {
    "id": "770fa622-04bd-63f6-c938-668877662222",
    "input_file": "D:/data/large_dem.tif",
    "output_file": "D:/data/processed_dem.tif",
    "status": "running",
    "progress": 65,
    "start_time": "2024-01-15T14:30:00.123456",
    "end_time": null,
    "processing_options": {
      "nodata_value": -9999,
      "compress": "LZW",
      "tiled": true,
      "overviews": true
    },
    "current_step": "生成概览图像",
    "estimated_remaining": "3分钟",
    "log_file": "D:\\Drone_Project\\geoserverAPIDJ\\geoserverRest\\tiflog\\770fa622-04bd-63f6-c938-668877662222.log"
  }
}
```

**任务状态说明**:
- `pending`: 任务等待中
- `running`: 任务执行中
- `completed`: 任务完成
- `failed`: 任务失败
- `cancelled`: 任务已取消

### 4. 获取处理任务日志

**端点**: `GET /api/tif/log/`

**描述**: 获取TIF处理任务的详细执行日志

**参数**:
- `task_id` (必选): 任务ID

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/tif/log/?task_id=770fa622-04bd-63f6-c938-668877662222"
```

**成功响应**:
```json
{
  "status": "success",
  "task_id": "770fa622-04bd-63f6-c938-668877662222",
  "log": "开始TIF处理: D:/data/large_dem.tif\n输出文件: D:/data/processed_dem.tif\n处理选项: {'nodata_value': -9999, 'compress': 'LZW', 'tiled': True}\n开始时间: 2024-01-15T14:30:00.123456\n--------------------------------------------------\n正在读取输入文件...\n文件大小: 2.1 GB\n图像尺寸: 25000 x 20000\n数据类型: Float32\n投影信息: EPSG:32650\n--------------------------------------------------\n步骤 1/4: 数据预处理\n处理NoData值: -9999\n进度: 25%\n--------------------------------------------------\n步骤 2/4: 应用压缩\n使用LZW压缩\n进度: 50%\n--------------------------------------------------\n步骤 3/4: 创建瓦片结构\n瓦片大小: 512x512\n进度: 75%\n--------------------------------------------------\n步骤 4/4: 生成概览图像\n概览级别: 2, 4, 8, 16\n进度: 100%\n--------------------------------------------------\n处理完成!\n输出文件大小: 1.3 GB\n压缩比: 38.1%\n总处理时间: 8分32秒\n结束时间: 2024-01-15T14:38:32.789012"
}
```

### 5. 获取所有处理任务

**端点**: `GET /api/tif/all/`

**描述**: 获取所有TIF处理任务的状态列表

**参数**: 无

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/tif/all/"
```

**成功响应**:
```json
{
  "status": "success",
  "count": 4,
  "tasks": [
    {
      "id": "770fa622-04bd-63f6-c938-668877662222",
      "input_file": "D:/data/large_dem.tif",
      "output_file": "D:/data/processed_dem.tif",
      "status": "completed",
      "progress": 100,
      "start_time": "2024-01-15T14:30:00.123456",
      "end_time": "2024-01-15T14:38:32.789012",
      "processing_time": "8分32秒"
    },
    {
      "id": "881fb733-15ce-74g7-d049-779988773333",
      "input_file": "D:/data/ortho_raw.tif",
      "output_file": "D:/data/ortho_final.tif",
      "status": "running",
      "progress": 45,
      "start_time": "2024-01-15T15:00:00.000000",
      "end_time": null,
      "current_step": "应用压缩"
    },
    {
      "id": "992fc844-26df-85h8-e15a-88aa99884444",
      "input_file": "D:/data/small_image.tif",
      "output_file": "D:/data/optimized_image.tif",
      "status": "failed",
      "progress": 0,
      "start_time": "2024-01-15T13:45:00.000000",
      "end_time": "2024-01-15T13:45:15.000000",
      "error": "输入文件损坏或格式不支持"
    },
    {
      "id": "aa3fd955-37eg-96i9-f26b-99bb00995555",
      "input_file": "D:/data/pending_file.tif",
      "output_file": "D:/data/pending_output.tif",
      "status": "pending",
      "progress": 0,
      "start_time": null,
      "end_time": null
    }
  ]
}
```

## 错误处理

### 常见错误响应

**参数缺失**:
```json
{
  "status": "error",
  "message": "缺少必要参数: input_tif, output_tif"
}
```

**输入文件不存在**:
```json
{
  "status": "error",
  "message": "输入文件不存在: D:/data/nonexistent.tif"
}
```

**输出目录不存在**:
```json
{
  "status": "error",
  "message": "输出目录不存在: D:/nonexistent_dir/"
}
```

**文件格式不支持**:
```json
{
  "status": "error",
  "message": "不支持的文件格式: .jpg"
}
```

**任务不存在**:
```json
{
  "status": "error",
  "message": "任务 770fa622-04bd-63f6-c938-668877662222 不存在"
}
```

**处理失败**:
```json
{
  "status": "error",
  "message": "TIF处理失败: 内存不足"
}
```

## 使用示例

### Python客户端示例

```python
import requests
import time
import os

class TifProcessorClient:
    def __init__(self, base_url):
        self.base_url = base_url
    
    def process_tif_sync(self, input_file, output_file, **options):
        """同步处理TIF文件"""
        url = f"{self.base_url}/api/tif/process/"
        params = {
            'input_tif': input_file,
            'output_tif': output_file,
            **options
        }
        response = requests.get(url, params=params)
        return response.json()
    
    def process_tif_async(self, input_file, output_file, **options):
        """异步处理TIF文件"""
        url = f"{self.base_url}/api/tif/execute/"
        params = {
            'input_tif': input_file,
            'output_tif': output_file,
            **options
        }
        response = requests.get(url, params=params)
        return response.json()
    
    def get_task_status(self, task_id):
        """获取任务状态"""
        url = f"{self.base_url}/api/tif/status/"
        params = {'task_id': task_id}
        response = requests.get(url, params=params)
        return response.json()
    
    def get_task_log(self, task_id):
        """获取任务日志"""
        url = f"{self.base_url}/api/tif/log/"
        params = {'task_id': task_id}
        response = requests.get(url, params=params)
        return response.json()
    
    def get_all_tasks(self):
        """获取所有任务"""
        url = f"{self.base_url}/api/tif/all/"
        response = requests.get(url)
        return response.json()
    
    def wait_for_completion(self, task_id, timeout=3600, check_interval=10):
        """等待任务完成"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status_result = self.get_task_status(task_id)
            
            if status_result['status'] != 'success':
                return False, f"无法获取任务状态: {status_result['message']}"
            
            task = status_result['task']
            task_status = task['status']
            progress = task.get('progress', 0)
            current_step = task.get('current_step', '处理中')
            
            print(f"任务进度: {progress}% - {current_step}")
            
            if task_status == 'completed':
                return True, "处理完成"
            elif task_status == 'failed':
                error_msg = task.get('error', '未知错误')
                return False, f"处理失败: {error_msg}"
            
            time.sleep(check_interval)
        
        return False, "处理超时"

# 使用示例
client = TifProcessorClient("http://127.0.0.1:5000")

# 1. 同步处理小文件
print("同步处理小文件...")
sync_result = client.process_tif_sync(
    "D:/data/small_dem.tif",
    "D:/data/small_dem_processed.tif",
    nodata_value=-32768,
    compress="DEFLATE",
    tiled=True
)

if sync_result['status'] == 'success':
    print(f"同步处理完成: {sync_result['message']}")
    print(f"处理时间: {sync_result['processing_time']}")
    print(f"压缩比: {sync_result['compression_ratio']}")
else:
    print(f"同步处理失败: {sync_result['message']}")

# 2. 异步处理大文件
print("\n异步处理大文件...")
async_result = client.process_tif_async(
    "D:/data/large_ortho.tif",
    "D:/data/large_ortho_optimized.tif",
    nodata_value=0,
    compress="JPEG",
    tiled=True,
    overviews=True,
    reproject="EPSG:4326"
)

if async_result['status'] == 'success':
    task_id = async_result['task_id']
    print(f"异步任务已启动，ID: {task_id}")
    
    # 等待完成
    success, message = client.wait_for_completion(task_id, timeout=1800)
    
    if success:
        print(f"异步处理完成: {message}")
        
        # 获取最终状态
        final_status = client.get_task_status(task_id)
        if final_status['status'] == 'success':
            task = final_status['task']
            print(f"处理时间: {task.get('processing_time', '未知')}")
    else:
        print(f"异步处理失败: {message}")
        
        # 获取错误日志
        log_result = client.get_task_log(task_id)
        if log_result['status'] == 'success':
            print("错误日志:")
            print(log_result['log'][-500:])  # 显示最后500个字符
else:
    print(f"异步任务启动失败: {async_result['message']}")
```

### 批量处理示例

```python
def batch_process_tif_files(client, file_list, output_dir, **default_options):
    """批量处理TIF文件"""
    
    results = []
    
    for input_file in file_list:
        # 生成输出文件名
        filename = os.path.basename(input_file)
        name, ext = os.path.splitext(filename)
        output_file = os.path.join(output_dir, f"{name}_processed{ext}")
        
        print(f"处理文件: {input_file}")
        
        # 根据文件大小选择处理方式
        file_size = os.path.getsize(input_file)
        
        if file_size < 100 * 1024 * 1024:  # 小于100MB使用同步处理
            result = client.process_tif_sync(input_file, output_file, **default_options)
            
            results.append({
                'input_file': input_file,
                'output_file': output_file,
                'method': 'sync',
                'result': result
            })
            
        else:  # 大于100MB使用异步处理
            result = client.process_tif_async(input_file, output_file, **default_options)
            
            if result['status'] == 'success':
                task_id = result['task_id']
                
                # 等待完成
                success, message = client.wait_for_completion(task_id, timeout=1800)
                
                results.append({
                    'input_file': input_file,
                    'output_file': output_file,
                    'method': 'async',
                    'task_id': task_id,
                    'success': success,
                    'message': message
                })
            else:
                results.append({
                    'input_file': input_file,
                    'output_file': output_file,
                    'method': 'async',
                    'success': False,
                    'message': result['message']
                })
    
    return results

# 使用示例
tif_files = [
    "D:/data/dem_01.tif",
    "D:/data/dem_02.tif", 
    "D:/data/ortho_large.tif",
    "D:/data/ortho_small.tif"
]

batch_results = batch_process_tif_files(
    client,
    tif_files,
    "D:/processed/",
    nodata_value=-9999,
    compress="LZW",
    tiled=True,
    overviews=True
)

# 输出结果摘要
print("\n批处理结果摘要:")
for result in batch_results:
    method = result['method']
    success = result.get('success', result.get('result', {}).get('status') == 'success')
    status = "✅ 成功" if success else "❌ 失败"
    print(f"{os.path.basename(result['input_file'])}: {status} ({method})")
```

### 高级处理选项示例

```python
def advanced_tif_processing(client, input_file, output_file):
    """高级TIF处理示例"""
    
    # 检查输入文件信息
    if not os.path.exists(input_file):
        print(f"输入文件不存在: {input_file}")
        return None
    
    file_size = os.path.getsize(input_file)
    print(f"输入文件大小: {file_size / (1024*1024):.1f} MB")
    
    # 根据文件大小和类型选择处理选项
    processing_options = {
        'nodata_value': -9999,
        'tiled': True,
        'overviews': True
    }
    
    # 根据文件大小选择压缩方式
    if file_size > 500 * 1024 * 1024:  # 大于500MB
        processing_options['compress'] = 'LZW'  # 无损压缩
        print("使用LZW压缩（大文件）")
    elif 'ortho' in input_file.lower():  # 正射影像
        processing_options['compress'] = 'JPEG'  # 有损压缩，适合影像
        print("使用JPEG压缩（正射影像）")
    else:  # DEM等数据
        processing_options['compress'] = 'DEFLATE'  # 无损压缩
        print("使用DEFLATE压缩（DEM数据）")
    
    # 如果需要重投影到Web墨卡托
    if 'web' in output_file.lower():
        processing_options['reproject'] = 'EPSG:3857'
        processing_options['resample'] = 'bilinear'
        print("重投影到Web墨卡托")
    
    # 执行处理
    if file_size < 50 * 1024 * 1024:  # 小于50MB同步处理
        print("使用同步处理...")
        result = client.process_tif_sync(input_file, output_file, **processing_options)
        
        if result['status'] == 'success':
            print(f"处理完成: {result['processing_time']}")
            print(f"文件大小变化: {result['file_size']['input']} -> {result['file_size']['output']}")
            print(f"压缩比: {result['compression_ratio']}")
            return result
        else:
            print(f"处理失败: {result['message']}")
            return None
    
    else:  # 大文件异步处理
        print("使用异步处理...")
        result = client.process_tif_async(input_file, output_file, **processing_options)
        
        if result['status'] == 'success':
            task_id = result['task_id']
            print(f"任务已启动: {task_id}")
            
            # 监控进度
            success, message = client.wait_for_completion(task_id, timeout=3600)
            
            if success:
                # 获取最终结果
                final_status = client.get_task_status(task_id)
                if final_status['status'] == 'success':
                    task = final_status['task']
                    print(f"处理完成: {task.get('processing_time', '未知')}")
                    return task
            else:
                print(f"处理失败: {message}")
                return None
        else:
            print(f"任务启动失败: {result['message']}")
            return None

# 使用示例
result = advanced_tif_processing(
    client,
    "D:/data/large_dem.tif",
    "D:/processed/large_dem_web.tif"
)

if result:
    print("高级处理完成!")
else:
    print("高级处理失败!")
```

## 最佳实践

### 1. 文件大小判断策略

```python
def choose_processing_method(file_path):
    """根据文件大小选择处理方法"""
    
    if not os.path.exists(file_path):
        return None, "文件不存在"
    
    file_size = os.path.getsize(file_path)
    
    # 文件大小分类
    if file_size < 10 * 1024 * 1024:  # 小于10MB
        return 'sync', '小文件，使用同步处理'
    elif file_size < 100 * 1024 * 1024:  # 10MB-100MB
        return 'sync', '中等文件，使用同步处理'
    elif file_size < 1024 * 1024 * 1024:  # 100MB-1GB
        return 'async', '大文件，使用异步处理'
    else:  # 大于1GB
        return 'async', '超大文件，使用异步处理，建议分块处理'

def get_optimal_options(file_path, file_type='auto'):
    """获取最优处理选项"""
    
    options = {
        'tiled': True,
        'overviews': True
    }
    
    # 自动检测文件类型
    if file_type == 'auto':
        filename = os.path.basename(file_path).lower()
        if any(keyword in filename for keyword in ['dem', 'dsm', 'dtm', 'elevation']):
            file_type = 'elevation'
        elif any(keyword in filename for keyword in ['ortho', 'rgb', 'image']):
            file_type = 'image'
        else:
            file_type = 'general'
    
    # 根据文件类型设置选项
    if file_type == 'elevation':
        options.update({
            'nodata_value': -9999,
            'compress': 'DEFLATE'  # 无损压缩保持精度
        })
    elif file_type == 'image':
        options.update({
            'nodata_value': 0,
            'compress': 'JPEG'  # 有损压缩减小文件大小
        })
    else:
        options.update({
            'nodata_value': -9999,
            'compress': 'LZW'  # 通用无损压缩
        })
    
    return options
```

### 2. 进度监控和通知

```python
class ProgressMonitor:
    def __init__(self, client, task_id):
        self.client = client
        self.task_id = task_id
        self.last_progress = 0
        self.callbacks = []
    
    def add_callback(self, callback):
        """添加进度回调函数"""
        self.callbacks.append(callback)
    
    def monitor(self, check_interval=5):
        """监控任务进度"""
        
        while True:
            status_result = self.client.get_task_status(self.task_id)
            
            if status_result['status'] != 'success':
                break
            
            task = status_result['task']
            current_progress = task.get('progress', 0)
            task_status = task['status']
            current_step = task.get('current_step', '处理中')
            
            # 如果进度有变化，调用回调函数
            if current_progress != self.last_progress:
                for callback in self.callbacks:
                    callback(current_progress, current_step, task_status)
                self.last_progress = current_progress
            
            if task_status in ['completed', 'failed', 'cancelled']:
                break
            
            time.sleep(check_interval)
        
        return task_status

def progress_callback(progress, step, status):
    """进度回调函数"""
    print(f"[{time.strftime('%H:%M:%S')}] 进度: {progress}% - {step}")
    
    # 可以在这里添加其他通知方式，如发送邮件、更新数据库等
    if progress == 100:
        print("🎉 处理完成!")

# 使用示例
result = client.process_tif_async("D:/data/large_file.tif", "D:/output/processed.tif")

if result['status'] == 'success':
    task_id = result['task_id']
    
    # 创建进度监控器
    monitor = ProgressMonitor(client, task_id)
    monitor.add_callback(progress_callback)
    
    # 开始监控
    final_status = monitor.monitor(check_interval=10)
    print(f"最终状态: {final_status}")
```

### 3. 错误恢复和重试

```python
def robust_tif_processing(client, input_file, output_file, max_retries=3, **options):
    """带错误恢复的TIF处理"""
    
    for attempt in range(max_retries):
        try:
            print(f"尝试 {attempt + 1}/{max_retries}: 处理 {input_file}")
            
            # 检查文件大小选择处理方式
            file_size = os.path.getsize(input_file)
            
            if file_size < 50 * 1024 * 1024:  # 小文件同步处理
                result = client.process_tif_sync(input_file, output_file, **options)
                
                if result['status'] == 'success':
                    print(f"同步处理成功: {result['message']}")
                    return result
                else:
                    print(f"同步处理失败: {result['message']}")
                    if attempt == max_retries - 1:
                        return result
            
            else:  # 大文件异步处理
                result = client.process_tif_async(input_file, output_file, **options)
                
                if result['status'] == 'success':
                    task_id = result['task_id']
                    
                    # 等待完成
                    success, message = client.wait_for_completion(task_id, timeout=3600)
                    
                    if success:
                        print(f"异步处理成功: {message}")
                        return {'status': 'success', 'task_id': task_id, 'message': message}
                    else:
                        print(f"异步处理失败: {message}")
                        if attempt == max_retries - 1:
                            return {'status': 'error', 'message': message}
                else:
                    print(f"异步任务启动失败: {result['message']}")
                    if attempt == max_retries - 1:
                        return result
            
            # 重试前等待
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 30  # 递增等待时间
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
                
        except Exception as e:
            print(f"尝试 {attempt + 1} 出现异常: {e}")
            if attempt == max_retries - 1:
                return {'status': 'error', 'message': str(e)}
            
            # 异常后等待更长时间
            wait_time = (attempt + 1) * 60
            print(f"等待 {wait_time} 秒后重试...")
            time.sleep(wait_time)
    
    return {'status': 'error', 'message': '所有重试都失败了'}
```

## 故障排除

### 常见问题诊断

```python
def diagnose_tif_processing_issues(client, input_file, output_file):
    """诊断TIF处理问题"""
    
    issues = []
    
    # 1. 检查输入文件
    if not os.path.exists(input_file):
        issues.append(f"输入文件不存在: {input_file}")
        return issues
    
    # 2. 检查文件权限
    if not os.access(input_file, os.R_OK):
        issues.append(f"输入文件无读取权限: {input_file}")
    
    # 3. 检查输出目录
    output_dir = os.path.dirname(output_file)
    if not os.path.exists(output_dir):
        issues.append(f"输出目录不存在: {output_dir}")
    elif not os.access(output_dir, os.W_OK):
        issues.append(f"输出目录无写入权限: {output_dir}")
    
    # 4. 检查文件格式
    if not input_file.lower().endswith(('.tif', '.tiff')):
        issues.append(f"输入文件不是TIF格式: {input_file}")
    
    # 5. 检查文件大小
    try:
        file_size = os.path.getsize(input_file)
        if file_size == 0:
            issues.append("输入文件为空")
        elif file_size > 10 * 1024 * 1024 * 1024:  # 大于10GB
            issues.append(f"文件过大 ({file_size / (1024**3):.1f} GB)，可能导致处理失败")
    except Exception as e:
        issues.append(f"无法获取文件大小: {e}")
    
    # 6. 检查磁盘空间
    try:
        free_space = psutil.disk_usage(output_dir).free
        if free_space < file_size * 2:  # 需要至少2倍的空间
            issues.append(f"磁盘空间不足，需要至少 {file_size * 2 / (1024**3):.1f} GB")
    except:
        issues.append("无法检查磁盘空间")
    
    # 7. 检查系统资源
    try:
        memory = psutil.virtual_memory()
        if memory.available < 1024 * 1024 * 1024:  # 小于1GB可用内存
            issues.append(f"可用内存不足: {memory.available / (1024**3):.1f} GB")
    except:
        issues.append("无法检查系统内存")
    
    return issues

# 使用示例
issues = diagnose_tif_processing_issues(
    client,
    "D:/data/large_dem.tif",
    "D:/output/processed_dem.tif"
)

if issues:
    print("发现以下问题:")
    for issue in issues:
        print(f"  ❌ {issue}")
else:
    print("✅ 未发现问题，可以开始处理")
```

TIF处理API文档已完成，涵盖了所有5个接口的详细说明、使用示例、最佳实践和故障排除。接下来我将创建GeoServer发布API文档。
