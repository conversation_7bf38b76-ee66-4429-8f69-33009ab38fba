2025-07-12 10:58:44,312 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250712_105844.log
2025-07-12 10:58:44,315 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 10:58:44,315 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 10:58:44,331 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 10:58:44,339 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 10:58:44,340 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 10:58:44,351 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 10:58:44,355 - root - INFO - === GeoServer REST API服务 ===
2025-07-12 10:58:44,355 - root - INFO - 主机: 0.0.0.0
2025-07-12 10:58:44,355 - root - INFO - 端口: 5083
2025-07-12 10:58:44,356 - root - INFO - 调试模式: 禁用
2025-07-12 10:58:44,356 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-12 10:58:44,369 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-12 10:58:44,369 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 10:58:47,410 - root - INFO - 工作区 'tttt' 不存在，正在创建...
2025-07-12 10:58:47,450 - root - INFO - 成功创建工作区: tttt
2025-07-12 10:58:47,451 - root - INFO - 成功创建工作区 'tttt'
2025-07-12 10:58:47,467 - root - INFO - 在目录 D:/Drone_Project/geoserverapi/data/20250701 中找到 2 个GeoTIFF文件
2025-07-12 10:58:47,469 - root - INFO - 正在发布GeoTIFF: D:/Drone_Project/geoserverapi/data/20250701\nanning.tif
2025-07-12 10:58:47,496 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/20250701/file.geotiff
2025-07-12 10:58:47,681 - root - INFO - 成功通过REST API发布GeoTIFF: 201
2025-07-12 10:58:47,780 - root - INFO - 成功发布GeoTIFF: tttt:20250701:nanning
2025-07-12 10:58:47,819 - root - INFO - 图层验证成功(使用存储名): tttt:20250701
2025-07-12 10:58:47,824 - root - INFO - 正在发布GeoTIFF: D:/Drone_Project/geoserverapi/data/20250701\nanning2.tif
2025-07-12 10:58:47,856 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/20250701/file.geotiff
2025-07-12 10:58:48,059 - root - INFO - 成功通过REST API发布GeoTIFF: 201
2025-07-12 10:58:48,059 - root - INFO - 成功发布GeoTIFF: tttt:20250701:nanning2
2025-07-12 10:58:48,099 - root - INFO - 图层验证成功(使用存储名): tttt:20250701
2025-07-12 10:58:48,103 - root - INFO - 成功发布 2/2 个GeoTIFF文件
2025-07-12 10:58:48,114 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 10:58:48] "GET /api/management/geotiff-directories/publish?directory=D:/Drone_Project/geoserverapi/data/20250701&workspace=tttt&charset=UTF-8 HTTP/1.1" 200 -
