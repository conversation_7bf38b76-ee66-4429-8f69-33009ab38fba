2025-07-21 10:29:25,354 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250721_102925.log
2025-07-21 10:29:25,356 - geo_publisher - INFO - 加载了 16 个任务状态
2025-07-21 10:29:25,373 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-21 10:29:25,374 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-21 10:29:25,385 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-21 10:29:25,391 - root - INFO - === GeoServer REST API服务 ===
2025-07-21 10:29:25,392 - root - INFO - 主机: 0.0.0.0
2025-07-21 10:29:25,392 - root - INFO - 端口: 5083
2025-07-21 10:29:25,393 - root - INFO - 调试模式: 禁用
2025-07-21 10:29:25,393 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-21 10:29:25,469 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-21 10:29:25,470 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-21 10:52:54,444 - batch_executor - INFO - 启动任务 b6dd38ff-63bd-4128-9ee1-e087b429ac28: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171601/project
2025-07-21 10:52:54,446 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 10:52:54] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-21 10:52:54,472 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 10:52:54] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 10:53:56,238 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 10:53:56] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 10:54:58,255 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 10:54:58] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 10:55:59,760 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 10:55:59] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 10:57:01,516 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 10:57:01] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 10:58:03,658 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 10:58:03] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 10:59:05,225 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 10:59:05] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 11:00:06,894 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:00:06] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 11:01:08,616 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:01:08] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 11:02:11,016 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:02:11] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 11:03:12,945 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:03:12] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 11:04:13,419 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:04:13] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 11:05:13,875 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:05:13] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 11:06:14,379 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:06:14] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 11:07:14,532 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:07:14] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 11:08:14,546 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:08:14] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 11:09:14,584 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:09:14] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 11:10:14,598 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:10:14] "GET /api/batch/status?task_id=b6dd38ff-63bd-4128-9ee1-e087b429ac28 HTTP/1.1" 200 -
2025-07-21 11:10:16,711 - tif_executor - INFO - 启动TIF处理任务 3fb3bacd-fccd-47a5-90b0-0a8231300ecb: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-21 11:10:16,711 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - INFO - TIF处理任务 3fb3bacd-fccd-47a5-90b0-0a8231300ecb 开始执行
2025-07-21 11:10:16,713 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:10:16] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-21 11:10:16,718 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - INFO - 开始时间: 2025-07-21 11:10:16
2025-07-21 11:10:16,723 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - INFO - 处理参数: {
  "input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-21 11:10:16,734 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-21 11:10:16,751 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:10:16] "GET /api/tif/status?task_id=3fb3bacd-fccd-47a5-90b0-0a8231300ecb HTTP/1.1" 200 -
2025-07-21 11:10:20,390 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (11:10:20)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-21 11:10:20,395 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-21 11:10:20,642 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:04,  4.79it/s]
2025-07-21 11:10:20,884 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 19.82it/s]
2025-07-21 11:10:20,985 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理数据块:  48%|#################################3                                    | 10/21 [00:00<00:00, 19.84it/s]
2025-07-21 11:10:21,169 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 22.80it/s]
2025-07-21 11:10:21,255 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 25.51it/s]
2025-07-21 11:10:21,263 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-21 11:10:21,264 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-21 11:10:21,311 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 475.96it/s]
2025-07-21 11:10:22,298 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 1.97 秒 (11:10:22)预计总处理时间: 约 5.92 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif (11:10:22)写入带有nodata值的影像...
2025-07-21 11:10:22,299 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-21 11:10:29,539 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-21 11:10:29,543 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:10:30,721 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:01<00:23,  1.17s/it]
2025-07-21 11:10:30,722 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:10:31,582 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:02<00:18,  1.01it/s]
2025-07-21 11:10:31,583 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:10:32,786 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:03<00:19,  1.09s/it]
2025-07-21 11:10:32,787 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:10:33,582 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:04<00:16,  1.03it/s]
2025-07-21 11:10:33,583 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:10:33,811 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:04<00:11,  1.42it/s]
2025-07-21 11:10:33,811 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:10:33,918 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:04<00:01,  6.06it/s]
2025-07-21 11:10:33,919 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:10:34,019 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:04<00:00,  9.94it/s]
2025-07-21 11:10:34,020 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:10:34,077 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:11:16,817 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:11:16] "GET /api/tif/status?task_id=3fb3bacd-fccd-47a5-90b0-0a8231300ecb HTTP/1.1" 200 -
2025-07-21 11:11:33,468 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:11<02:22, 71.16s/it]
2025-07-21 11:11:43,220 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-21 11:11:43,223 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:11:43,383 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  6.43it/s]
2025-07-21 11:11:43,385 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:11:43,500 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:01, 16.38it/s]
2025-07-21 11:11:43,500 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:11:43,624 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:00, 19.76it/s]
2025-07-21 11:11:43,625 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:11:43,736 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 22.26it/s]
2025-07-21 11:11:43,737 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:11:43,840 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:00<00:00, 24.40it/s]
2025-07-21 11:11:43,841 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:11:43,969 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:00<00:00, 26.82it/s]
2025-07-21 11:11:43,970 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:11:45,395 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:02<00:00,  5.76it/s]
2025-07-21 11:11:45,395 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:11:45,701 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:12:17,751 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:12:17] "GET /api/tif/status?task_id=3fb3bacd-fccd-47a5-90b0-0a8231300ecb HTTP/1.1" 200 -
2025-07-21 11:13:18,444 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:13:18] "GET /api/tif/status?task_id=3fb3bacd-fccd-47a5-90b0-0a8231300ecb HTTP/1.1" 200 -
2025-07-21 11:14:01,235 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [03:38<01:56, 116.22s/it]
2025-07-21 11:14:07,917 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-21 11:14:07,917 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:14:08,080 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  6.25it/s]
2025-07-21 11:14:08,081 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:14:08,182 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:00, 17.32it/s]
2025-07-21 11:14:08,183 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:14:08,316 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:00<00:00, 23.19it/s]
2025-07-21 11:14:08,317 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:14:08,429 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:00<00:00, 24.41it/s]
2025-07-21 11:14:08,430 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:14:08,535 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:00<00:00, 25.71it/s]
2025-07-21 11:14:08,536 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:14:08,640 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [00:00<00:00, 26.70it/s]
2025-07-21 11:14:08,640 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:14:08,750 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [00:00<00:00, 26.82it/s]
2025-07-21 11:14:08,751 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:14:08,947 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - [A
2025-07-21 11:14:19,614 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:14:19] "GET /api/tif/status?task_id=3fb3bacd-fccd-47a5-90b0-0a8231300ecb HTTP/1.1" 200 -
2025-07-21 11:15:23,075 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:15:23] "GET /api/tif/status?task_id=3fb3bacd-fccd-47a5-90b0-0a8231300ecb HTTP/1.1" 200 -
2025-07-21 11:16:25,432 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:16:25] "GET /api/tif/status?task_id=3fb3bacd-fccd-47a5-90b0-0a8231300ecb HTTP/1.1" 200 -
2025-07-21 11:16:27,741 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [06:05<00:00, 130.05s/it]
2025-07-21 11:16:27,742 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [06:05<00:00, 121.81s/it]
2025-07-21 11:17:27,228 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:17:27] "GET /api/tif/status?task_id=3fb3bacd-fccd-47a5-90b0-0a8231300ecb HTTP/1.1" 200 -
2025-07-21 11:17:49,910 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - INFO - 影像保存完成，耗时: 365.64 秒 (11:16:27)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp (11:16:27)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif创建掩码，识别有效区域...轻微平滑处理掩码，减少边缘锯齿...寻找有效区域轮廓...找到 1 个初始轮廓使用所有 1 个轮廓，不进行面积过滤转换轮廓为地理坐标...
2025-07-21 11:17:49,933 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-21 11:17:50,515 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.75it/s]
2025-07-21 11:17:50,517 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.75it/s]
2025-07-21 11:17:50,631 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - INFO - 成功创建 1 个多边形合并多边形...创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp写入shapefile特征...处理单个Polygon
2025-07-21 11:17:50,632 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
写入多边形:   0%|                                                                                | 0/1 [00:00<?, ?it/s]
2025-07-21 11:17:50,644 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - ERROR - 
写入多边形: 100%|#######################################################################| 1/1 [00:00<00:00, 109.65it/s]
2025-07-21 11:18:00,669 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - INFO - TIF处理任务 3fb3bacd-fccd-47a5-90b0-0a8231300ecb 执行成功
2025-07-21 11:18:00,671 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - INFO - 完成时间: 2025-07-21 11:18:00
2025-07-21 11:18:00,675 - tif_task_3fb3bacd-fccd-47a5-90b0-0a8231300ecb - INFO - 状态: 运行成功
2025-07-21 11:18:30,076 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:18:30] "GET /api/tif/status?task_id=3fb3bacd-fccd-47a5-90b0-0a8231300ecb HTTP/1.1" 200 -
2025-07-21 11:18:31,823 - geo_task_0606cfc0-cecb-480e-8ba3-da2988e8a32a - INFO - GeoServer发布任务 0606cfc0-cecb-480e-8ba3-da2988e8a32a 开始执行
2025-07-21 11:18:31,821 - geo_publisher - INFO - 启动GeoTIFF发布任务 0606cfc0-cecb-480e-8ba3-da2988e8a32a: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-07-21 11:18:31,828 - geo_task_0606cfc0-cecb-480e-8ba3-da2988e8a32a - INFO - 开始时间: 2025-07-21 11:18:31
2025-07-21 11:18:31,833 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:18:31] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-21 11:18:31,833 - geo_task_0606cfc0-cecb-480e-8ba3-da2988e8a32a - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-21 11:18:31,838 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-21 11:18:31,839 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-21 11:18:31,858 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:18:31] "GET /api/geo/status?task_id=0606cfc0-cecb-480e-8ba3-da2988e8a32a HTTP/1.1" 200 -
2025-07-21 11:18:32,437 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-21 11:18:32,437 - root - INFO - 未提供存储名称，使用文件名: 20250705171601
2025-07-21 11:18:32,441 - root - INFO - 未提供图层名称，使用存储名称: 20250705171601
2025-07-21 11:18:32,831 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601.tif
2025-07-21 11:19:10,995 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 到 'testodm:20250705171601'
2025-07-21 11:19:11,127 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171601
2025-07-21 11:19:11,128 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 成功发布为图层 'testodm:20250705171601'
2025-07-21 11:19:11,134 - geo_task_0606cfc0-cecb-480e-8ba3-da2988e8a32a - INFO - GeoServer发布任务 0606cfc0-cecb-480e-8ba3-da2988e8a32a 执行成功
2025-07-21 11:19:11,135 - geo_task_0606cfc0-cecb-480e-8ba3-da2988e8a32a - INFO - 完成时间: 2025-07-21 11:19:11
2025-07-21 11:19:11,136 - geo_task_0606cfc0-cecb-480e-8ba3-da2988e8a32a - INFO - 状态: 发布成功
2025-07-21 11:19:33,005 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:19:33] "GET /api/geo/status?task_id=0606cfc0-cecb-480e-8ba3-da2988e8a32a HTTP/1.1" 200 -
2025-07-21 11:20:02,444 - batch_executor - INFO - 启动任务 d199ba41-c702-447c-a5ff-cf0269b8a3b1: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
2025-07-21 11:20:02,455 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:20:02] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-21 11:20:02,480 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:20:02] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:21:03,685 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:21:03] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:22:05,340 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:22:05] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:23:10,144 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:23:10] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:23:50,829 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:23:50] "[33mGET /api/management/layers/bbox/?workspace=test_myworkspace&layer=nanning HTTP/1.1[0m" 404 -
2025-07-21 11:23:51,314 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:23:51] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-21 11:23:51,327 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-21 11:23:51,442 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-21 11:23:51,443 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:23:51] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-21 11:23:55,757 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:23:55] "[33mGET /api/management/layers/bbox/?workspace=test_myworkspace&layer=nanning HTTP/1.1[0m" 404 -
2025-07-21 11:24:03,132 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:24:03] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-21 11:24:03,153 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-21 11:24:03,249 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-21 11:24:03,347 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:24:03] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-21 11:24:11,672 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:24:11] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:24:32,440 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:24:32] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-21 11:24:32,449 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-21 11:24:32,499 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-21 11:24:32,501 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:24:32] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-21 11:24:43,785 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:24:43] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171601 HTTP/1.1" 200 -
2025-07-21 11:24:43,845 - root - INFO - 获取图层 testodm:20250705171601 的信息
2025-07-21 11:24:43,934 - root - INFO - 成功获取图层 testodm:20250705171601 的边界框信息
2025-07-21 11:24:43,937 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:24:43] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171601 HTTP/1.1" 200 -
2025-07-21 11:25:09,591 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:25:09] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-21 11:25:10,062 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-21 11:25:10,170 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-21 11:25:10,208 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:25:10] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-21 11:25:15,118 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:25:15] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:26:16,388 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:26:16] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:27:17,880 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:27:17] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:28:10,050 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:28:10] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171601 HTTP/1.1" 200 -
2025-07-21 11:28:10,055 - root - INFO - 获取图层 testodm:20250705171601 的信息
2025-07-21 11:28:10,084 - root - INFO - 成功获取图层 testodm:20250705171601 的边界框信息
2025-07-21 11:28:10,085 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 11:28:10] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171601 HTTP/1.1" 200 -
2025-07-21 11:28:19,027 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:28:19] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:29:20,136 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:29:20] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:30:22,215 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:30:22] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:31:23,487 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:31:23] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:32:25,463 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:32:25] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:33:26,805 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:33:26] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:34:27,885 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:34:27] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:35:29,192 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:35:29] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:36:30,643 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:36:30] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:37:32,013 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:37:32] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:38:33,405 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:38:33] "GET /api/batch/status?task_id=d199ba41-c702-447c-a5ff-cf0269b8a3b1 HTTP/1.1" 200 -
2025-07-21 11:38:34,654 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - INFO - TIF处理任务 f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 开始执行
2025-07-21 11:38:34,653 - tif_executor - INFO - 启动TIF处理任务 f3a51a13-d851-4c11-8cac-ab5a5c40a9d8: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp
2025-07-21 11:38:34,656 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - INFO - 开始时间: 2025-07-21 11:38:34
2025-07-21 11:38:34,661 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:38:34] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-21 11:38:34,663 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - INFO - 处理参数: {
  "input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-21 11:38:34,690 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:38:34] "GET /api/tif/status?task_id=f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1" 200 -
2025-07-21 11:38:38,639 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 18100x20866x3估计内存使用: 总计 5.63 GB, 单个处理块 276.18 MB开始创建掩码... (11:38:38)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-21 11:38:38,676 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-21 11:38:38,894 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理数据块:  29%|####################2                                                  | 6/21 [00:00<00:00, 38.40it/s]
2025-07-21 11:38:39,094 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理数据块:  48%|#################################3                                    | 10/21 [00:00<00:00, 26.49it/s]
2025-07-21 11:38:39,277 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理数据块:  67%|##############################################6                       | 14/21 [00:00<00:00, 24.47it/s]
2025-07-21 11:38:39,454 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理数据块:  81%|########################################################6             | 17/21 [00:00<00:00, 21.51it/s]
2025-07-21 11:38:39,493 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 27.82it/s]
2025-07-21 11:38:39,501 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-21 11:38:39,502 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-21 11:38:39,553 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 425.45it/s]
2025-07-21 11:38:40,640 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 377674600, 有效像素 130471159 (34.55%), 无效像素 247203441 (65.45%)掩码创建完成，耗时: 2.17 秒 (11:38:40)预计总处理时间: 约 6.52 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif (11:38:40)写入带有nodata值的影像...
2025-07-21 11:38:40,641 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-21 11:38:41,526 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-21 11:38:41,526 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:38:41,664 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:00, 22.27it/s]
2025-07-21 11:38:41,664 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:38:41,778 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 43.48it/s]
2025-07-21 11:38:41,779 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:38:41,878 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:00<00:00, 50.18it/s]
2025-07-21 11:38:41,879 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:38:41,965 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:39:27,065 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:46<01:32, 46.42s/it]
2025-07-21 11:39:28,040 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-21 11:39:28,040 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:39:28,193 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  6.67it/s]
2025-07-21 11:39:28,194 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:39:28,322 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:01, 15.78it/s]
2025-07-21 11:39:28,323 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:39:28,433 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:00, 20.40it/s]
2025-07-21 11:39:28,433 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:39:28,551 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 22.25it/s]
2025-07-21 11:39:28,552 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:39:28,694 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:00<00:00, 21.74it/s]
2025-07-21 11:39:28,696 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:39:28,828 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:00<00:00, 22.04it/s]
2025-07-21 11:39:28,828 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:39:28,995 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:00<00:00, 20.41it/s]
2025-07-21 11:39:28,996 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:39:29,070 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:39:35,845 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:39:35] "GET /api/tif/status?task_id=f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1" 200 -
2025-07-21 11:40:36,845 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:40:36] "GET /api/tif/status?task_id=f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1" 200 -
2025-07-21 11:41:38,125 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:41:38] "GET /api/tif/status?task_id=f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1" 200 -
2025-07-21 11:41:43,792 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [03:03<01:39, 99.54s/it]
2025-07-21 11:41:44,751 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-21 11:41:44,752 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:41:44,946 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  5.22it/s]
2025-07-21 11:41:44,947 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:41:45,082 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:01, 13.70it/s]
2025-07-21 11:41:45,082 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:41:45,209 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:00, 17.70it/s]
2025-07-21 11:41:45,237 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:41:45,345 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:00, 16.54it/s]
2025-07-21 11:41:45,346 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:41:45,461 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 12/21 [00:00<00:00, 19.48it/s]
2025-07-21 11:41:45,461 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:41:45,572 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 15/21 [00:00<00:00, 21.80it/s]
2025-07-21 11:41:45,573 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:41:45,683 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 18/21 [00:00<00:00, 23.37it/s]
2025-07-21 11:41:45,684 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:41:45,799 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
波段 3/3 写入进度: 100%|###############################################################| 21/21 [00:01<00:00, 24.17it/s]
2025-07-21 11:41:45,799 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:41:45,802 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - [A
2025-07-21 11:42:39,895 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:42:39] "GET /api/tif/status?task_id=f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1" 200 -
2025-07-21 11:43:41,738 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:43:41] "GET /api/tif/status?task_id=f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1" 200 -
2025-07-21 11:44:01,834 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [05:21<00:00, 117.12s/it]
2025-07-21 11:44:01,836 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [05:21<00:00, 107.06s/it]
2025-07-21 11:44:43,791 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:44:43] "GET /api/tif/status?task_id=f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1" 200 -
2025-07-21 11:45:33,808 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - INFO - 影像保存完成，耗时: 321.46 秒 (11:44:02)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp (11:44:02)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif创建掩码，识别有效区域...轻微平滑处理掩码，减少边缘锯齿...寻找有效区域轮廓...找到 2 个初始轮廓使用所有 2 个轮廓，不进行面积过滤转换轮廓为地理坐标...
2025-07-21 11:45:33,832 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/2 [00:00<?, ?it/s]
2025-07-21 11:45:34,358 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理轮廓: 100%|##########################################################################| 2/2 [00:00<00:00,  3.82it/s]
2025-07-21 11:45:34,359 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
处理轮廓: 100%|##########################################################################| 2/2 [00:00<00:00,  3.81it/s]
2025-07-21 11:45:34,474 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - INFO - 成功创建 2 个多边形合并多边形...创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp写入shapefile特征...处理MultiPolygon，包含 2 个多边形
2025-07-21 11:45:34,476 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
写入多边形:   0%|                                                                                | 0/2 [00:00<?, ?it/s]
2025-07-21 11:45:34,498 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - ERROR - 
写入多边形: 100%|#######################################################################| 2/2 [00:00<00:00, 110.46it/s]
2025-07-21 11:45:43,676 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - INFO - TIF处理任务 f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 执行成功
2025-07-21 11:45:43,677 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - INFO - 完成时间: 2025-07-21 11:45:43
2025-07-21 11:45:43,679 - tif_task_f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 - INFO - 状态: 运行成功
2025-07-21 11:45:45,694 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:45:45] "GET /api/tif/status?task_id=f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1" 200 -
2025-07-21 11:45:47,280 - geo_task_34dc3714-3005-42b1-8857-7a1fd8b98aaa - INFO - GeoServer发布任务 34dc3714-3005-42b1-8857-7a1fd8b98aaa 开始执行
2025-07-21 11:45:47,279 - geo_publisher - INFO - 启动GeoTIFF发布任务 34dc3714-3005-42b1-8857-7a1fd8b98aaa: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-21 11:45:47,283 - geo_task_34dc3714-3005-42b1-8857-7a1fd8b98aaa - INFO - 开始时间: 2025-07-21 11:45:47
2025-07-21 11:45:47,285 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:45:47] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-21 11:45:47,286 - geo_task_34dc3714-3005-42b1-8857-7a1fd8b98aaa - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-21 11:45:47,290 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-21 11:45:47,293 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-21 11:45:47,322 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:45:47] "GET /api/geo/status?task_id=34dc3714-3005-42b1-8857-7a1fd8b98aaa HTTP/1.1" 200 -
2025-07-21 11:45:47,385 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-21 11:45:47,386 - root - INFO - 未提供存储名称，使用文件名: 20250705171602
2025-07-21 11:45:47,388 - root - INFO - 未提供图层名称，使用存储名称: 20250705171602
2025-07-21 11:45:47,409 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171602\20250705171602.tif
2025-07-21 11:46:28,556 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 到 'testodm:20250705171602'
2025-07-21 11:46:28,617 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171602
2025-07-21 11:46:28,618 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 成功发布为图层 'testodm:20250705171602'
2025-07-21 11:46:28,624 - geo_task_34dc3714-3005-42b1-8857-7a1fd8b98aaa - INFO - GeoServer发布任务 34dc3714-3005-42b1-8857-7a1fd8b98aaa 执行成功
2025-07-21 11:46:28,625 - geo_task_34dc3714-3005-42b1-8857-7a1fd8b98aaa - INFO - 完成时间: 2025-07-21 11:46:28
2025-07-21 11:46:28,626 - geo_task_34dc3714-3005-42b1-8857-7a1fd8b98aaa - INFO - 状态: 发布成功
2025-07-21 11:46:48,928 - werkzeug - INFO - 127.0.0.1 - - [21/Jul/2025 11:46:48] "GET /api/geo/status?task_id=34dc3714-3005-42b1-8857-7a1fd8b98aaa HTTP/1.1" 200 -
2025-07-21 12:10:25,439 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 12:10:25] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-21 12:10:26,177 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-21 12:10:26,235 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-21 12:10:26,237 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 12:10:26] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-21 12:10:30,556 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 12:10:30] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171602 HTTP/1.1" 200 -
2025-07-21 12:10:30,563 - root - INFO - 获取图层 testodm:20250705171602 的信息
2025-07-21 12:10:30,742 - root - INFO - 成功获取图层 testodm:20250705171602 的边界框信息
2025-07-21 12:10:30,745 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 12:10:30] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171602 HTTP/1.1" 200 -
2025-07-21 15:18:37,878 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 15:18:37] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-21 15:18:37,885 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-21 15:18:37,974 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-21 15:18:37,977 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 15:18:37] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-21 15:18:45,129 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 15:18:45] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171602 HTTP/1.1" 200 -
2025-07-21 15:18:45,136 - root - INFO - 获取图层 testodm:20250705171602 的信息
2025-07-21 15:18:45,164 - root - INFO - 成功获取图层 testodm:20250705171602 的边界框信息
2025-07-21 15:18:45,165 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 15:18:45] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171602 HTTP/1.1" 200 -
2025-07-21 19:23:27,126 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 19:23:27] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-21 19:23:27,131 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-21 19:23:27,296 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-21 19:23:27,298 - werkzeug - INFO - 192.168.43.148 - - [21/Jul/2025 19:23:27] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
