2025-07-14 11:05:10,922 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250714_110510.log
2025-07-14 11:05:10,924 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 11:05:10,924 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 11:05:10,983 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 11:05:10,993 - batch_executor - INFO - 加载了 13 个任务状态
2025-07-14 11:05:11,004 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 11:05:11,004 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 11:05:11,020 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 11:05:11,025 - root - INFO - === GeoServer REST API服务 ===
2025-07-14 11:05:11,025 - root - INFO - 主机: 0.0.0.0
2025-07-14 11:05:11,026 - root - INFO - 端口: 5083
2025-07-14 11:05:11,026 - root - INFO - 调试模式: 禁用
2025-07-14 11:05:11,026 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-14 11:05:11,041 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-14 11:05:11,042 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-14 11:06:35,039 - batch_executor - INFO - 启动任务 a1d750b9-5e16-40a8-8c5b-b5c6327e3554: D:\Drone_Project\ODM\ODM\run.bat c://user/20250714140500/project/images --fast-orthophoto
2025-07-14 11:06:35,042 - werkzeug - INFO - ************** - - [14/Jul/2025 11:06:35] "GET /api/batch/execute?batch_path=D:\\Drone_Project\\ODM\\ODM\\run.bat&pos_args=c://user/20250714140500/project/images&flags=fast-orthophoto HTTP/1.1" 200 -
2025-07-14 11:06:35,122 - werkzeug - INFO - ************** - - [14/Jul/2025 11:06:35] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-14 11:08:57,374 - batch_executor - INFO - 启动任务 f30071ad-d51c-43c0-bb1f-02a8f48b4448: D:\Drone_Project\ODM\ODM\run.bat D:\Drone_Project\dataset\DJITESTIMAGE\20250714140500\project --fast-orthophoto
2025-07-14 11:08:57,375 - werkzeug - INFO - ************** - - [14/Jul/2025 11:08:57] "GET /api/batch/execute?batch_path=D:\\Drone_Project\\ODM\\ODM\\run.bat&pos_args=D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250714140500\\project&flags=fast-orthophoto HTTP/1.1" 200 -
2025-07-14 11:09:52,666 - batch_executor - INFO - 启动任务 f8e80a38-1c1e-4286-9a13-580985be1212: D:\Drone_Project\ODM\ODM\run.bat D:\Drone_Project\dataset\DJITESTIMAGE\20250714140500\project --fast-orthophoto
2025-07-14 11:09:52,773 - werkzeug - INFO - ************** - - [14/Jul/2025 11:09:52] "GET /api/batch/execute?batch_path=D:\\Drone_Project\\ODM\\ODM\\run.bat&pos_args=D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250714140500\\project&flags=fast-orthophoto HTTP/1.1" 200 -
