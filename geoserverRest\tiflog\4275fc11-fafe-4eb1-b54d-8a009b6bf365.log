2025-08-04 14:59:12,397 - INFO - ============ TIF处理任务 4275fc11-fafe-4eb1-b54d-8a009b6bf365 开始执行 ============
2025-08-04 14:59:12,398 - INFO - 开始时间: 2025-08-04 14:59:12
2025-08-04 14:59:12,399 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-08-04 14:59:12,400 - INFO - 系统信息:
2025-08-04 14:59:12,401 - INFO -   操作系统: Windows 10.0.19045
2025-08-04 14:59:12,402 - INFO -   Python版本: 3.8.20
2025-08-04 14:59:12,403 - INFO -   GDAL版本: 3.9.2
2025-08-04 14:59:12,404 - INFO -   GPU可用: 否
2025-08-04 14:59:12,404 - INFO - 检查参数有效性...
2025-08-04 14:59:12,405 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 14:59:12,405 - INFO - 开始执行TIF处理流程...
2025-08-04 14:59:12,406 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 14:59:12,407 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-04 14:59:12,408 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-08-04 14:59:33,701 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17007, 高=20364, 波段数=4开始读取影像数据，大小约: 5284.58 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (14:59:29)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-08-04 14:59:33,733 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-08-04 14:59:37,936 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:04<01:23,  4.16s/it]
2025-08-04 14:59:38,476 - ERROR - 
处理数据块:  10%|######7                                                                | 2/21 [00:04<00:38,  2.03s/it]
2025-08-04 14:59:40,235 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:06<00:07,  1.72it/s]
2025-08-04 14:59:40,482 - ERROR - 
处理数据块:  43%|##############################4                                        | 9/21 [00:06<00:06,  1.90it/s]
2025-08-04 14:59:40,967 - ERROR - 
处理数据块:  48%|#################################3                                    | 10/21 [00:07<00:05,  1.93it/s]
2025-08-04 14:59:42,382 - ERROR - 
处理数据块:  52%|####################################6                                 | 11/21 [00:08<00:07,  1.41it/s]
2025-08-04 14:59:42,914 - ERROR - 
处理数据块:  67%|##############################################6                       | 14/21 [00:09<00:03,  2.20it/s]
2025-08-04 14:59:43,330 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:09<00:02,  2.23it/s]
2025-08-04 14:59:43,487 - ERROR - 
处理数据块:  76%|#####################################################3                | 16/21 [00:09<00:01,  2.59it/s]
2025-08-04 14:59:43,906 - ERROR - 
处理数据块:  81%|########################################################6             | 17/21 [00:10<00:01,  2.53it/s]
2025-08-04 14:59:45,157 - ERROR - 
处理数据块:  86%|############################################################          | 18/21 [00:11<00:01,  1.65it/s]
2025-08-04 14:59:45,448 - ERROR - 
处理数据块:  90%|###############################################################3      | 19/21 [00:11<00:01,  1.91it/s]
2025-08-04 14:59:45,448 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:11<00:00,  1.80it/s]
2025-08-04 14:59:45,459 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-08-04 14:59:45,460 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-08-04 14:59:45,505 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 488.37it/s]
2025-08-04 14:59:51,449 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 21.70 秒 (14:59:50)预计总处理时间: 约 65.10 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif (14:59:50)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif使用已处理过的影像信息影像信息: 宽=17007, 高=20364, 波段数=3掩码形状: (20364, 17007), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 尺寸: 17007x20364x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-08-04 14:59:51,453 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-08-04 15:00:01,310 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=42.58, std=63.34应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-08-04 15:00:01,311 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-08-04 15:00:01,315 - ERROR - [A
2025-08-04 15:00:01,434 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:01, 17.18it/s]
2025-08-04 15:00:01,434 - ERROR - [A
2025-08-04 15:00:01,617 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:00, 16.61it/s]
2025-08-04 15:00:01,618 - ERROR - [A
2025-08-04 15:00:01,878 - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:01, 11.40it/s]
2025-08-04 15:00:01,878 - ERROR - [A
2025-08-04 15:00:02,161 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:01,  9.33it/s]
2025-08-04 15:00:02,162 - ERROR - [A
2025-08-04 15:00:02,463 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:01,  8.20it/s]
2025-08-04 15:00:02,466 - ERROR - [A
2025-08-04 15:00:02,681 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:01<00:01,  7.11it/s]
2025-08-04 15:00:02,683 - ERROR - [A
2025-08-04 15:00:02,953 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:01,  5.91it/s]
2025-08-04 15:00:02,954 - ERROR - [A
2025-08-04 15:00:03,133 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:01<00:01,  5.81it/s]
2025-08-04 15:00:03,134 - ERROR - [A
2025-08-04 15:00:03,307 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:01<00:01,  5.79it/s]
2025-08-04 15:00:03,308 - ERROR - [A
2025-08-04 15:00:03,478 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:02<00:00,  5.82it/s]
2025-08-04 15:00:03,479 - ERROR - [A
2025-08-04 15:00:03,661 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:02<00:00,  5.71it/s]
2025-08-04 15:00:03,661 - ERROR - [A
2025-08-04 15:00:03,827 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:02<00:00,  5.79it/s]
2025-08-04 15:00:03,828 - ERROR - [A
2025-08-04 15:00:04,007 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:02<00:00,  5.72it/s]
2025-08-04 15:00:04,009 - ERROR - [A
2025-08-04 15:00:04,179 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:02<00:00,  5.76it/s]
2025-08-04 15:00:04,179 - ERROR - [A
2025-08-04 15:00:04,225 - ERROR - [A
2025-08-04 15:00:54,396 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:02<02:05, 62.94s/it]
2025-08-04 15:00:59,253 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=45.79, std=65.41应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-08-04 15:00:59,254 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-08-04 15:00:59,257 - ERROR - [A
2025-08-04 15:00:59,500 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:04,  4.11it/s]
2025-08-04 15:00:59,501 - ERROR - [A
2025-08-04 15:00:59,615 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:03,  5.97it/s]
2025-08-04 15:00:59,616 - ERROR - [A
2025-08-04 15:00:59,724 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  7.13it/s]
2025-08-04 15:00:59,725 - ERROR - [A
2025-08-04 15:00:59,877 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:01,  9.61it/s]
2025-08-04 15:00:59,877 - ERROR - [A
2025-08-04 15:01:00,021 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:01, 11.13it/s]
2025-08-04 15:01:00,021 - ERROR - [A
2025-08-04 15:01:00,172 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:01, 11.85it/s]
2025-08-04 15:01:00,172 - ERROR - [A
2025-08-04 15:01:00,318 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:00, 12.46it/s]
2025-08-04 15:01:00,319 - ERROR - [A
2025-08-04 15:01:00,475 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:00, 12.55it/s]
2025-08-04 15:01:00,476 - ERROR - [A
2025-08-04 15:01:00,835 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:01<00:00,  8.89it/s]
2025-08-04 15:01:00,836 - ERROR - [A
2025-08-04 15:01:02,516 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:03<00:01,  2.91it/s]
2025-08-04 15:01:02,517 - ERROR - [A
2025-08-04 15:01:05,324 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:06<00:02,  1.25it/s]
2025-08-04 15:01:05,324 - ERROR - [A
2025-08-04 15:01:08,788 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:09<00:02,  1.36s/it]
2025-08-04 15:01:08,789 - ERROR - [A
2025-08-04 15:01:13,383 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:14<00:02,  2.10s/it]
2025-08-04 15:01:13,385 - ERROR - [A
2025-08-04 15:01:13,399 - ERROR - [A
2025-08-04 15:02:27,550 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [02:36<01:20, 80.71s/it]
2025-08-04 15:02:32,828 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=35.76, std=51.77应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-08-04 15:02:32,830 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-08-04 15:02:32,834 - ERROR - [A
2025-08-04 15:02:33,063 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:04,  4.40it/s]
2025-08-04 15:02:33,064 - ERROR - [A
2025-08-04 15:02:33,225 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  8.41it/s]
2025-08-04 15:02:33,226 - ERROR - [A
2025-08-04 15:02:33,348 - ERROR - 
波段 3/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:01, 14.07it/s]
2025-08-04 15:02:33,349 - ERROR - [A
2025-08-04 15:02:33,450 - ERROR - 
波段 3/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 21.44it/s]
2025-08-04 15:02:33,450 - ERROR - [A
2025-08-04 15:02:33,564 - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 15/21 [00:00<00:00, 28.64it/s]
2025-08-04 15:02:33,565 - ERROR - [A
2025-08-04 15:02:33,669 - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [00:00<00:00, 34.30it/s]
2025-08-04 15:02:33,670 - ERROR - [A
2025-08-04 15:02:33,676 - ERROR - [A
2025-08-04 15:04:28,988 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:37<00:00, 99.31s/it]
2025-08-04 15:04:28,989 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:37<00:00, 92.51s/it]
2025-08-04 15:05:07,640 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif影像保存完成，耗时: 278.58 秒 (15:04:29)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp (15:04:29)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tifTIF文件信息: 宽=17007, 高=20364, 波段数=3开始读取影像数据，大小约: 3963.44 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif提取TIF文件信息...TIF文件信息: 宽=17007, 高=20364, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 218929698 个无效像素检查波段 2...波段 2 检测到 218929698 个无效像素检查波段 3...波段 3 检测到 218929698 个无效像素掩码统计: 总像素数 346330548, 有效像素数 127400850 (36.79%), 无效像素数 218929698 (63.21%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 1 个初始轮廓轮廓面积统计: 最小=127380512.50, 最大=127380512.50, 平均=127380512.50, 总数=1将 1 个轮廓转换为地理坐标...
2025-08-04 15:05:16,238 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-08-04 15:05:16,710 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.16it/s]
2025-08-04 15:05:16,710 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.16it/s]
2025-08-04 15:05:17,080 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-08-04 15:05:29,780 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-08-04 15:05:29,781 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-08-04 15:05:29,984 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:02, 10.02it/s]
2025-08-04 15:05:30,167 - ERROR - 
处理轮廓:  13%|#########4                                                               | 4/31 [00:00<00:02, 10.52it/s]
2025-08-04 15:05:30,302 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:00<00:00, 81.35it/s]
2025-08-04 15:05:30,303 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:00<00:00, 59.73it/s]
2025-08-04 15:05:31,058 - INFO - 处理了 82263 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp写入shapefile特征...处理MultiPolygon，包含 19 个多边形
2025-08-04 15:05:31,060 - ERROR - 
写入多边形:   0%|                                                                               | 0/19 [00:00<?, ?it/s]
2025-08-04 15:05:31,768 - ERROR - 
写入多边形:  26%|##################6                                                    | 5/19 [00:00<00:01,  7.10it/s]
2025-08-04 15:05:31,993 - ERROR - 
写入多边形:  58%|########################################5                             | 11/19 [00:00<00:00, 13.23it/s]
2025-08-04 15:05:31,996 - ERROR - 
写入多边形: 100%|######################################################################| 19/19 [00:00<00:00, 20.38it/s]
2025-08-04 15:05:32,716 - INFO - 处理完成，耗时: 380.31 秒 (6.34 分钟)
2025-08-04 15:05:32,716 - INFO - 处理结果: 成功
2025-08-04 15:05:32,718 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 大小: 740.84 MB
2025-08-04 15:05:32,719 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp, 大小: 22.81 KB
2025-08-04 15:05:32,724 - INFO - TIF处理任务 4275fc11-fafe-4eb1-b54d-8a009b6bf365 执行成功
2025-08-04 15:05:32,725 - INFO - 完成时间: 2025-08-04 15:05:32
2025-08-04 15:05:32,725 - INFO - 状态: 运行成功
2025-08-04 15:05:32,726 - INFO - ============ 任务执行结束 ============
