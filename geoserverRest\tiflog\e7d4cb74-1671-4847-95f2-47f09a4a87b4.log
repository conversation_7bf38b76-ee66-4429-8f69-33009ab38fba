2025-07-28 15:14:14,951 - INFO - ============ TIF处理任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 开始执行 ============
2025-07-28 15:14:14,953 - INFO - 开始时间: 2025-07-28 15:14:14
2025-07-28 15:14:14,955 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 15:14:14,957 - INFO - 系统信息:
2025-07-28 15:14:14,958 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 15:14:14,959 - INFO -   Python版本: 3.8.20
2025-07-28 15:14:14,960 - INFO -   GDAL版本: 3.9.2
2025-07-28 15:14:14,962 - INFO -   GPU可用: 否
2025-07-28 15:14:14,964 - INFO - 检查参数有效性...
2025-07-28 15:14:14,965 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 15:14:14,966 - INFO - 开始执行TIF处理流程...
2025-07-28 15:14:14,966 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 15:14:14,967 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 15:14:14,967 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 15:14:19,030 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (15:14:18)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 15:14:19,035 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 15:14:19,351 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:06,  3.18it/s]
2025-07-28 15:14:19,669 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 14.30it/s]
2025-07-28 15:14:19,931 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 19.40it/s]
2025-07-28 15:14:19,979 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 22.27it/s]
2025-07-28 15:14:19,980 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 15:14:19,981 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 15:14:20,014 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 656.24it/s]
2025-07-28 15:14:21,600 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.06 秒 (15:14:21)预计总处理时间: 约 6.19 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (15:14:21)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 15:14:21,605 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 15:14:27,061 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 15:14:27,062 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 15:14:27,064 - ERROR - [A
2025-07-28 15:14:27,178 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:01, 17.71it/s]
2025-07-28 15:14:27,179 - ERROR - [A
2025-07-28 15:14:27,293 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:00, 27.81it/s]
2025-07-28 15:14:27,293 - ERROR - [A
2025-07-28 15:14:27,394 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 32.64it/s]
2025-07-28 15:14:27,395 - ERROR - [A
2025-07-28 15:14:27,507 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:00<00:00, 37.41it/s]
2025-07-28 15:14:27,508 - ERROR - [A
2025-07-28 15:14:27,631 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:00<00:00, 38.60it/s]
2025-07-28 15:14:27,632 - ERROR - [A
2025-07-28 15:14:27,661 - ERROR - [A
2025-07-28 15:15:17,285 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:55<01:51, 55.68s/it]
2025-07-28 15:15:22,088 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 15:15:22,090 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 15:15:22,091 - ERROR - [A
2025-07-28 15:15:25,587 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:03<01:09,  3.50s/it]
2025-07-28 15:15:25,590 - ERROR - [A
2025-07-28 15:15:30,488 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:08<01:22,  4.32s/it]
2025-07-28 15:15:30,489 - ERROR - [A
2025-07-28 15:15:35,926 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:13<01:26,  4.83s/it]
2025-07-28 15:15:35,927 - ERROR - [A
2025-07-28 15:15:39,519 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:17<01:13,  4.34s/it]
2025-07-28 15:15:39,519 - ERROR - [A
2025-07-28 15:15:44,393 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:22<01:12,  4.53s/it]
2025-07-28 15:15:44,394 - ERROR - [A
2025-07-28 15:15:49,073 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:26<01:08,  4.58s/it]
2025-07-28 15:15:49,075 - ERROR - [A
2025-07-28 15:15:56,944 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:34<01:19,  5.66s/it]
2025-07-28 15:15:56,944 - ERROR - [A
2025-07-28 15:16:00,669 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:38<01:05,  5.04s/it]
2025-07-28 15:16:00,670 - ERROR - [A
2025-07-28 15:16:04,409 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:42<00:55,  4.64s/it]
2025-07-28 15:16:04,410 - ERROR - [A
2025-07-28 15:16:08,783 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:46<00:50,  4.55s/it]
2025-07-28 15:16:08,783 - ERROR - [A
2025-07-28 15:16:08,938 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:46<00:22,  2.46s/it]
2025-07-28 15:16:08,938 - ERROR - [A
2025-07-28 15:16:09,100 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:47<00:10,  1.50s/it]
2025-07-28 15:16:09,101 - ERROR - [A
2025-07-28 15:17:03,170 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [01:41<00:54, 10.80s/it]
2025-07-28 15:17:03,171 - ERROR - [A
2025-07-28 15:18:24,968 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [03:02<01:41, 25.46s/it]
2025-07-28 15:18:24,969 - ERROR - [A
2025-07-28 15:20:05,040 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [04:42<02:07, 42.45s/it]
2025-07-28 15:20:05,041 - ERROR - [A
2025-07-28 15:21:16,590 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [05:54<01:39, 49.60s/it]
2025-07-28 15:21:16,591 - ERROR - [A
2025-07-28 15:21:57,902 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [06:35<00:47, 47.45s/it]
2025-07-28 15:21:57,902 - ERROR - [A
2025-07-28 15:21:59,048 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [06:36<00:00, 34.92s/it]
2025-07-28 15:21:59,049 - ERROR - [A
2025-07-28 15:21:59,050 - ERROR - [A
2025-07-28 15:24:44,936 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [10:23<05:56, 356.84s/it]
2025-07-28 15:24:50,350 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 15:24:50,355 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 15:24:50,356 - ERROR - [A
2025-07-28 15:24:52,424 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:02<00:41,  2.07s/it]
2025-07-28 15:24:52,424 - ERROR - [A
2025-07-28 15:24:54,572 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:04<00:40,  2.11s/it]
2025-07-28 15:24:54,572 - ERROR - [A
2025-07-28 15:24:55,284 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:04<00:26,  1.47s/it]
2025-07-28 15:24:55,284 - ERROR - [A
2025-07-28 15:24:55,617 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:05<00:17,  1.02s/it]
2025-07-28 15:24:55,618 - ERROR - [A
2025-07-28 15:24:56,059 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:05<00:13,  1.23it/s]
2025-07-28 15:24:56,060 - ERROR - [A
2025-07-28 15:24:56,171 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:05<00:03,  3.51it/s]
2025-07-28 15:24:56,172 - ERROR - [A
2025-07-28 15:24:56,302 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:05<00:01,  6.20it/s]
2025-07-28 15:24:56,302 - ERROR - [A
2025-07-28 15:24:56,410 - ERROR - 
波段 3/3 写入进度:  76%|################################################               | 16/21 [00:06<00:00,  8.46it/s]
2025-07-28 15:24:56,411 - ERROR - [A
2025-07-28 15:24:56,489 - ERROR - [A
2025-07-28 15:26:42,263 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [12:20<00:00, 247.47s/it]
2025-07-28 15:26:42,264 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [12:20<00:00, 246.89s/it]
2025-07-28 15:27:36,138 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 741.63 秒 (15:26:42)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (15:26:42)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-28 15:27:36,158 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-28 15:27:37,871 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:01<00:00,  1.71s/it]
2025-07-28 15:27:37,872 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:01<00:00,  1.71s/it]
2025-07-28 15:27:38,231 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-28 15:27:52,382 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-28 15:27:52,383 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-28 15:27:52,554 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:02, 11.79it/s]
2025-07-28 15:27:52,759 - ERROR - 
处理轮廓:  13%|#########4                                                               | 4/31 [00:00<00:02, 10.48it/s]
2025-07-28 15:27:52,794 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:00<00:00, 75.60it/s]
2025-07-28 15:27:53,469 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-28 15:27:53,470 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-28 15:27:53,482 - ERROR - 
写入多边形: 100%|####################################################################| 35/35 [00:00<00:00, 3182.12it/s]
2025-07-28 15:27:53,643 - INFO - 处理完成，耗时: 818.67 秒 (13.64 分钟)
2025-07-28 15:27:53,644 - INFO - 处理结果: 成功
2025-07-28 15:27:53,647 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-28 15:27:53,648 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-28 15:27:53,651 - INFO - TIF处理任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 执行成功
2025-07-28 15:27:53,652 - INFO - 完成时间: 2025-07-28 15:27:53
2025-07-28 15:27:53,652 - INFO - 状态: 运行成功
2025-07-28 15:27:53,652 - INFO - ============ 任务执行结束 ============
