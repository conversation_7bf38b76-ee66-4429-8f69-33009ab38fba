2025-07-18 11:21:33,358 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250718_112133.log
2025-07-18 11:21:33,360 - geo_publisher - INFO - 加载了 7 个任务状态
2025-07-18 11:21:33,376 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 11:21:33,377 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 11:21:33,394 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 11:21:33,399 - root - INFO - === GeoServer REST API服务 ===
2025-07-18 11:21:33,400 - root - INFO - 主机: 0.0.0.0
2025-07-18 11:21:33,400 - root - INFO - 端口: 5083
2025-07-18 11:21:33,401 - root - INFO - 调试模式: 禁用
2025-07-18 11:21:33,402 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-18 11:21:33,493 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-18 11:21:33,494 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 11:21:45,672 - geo_publisher - INFO - 启动GeoTIFF发布任务 cc386f17-774b-42bd-bf93-151c3648b22f: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=tttt
2025-07-18 11:21:45,684 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:21:45] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=tttt HTTP/1.1" 200 -
2025-07-18 11:21:45,696 - geo_task_cc386f17-774b-42bd-bf93-151c3648b22f - INFO - GeoServer发布任务 cc386f17-774b-42bd-bf93-151c3648b22f 开始执行
2025-07-18 11:21:45,699 - geo_task_cc386f17-774b-42bd-bf93-151c3648b22f - INFO - 开始时间: 2025-07-18 11:21:45
2025-07-18 11:21:45,703 - geo_task_cc386f17-774b-42bd-bf93-151c3648b22f - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "tttt",
  "store_name": null,
  "layer_name": null
}
2025-07-18 11:21:45,716 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 11:21:45,718 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 11:21:45,746 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 11:21:45,750 - root - INFO - 未提供存储名称，使用文件名: 20250705171600
2025-07-18 11:21:45,754 - root - INFO - 未提供图层名称，使用存储名称: 20250705171600
2025-07-18 11:21:45,807 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171600\20250705171600.tif
2025-07-18 11:21:55,402 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:21:55] "GET /api/geo/status?task_id=cc386f17-774b-42bd-bf93-151c3648b22f HTTP/1.1" 200 -
2025-07-18 11:21:55,983 - geo_publisher - INFO - 启动GeoTIFF发布任务 e737a7af-417b-4395-bb63-c72e5e48afb3: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace="tttt"
2025-07-18 11:21:55,983 - geo_task_e737a7af-417b-4395-bb63-c72e5e48afb3 - INFO - GeoServer发布任务 e737a7af-417b-4395-bb63-c72e5e48afb3 开始执行
2025-07-18 11:21:55,989 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:21:55] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1" 200 -
2025-07-18 11:21:55,989 - geo_task_e737a7af-417b-4395-bb63-c72e5e48afb3 - INFO - 开始时间: 2025-07-18 11:21:55
2025-07-18 11:21:55,992 - geo_task_e737a7af-417b-4395-bb63-c72e5e48afb3 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "\"tttt\"",
  "store_name": null,
  "layer_name": null
}
2025-07-18 11:21:55,996 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 11:21:55,997 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 11:21:56,026 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:21:56] "GET /api/geo/status?task_id=e737a7af-417b-4395-bb63-c72e5e48afb3 HTTP/1.1" 200 -
2025-07-18 11:21:56,032 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 11:21:56,042 - root - INFO - 未提供存储名称，使用文件名: 20250705171600
2025-07-18 11:21:56,047 - root - INFO - 未提供图层名称，使用存储名称: 20250705171600
2025-07-18 11:22:58,004 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:22:58] "GET /api/geo/status?task_id=e737a7af-417b-4395-bb63-c72e5e48afb3 HTTP/1.1" 200 -
2025-07-18 11:23:59,955 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:23:59] "GET /api/geo/status?task_id=e737a7af-417b-4395-bb63-c72e5e48afb3 HTTP/1.1" 200 -
2025-07-18 11:25:01,902 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:25:01] "GET /api/geo/status?task_id=e737a7af-417b-4395-bb63-c72e5e48afb3 HTTP/1.1" 200 -
2025-07-18 11:25:11,983 - geo_publisher - INFO - 启动GeoTIFF发布任务 84cf9165-a1c6-41e2-9bbf-ebddb19735e2: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=tttt
2025-07-18 11:25:11,985 - geo_task_84cf9165-a1c6-41e2-9bbf-ebddb19735e2 - INFO - GeoServer发布任务 84cf9165-a1c6-41e2-9bbf-ebddb19735e2 开始执行
2025-07-18 11:25:11,990 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:25:11] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=tttt HTTP/1.1" 200 -
2025-07-18 11:25:11,995 - geo_task_84cf9165-a1c6-41e2-9bbf-ebddb19735e2 - INFO - 开始时间: 2025-07-18 11:25:11
2025-07-18 11:25:11,997 - geo_task_84cf9165-a1c6-41e2-9bbf-ebddb19735e2 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "tttt",
  "store_name": null,
  "layer_name": null
}
2025-07-18 11:25:11,998 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 11:25:12,000 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 11:25:12,025 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 11:25:12,797 - root - INFO - 未提供存储名称，使用文件名: 20250705171600
2025-07-18 11:25:12,798 - root - INFO - 未提供图层名称，使用存储名称: 20250705171600
