2025-07-28 10:27:31,142 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250728_102731.log
2025-07-28 10:27:31,144 - geo_publisher - INFO - 加载了 25 个任务状态
2025-07-28 10:27:31,309 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-28 10:27:31,310 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-28 10:27:31,337 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-28 10:27:31,347 - root - INFO - === GeoServer REST API服务 ===
2025-07-28 10:27:31,347 - root - INFO - 主机: 0.0.0.0
2025-07-28 10:27:31,348 - root - INFO - 端口: 5083
2025-07-28 10:27:31,350 - root - INFO - 调试模式: 禁用
2025-07-28 10:27:31,351 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-28 10:27:31,382 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-28 10:27:31,383 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-28 10:27:34,879 - map_api - INFO - 请求重置ODM任务配置
2025-07-28 10:27:34,885 - map_api - INFO - 开始重置Task.cfg中的任务信息
2025-07-28 10:27:34,912 - map_api - INFO - 成功读取ODM配置: {'PATHS': {'airflow_data_path': '/opt/airflow/data', 'window_data_path': 'D:/Drone_Project/nginxData'}, 'API': {'geoserver_host': 'host.docker.internal', 'geoserver_hd_port': '5083'}, 'GEOSERVER': {'odm_workspace': 'testodm'}, 'TASK': {'id': '20250705171602', 'time': '2025-07-28 01:34:01'}}
2025-07-28 10:27:34,918 - map_api - INFO - Task.cfg文件路径: D:/Drone_Project/nginxData\ODM\Task.cfg
2025-07-28 10:27:35,252 - map_api - INFO - 成功重置Task.cfg中的任务信息
2025-07-28 10:27:35,256 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:27:35] "GET /api/map/odm/reset-task-config HTTP/1.1" 200 -
2025-07-28 10:27:44,208 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:27:44,310 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:27:44,325 - map_api - INFO - 找到 3 个目录
2025-07-28 10:27:44,327 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:27:44,333 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:27:44,336 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:27:44,344 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-07-28 10:27:44,345 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:27:44,377 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:27:44,380 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:27:44,381 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(2) > 已完成(1)
2025-07-28 10:27:44,383 - werkzeug - INFO - ************** - - [28/Jul/2025 10:27:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:28:04,160 - batch_executor - INFO - 启动任务 caee250a-e3c4-4198-8f89-d60ed6ba6aaa: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171601/project
2025-07-28 10:28:04,163 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:28:04] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-28 10:28:04,331 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:28:04] "GET /api/batch/status?task_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 10:28:12,831 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:28:12,833 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:28:12,844 - map_api - INFO - 找到 3 个目录
2025-07-28 10:28:12,844 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:28:12,850 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:28:12,854 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:28:12,860 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:28:12,861 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:28:12,867 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:28:12,871 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:28:12,872 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:28:12,874 - werkzeug - INFO - ************** - - [28/Jul/2025 10:28:12] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:28:15,677 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:28:15,678 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:28:15,688 - map_api - INFO - 找到 3 个目录
2025-07-28 10:28:15,691 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:28:15,698 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:28:15,701 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:28:15,708 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:28:15,710 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:28:15,717 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:28:15,719 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:28:15,724 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:28:15,726 - werkzeug - INFO - ************** - - [28/Jul/2025 10:28:15] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:28:59,421 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:28:59,424 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:28:59,436 - map_api - INFO - 找到 3 个目录
2025-07-28 10:28:59,441 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:28:59,446 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:28:59,448 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:28:59,458 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:28:59,459 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:28:59,480 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:28:59,485 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:28:59,486 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:28:59,487 - werkzeug - INFO - ************** - - [28/Jul/2025 10:28:59] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:29:05,839 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:29:05] "GET /api/batch/status?task_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 10:29:12,884 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:29:12,897 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:29:12,920 - map_api - INFO - 找到 3 个目录
2025-07-28 10:29:12,921 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:29:12,932 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:29:12,934 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:29:12,939 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:29:12,948 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:29:12,954 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:29:12,955 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:29:12,965 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:29:12,968 - werkzeug - INFO - ************** - - [28/Jul/2025 10:29:12] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:30:07,382 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:30:07] "GET /api/batch/status?task_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 10:30:12,845 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:30:12,852 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:30:12,863 - map_api - INFO - 找到 3 个目录
2025-07-28 10:30:12,865 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:30:12,878 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:30:12,879 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:30:12,890 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:30:12,902 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:30:12,911 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:30:12,936 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:30:12,945 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:30:12,957 - werkzeug - INFO - ************** - - [28/Jul/2025 10:30:12] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:31:08,863 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:31:08] "GET /api/batch/status?task_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 10:31:12,839 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:31:12,845 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:31:12,886 - map_api - INFO - 找到 3 个目录
2025-07-28 10:31:12,892 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:31:12,914 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:31:12,918 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:31:12,933 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:31:12,955 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:31:12,967 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:31:12,971 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:31:12,990 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:31:13,005 - werkzeug - INFO - ************** - - [28/Jul/2025 10:31:13] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:32:10,392 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:32:10] "GET /api/batch/status?task_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 10:32:12,839 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:32:12,845 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:32:12,867 - map_api - INFO - 找到 3 个目录
2025-07-28 10:32:12,871 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:32:12,897 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:32:12,931 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:32:12,960 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:32:12,963 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:32:12,984 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:32:12,990 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:32:13,003 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:32:13,004 - werkzeug - INFO - ************** - - [28/Jul/2025 10:32:13] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:33:11,949 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:33:11] "GET /api/batch/status?task_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 10:33:12,846 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:33:12,850 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:33:12,876 - map_api - INFO - 找到 3 个目录
2025-07-28 10:33:12,886 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:33:12,905 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:33:12,907 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:33:12,918 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:33:12,932 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:33:12,966 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:33:12,969 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:33:12,987 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:33:12,992 - werkzeug - INFO - ************** - - [28/Jul/2025 10:33:12] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:33:26,882 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:33:26,886 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:33:26,898 - map_api - INFO - 找到 3 个目录
2025-07-28 10:33:26,899 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:33:26,908 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:33:26,912 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:33:26,923 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:33:26,924 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:33:26,930 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:33:26,932 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:33:26,933 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:33:26,937 - werkzeug - INFO - ************** - - [28/Jul/2025 10:33:26] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:34:13,198 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:34:13,203 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:34:13,222 - map_api - INFO - 找到 3 个目录
2025-07-28 10:34:13,223 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:34:13,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:34:13,247 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:34:13,273 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:34:13,277 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:34:13,306 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:34:13,309 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:34:13,312 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:34:13,315 - werkzeug - INFO - ************** - - [28/Jul/2025 10:34:13] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:34:13,810 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:34:13] "GET /api/batch/status?task_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 10:34:43,406 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:34:43,409 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:34:43,421 - map_api - INFO - 找到 3 个目录
2025-07-28 10:34:43,424 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:34:43,443 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:34:43,449 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:34:43,453 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:34:43,454 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:34:43,474 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:34:43,480 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:34:43,481 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:34:43,483 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:34:43] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:35:13,208 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:35:13,214 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:35:13,241 - map_api - INFO - 找到 3 个目录
2025-07-28 10:35:13,247 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:35:13,271 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:35:13,275 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:35:13,303 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:35:13,310 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:35:13,334 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:35:13,338 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:35:13,339 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:35:13,341 - werkzeug - INFO - ************** - - [28/Jul/2025 10:35:13] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:35:15,855 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:35:15] "GET /api/batch/status?task_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 10:36:17,608 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:36:17] "GET /api/batch/status?task_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 10:36:27,200 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:36:27,203 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:36:27,211 - map_api - INFO - 找到 3 个目录
2025-07-28 10:36:27,215 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:36:27,221 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:36:27,225 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:36:27,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:36:27,231 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:36:27,237 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:36:27,239 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:36:27,241 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:36:27,242 - werkzeug - INFO - ************** - - [28/Jul/2025 10:36:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:37:19,323 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:37:19] "GET /api/batch/status?task_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 10:37:27,211 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:37:27,215 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:37:27,227 - map_api - INFO - 找到 3 个目录
2025-07-28 10:37:27,228 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:37:27,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:37:27,241 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:37:27,248 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:37:27,251 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:37:27,277 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:37:27,280 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:37:27,283 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:37:27,285 - werkzeug - INFO - ************** - - [28/Jul/2025 10:37:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:38:21,065 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:38:21] "GET /api/batch/status?task_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 10:38:27,205 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:38:27,208 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:38:27,223 - map_api - INFO - 找到 3 个目录
2025-07-28 10:38:27,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:38:27,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:38:27,262 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:38:27,290 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 10:38:27,293 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:38:27,319 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:38:27,331 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:38:27,333 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:38:27,341 - werkzeug - INFO - ************** - - [28/Jul/2025 10:38:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:39:22,762 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:39:22] "GET /api/batch/status?task_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 10:39:27,201 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:39:27,205 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:39:27,229 - map_api - INFO - 找到 3 个目录
2025-07-28 10:39:27,231 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:39:42,587 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:39:42,589 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:39:42,595 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 10:39:42,596 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:39:42,603 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:39:42,605 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:39:42,612 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:39:42,614 - werkzeug - INFO - ************** - - [28/Jul/2025 10:39:42] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:40:27,208 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:40:27,214 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:40:27,226 - map_api - INFO - 找到 3 个目录
2025-07-28 10:40:27,233 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:40:27,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:40:27,247 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:40:27,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 10:40:27,256 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:40:27,262 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:40:27,270 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:40:27,273 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:40:27,277 - werkzeug - INFO - ************** - - [28/Jul/2025 10:40:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:41:17,302 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-28 10:41:17,306 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 10:41:17,308 - tif_api - INFO - 输入文件大小: 222.51 MB
2025-07-28 10:41:17,309 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-07-28 10:41:17,311 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-07-28 10:41:17,313 - tif_api - INFO - 黑色阈值: 0
2025-07-28 10:41:17,315 - tif_api - INFO - 白色阈值: 255
2025-07-28 10:41:17,316 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-28 10:41:17,320 - tif_executor - INFO - 启动TIF处理任务 1f376117-4639-4e05-8be5-839efea9682a: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-07-28 10:41:17,321 - tif_api - INFO - 异步任务启动成功，任务ID: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:41:17,323 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:41:17] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-28 10:41:17,343 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:41:17,344 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:41:17,357 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - ============ TIF处理任务 1f376117-4639-4e05-8be5-839efea9682a 开始执行 ============
2025-07-28 10:41:17,358 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 正在运行
2025-07-28 10:41:17,361 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 开始时间: 2025-07-28 10:41:17
2025-07-28 10:41:17,363 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:41:17] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:41:17,364 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 10:41:17,366 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 系统信息:
2025-07-28 10:41:17,367 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO -   操作系统: Windows 10.0.19045
2025-07-28 10:41:17,369 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO -   Python版本: 3.8.20
2025-07-28 10:41:17,371 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO -   GDAL版本: 3.9.2
2025-07-28 10:41:17,372 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO -   GPU可用: 否
2025-07-28 10:41:17,373 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 检查参数有效性...
2025-07-28 10:41:17,384 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 10:41:17,387 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 开始执行TIF处理流程...
2025-07-28 10:41:17,388 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 10:41:17,388 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-07-28 10:41:17,389 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-07-28 10:41:17,393 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-28 10:41:20,966 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17007, 高=20364, 波段数=4开始读取影像数据，大小约: 5284.58 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (10:41:20)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 10:41:21,023 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 10:41:21,194 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:02,  7.20it/s]
2025-07-28 10:41:21,345 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 31.06it/s]
2025-07-28 10:41:21,560 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理数据块:  57%|########################################                              | 12/21 [00:00<00:00, 24.18it/s]
2025-07-28 10:41:21,749 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理数据块:  81%|########################################################6             | 17/21 [00:00<00:00, 25.19it/s]
2025-07-28 10:41:21,788 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 28.66it/s]
2025-07-28 10:41:21,789 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 10:41:21,794 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 10:41:21,842 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 528.69it/s]
2025-07-28 10:41:23,612 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 2.07 秒 (10:41:22)预计总处理时间: 约 6.21 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif (10:41:22)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif使用已处理过的影像信息影像信息: 宽=17007, 高=20364, 波段数=3掩码形状: (20364, 17007), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 尺寸: 17007x20364x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 10:41:23,614 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 10:41:27,274 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:41:27,279 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:41:27,975 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=42.58, std=63.34应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 10:41:27,979 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 10:41:27,984 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:41:28,651 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:13,  1.51it/s]
2025-07-28 10:41:28,653 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:41:28,756 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  6.49it/s]
2025-07-28 10:41:28,760 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:41:28,862 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 17.02it/s]
2025-07-28 10:41:28,862 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:41:28,874 - map_api - INFO - 找到 3 个目录
2025-07-28 10:41:28,879 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:41:28,978 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:00<00:00, 25.60it/s]
2025-07-28 10:41:28,979 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:41:29,037 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:41:29,044 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:41:29,061 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:41:29,064 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:41:29,071 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:41:29,073 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:41:29,074 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:41:29,080 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:41:29,087 - werkzeug - INFO - ************** - - [28/Jul/2025 10:41:29] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:42:11,468 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:47<01:35, 47.85s/it]
2025-07-28 10:42:15,569 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=45.79, std=65.41应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 10:42:15,573 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 10:42:15,576 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:42:19,066 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:42:19,070 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:42:20,537 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 正在运行
2025-07-28 10:42:20,546 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:42:20] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:42:20,598 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:05<01:40,  5.02s/it]
2025-07-28 10:42:20,598 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:42:25,349 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:09<01:32,  4.86s/it]
2025-07-28 10:42:25,350 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:42:27,207 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:42:27,213 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:42:27,236 - map_api - INFO - 找到 3 个目录
2025-07-28 10:42:27,251 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:42:28,730 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:42:28,734 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:42:29,258 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:42:29,264 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:42:29,393 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:42:29,397 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:42:29,402 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:42:29,407 - werkzeug - INFO - ************** - - [28/Jul/2025 10:42:29] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:43:08,261 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:43:08,270 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:43:08,290 - map_api - INFO - 找到 3 个目录
2025-07-28 10:43:08,292 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:43:08,320 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:43:08,325 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:43:08,596 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:43:08,602 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:43:08,773 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:43:08,778 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:43:08,780 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:43:08,782 - werkzeug - INFO - ************** - - [28/Jul/2025 10:43:08] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:43:12,834 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:43:12,840 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:43:13,822 - map_api - INFO - 找到 3 个目录
2025-07-28 10:43:13,826 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:43:14,214 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:43:14,219 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:43:14,226 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:43:14,229 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:43:14,257 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:43:14,261 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:43:14,262 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:43:14,264 - werkzeug - INFO - ************** - - [28/Jul/2025 10:43:14] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:43:24,002 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:43:24,006 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:44:13,203 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:44:13,206 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:44:13,231 - map_api - INFO - 找到 3 个目录
2025-07-28 10:44:13,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:44:13,260 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:44:13,264 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:44:13,291 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:44:13,295 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:44:13,321 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:44:13,325 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:44:13,326 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:44:13,329 - werkzeug - INFO - ************** - - [28/Jul/2025 10:44:13] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:45:27,209 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:45:27,216 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:45:27,251 - map_api - INFO - 找到 3 个目录
2025-07-28 10:45:27,254 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:45:27,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:45:27,262 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:45:27,295 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:45:27,298 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:45:27,327 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:45:27,330 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:45:27,339 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:45:27,341 - werkzeug - INFO - ************** - - [28/Jul/2025 10:45:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:42:29,173 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:13<01:18,  4.39s/it]
2025-07-28 10:46:02,288 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:46:02,289 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 正在运行
2025-07-28 10:46:02,297 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:46:02] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:46:04,391 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [03:48<24:49, 87.62s/it]
2025-07-28 10:46:04,394 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:46:14,669 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [03:59<15:55, 59.73s/it]
2025-07-28 10:46:14,670 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:46:27,220 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:46:27,228 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:46:28,435 - map_api - INFO - 找到 3 个目录
2025-07-28 10:46:28,443 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:46:28,453 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:46:28,455 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:46:28,463 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:46:28,470 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:46:29,498 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [04:13<11:06, 44.46s/it]
2025-07-28 10:46:29,499 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:46:29,985 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:46:29,999 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:46:30,001 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:46:30,008 - werkzeug - INFO - ************** - - [28/Jul/2025 10:46:30] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:46:41,391 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [04:25<07:53, 33.82s/it]
2025-07-28 10:46:41,392 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:46:45,560 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [04:29<05:16, 24.38s/it]
2025-07-28 10:46:45,563 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:46:49,832 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [04:34<03:37, 18.09s/it]
2025-07-28 10:46:49,833 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:46:51,866 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [04:36<02:24, 13.13s/it]
2025-07-28 10:46:51,867 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:46:57,092 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [04:41<01:47, 10.71s/it]
2025-07-28 10:46:57,093 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:47:00,244 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [04:44<01:15,  8.41s/it]
2025-07-28 10:47:00,245 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:47:01,796 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [04:46<00:50,  6.33s/it]
2025-07-28 10:47:01,796 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:47:03,188 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:47:03,192 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:47:04,215 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 正在运行
2025-07-28 10:47:04,227 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:47:04] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:47:06,133 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [04:50<00:40,  5.73s/it]
2025-07-28 10:47:06,133 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:47:06,301 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [04:50<00:15,  3.12s/it]
2025-07-28 10:47:06,302 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:47:12,279 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [04:56<00:15,  3.83s/it]
2025-07-28 10:47:12,280 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:47:27,212 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:47:27,217 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:47:27,230 - map_api - INFO - 找到 3 个目录
2025-07-28 10:47:27,231 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:47:27,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:47:27,243 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:47:27,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:47:27,258 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:47:27,264 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:47:27,267 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:47:27,271 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:47:27,272 - werkzeug - INFO - ************** - - [28/Jul/2025 10:47:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:47:37,136 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [05:21<00:28,  9.34s/it]
2025-07-28 10:47:37,137 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:48:04,688 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:48:04,693 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:48:05,063 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 正在运行
2025-07-28 10:48:05,070 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:48:05] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:48:17,882 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [06:02<00:35, 17.89s/it]
2025-07-28 10:48:17,882 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:48:54,224 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:48:54,228 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:48:54,242 - map_api - INFO - 找到 3 个目录
2025-07-28 10:48:54,251 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:48:54,258 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:48:54,262 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:48:54,272 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:48:54,279 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:48:54,286 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:48:54,292 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:48:54,301 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:48:54,309 - werkzeug - INFO - ************** - - [28/Jul/2025 10:48:54] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:49:06,428 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:49:06,436 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:49:06,822 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 正在运行
2025-07-28 10:49:06,832 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:49:06] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:49:29,812 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [07:14<00:33, 33.03s/it]
2025-07-28 10:49:29,814 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:49:29,821 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:49:30,795 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:49:30,799 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:49:30,810 - map_api - INFO - 找到 3 个目录
2025-07-28 10:49:30,812 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:49:30,839 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:49:30,843 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:49:30,849 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:49:30,852 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:49:30,856 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:49:30,859 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:49:30,860 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:49:30,863 - werkzeug - INFO - ************** - - [28/Jul/2025 10:49:30] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:49:52,223 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:49:52,228 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:49:52,236 - map_api - INFO - 找到 3 个目录
2025-07-28 10:49:52,237 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:49:52,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:49:52,246 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:49:52,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:49:52,251 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:49:52,255 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:49:52,257 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:49:52,265 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:49:52,268 - werkzeug - INFO - ************** - - [28/Jul/2025 10:49:52] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:50:06,072 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:50:06,078 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:50:06,099 - map_api - INFO - 找到 3 个目录
2025-07-28 10:50:06,108 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:50:06,119 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:50:06,124 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:50:06,134 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:50:06,137 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:50:06,143 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:50:06,145 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:50:06,154 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:50:06,161 - werkzeug - INFO - ************** - - [28/Jul/2025 10:50:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:50:08,284 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:50:08,289 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:50:08,934 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 正在运行
2025-07-28 10:50:09,223 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:50:09] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:50:11,162 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:50:11,174 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:50:11,186 - map_api - INFO - 找到 3 个目录
2025-07-28 10:50:11,188 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:50:11,196 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:50:11,208 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:50:11,214 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:50:11,215 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:50:11,221 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:50:11,223 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:50:11,225 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:50:11,227 - werkzeug - INFO - ************** - - [28/Jul/2025 10:50:11] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:50:33,142 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:50:33,147 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:50:33,161 - map_api - INFO - 找到 3 个目录
2025-07-28 10:50:33,168 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:50:33,175 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:50:33,177 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:50:33,188 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:50:33,190 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:50:33,196 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:50:33,200 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:50:33,204 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:50:33,207 - werkzeug - INFO - ************** - - [28/Jul/2025 10:50:33] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:50:39,846 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:50:39,855 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:50:39,869 - map_api - INFO - 找到 3 个目录
2025-07-28 10:50:39,874 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:50:39,882 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:50:39,891 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:50:39,902 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:50:39,918 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:50:39,938 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:50:39,955 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:50:39,958 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:50:39,970 - werkzeug - INFO - ************** - - [28/Jul/2025 10:50:39] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:51:07,243 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:51:07,250 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:51:07,272 - map_api - INFO - 找到 3 个目录
2025-07-28 10:51:07,278 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:51:07,295 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:51:07,297 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:51:07,307 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:51:07,311 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:51:07,335 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:51:07,344 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:51:07,348 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:51:07,363 - werkzeug - INFO - ************** - - [28/Jul/2025 10:51:07] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:51:07,543 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:51:07,549 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:51:07,564 - map_api - INFO - 找到 3 个目录
2025-07-28 10:51:07,566 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:51:07,576 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:51:07,578 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:51:07,590 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:51:07,596 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:51:07,607 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:51:07,610 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:51:07,613 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:51:07,616 - werkzeug - INFO - ************** - - [28/Jul/2025 10:51:07] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:51:10,710 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:51:10,714 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:52:05,905 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:52:05,912 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:52:05,929 - map_api - INFO - 找到 3 个目录
2025-07-28 10:52:05,934 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:52:05,942 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:52:05,951 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:52:05,962 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:52:05,970 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:52:05,976 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:52:05,979 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:52:05,980 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:52:05,986 - werkzeug - INFO - ************** - - [28/Jul/2025 10:52:05] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:52:36,513 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 正在运行
2025-07-28 10:52:36,522 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:52:36] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:53:06,204 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:53:06,208 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:53:06,224 - map_api - INFO - 找到 3 个目录
2025-07-28 10:53:06,228 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:53:06,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 10:53:06,244 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:53:06,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:53:06,256 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:53:06,263 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:53:06,266 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:53:06,267 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:53:06,269 - werkzeug - INFO - ************** - - [28/Jul/2025 10:53:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:53:38,552 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:53:38,557 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:53:39,238 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 正在运行
2025-07-28 10:53:39,242 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:53:39] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:54:05,912 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:54:05,917 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:54:05,928 - map_api - INFO - 找到 3 个目录
2025-07-28 10:54:05,931 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:54:05,944 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 10:54:05,950 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:54:05,960 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:54:05,967 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:54:05,974 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:54:05,977 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:54:05,979 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:54:05,981 - werkzeug - INFO - ************** - - [28/Jul/2025 10:54:05] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:54:20,419 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:54:20,425 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:54:20,442 - map_api - INFO - 找到 3 个目录
2025-07-28 10:54:20,454 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:54:20,464 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 10:54:20,473 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:54:20,482 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 10:54:20,484 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:54:20,493 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 10:54:20,501 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:54:20,503 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 10:54:20,509 - werkzeug - INFO - ************** - - [28/Jul/2025 10:54:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:54:40,989 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:54:40,998 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:54:41,701 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 正在运行
2025-07-28 10:54:41,706 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:54:41] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:55:14,444 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [13:50<08:00, 480.28s/it]
2025-07-28 10:55:20,559 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=35.76, std=51.77应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 10:55:20,560 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 10:55:20,565 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:55:21,161 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:11,  1.68it/s]
2025-07-28 10:55:21,163 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:55:22,588 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:02<00:20,  1.08s/it]
2025-07-28 10:55:22,589 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:55:24,857 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:04<00:29,  1.63s/it]
2025-07-28 10:55:24,858 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:55:27,344 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:06<00:33,  1.97s/it]
2025-07-28 10:55:27,503 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:55:27,535 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:06<00:21,  1.33s/it]
2025-07-28 10:55:27,535 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:55:27,638 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:07<00:07,  1.85it/s]
2025-07-28 10:55:27,639 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:55:27,777 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 12/21 [00:07<00:02,  3.71it/s]
2025-07-28 10:55:27,778 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:55:27,900 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 15/21 [00:07<00:01,  5.37it/s]
2025-07-28 10:55:27,903 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:55:28,029 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 18/21 [00:07<00:00,  7.29it/s]
2025-07-28 10:55:28,029 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:55:28,093 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - [A
2025-07-28 10:55:43,528 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:55:43,532 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:55:43,537 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 正在运行
2025-07-28 10:55:43,540 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:55:43] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:56:45,425 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:56:45,430 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:56:45,581 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 正在运行
2025-07-28 10:56:45,584 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:56:45] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:57:46,747 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:57:46,751 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:57:46,776 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 正在运行
2025-07-28 10:57:46,779 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:57:46] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:57:48,722 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [16:25<00:00, 331.42s/it]
2025-07-28 10:57:48,723 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [16:25<00:00, 328.37s/it]
2025-07-28 10:58:08,965 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif影像保存完成，耗时: 986.53 秒 (10:57:49)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp (10:57:49)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tifTIF文件信息: 宽=17007, 高=20364, 波段数=3开始读取影像数据，大小约: 3963.44 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif提取TIF文件信息...TIF文件信息: 宽=17007, 高=20364, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 218929698 个无效像素检查波段 2...波段 2 检测到 218929698 个无效像素检查波段 3...波段 3 检测到 218929698 个无效像素掩码统计: 总像素数 346330548, 有效像素数 127400850 (36.79%), 无效像素数 218929698 (63.21%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 1 个初始轮廓轮廓面积统计: 最小=127380512.50, 最大=127380512.50, 平均=127380512.50, 总数=1将 1 个轮廓转换为地理坐标...
2025-07-28 10:58:08,991 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-28 10:58:09,416 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.37it/s]
2025-07-28 10:58:09,417 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.36it/s]
2025-07-28 10:58:10,177 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-28 10:58:22,500 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-28 10:58:22,501 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-28 10:58:23,352 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:12,  2.36it/s]
2025-07-28 10:58:23,485 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:00<00:08,  3.30it/s]
2025-07-28 10:58:23,520 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 30.50it/s]
2025-07-28 10:58:46,206 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 处理了 82263 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp写入shapefile特征...处理MultiPolygon，包含 19 个多边形
2025-07-28 10:58:46,209 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
写入多边形:   0%|                                                                               | 0/19 [00:00<?, ?it/s]
2025-07-28 10:58:46,265 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - ERROR - 
写入多边形: 100%|#####################################################################| 19/19 [00:00<00:00, 387.73it/s]
2025-07-28 10:58:46,409 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 处理完成，耗时: 1049.02 秒 (17.48 分钟)
2025-07-28 10:58:46,409 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 处理结果: 成功
2025-07-28 10:58:46,413 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 大小: 740.84 MB
2025-07-28 10:58:46,417 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp, 大小: 22.81 KB
2025-07-28 10:58:46,423 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - TIF处理任务 1f376117-4639-4e05-8be5-839efea9682a 执行成功
2025-07-28 10:58:46,428 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 完成时间: 2025-07-28 10:58:46
2025-07-28 10:58:46,428 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - 状态: 运行成功
2025-07-28 10:58:46,432 - tif_task_1f376117-4639-4e05-8be5-839efea9682a - INFO - ============ 任务执行结束 ============
2025-07-28 10:58:47,638 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:58:47,643 - tif_api - INFO - 查询任务: 1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 10:58:47,648 - tif_api - INFO - 任务 1f376117-4639-4e05-8be5-839efea9682a 状态查询成功，当前状态: 运行成功
2025-07-28 10:58:47,652 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:58:47] "GET /api/tif/status?task_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 10:58:49,680 - geo_task_fd7c85a1-5ebd-489a-845b-f95bb5449d04 - INFO - GeoServer发布任务 fd7c85a1-5ebd-489a-845b-f95bb5449d04 开始执行
2025-07-28 10:58:49,680 - geo_publisher - INFO - 启动GeoTIFF发布任务 fd7c85a1-5ebd-489a-845b-f95bb5449d04: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-07-28 10:58:49,687 - geo_task_fd7c85a1-5ebd-489a-845b-f95bb5449d04 - INFO - 开始时间: 2025-07-28 10:58:49
2025-07-28 10:58:49,688 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:58:49] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-28 10:58:49,689 - geo_task_fd7c85a1-5ebd-489a-845b-f95bb5449d04 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-28 10:58:49,692 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-28 10:58:49,693 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-28 10:58:49,718 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:58:49] "GET /api/geo/status?task_id=fd7c85a1-5ebd-489a-845b-f95bb5449d04 HTTP/1.1" 200 -
2025-07-28 10:58:52,751 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-28 10:58:52,752 - root - INFO - 未提供存储名称，使用文件名: 20250705171601
2025-07-28 10:58:52,755 - root - INFO - 未提供图层名称，使用存储名称: 20250705171601
2025-07-28 10:58:53,102 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601.tif
2025-07-28 10:59:50,740 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:59:50] "GET /api/geo/status?task_id=fd7c85a1-5ebd-489a-845b-f95bb5449d04 HTTP/1.1" 200 -
2025-07-28 11:00:03,535 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 到 'testodm:20250705171601'
2025-07-28 11:00:03,792 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171601
2025-07-28 11:00:03,793 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 成功发布为图层 'testodm:20250705171601'
2025-07-28 11:00:03,806 - geo_task_fd7c85a1-5ebd-489a-845b-f95bb5449d04 - INFO - GeoServer发布任务 fd7c85a1-5ebd-489a-845b-f95bb5449d04 执行成功
2025-07-28 11:00:03,808 - geo_task_fd7c85a1-5ebd-489a-845b-f95bb5449d04 - INFO - 完成时间: 2025-07-28 11:00:03
2025-07-28 11:00:03,809 - geo_task_fd7c85a1-5ebd-489a-845b-f95bb5449d04 - INFO - 状态: 发布成功
2025-07-28 11:02:35,359 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:02:35,363 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:02:35,374 - map_api - INFO - 找到 3 个目录
2025-07-28 11:02:35,376 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:02:35,499 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:02:35,501 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:02:35,509 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:02:35,510 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:02:35,517 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:02:35,518 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:02:35,519 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:02:35,522 - werkzeug - INFO - ************** - - [28/Jul/2025 11:02:35] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:02:49,658 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:02:49,660 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:02:49,669 - map_api - INFO - 找到 3 个目录
2025-07-28 11:02:49,671 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:02:49,677 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:02:49,679 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:02:49,699 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:02:49,700 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:02:49,704 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:02:49,705 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:02:49,706 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:02:49,706 - werkzeug - INFO - ************** - - [28/Jul/2025 11:02:49] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:03:35,373 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:03:35,375 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:03:35,385 - map_api - INFO - 找到 3 个目录
2025-07-28 11:03:35,387 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:03:35,416 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:03:35,417 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:03:35,423 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:03:35,424 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:03:35,427 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:03:35,428 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:03:35,429 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:03:35,431 - werkzeug - INFO - ************** - - [28/Jul/2025 11:03:35] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:04:35,361 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:04:35,362 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:04:35,392 - map_api - INFO - 找到 3 个目录
2025-07-28 11:04:35,393 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:04:35,397 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:04:35,398 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:04:35,420 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:04:35,421 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:04:35,436 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:04:35,437 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:04:35,437 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:04:35,438 - werkzeug - INFO - ************** - - [28/Jul/2025 11:04:35] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:05:36,209 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:05:36,211 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:05:36,217 - map_api - INFO - 找到 3 个目录
2025-07-28 11:05:36,218 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:05:36,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:05:36,238 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:05:36,244 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:05:36,245 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:05:36,248 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:05:36,249 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:05:36,249 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:05:36,251 - werkzeug - INFO - ************** - - [28/Jul/2025 11:05:36] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:06:36,211 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:06:36,212 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:06:36,238 - map_api - INFO - 找到 3 个目录
2025-07-28 11:06:36,239 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:06:36,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:06:36,268 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:06:36,283 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:06:36,284 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:06:36,287 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:06:36,289 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:06:36,290 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:06:36,295 - werkzeug - INFO - ************** - - [28/Jul/2025 11:06:36] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:07:36,210 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:07:36,212 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:07:36,221 - map_api - INFO - 找到 3 个目录
2025-07-28 11:07:36,222 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:07:36,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:07:36,251 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:07:36,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:07:36,266 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:07:36,280 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:07:36,280 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:07:36,281 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:07:36,282 - werkzeug - INFO - ************** - - [28/Jul/2025 11:07:36] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:08:36,209 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:08:36,210 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:08:36,219 - map_api - INFO - 找到 3 个目录
2025-07-28 11:08:36,220 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:08:36,224 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:08:36,225 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:08:36,228 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:08:36,229 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:08:36,248 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:08:36,250 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:08:36,251 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:08:36,252 - werkzeug - INFO - ************** - - [28/Jul/2025 11:08:36] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:10:06,337 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:10:06,338 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:10:06,349 - map_api - INFO - 找到 3 个目录
2025-07-28 11:10:06,350 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:10:06,356 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:10:06,357 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:10:06,363 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:10:06,364 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:10:06,368 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:10:06,370 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:10:06,372 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:10:06,373 - werkzeug - INFO - ************** - - [28/Jul/2025 11:10:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:11:07,195 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:11:07,197 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:11:07,206 - map_api - INFO - 找到 3 个目录
2025-07-28 11:11:07,207 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:11:07,213 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:11:07,215 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:11:07,221 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:11:07,222 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:11:07,226 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:11:07,228 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:11:07,229 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:11:07,233 - werkzeug - INFO - ************** - - [28/Jul/2025 11:11:07] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:12:07,204 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:12:07,206 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:12:07,216 - map_api - INFO - 找到 3 个目录
2025-07-28 11:12:07,217 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:12:07,223 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:12:07,225 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:12:07,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:12:07,232 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:12:07,237 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:12:07,239 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:12:07,239 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:12:07,241 - werkzeug - INFO - ************** - - [28/Jul/2025 11:12:07] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:12:46,254 - map_api - INFO - 请求获取日志: 类型=batlog, ID=4d238329-a277-4cee-8b55-99037586a151
2025-07-28 11:12:46,255 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\4d238329-a277-4cee-8b55-99037586a151.log
2025-07-28 11:12:46,259 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\4d238329-a277-4cee-8b55-99037586a151.log, 内容大小: 363540 字节
2025-07-28 11:12:46,264 - werkzeug - INFO - ************** - - [28/Jul/2025 11:12:46] "GET /api/map/logs?log_type=batlog&log_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-28 11:12:57,679 - map_api - INFO - 请求获取日志: 类型=batlog, ID=caee250a-e3c4-4198-8f89-d60ed6ba6aaa
2025-07-28 11:12:57,682 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\caee250a-e3c4-4198-8f89-d60ed6ba6aaa.log
2025-07-28 11:12:57,684 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\caee250a-e3c4-4198-8f89-d60ed6ba6aaa.log, 内容大小: 11524 字节
2025-07-28 11:12:57,686 - werkzeug - INFO - ************** - - [28/Jul/2025 11:12:57] "GET /api/map/logs?log_type=batlog&log_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 11:13:03,022 - map_api - INFO - 请求获取日志: 类型=geolog, ID=fd7c85a1-5ebd-489a-845b-f95bb5449d04
2025-07-28 11:13:03,023 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\fd7c85a1-5ebd-489a-845b-f95bb5449d04.log
2025-07-28 11:13:03,024 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\fd7c85a1-5ebd-489a-845b-f95bb5449d04.log, 内容大小: 829 字节
2025-07-28 11:13:03,029 - werkzeug - INFO - ************** - - [28/Jul/2025 11:13:03] "GET /api/map/logs?log_type=geolog&log_id=fd7c85a1-5ebd-489a-845b-f95bb5449d04 HTTP/1.1" 200 -
2025-07-28 11:13:06,295 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:13:06,297 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:13:06,299 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:13:06,301 - werkzeug - INFO - ************** - - [28/Jul/2025 11:13:06] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:13:06,417 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:13:06,422 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:13:06,454 - map_api - INFO - 找到 3 个目录
2025-07-28 11:13:06,455 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:13:06,459 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:13:06,462 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:13:06,482 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:13:06,483 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:13:06,487 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:13:06,488 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:13:06,489 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:13:06,490 - werkzeug - INFO - ************** - - [28/Jul/2025 11:13:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:13:09,650 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:13:09,652 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:13:09,662 - map_api - INFO - 找到 3 个目录
2025-07-28 11:13:09,663 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:13:09,671 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:13:09,672 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:13:09,696 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:13:09,697 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:13:09,701 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:13:09,702 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:13:09,703 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:13:09,705 - werkzeug - INFO - ************** - - [28/Jul/2025 11:13:09] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:13:11,069 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:13:11,071 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:13:11,073 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:13:11,075 - werkzeug - INFO - ************** - - [28/Jul/2025 11:13:11] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:13:16,105 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:13:16,106 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:13:16,108 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:13:16,110 - werkzeug - INFO - ************** - - [28/Jul/2025 11:13:16] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:14:06,353 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:14:06,355 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:14:06,365 - map_api - INFO - 找到 3 个目录
2025-07-28 11:14:06,366 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:14:06,395 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:14:06,397 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:14:06,426 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:14:06,427 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:14:06,457 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:14:06,458 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:14:06,459 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:14:06,460 - werkzeug - INFO - ************** - - [28/Jul/2025 11:14:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:14:26,381 - map_api - INFO - 请求获取日志: 类型=batlog, ID=caee250a-e3c4-4198-8f89-d60ed6ba6aaa
2025-07-28 11:14:26,386 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\caee250a-e3c4-4198-8f89-d60ed6ba6aaa.log
2025-07-28 11:14:26,387 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\caee250a-e3c4-4198-8f89-d60ed6ba6aaa.log, 内容大小: 11524 字节
2025-07-28 11:14:26,388 - werkzeug - INFO - ************** - - [28/Jul/2025 11:14:26] "GET /api/map/logs?log_type=batlog&log_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 11:14:30,522 - map_api - INFO - 请求获取日志: 类型=batlog, ID=caee250a-e3c4-4198-8f89-d60ed6ba6aaa
2025-07-28 11:14:30,627 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\caee250a-e3c4-4198-8f89-d60ed6ba6aaa.log
2025-07-28 11:14:30,630 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\caee250a-e3c4-4198-8f89-d60ed6ba6aaa.log, 内容大小: 11524 字节
2025-07-28 11:14:30,633 - werkzeug - INFO - ************** - - [28/Jul/2025 11:14:30] "GET /api/map/logs?log_type=batlog&log_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 11:14:45,208 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:14:45,516 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:14:46,473 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:14:46,475 - werkzeug - INFO - ************** - - [28/Jul/2025 11:14:46] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:15:06,337 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:15:06,340 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:15:06,350 - map_api - INFO - 找到 3 个目录
2025-07-28 11:15:06,352 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:15:06,356 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:15:06,360 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:15:06,378 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:15:06,379 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:15:06,383 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:15:06,386 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:15:06,387 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:15:06,388 - werkzeug - INFO - ************** - - [28/Jul/2025 11:15:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:15:16,650 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:15:16,653 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:15:16,661 - map_api - INFO - 找到 3 个目录
2025-07-28 11:15:16,663 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:15:16,684 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:15:16,685 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:15:16,715 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:15:16,716 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:15:16,745 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:15:16,746 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:15:16,747 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:15:16,748 - werkzeug - INFO - ************** - - [28/Jul/2025 11:15:16] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:16:06,345 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:16:06,346 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:16:06,354 - map_api - INFO - 找到 3 个目录
2025-07-28 11:16:06,355 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:16:06,373 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:16:06,374 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:16:06,389 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:16:06,390 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:16:06,404 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:16:06,405 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:16:06,406 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:16:06,407 - werkzeug - INFO - ************** - - [28/Jul/2025 11:16:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:16:46,131 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=a413982e-ed7d-4158-b465-872ed73274df
2025-07-28 11:16:46,133 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\a413982e-ed7d-4158-b465-872ed73274df.log
2025-07-28 11:16:46,137 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\a413982e-ed7d-4158-b465-872ed73274df.log, 内容大小: 10343 字节
2025-07-28 11:16:46,138 - werkzeug - INFO - ************** - - [28/Jul/2025 11:16:46] "GET /api/map/logs?log_type=tiflog&log_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-28 11:16:53,510 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:16:53,512 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:16:53,514 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:16:53,516 - werkzeug - INFO - ************** - - [28/Jul/2025 11:16:53] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:16:59,869 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:16:59,871 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:16:59,874 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:16:59,876 - werkzeug - INFO - ************** - - [28/Jul/2025 11:16:59] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:17:06,346 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:17:06,347 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:17:06,357 - map_api - INFO - 找到 3 个目录
2025-07-28 11:17:06,360 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:17:06,375 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:17:06,377 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:17:06,382 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:17:06,383 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:17:06,387 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:17:06,390 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:17:06,392 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:17:06,395 - werkzeug - INFO - ************** - - [28/Jul/2025 11:17:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:17:57,115 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:17:57,116 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:17:57,133 - map_api - INFO - 找到 3 个目录
2025-07-28 11:17:57,136 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:17:57,142 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:17:57,143 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:17:57,150 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:17:57,153 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:17:57,159 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:17:57,161 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:17:57,162 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:17:57,164 - werkzeug - INFO - ************** - - [28/Jul/2025 11:17:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:17:59,330 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:17:59,332 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:17:59,335 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:17:59,338 - werkzeug - INFO - ************** - - [28/Jul/2025 11:17:59] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:18:02,278 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-28 11:18:02,279 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log
2025-07-28 11:18:02,281 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log, 内容大小: 17354 字节
2025-07-28 11:18:02,283 - werkzeug - INFO - ************** - - [28/Jul/2025 11:18:02] "GET /api/map/logs?log_type=tiflog&log_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-28 11:18:05,106 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:18:05,107 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:18:05,109 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:18:05,112 - werkzeug - INFO - ************** - - [28/Jul/2025 11:18:05] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:18:06,882 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-28 11:18:06,884 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log
2025-07-28 11:18:06,887 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log, 内容大小: 17354 字节
2025-07-28 11:18:06,889 - werkzeug - INFO - ************** - - [28/Jul/2025 11:18:06] "GET /api/map/logs?log_type=tiflog&log_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-28 11:18:14,468 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:18:14,469 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:18:14,470 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:18:14,472 - werkzeug - INFO - ************** - - [28/Jul/2025 11:18:14] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:18:15,890 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-28 11:18:15,891 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log
2025-07-28 11:18:15,892 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log, 内容大小: 17354 字节
2025-07-28 11:18:15,893 - werkzeug - INFO - ************** - - [28/Jul/2025 11:18:15] "GET /api/map/logs?log_type=tiflog&log_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-28 11:18:17,658 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:18:17,660 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:18:17,662 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:18:17,664 - werkzeug - INFO - ************** - - [28/Jul/2025 11:18:17] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:18:18,979 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-28 11:18:18,982 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log
2025-07-28 11:18:18,984 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log, 内容大小: 17354 字节
2025-07-28 11:18:19,276 - werkzeug - INFO - ************** - - [28/Jul/2025 11:18:19] "GET /api/map/logs?log_type=tiflog&log_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-28 11:18:22,698 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:18:22,699 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:18:22,700 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:18:22,702 - werkzeug - INFO - ************** - - [28/Jul/2025 11:18:22] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:18:29,301 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:18:29,806 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:18:29,810 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:18:29,812 - werkzeug - INFO - ************** - - [28/Jul/2025 11:18:29] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:18:31,603 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-28 11:18:31,605 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log
2025-07-28 11:18:31,607 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log, 内容大小: 17354 字节
2025-07-28 11:18:31,608 - werkzeug - INFO - ************** - - [28/Jul/2025 11:18:31] "GET /api/map/logs?log_type=tiflog&log_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-28 11:18:57,201 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:18:57,202 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:18:57,210 - map_api - INFO - 找到 3 个目录
2025-07-28 11:18:57,213 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:18:57,218 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:18:57,220 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:18:57,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:18:57,248 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:18:57,274 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:18:57,276 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:18:57,276 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:18:57,277 - werkzeug - INFO - ************** - - [28/Jul/2025 11:18:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:19:57,200 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:19:57,202 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:19:57,211 - map_api - INFO - 找到 3 个目录
2025-07-28 11:19:57,212 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:19:57,217 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:19:57,218 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:19:57,223 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:19:57,224 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:19:57,229 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:19:57,231 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:19:57,231 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:19:57,233 - werkzeug - INFO - ************** - - [28/Jul/2025 11:19:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:20:57,205 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:20:57,207 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:20:57,235 - map_api - INFO - 找到 3 个目录
2025-07-28 11:20:57,237 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:20:57,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:20:57,265 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:20:57,295 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:20:57,297 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:20:57,300 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:20:57,301 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:20:57,302 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:20:57,303 - werkzeug - INFO - ************** - - [28/Jul/2025 11:20:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:21:57,203 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:21:57,205 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:21:57,217 - map_api - INFO - 找到 3 个目录
2025-07-28 11:21:57,218 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:21:57,226 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:21:57,227 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:21:57,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:21:57,236 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:21:57,241 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:21:57,242 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:21:57,243 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:21:57,247 - werkzeug - INFO - ************** - - [28/Jul/2025 11:21:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:22:57,205 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:22:57,207 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:22:57,217 - map_api - INFO - 找到 3 个目录
2025-07-28 11:22:57,218 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:22:57,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:22:57,250 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:22:57,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:22:57,256 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:22:57,278 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:22:57,279 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:22:57,280 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:22:57,281 - werkzeug - INFO - ************** - - [28/Jul/2025 11:22:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:23:57,200 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:23:57,201 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:23:57,231 - map_api - INFO - 找到 3 个目录
2025-07-28 11:23:57,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:23:57,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:23:57,237 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:23:57,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:23:57,243 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:23:57,248 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:23:57,249 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:23:57,249 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:23:57,250 - werkzeug - INFO - ************** - - [28/Jul/2025 11:23:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:25:27,226 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:25:27,228 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:25:27,237 - map_api - INFO - 找到 3 个目录
2025-07-28 11:25:27,239 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:25:27,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:25:27,267 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:25:27,273 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:25:27,275 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:25:27,282 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:25:27,283 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:25:27,284 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:25:27,286 - werkzeug - INFO - ************** - - [28/Jul/2025 11:25:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:26:27,206 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:26:27,209 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:26:27,220 - map_api - INFO - 找到 3 个目录
2025-07-28 11:26:27,233 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:26:27,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:26:27,252 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:26:27,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:26:27,262 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:26:27,269 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:26:27,274 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:26:27,278 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:26:27,280 - werkzeug - INFO - ************** - - [28/Jul/2025 11:26:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:27:27,224 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:27:27,226 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:27:27,235 - map_api - INFO - 找到 3 个目录
2025-07-28 11:27:27,237 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:27:27,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:27:27,245 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:27:27,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:27:27,252 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:27:27,257 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:27:27,258 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:27:27,260 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:27:27,261 - werkzeug - INFO - ************** - - [28/Jul/2025 11:27:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:28:27,222 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:28:27,224 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:28:27,233 - map_api - INFO - 找到 3 个目录
2025-07-28 11:28:27,235 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:28:27,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:28:27,242 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:28:27,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:28:27,251 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:28:27,256 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:28:27,257 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:28:27,258 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:28:27,260 - werkzeug - INFO - ************** - - [28/Jul/2025 11:28:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:29:27,223 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:29:27,224 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:29:27,233 - map_api - INFO - 找到 3 个目录
2025-07-28 11:29:27,235 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:29:27,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:29:27,245 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:29:27,251 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:29:27,252 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:29:27,256 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:29:27,257 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:29:27,258 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:29:27,261 - werkzeug - INFO - ************** - - [28/Jul/2025 11:29:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:30:27,220 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:30:27,221 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:30:27,229 - map_api - INFO - 找到 3 个目录
2025-07-28 11:30:27,230 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:30:27,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:30:27,237 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:30:27,263 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:30:27,264 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:30:27,293 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:30:27,295 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:30:27,295 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:30:27,297 - werkzeug - INFO - ************** - - [28/Jul/2025 11:30:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:31:27,215 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:31:27,217 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:31:27,224 - map_api - INFO - 找到 3 个目录
2025-07-28 11:31:27,225 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:31:27,248 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:31:27,249 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:31:27,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2474 字节
2025-07-28 11:31:27,255 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:31:27,260 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:31:27,261 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:31:27,262 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:31:27,263 - werkzeug - INFO - ************** - - [28/Jul/2025 11:31:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:31:49,551 - geo_publisher - INFO - 启动GeoTIFF发布任务 2f2cd194-6d18-437b-a91d-de467720b289: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-07-28 11:31:49,552 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:31:49] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-28 11:31:49,552 - geo_task_2f2cd194-6d18-437b-a91d-de467720b289 - INFO - GeoServer发布任务 2f2cd194-6d18-437b-a91d-de467720b289 开始执行
2025-07-28 11:31:49,553 - geo_task_2f2cd194-6d18-437b-a91d-de467720b289 - INFO - 开始时间: 2025-07-28 11:31:49
2025-07-28 11:31:49,554 - geo_task_2f2cd194-6d18-437b-a91d-de467720b289 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-28 11:31:49,557 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-28 11:31:49,558 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-28 11:31:49,570 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:31:49] "GET /api/geo/status?task_id=2f2cd194-6d18-437b-a91d-de467720b289 HTTP/1.1" 200 -
2025-07-28 11:31:49,628 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-28 11:31:49,629 - root - INFO - 未提供存储名称，使用文件名: 20250705171601
2025-07-28 11:31:49,630 - root - INFO - 未提供图层名称，使用存储名称: 20250705171601
2025-07-28 11:31:49,657 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601.tif
2025-07-28 11:31:51,371 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 到 'testodm:20250705171601'
2025-07-28 11:31:51,462 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171601
2025-07-28 11:31:51,462 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 成功发布为图层 'testodm:20250705171601'
2025-07-28 11:31:51,465 - geo_task_2f2cd194-6d18-437b-a91d-de467720b289 - INFO - GeoServer发布任务 2f2cd194-6d18-437b-a91d-de467720b289 执行成功
2025-07-28 11:31:51,467 - geo_task_2f2cd194-6d18-437b-a91d-de467720b289 - INFO - 完成时间: 2025-07-28 11:31:51
2025-07-28 11:31:51,468 - geo_task_2f2cd194-6d18-437b-a91d-de467720b289 - INFO - 状态: 发布成功
2025-07-28 11:32:27,233 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:32:27,234 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:32:27,244 - map_api - INFO - 找到 3 个目录
2025-07-28 11:32:27,247 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:32:27,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:32:27,254 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:32:27,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2534 字节
2025-07-28 11:32:27,261 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:32:27,266 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 11:32:27,267 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:32:27,268 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 11:32:27,271 - werkzeug - INFO - ************** - - [28/Jul/2025 11:32:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:32:51,279 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:32:51] "GET /api/geo/status?task_id=2f2cd194-6d18-437b-a91d-de467720b289 HTTP/1.1" 200 -
2025-07-28 11:33:03,495 - batch_executor - INFO - 启动任务 45afaa23-2843-456a-aac6-589d464d4ad8: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
2025-07-28 11:33:03,497 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:33:03] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-28 11:33:03,522 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:33:03] "GET /api/batch/status?task_id=45afaa23-2843-456a-aac6-589d464d4ad8 HTTP/1.1" 200 -
2025-07-28 11:33:27,216 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:33:27,217 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:33:27,225 - map_api - INFO - 找到 3 个目录
2025-07-28 11:33:27,226 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:33:27,232 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:33:27,234 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:33:27,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:33:27,241 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:33:27,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 11:33:27,249 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:33:27,250 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 11:33:27,251 - werkzeug - INFO - ************** - - [28/Jul/2025 11:33:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:34:05,270 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:34:05] "GET /api/batch/status?task_id=45afaa23-2843-456a-aac6-589d464d4ad8 HTTP/1.1" 200 -
2025-07-28 11:34:14,723 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-28 11:34:14,725 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 11:34:14,726 - tif_api - INFO - 输入文件大小: 237.56 MB
2025-07-28 11:34:14,727 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 11:34:14,728 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 11:34:14,732 - tif_api - INFO - 黑色阈值: 0
2025-07-28 11:34:14,732 - tif_api - INFO - 白色阈值: 255
2025-07-28 11:34:14,733 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-28 11:34:14,736 - tif_executor - INFO - 启动TIF处理任务 da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 11:34:14,737 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - ============ TIF处理任务 da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd 开始执行 ============
2025-07-28 11:34:14,737 - tif_api - INFO - 异步任务启动成功，任务ID: da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd
2025-07-28 11:34:14,737 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 开始时间: 2025-07-28 11:34:14
2025-07-28 11:34:14,738 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:34:14] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-28 11:34:14,738 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 11:34:14,741 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 系统信息:
2025-07-28 11:34:14,742 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO -   操作系统: Windows 10.0.19045
2025-07-28 11:34:14,743 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO -   Python版本: 3.8.20
2025-07-28 11:34:14,745 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO -   GDAL版本: 3.9.2
2025-07-28 11:34:14,745 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO -   GPU可用: 否
2025-07-28 11:34:14,746 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 检查参数有效性...
2025-07-28 11:34:14,746 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 11:34:14,748 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 开始执行TIF处理流程...
2025-07-28 11:34:14,748 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 11:34:14,749 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 11:34:14,749 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 11:34:14,762 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 11:34:14,762 - tif_api - INFO - 查询任务: da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd
2025-07-28 11:34:14,765 - tif_api - INFO - 任务 da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd 状态查询成功，当前状态: 正在运行
2025-07-28 11:34:14,766 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:34:14] "GET /api/tif/status?task_id=da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd HTTP/1.1" 200 -
2025-07-28 11:34:18,146 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (11:34:18)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 11:34:18,152 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 11:34:18,455 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:05,  3.57it/s]
2025-07-28 11:34:18,714 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 16.90it/s]
2025-07-28 11:34:18,973 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 21.44it/s]
2025-07-28 11:34:18,998 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 25.48it/s]
2025-07-28 11:34:18,999 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 11:34:18,999 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 11:34:19,029 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 732.94it/s]
2025-07-28 11:34:20,623 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 1.92 秒 (11:34:19)预计总处理时间: 约 5.77 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (11:34:19)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 11:34:20,625 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 11:34:27,257 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:34:27,259 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:34:27,286 - map_api - INFO - 找到 3 个目录
2025-07-28 11:34:27,287 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:34:27,292 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:34:27,293 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:34:27,300 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:34:27,300 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:34:27,304 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 11:34:27,306 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:34:27,307 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 11:34:27,308 - werkzeug - INFO - ************** - - [28/Jul/2025 11:34:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:34:31,317 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 11:34:31,318 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 11:34:31,318 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:34:31,574 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:05,  3.92it/s]
2025-07-28 11:34:31,575 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:34:31,679 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:00, 23.58it/s]
2025-07-28 11:34:31,680 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:34:31,781 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:00<00:00, 45.51it/s]
2025-07-28 11:34:31,782 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:34:31,841 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:35:16,393 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:55<01:51, 55.77s/it]
2025-07-28 11:35:16,464 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 11:35:16,465 - tif_api - INFO - 查询任务: da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd
2025-07-28 11:35:16,469 - tif_api - INFO - 任务 da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd 状态查询成功，当前状态: 正在运行
2025-07-28 11:35:16,470 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:35:16] "GET /api/tif/status?task_id=da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd HTTP/1.1" 200 -
2025-07-28 11:35:27,220 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:35:27,222 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:35:27,232 - map_api - INFO - 找到 3 个目录
2025-07-28 11:35:27,234 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:35:27,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:35:27,243 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:35:27,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:35:27,251 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:35:27,257 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 11:35:27,258 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:35:27,259 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 11:35:27,262 - werkzeug - INFO - ************** - - [28/Jul/2025 11:35:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:35:43,795 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 11:35:43,796 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 11:35:43,797 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:35:44,020 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:04,  4.50it/s]
2025-07-28 11:35:44,021 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:35:44,206 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  7.89it/s]
2025-07-28 11:35:44,207 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:35:44,371 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:01,  9.62it/s]
2025-07-28 11:35:44,372 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:35:44,541 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:01, 10.46it/s]
2025-07-28 11:35:46,250 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:35:46,432 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:02<00:04,  2.40it/s]
2025-07-28 11:35:46,432 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:35:46,582 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:02<00:02,  3.37it/s]
2025-07-28 11:35:46,583 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:35:46,735 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:02<00:01,  4.48it/s]
2025-07-28 11:35:46,736 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:35:46,860 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:03<00:01,  5.83it/s]
2025-07-28 11:35:46,861 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:35:54,017 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:10<00:04,  1.25s/it]
2025-07-28 11:35:54,017 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:35:57,363 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:13<00:04,  1.63s/it]
2025-07-28 11:35:57,363 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:36:07,239 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:23<00:06,  3.35s/it]
2025-07-28 11:36:07,240 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:36:11,631 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:27<00:03,  3.59s/it]
2025-07-28 11:36:11,631 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:36:12,762 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:28<00:00,  2.98s/it]
2025-07-28 11:36:12,762 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:36:12,763 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:36:18,237 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 11:36:18,238 - tif_api - INFO - 查询任务: da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd
2025-07-28 11:36:18,240 - tif_api - INFO - 任务 da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd 状态查询成功，当前状态: 正在运行
2025-07-28 11:36:18,243 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:36:18] "GET /api/tif/status?task_id=da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd HTTP/1.1" 200 -
2025-07-28 11:36:27,205 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:36:27,206 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:36:27,213 - map_api - INFO - 找到 3 个目录
2025-07-28 11:36:27,214 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:36:27,218 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:36:27,220 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:36:27,224 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:36:27,225 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:36:27,231 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 11:36:27,232 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:36:27,232 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 11:36:27,234 - werkzeug - INFO - ************** - - [28/Jul/2025 11:36:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:37:10,027 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [02:49<01:29, 89.81s/it]
2025-07-28 11:37:20,013 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 11:37:20,015 - tif_api - INFO - 查询任务: da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd
2025-07-28 11:37:20,017 - tif_api - INFO - 任务 da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd 状态查询成功，当前状态: 正在运行
2025-07-28 11:37:20,018 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:37:20] "GET /api/tif/status?task_id=da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd HTTP/1.1" 200 -
2025-07-28 11:37:27,394 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:37:27,402 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:37:27,419 - map_api - INFO - 找到 3 个目录
2025-07-28 11:37:27,420 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:37:27,425 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:37:27,427 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:37:27,433 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:37:27,434 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:37:27,440 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 11:37:27,441 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:37:27,442 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 11:37:27,444 - werkzeug - INFO - ************** - - [28/Jul/2025 11:37:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:37:47,745 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 11:37:47,747 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 11:37:47,747 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:37:48,249 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:10,  2.00it/s]
2025-07-28 11:37:48,250 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:37:48,694 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:08,  2.14it/s]
2025-07-28 11:37:48,694 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:37:49,224 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:08,  2.01it/s]
2025-07-28 11:37:49,224 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:37:49,779 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:02<00:08,  1.93it/s]
2025-07-28 11:37:49,779 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:37:49,887 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 3/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:04,  3.67it/s]
2025-07-28 11:37:49,888 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:37:49,990 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 3/3 写入进度:  48%|##############################                                 | 10/21 [00:02<00:01,  8.06it/s]
2025-07-28 11:37:49,991 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:37:50,096 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:00, 12.67it/s]
2025-07-28 11:37:50,097 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:37:50,209 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [00:02<00:00, 20.38it/s]
2025-07-28 11:37:50,210 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:37:50,224 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - [A
2025-07-28 11:38:21,768 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 11:38:21,769 - tif_api - INFO - 查询任务: da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd
2025-07-28 11:38:22,292 - tif_api - INFO - 任务 da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd 状态查询成功，当前状态: 正在运行
2025-07-28 11:38:22,294 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:38:22] "GET /api/tif/status?task_id=da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd HTTP/1.1" 200 -
2025-07-28 11:38:27,217 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:38:27,220 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:38:27,243 - map_api - INFO - 找到 3 个目录
2025-07-28 11:38:27,245 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:38:27,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:38:27,255 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:38:27,260 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:38:27,262 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:38:27,268 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 11:38:27,269 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:38:27,269 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 11:38:27,271 - werkzeug - INFO - ************** - - [28/Jul/2025 11:38:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:39:24,084 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 11:39:24,086 - tif_api - INFO - 查询任务: da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd
2025-07-28 11:39:24,878 - tif_api - INFO - 任务 da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd 状态查询成功，当前状态: 正在运行
2025-07-28 11:39:24,880 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:39:24] "GET /api/tif/status?task_id=da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd HTTP/1.1" 200 -
2025-07-28 11:39:27,214 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:39:27,216 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:39:27,223 - map_api - INFO - 找到 3 个目录
2025-07-28 11:39:27,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:39:27,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:39:27,231 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:39:27,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:39:27,237 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:39:27,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 11:39:27,244 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:39:27,245 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 11:39:27,247 - werkzeug - INFO - ************** - - [28/Jul/2025 11:39:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:39:59,184 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [05:38<00:00, 126.04s/it]
2025-07-28 11:39:59,184 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [05:38<00:00, 112.85s/it]
2025-07-28 11:40:26,828 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 11:40:26,835 - tif_api - INFO - 查询任务: da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd
2025-07-28 11:40:26,848 - tif_api - INFO - 任务 da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd 状态查询成功，当前状态: 正在运行
2025-07-28 11:40:26,853 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:40:26] "GET /api/tif/status?task_id=da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd HTTP/1.1" 200 -
2025-07-28 11:40:27,250 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:40:27,252 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:40:27,267 - map_api - INFO - 找到 3 个目录
2025-07-28 11:40:27,269 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:40:28,349 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:40:28,357 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:40:28,421 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:40:28,423 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:40:28,430 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 11:40:28,432 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:40:28,432 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 11:40:28,436 - werkzeug - INFO - ************** - - [28/Jul/2025 11:40:28] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:40:55,722 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 339.58 秒 (11:39:59)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (11:39:59)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-28 11:40:55,756 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-28 11:40:56,084 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  3.06it/s]
2025-07-28 11:40:56,085 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  3.04it/s]
2025-07-28 11:40:56,092 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-28 11:41:07,984 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-28 11:41:07,985 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-28 11:41:09,180 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:01<00:17,  1.68it/s]
2025-07-28 11:41:09,306 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:11,  2.50it/s]
2025-07-28 11:41:11,972 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理轮廓:  13%|#########4                                                               | 4/31 [00:03<00:33,  1.25s/it]
2025-07-28 11:41:12,014 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:04<00:00,  7.70it/s]
2025-07-28 11:41:13,418 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-28 11:41:13,420 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-28 11:41:14,967 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
写入多边形:   6%|####                                                                   | 2/35 [00:01<00:25,  1.29it/s]
2025-07-28 11:41:14,985 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - ERROR - 
写入多边形: 100%|######################################################################| 35/35 [00:01<00:00, 22.38it/s]
2025-07-28 11:41:15,216 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 处理完成，耗时: 420.47 秒 (7.01 分钟)
2025-07-28 11:41:15,217 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 处理结果: 成功
2025-07-28 11:41:15,223 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-28 11:41:15,225 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-28 11:41:15,231 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - TIF处理任务 da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd 执行成功
2025-07-28 11:41:15,232 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 完成时间: 2025-07-28 11:41:15
2025-07-28 11:41:15,233 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - 状态: 运行成功
2025-07-28 11:41:15,233 - tif_task_da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd - INFO - ============ 任务执行结束 ============
2025-07-28 11:41:27,223 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:41:27,225 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:41:27,263 - map_api - INFO - 找到 3 个目录
2025-07-28 11:41:27,264 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:41:27,854 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:41:27,857 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:41:28,609 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 11:41:28,611 - tif_api - INFO - 查询任务: da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd
2025-07-28 11:41:28,614 - tif_api - INFO - 任务 da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd 状态查询成功，当前状态: 运行成功
2025-07-28 11:41:28,616 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:41:28] "GET /api/tif/status?task_id=da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd HTTP/1.1" 200 -
2025-07-28 11:41:29,170 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:41:29,171 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:41:29,186 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2146 字节
2025-07-28 11:41:29,187 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:41:29,188 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 11:41:29,189 - werkzeug - INFO - ************** - - [28/Jul/2025 11:41:29] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:41:34,147 - geo_task_73df475a-9777-404f-be70-a349026590b7 - INFO - GeoServer发布任务 73df475a-9777-404f-be70-a349026590b7 开始执行
2025-07-28 11:41:34,146 - geo_publisher - INFO - 启动GeoTIFF发布任务 73df475a-9777-404f-be70-a349026590b7: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-28 11:41:34,151 - geo_task_73df475a-9777-404f-be70-a349026590b7 - INFO - 开始时间: 2025-07-28 11:41:34
2025-07-28 11:41:34,152 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:41:34] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-28 11:41:34,152 - geo_task_73df475a-9777-404f-be70-a349026590b7 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-28 11:41:34,155 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-28 11:41:34,155 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-28 11:41:34,175 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:41:34] "GET /api/geo/status?task_id=73df475a-9777-404f-be70-a349026590b7 HTTP/1.1" 200 -
2025-07-28 11:41:34,242 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-28 11:41:34,243 - root - INFO - 未提供存储名称，使用文件名: 20250705171602
2025-07-28 11:41:34,244 - root - INFO - 未提供图层名称，使用存储名称: 20250705171602
2025-07-28 11:41:34,273 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171602\20250705171602.tif
2025-07-28 11:42:27,211 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:42:27,214 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:42:27,222 - map_api - INFO - 找到 3 个目录
2025-07-28 11:42:27,223 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:42:27,229 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:42:27,231 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:42:27,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:42:27,238 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:42:27,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:42:27,263 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:42:27,263 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 11:42:27,265 - werkzeug - INFO - ************** - - [28/Jul/2025 11:42:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:42:35,744 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:42:35] "GET /api/geo/status?task_id=73df475a-9777-404f-be70-a349026590b7 HTTP/1.1" 200 -
2025-07-28 11:42:54,127 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:42:54,128 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:42:54,130 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:42:54,132 - werkzeug - INFO - ************** - - [28/Jul/2025 11:42:54] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:42:56,754 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-28 11:42:56,755 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log
2025-07-28 11:42:57,125 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:42:57,128 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:42:57,138 - map_api - INFO - 找到 3 个目录
2025-07-28 11:42:57,139 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:42:57,147 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:42:57,150 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:42:57,158 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:42:57,159 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:42:57,164 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:42:57,165 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:42:57,166 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 11:42:57,168 - werkzeug - INFO - ************** - - [28/Jul/2025 11:42:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:42:57,427 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log, 内容大小: 17354 字节
2025-07-28 11:42:57,429 - werkzeug - INFO - ************** - - [28/Jul/2025 11:42:57] "GET /api/map/logs?log_type=tiflog&log_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-28 11:42:58,850 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:42:58,851 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:42:58,854 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:42:58,855 - werkzeug - INFO - ************** - - [28/Jul/2025 11:42:58] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:43:04,281 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 11:43:04,282 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 11:43:04,284 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 11:43:04,286 - werkzeug - INFO - ************** - - [28/Jul/2025 11:43:04] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 11:43:06,848 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-28 11:43:06,850 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log
2025-07-28 11:43:06,852 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log, 内容大小: 17354 字节
2025-07-28 11:43:06,857 - werkzeug - INFO - ************** - - [28/Jul/2025 11:43:06] "GET /api/map/logs?log_type=tiflog&log_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-28 11:43:36,777 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:43:36] "GET /api/geo/status?task_id=73df475a-9777-404f-be70-a349026590b7 HTTP/1.1" 200 -
2025-07-28 11:43:57,194 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:43:57,199 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:43:57,205 - map_api - INFO - 找到 3 个目录
2025-07-28 11:43:57,206 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:43:57,211 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:43:57,212 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:43:57,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:43:57,237 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:43:57,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:43:57,253 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:43:57,254 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 11:43:57,256 - werkzeug - INFO - ************** - - [28/Jul/2025 11:43:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:44:37,478 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:44:37] "GET /api/geo/status?task_id=73df475a-9777-404f-be70-a349026590b7 HTTP/1.1" 200 -
2025-07-28 11:45:17,868 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 到 'testodm:20250705171602'
2025-07-28 11:45:19,473 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171602
2025-07-28 11:45:19,474 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 成功发布为图层 'testodm:20250705171602'
2025-07-28 11:45:19,561 - geo_task_73df475a-9777-404f-be70-a349026590b7 - INFO - GeoServer发布任务 73df475a-9777-404f-be70-a349026590b7 执行成功
2025-07-28 11:45:19,563 - geo_task_73df475a-9777-404f-be70-a349026590b7 - INFO - 完成时间: 2025-07-28 11:45:19
2025-07-28 11:45:19,564 - geo_task_73df475a-9777-404f-be70-a349026590b7 - INFO - 状态: 发布成功
2025-07-28 11:45:27,237 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:45:27,243 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:45:27,255 - map_api - INFO - 找到 3 个目录
2025-07-28 11:45:27,256 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:45:27,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:45:27,263 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:45:27,269 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:45:27,271 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:45:27,277 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 11:45:27,278 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:45:27,279 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 11:45:27,281 - werkzeug - INFO - ************** - - [28/Jul/2025 11:45:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:45:38,787 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 11:45:38] "GET /api/geo/status?task_id=73df475a-9777-404f-be70-a349026590b7 HTTP/1.1" 200 -
2025-07-28 11:46:27,205 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:46:27,207 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:46:27,217 - map_api - INFO - 找到 3 个目录
2025-07-28 11:46:27,218 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:46:27,224 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:46:27,228 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:46:27,251 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:46:27,252 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:46:27,258 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:46:27,259 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:46:27,260 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:46:27,262 - werkzeug - INFO - ************** - - [28/Jul/2025 11:46:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:47:27,220 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:47:27,222 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:47:27,230 - map_api - INFO - 找到 3 个目录
2025-07-28 11:47:27,231 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:47:27,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:47:27,253 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:47:27,258 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:47:27,259 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:47:27,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:47:27,265 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:47:27,266 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:47:27,268 - werkzeug - INFO - ************** - - [28/Jul/2025 11:47:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:48:27,218 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:48:27,220 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:48:27,244 - map_api - INFO - 找到 3 个目录
2025-07-28 11:48:27,246 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:48:27,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:48:27,254 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:48:27,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:48:27,264 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:48:27,273 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:48:27,282 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:48:27,287 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:48:27,298 - werkzeug - INFO - ************** - - [28/Jul/2025 11:48:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:49:27,217 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:49:27,219 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:49:27,228 - map_api - INFO - 找到 3 个目录
2025-07-28 11:49:27,229 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:49:27,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:49:27,236 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:49:27,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:49:27,243 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:49:27,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:49:27,251 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:49:27,252 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:49:27,253 - werkzeug - INFO - ************** - - [28/Jul/2025 11:49:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:50:27,213 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:50:27,215 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:50:27,223 - map_api - INFO - 找到 3 个目录
2025-07-28 11:50:27,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:50:27,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:50:27,231 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:50:27,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:50:27,238 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:50:27,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:50:27,248 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:50:27,248 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:50:27,250 - werkzeug - INFO - ************** - - [28/Jul/2025 11:50:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:51:27,232 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:51:27,238 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:51:27,250 - map_api - INFO - 找到 3 个目录
2025-07-28 11:51:27,252 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:51:27,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:51:27,268 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:51:27,275 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:51:27,276 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:51:27,285 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:51:27,286 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:51:27,290 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:51:27,292 - werkzeug - INFO - ************** - - [28/Jul/2025 11:51:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:52:27,211 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:52:27,213 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:52:27,223 - map_api - INFO - 找到 3 个目录
2025-07-28 11:52:27,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:52:27,229 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:52:27,231 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:52:27,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:52:27,237 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:52:27,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:52:27,245 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:52:27,246 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:52:27,247 - werkzeug - INFO - ************** - - [28/Jul/2025 11:52:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:53:27,221 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:53:27,225 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:53:27,236 - map_api - INFO - 找到 3 个目录
2025-07-28 11:53:27,238 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:53:27,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:53:27,247 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:53:27,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:53:27,256 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:53:27,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:53:27,267 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:53:27,268 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:53:27,270 - werkzeug - INFO - ************** - - [28/Jul/2025 11:53:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:54:27,228 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:54:27,231 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:54:27,239 - map_api - INFO - 找到 3 个目录
2025-07-28 11:54:27,240 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:54:27,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:54:27,247 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:54:27,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:54:27,254 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:54:27,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:54:27,260 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:54:27,261 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:54:27,262 - werkzeug - INFO - ************** - - [28/Jul/2025 11:54:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:55:27,222 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:55:27,224 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:55:27,234 - map_api - INFO - 找到 3 个目录
2025-07-28 11:55:27,235 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:55:27,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:55:27,242 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:55:27,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:55:27,250 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:55:27,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:55:27,259 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:55:27,259 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:55:27,262 - werkzeug - INFO - ************** - - [28/Jul/2025 11:55:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:56:27,215 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:56:27,226 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:56:27,240 - map_api - INFO - 找到 3 个目录
2025-07-28 11:56:27,242 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:56:27,248 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:56:27,249 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:56:27,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:56:27,256 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:56:27,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:56:27,262 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:56:27,263 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:56:27,265 - werkzeug - INFO - ************** - - [28/Jul/2025 11:56:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:57:27,213 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:57:27,214 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:57:27,222 - map_api - INFO - 找到 3 个目录
2025-07-28 11:57:27,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:57:27,229 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:57:27,230 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:57:27,235 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:57:27,236 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:57:27,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:57:27,245 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:57:27,245 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:57:27,247 - werkzeug - INFO - ************** - - [28/Jul/2025 11:57:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:58:27,247 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:58:27,248 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:58:27,257 - map_api - INFO - 找到 3 个目录
2025-07-28 11:58:27,258 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:58:27,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:58:27,263 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:58:27,268 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:58:27,270 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:58:27,275 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:58:27,276 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:58:27,277 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:58:27,279 - werkzeug - INFO - ************** - - [28/Jul/2025 11:58:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 11:59:27,218 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 11:59:27,219 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 11:59:27,228 - map_api - INFO - 找到 3 个目录
2025-07-28 11:59:27,229 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 11:59:27,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 11:59:27,235 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 11:59:27,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 11:59:27,243 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 11:59:27,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 11:59:27,250 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 11:59:27,251 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 11:59:27,253 - werkzeug - INFO - ************** - - [28/Jul/2025 11:59:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:00:27,224 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:00:27,227 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:00:27,236 - map_api - INFO - 找到 3 个目录
2025-07-28 12:00:27,237 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:00:27,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:00:27,245 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:00:27,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:00:27,251 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:00:27,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:00:27,258 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:00:27,258 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:00:27,260 - werkzeug - INFO - ************** - - [28/Jul/2025 12:00:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:01:27,227 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:01:27,228 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:01:27,237 - map_api - INFO - 找到 3 个目录
2025-07-28 12:01:27,238 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:01:27,244 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:01:27,246 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:01:27,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:01:27,253 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:01:27,258 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:01:27,260 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:01:27,261 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:01:27,263 - werkzeug - INFO - ************** - - [28/Jul/2025 12:01:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:02:27,219 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:02:27,220 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:02:27,229 - map_api - INFO - 找到 3 个目录
2025-07-28 12:02:27,230 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:02:27,235 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:02:27,236 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:02:27,251 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:02:27,252 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:02:27,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:02:27,268 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:02:27,269 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:02:27,271 - werkzeug - INFO - ************** - - [28/Jul/2025 12:02:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:03:27,212 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:03:27,213 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:03:27,241 - map_api - INFO - 找到 3 个目录
2025-07-28 12:03:27,246 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:03:27,251 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:03:27,252 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:03:27,257 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:03:27,259 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:03:27,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:03:27,266 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:03:27,267 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:03:27,269 - werkzeug - INFO - ************** - - [28/Jul/2025 12:03:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:04:27,240 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:04:27,244 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:04:27,253 - map_api - INFO - 找到 3 个目录
2025-07-28 12:04:27,254 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:04:27,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:04:27,260 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:04:27,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:04:27,267 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:04:27,272 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:04:27,274 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:04:27,274 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:04:27,276 - werkzeug - INFO - ************** - - [28/Jul/2025 12:04:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:05:27,221 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:05:27,222 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:05:27,230 - map_api - INFO - 找到 3 个目录
2025-07-28 12:05:27,231 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:05:27,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:05:27,238 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:05:27,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:05:27,244 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:05:27,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:05:27,251 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:05:27,251 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:05:27,253 - werkzeug - INFO - ************** - - [28/Jul/2025 12:05:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:06:27,213 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:06:27,215 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:06:27,222 - map_api - INFO - 找到 3 个目录
2025-07-28 12:06:27,223 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:06:27,228 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:06:27,230 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:06:27,235 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:06:27,237 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:06:27,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:06:27,244 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:06:27,244 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:06:27,246 - werkzeug - INFO - ************** - - [28/Jul/2025 12:06:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:07:27,239 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:07:27,244 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:07:27,256 - map_api - INFO - 找到 3 个目录
2025-07-28 12:07:27,259 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:07:27,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:07:27,268 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:07:27,274 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:07:27,277 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:07:27,305 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:07:27,309 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:07:27,310 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:07:27,311 - werkzeug - INFO - ************** - - [28/Jul/2025 12:07:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:08:27,227 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:08:27,229 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:08:27,239 - map_api - INFO - 找到 3 个目录
2025-07-28 12:08:27,241 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:08:27,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:08:27,248 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:08:27,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:08:27,256 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:08:27,263 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:08:27,265 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:08:27,265 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:08:27,267 - werkzeug - INFO - ************** - - [28/Jul/2025 12:08:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:08:42,621 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:08:42,623 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:08:42,634 - map_api - INFO - 找到 3 个目录
2025-07-28 12:08:42,637 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:08:42,645 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:08:42,647 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:08:42,655 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:08:42,656 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:08:42,663 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:08:42,664 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:08:42,667 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:08:42,669 - werkzeug - INFO - ************** - - [28/Jul/2025 12:08:42] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:08:44,323 - map_api - INFO - 请求获取日志: 类型=geolog, ID=73df475a-9777-404f-be70-a349026590b7
2025-07-28 12:08:44,324 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\73df475a-9777-404f-be70-a349026590b7.log
2025-07-28 12:08:44,326 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\73df475a-9777-404f-be70-a349026590b7.log, 内容大小: 829 字节
2025-07-28 12:08:44,327 - werkzeug - INFO - ************** - - [28/Jul/2025 12:08:44] "GET /api/map/logs?log_type=geolog&log_id=73df475a-9777-404f-be70-a349026590b7 HTTP/1.1" 200 -
2025-07-28 12:08:48,528 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd
2025-07-28 12:08:48,530 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd.log
2025-07-28 12:08:48,531 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd.log, 内容大小: 15834 字节
2025-07-28 12:08:48,533 - werkzeug - INFO - ************** - - [28/Jul/2025 12:08:48] "GET /api/map/logs?log_type=tiflog&log_id=da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd HTTP/1.1" 200 -
2025-07-28 12:08:51,711 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 12:08:51,715 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 12:08:51,716 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 12:08:51,718 - werkzeug - INFO - ************** - - [28/Jul/2025 12:08:51] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 12:08:53,545 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-28 12:08:53,546 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log
2025-07-28 12:08:53,548 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log, 内容大小: 17354 字节
2025-07-28 12:08:53,549 - werkzeug - INFO - ************** - - [28/Jul/2025 12:08:53] "GET /api/map/logs?log_type=tiflog&log_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-28 12:08:54,970 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 12:08:54,972 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 12:08:54,974 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 12:08:54,977 - werkzeug - INFO - ************** - - [28/Jul/2025 12:08:54] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 12:09:03,731 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-28 12:09:03,732 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log
2025-07-28 12:09:03,735 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log, 内容大小: 17354 字节
2025-07-28 12:09:03,736 - werkzeug - INFO - ************** - - [28/Jul/2025 12:09:03] "GET /api/map/logs?log_type=tiflog&log_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-28 12:09:05,267 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 12:09:05,269 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 12:09:05,271 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 12:09:05,274 - werkzeug - INFO - ************** - - [28/Jul/2025 12:09:05] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 12:09:10,017 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 12:09:10,019 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 12:09:10,024 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 12:09:10,028 - werkzeug - INFO - ************** - - [28/Jul/2025 12:09:10] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 12:09:25,389 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 12:09:25,391 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 12:09:25,393 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 12:09:25,394 - werkzeug - INFO - ************** - - [28/Jul/2025 12:09:25] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 12:09:27,306 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-28 12:09:27,308 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log
2025-07-28 12:09:27,309 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log, 内容大小: 17354 字节
2025-07-28 12:09:27,311 - werkzeug - INFO - ************** - - [28/Jul/2025 12:09:27] "GET /api/map/logs?log_type=tiflog&log_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-28 12:09:28,594 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 12:09:28,595 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 12:09:28,598 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 12:09:28,599 - werkzeug - INFO - ************** - - [28/Jul/2025 12:09:28] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 12:09:35,333 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=a413982e-ed7d-4158-b465-872ed73274df
2025-07-28 12:09:35,336 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\a413982e-ed7d-4158-b465-872ed73274df.log
2025-07-28 12:09:35,338 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\a413982e-ed7d-4158-b465-872ed73274df.log, 内容大小: 10343 字节
2025-07-28 12:09:35,339 - werkzeug - INFO - ************** - - [28/Jul/2025 12:09:35] "GET /api/map/logs?log_type=tiflog&log_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-28 12:09:42,622 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:09:42,624 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:09:42,634 - map_api - INFO - 找到 3 个目录
2025-07-28 12:09:42,636 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:09:42,642 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:09:42,643 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:09:42,667 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:09:42,667 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:09:42,671 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:09:42,671 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:09:42,672 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:09:42,673 - werkzeug - INFO - ************** - - [28/Jul/2025 12:09:42] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:10:02,490 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:10:02,492 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:10:02,503 - map_api - INFO - 找到 3 个目录
2025-07-28 12:10:02,505 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:10:02,511 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:10:02,512 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:10:02,527 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:10:02,528 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:10:02,534 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:10:02,535 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:10:02,536 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:10:02,538 - werkzeug - INFO - ************** - - [28/Jul/2025 12:10:02] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:10:57,936 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:10:57,937 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:10:57,967 - map_api - INFO - 找到 3 个目录
2025-07-28 12:10:57,971 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:10:57,996 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:10:57,999 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:10:58,028 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:10:58,030 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:10:58,036 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:10:58,039 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:10:58,041 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:10:58,042 - werkzeug - INFO - ************** - - [28/Jul/2025 12:10:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:11:58,214 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:11:58,216 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:11:58,226 - map_api - INFO - 找到 3 个目录
2025-07-28 12:11:58,227 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:11:58,232 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:11:58,234 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:11:58,239 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:11:58,242 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:11:58,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:11:58,248 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:11:58,250 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:11:58,251 - werkzeug - INFO - ************** - - [28/Jul/2025 12:11:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:12:57,931 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:12:57,933 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:12:57,943 - map_api - INFO - 找到 3 个目录
2025-07-28 12:12:57,945 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:12:57,952 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:12:57,953 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:12:57,960 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:12:57,961 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:12:57,967 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:12:57,968 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:12:57,971 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:12:57,974 - werkzeug - INFO - ************** - - [28/Jul/2025 12:12:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:13:57,934 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:13:57,936 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:13:57,968 - map_api - INFO - 找到 3 个目录
2025-07-28 12:13:57,970 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:13:57,995 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:13:57,997 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:13:58,002 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:13:58,003 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:13:58,008 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:13:58,009 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:13:58,012 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:13:58,013 - werkzeug - INFO - ************** - - [28/Jul/2025 12:13:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:14:57,934 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:14:57,935 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:14:57,943 - map_api - INFO - 找到 3 个目录
2025-07-28 12:14:57,944 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:14:57,949 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:14:57,950 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:14:57,979 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:14:57,981 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:14:57,985 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:14:57,986 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:14:57,987 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:14:57,988 - werkzeug - INFO - ************** - - [28/Jul/2025 12:14:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:15:57,935 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:15:57,936 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:15:57,967 - map_api - INFO - 找到 3 个目录
2025-07-28 12:15:57,969 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:15:57,976 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:15:57,978 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:15:57,986 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:15:57,991 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:15:58,011 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:15:58,012 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:15:58,013 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:15:58,014 - werkzeug - INFO - ************** - - [28/Jul/2025 12:15:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:16:57,940 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:16:57,942 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:16:57,951 - map_api - INFO - 找到 3 个目录
2025-07-28 12:16:57,952 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:16:57,957 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:16:57,958 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:16:57,985 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:16:57,987 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:16:57,991 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:16:57,992 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:16:57,992 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:16:57,994 - werkzeug - INFO - ************** - - [28/Jul/2025 12:16:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:17:57,933 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:17:57,934 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:17:57,945 - map_api - INFO - 找到 3 个目录
2025-07-28 12:17:57,947 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:17:57,953 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:17:57,955 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:17:57,961 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:17:57,965 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:17:57,972 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:17:57,974 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:17:57,976 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:17:57,980 - werkzeug - INFO - ************** - - [28/Jul/2025 12:17:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:18:57,944 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:18:57,947 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:18:57,956 - map_api - INFO - 找到 3 个目录
2025-07-28 12:18:57,958 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:18:57,962 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:18:57,964 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:18:57,969 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:18:57,971 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:18:58,001 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:18:58,002 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:18:58,003 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:18:58,004 - werkzeug - INFO - ************** - - [28/Jul/2025 12:18:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:19:57,940 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:19:57,942 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:19:57,950 - map_api - INFO - 找到 3 个目录
2025-07-28 12:19:57,951 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:19:57,969 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:19:57,971 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:19:57,977 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:19:57,980 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:19:58,000 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:19:58,002 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:19:58,003 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:19:58,004 - werkzeug - INFO - ************** - - [28/Jul/2025 12:19:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:20:57,943 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:20:57,945 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:20:57,956 - map_api - INFO - 找到 3 个目录
2025-07-28 12:20:57,970 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:20:57,976 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:20:57,979 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:20:57,986 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:20:57,988 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:20:57,994 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:20:57,997 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:20:58,002 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:20:58,004 - werkzeug - INFO - ************** - - [28/Jul/2025 12:20:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:21:57,934 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:21:57,935 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:21:57,942 - map_api - INFO - 找到 3 个目录
2025-07-28 12:21:57,944 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:21:57,948 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:21:57,950 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:21:57,955 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:21:57,956 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:21:57,981 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:21:57,983 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:21:57,984 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:21:57,985 - werkzeug - INFO - ************** - - [28/Jul/2025 12:21:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:22:57,941 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:22:57,943 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:22:57,952 - map_api - INFO - 找到 3 个目录
2025-07-28 12:22:57,954 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:22:57,960 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:22:57,962 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:22:57,968 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:22:57,970 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:22:57,976 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:22:57,978 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:22:57,980 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:22:57,990 - werkzeug - INFO - ************** - - [28/Jul/2025 12:22:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:23:57,932 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:23:57,934 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:23:57,941 - map_api - INFO - 找到 3 个目录
2025-07-28 12:23:57,942 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:23:57,961 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:23:57,963 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:23:57,967 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:23:57,969 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:23:57,975 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:23:57,979 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:23:57,981 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:23:57,986 - werkzeug - INFO - ************** - - [28/Jul/2025 12:23:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:24:57,933 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:24:57,935 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:24:57,943 - map_api - INFO - 找到 3 个目录
2025-07-28 12:24:57,944 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:24:57,953 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:24:57,954 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:24:57,959 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:24:57,960 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:24:57,975 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:24:57,979 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:24:57,980 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:24:57,982 - werkzeug - INFO - ************** - - [28/Jul/2025 12:24:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:25:57,943 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:25:57,946 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:25:57,982 - map_api - INFO - 找到 3 个目录
2025-07-28 12:25:57,984 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:25:57,999 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:25:58,006 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:25:58,035 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:25:58,036 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:25:58,041 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:25:58,041 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:25:58,042 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:25:58,043 - werkzeug - INFO - ************** - - [28/Jul/2025 12:25:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:26:57,934 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:26:57,936 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:26:57,967 - map_api - INFO - 找到 3 个目录
2025-07-28 12:26:57,969 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:26:57,977 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:26:57,981 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:26:58,011 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:26:58,012 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:26:58,017 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:26:58,018 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:26:58,019 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:26:58,020 - werkzeug - INFO - ************** - - [28/Jul/2025 12:26:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:27:57,945 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:27:57,951 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:27:57,958 - map_api - INFO - 找到 3 个目录
2025-07-28 12:27:57,960 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:27:57,965 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:27:57,967 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:27:57,974 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:27:57,975 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:27:57,980 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:27:57,983 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:27:57,985 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:27:57,988 - werkzeug - INFO - ************** - - [28/Jul/2025 12:27:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:28:57,944 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:28:57,949 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:28:57,978 - map_api - INFO - 找到 3 个目录
2025-07-28 12:28:57,980 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:28:58,005 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:28:58,006 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:28:58,011 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:28:58,013 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:28:58,018 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:28:58,019 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:28:58,020 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:28:58,022 - werkzeug - INFO - ************** - - [28/Jul/2025 12:28:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:29:57,940 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:29:57,941 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:29:57,950 - map_api - INFO - 找到 3 个目录
2025-07-28 12:29:57,951 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:29:57,970 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:29:57,973 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:29:57,980 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:29:57,985 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:29:57,994 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:29:57,996 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:29:57,997 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:29:57,998 - werkzeug - INFO - ************** - - [28/Jul/2025 12:29:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:30:57,933 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:30:57,934 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:30:57,942 - map_api - INFO - 找到 3 个目录
2025-07-28 12:30:57,948 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:30:57,975 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:30:57,979 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:30:57,985 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:30:57,986 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:30:58,006 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:30:58,008 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:30:58,008 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:30:58,010 - werkzeug - INFO - ************** - - [28/Jul/2025 12:30:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:31:57,941 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:31:57,944 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:31:57,972 - map_api - INFO - 找到 3 个目录
2025-07-28 12:31:57,974 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:31:57,983 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:31:57,985 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:31:57,989 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:31:57,990 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:31:57,997 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:31:57,999 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:31:58,000 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:31:58,001 - werkzeug - INFO - ************** - - [28/Jul/2025 12:31:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:32:57,937 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:32:57,938 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:32:57,947 - map_api - INFO - 找到 3 个目录
2025-07-28 12:32:57,949 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:32:57,956 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:32:57,957 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:32:57,963 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:32:57,964 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:32:57,982 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:32:57,983 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:32:57,984 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:32:57,986 - werkzeug - INFO - ************** - - [28/Jul/2025 12:32:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:33:57,940 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:33:57,941 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:33:57,971 - map_api - INFO - 找到 3 个目录
2025-07-28 12:33:57,973 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:33:57,980 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:33:57,983 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:33:57,991 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:33:57,992 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:33:57,998 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:33:58,001 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:33:58,002 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:33:58,003 - werkzeug - INFO - ************** - - [28/Jul/2025 12:33:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:34:57,944 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:34:57,945 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:34:57,979 - map_api - INFO - 找到 3 个目录
2025-07-28 12:34:57,985 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:34:57,992 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:34:57,995 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:34:58,021 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:34:58,023 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:34:58,028 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:34:58,029 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:34:58,030 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:34:58,031 - werkzeug - INFO - ************** - - [28/Jul/2025 12:34:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:35:57,945 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:35:57,949 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:35:57,976 - map_api - INFO - 找到 3 个目录
2025-07-28 12:35:57,982 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:35:58,003 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:35:58,004 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:35:58,019 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:35:58,020 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:35:58,051 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:35:58,052 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:35:58,054 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:35:58,058 - werkzeug - INFO - ************** - - [28/Jul/2025 12:35:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:36:57,933 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:36:57,936 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:36:57,946 - map_api - INFO - 找到 3 个目录
2025-07-28 12:36:57,947 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:36:57,953 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:36:57,955 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:36:57,960 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:36:57,965 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:36:57,971 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:36:57,973 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:36:57,974 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:36:57,981 - werkzeug - INFO - ************** - - [28/Jul/2025 12:36:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:37:57,932 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:37:57,934 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:37:57,942 - map_api - INFO - 找到 3 个目录
2025-07-28 12:37:57,943 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:37:57,950 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:37:57,951 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:37:57,955 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:37:57,956 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:37:57,961 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:37:57,963 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:37:57,964 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:37:57,965 - werkzeug - INFO - ************** - - [28/Jul/2025 12:37:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:38:57,937 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:38:57,938 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:38:57,946 - map_api - INFO - 找到 3 个目录
2025-07-28 12:38:57,950 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:38:57,955 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:38:57,957 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:38:57,963 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:38:57,964 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:38:57,982 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:38:57,983 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:38:57,984 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:38:57,985 - werkzeug - INFO - ************** - - [28/Jul/2025 12:38:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:39:57,943 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:39:57,944 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:39:57,953 - map_api - INFO - 找到 3 个目录
2025-07-28 12:39:57,954 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:39:57,960 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:39:57,965 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:39:57,972 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:39:57,974 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:39:57,981 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:39:57,983 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:39:57,987 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:39:57,992 - werkzeug - INFO - ************** - - [28/Jul/2025 12:39:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:40:57,933 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:40:57,935 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:40:57,964 - map_api - INFO - 找到 3 个目录
2025-07-28 12:40:57,967 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:40:57,993 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:40:57,997 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:40:58,001 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:40:58,003 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:40:58,009 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:40:58,010 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:40:58,012 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:40:58,014 - werkzeug - INFO - ************** - - [28/Jul/2025 12:40:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:41:57,933 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:41:57,935 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:41:57,965 - map_api - INFO - 找到 3 个目录
2025-07-28 12:41:57,967 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:41:57,972 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:41:57,974 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:41:57,981 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:41:57,985 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:41:57,997 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:41:57,998 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:41:57,999 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:41:58,001 - werkzeug - INFO - ************** - - [28/Jul/2025 12:41:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:42:57,935 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:42:57,936 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:42:57,944 - map_api - INFO - 找到 3 个目录
2025-07-28 12:42:57,945 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:42:57,962 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:42:57,963 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:42:57,968 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:42:57,971 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:42:57,994 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:42:57,997 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:42:57,998 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:42:57,999 - werkzeug - INFO - ************** - - [28/Jul/2025 12:42:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:43:57,939 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:43:57,941 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:43:57,950 - map_api - INFO - 找到 3 个目录
2025-07-28 12:43:57,951 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:43:57,956 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:43:57,958 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:43:57,963 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:43:57,966 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:43:57,971 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:43:57,973 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:43:57,975 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:43:57,977 - werkzeug - INFO - ************** - - [28/Jul/2025 12:43:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:44:57,930 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:44:57,932 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:44:57,940 - map_api - INFO - 找到 3 个目录
2025-07-28 12:44:57,941 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:44:57,946 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:44:57,948 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:44:57,975 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:44:57,979 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:44:58,007 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:44:58,008 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:44:58,009 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:44:58,011 - werkzeug - INFO - ************** - - [28/Jul/2025 12:44:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:45:58,206 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:45:58,208 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:45:58,236 - map_api - INFO - 找到 3 个目录
2025-07-28 12:45:58,237 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:45:58,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:45:58,266 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:45:58,281 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:45:58,282 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:45:58,296 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:45:58,297 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:45:58,298 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:45:58,299 - werkzeug - INFO - ************** - - [28/Jul/2025 12:45:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:47:27,220 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:47:27,222 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:47:27,232 - map_api - INFO - 找到 3 个目录
2025-07-28 12:47:27,233 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:47:27,239 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:47:27,241 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:47:27,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:47:27,251 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:47:27,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:47:27,262 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:47:27,263 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:47:27,265 - werkzeug - INFO - ************** - - [28/Jul/2025 12:47:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:48:27,244 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:48:27,250 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:48:27,261 - map_api - INFO - 找到 3 个目录
2025-07-28 12:48:27,265 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:48:27,272 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:48:27,276 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:48:27,282 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:48:27,285 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:48:27,293 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:48:27,297 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:48:27,298 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:48:27,301 - werkzeug - INFO - ************** - - [28/Jul/2025 12:48:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:49:27,230 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:49:27,232 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:49:27,241 - map_api - INFO - 找到 3 个目录
2025-07-28 12:49:27,243 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:49:27,248 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:49:27,250 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:49:27,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:49:27,257 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:49:27,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:49:27,263 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:49:27,264 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:49:27,266 - werkzeug - INFO - ************** - - [28/Jul/2025 12:49:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:50:27,272 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:50:27,274 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:50:27,283 - map_api - INFO - 找到 3 个目录
2025-07-28 12:50:27,284 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:50:27,290 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:50:27,292 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:50:27,297 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:50:27,299 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:50:27,305 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:50:27,306 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:50:27,307 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:50:27,309 - werkzeug - INFO - ************** - - [28/Jul/2025 12:50:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:51:27,227 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:51:27,229 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:51:27,238 - map_api - INFO - 找到 3 个目录
2025-07-28 12:51:27,239 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:51:27,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:51:27,247 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:51:27,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:51:27,255 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:51:27,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:51:27,263 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:51:27,264 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:51:27,265 - werkzeug - INFO - ************** - - [28/Jul/2025 12:51:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:52:27,243 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:52:27,245 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:52:27,255 - map_api - INFO - 找到 3 个目录
2025-07-28 12:52:27,256 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:52:27,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:52:27,263 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:52:27,271 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:52:27,273 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:52:27,278 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:52:27,280 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:52:27,281 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:52:27,283 - werkzeug - INFO - ************** - - [28/Jul/2025 12:52:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:53:27,222 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:53:27,226 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:53:27,239 - map_api - INFO - 找到 3 个目录
2025-07-28 12:53:27,240 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:53:27,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:53:27,248 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:53:27,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:53:27,256 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:53:27,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:53:27,265 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:53:27,266 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:53:27,268 - werkzeug - INFO - ************** - - [28/Jul/2025 12:53:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:54:27,221 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:54:27,222 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:54:27,232 - map_api - INFO - 找到 3 个目录
2025-07-28 12:54:27,234 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:54:27,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:54:27,241 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:54:27,248 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:54:27,250 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:54:27,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:54:27,257 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:54:27,258 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:54:27,260 - werkzeug - INFO - ************** - - [28/Jul/2025 12:54:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:55:27,225 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:55:27,226 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:55:27,252 - map_api - INFO - 找到 3 个目录
2025-07-28 12:55:27,254 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:55:27,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:55:27,263 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:55:27,268 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:55:27,296 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:55:27,305 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:55:27,306 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:55:27,307 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:55:27,308 - werkzeug - INFO - ************** - - [28/Jul/2025 12:55:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:56:27,221 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:56:27,222 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:56:27,230 - map_api - INFO - 找到 3 个目录
2025-07-28 12:56:27,231 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:56:27,235 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:56:27,237 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:56:27,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:56:27,243 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:56:27,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:56:27,251 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:56:27,252 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:56:27,253 - werkzeug - INFO - ************** - - [28/Jul/2025 12:56:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:57:27,213 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:57:27,215 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:57:27,224 - map_api - INFO - 找到 3 个目录
2025-07-28 12:57:27,225 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:57:27,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:57:27,232 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:57:27,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:57:27,239 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:57:27,245 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:57:27,248 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:57:27,248 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:57:27,250 - werkzeug - INFO - ************** - - [28/Jul/2025 12:57:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:58:27,234 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:58:27,235 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:58:27,245 - map_api - INFO - 找到 3 个目录
2025-07-28 12:58:27,249 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:58:27,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:58:27,256 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:58:27,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:58:27,263 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:58:27,270 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:58:27,272 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:58:27,273 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:58:27,276 - werkzeug - INFO - ************** - - [28/Jul/2025 12:58:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 12:59:27,224 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 12:59:27,225 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 12:59:27,234 - map_api - INFO - 找到 3 个目录
2025-07-28 12:59:27,235 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 12:59:27,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 12:59:27,242 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 12:59:27,248 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 12:59:27,249 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 12:59:27,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 12:59:27,257 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 12:59:27,258 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 12:59:27,260 - werkzeug - INFO - ************** - - [28/Jul/2025 12:59:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 13:00:27,226 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 13:00:27,228 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 13:00:27,236 - map_api - INFO - 找到 3 个目录
2025-07-28 13:00:27,238 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 13:00:27,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 13:00:27,246 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 13:00:27,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 13:00:27,254 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 13:00:27,260 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 13:00:27,261 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 13:00:27,262 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 13:00:27,264 - werkzeug - INFO - ************** - - [28/Jul/2025 13:00:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 13:01:27,212 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 13:01:27,215 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 13:01:27,223 - map_api - INFO - 找到 3 个目录
2025-07-28 13:01:27,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 13:01:27,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 13:01:27,231 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 13:01:27,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 13:01:27,238 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 13:01:27,244 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 13:01:27,245 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 13:01:27,246 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 13:01:27,248 - werkzeug - INFO - ************** - - [28/Jul/2025 13:01:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 13:02:27,208 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 13:02:27,209 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 13:02:27,218 - map_api - INFO - 找到 3 个目录
2025-07-28 13:02:27,219 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 13:02:27,224 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 13:02:27,225 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 13:02:27,231 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 13:02:27,232 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 13:02:27,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 13:02:27,238 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 13:02:27,239 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 13:02:27,240 - werkzeug - INFO - ************** - - [28/Jul/2025 13:02:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 13:03:27,229 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 13:03:27,231 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 13:03:27,242 - map_api - INFO - 找到 3 个目录
2025-07-28 13:03:27,245 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 13:03:27,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 13:03:27,253 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 13:03:27,258 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 13:03:27,261 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 13:03:27,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 13:03:27,269 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 13:03:27,270 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 13:03:27,272 - werkzeug - INFO - ************** - - [28/Jul/2025 13:03:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 13:04:27,210 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 13:04:27,212 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 13:04:27,221 - map_api - INFO - 找到 3 个目录
2025-07-28 13:04:27,223 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 13:04:27,229 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 13:04:27,230 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 13:04:27,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 13:04:27,238 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 13:04:27,244 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 13:04:27,247 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 13:04:27,248 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 13:04:27,250 - werkzeug - INFO - ************** - - [28/Jul/2025 13:04:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 13:05:27,211 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 13:05:27,213 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 13:05:27,223 - map_api - INFO - 找到 3 个目录
2025-07-28 13:05:27,225 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 13:05:27,231 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 13:05:27,232 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 13:05:27,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 13:05:27,239 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 13:05:27,245 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 13:05:27,246 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 13:05:27,247 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 13:05:27,249 - werkzeug - INFO - ************** - - [28/Jul/2025 13:05:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 13:06:27,229 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 13:06:27,231 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 13:06:27,241 - map_api - INFO - 找到 3 个目录
2025-07-28 13:06:27,243 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 13:06:27,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 13:06:27,251 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 13:06:27,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 13:06:27,258 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 13:06:27,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 13:06:27,266 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 13:06:27,268 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 13:06:27,269 - werkzeug - INFO - ************** - - [28/Jul/2025 13:06:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 13:07:27,217 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 13:07:27,219 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 13:07:27,228 - map_api - INFO - 找到 3 个目录
2025-07-28 13:07:27,229 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 13:07:27,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 13:07:27,235 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 13:07:27,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 13:07:27,242 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 13:07:27,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 13:07:27,250 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 13:07:27,251 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 13:07:27,253 - werkzeug - INFO - ************** - - [28/Jul/2025 13:07:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 13:08:27,233 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 13:08:27,235 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 13:08:27,247 - map_api - INFO - 找到 3 个目录
2025-07-28 13:08:27,250 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 13:08:27,257 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 13:08:27,259 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 13:08:27,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 13:08:27,266 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 13:08:27,272 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 13:08:27,274 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 13:08:27,274 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 13:08:27,276 - werkzeug - INFO - ************** - - [28/Jul/2025 13:08:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 13:09:27,217 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 13:09:27,219 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 13:09:27,229 - map_api - INFO - 找到 3 个目录
2025-07-28 13:09:27,230 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 13:09:27,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 13:09:27,237 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 13:09:27,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 13:09:27,246 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 13:09:27,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 13:09:27,254 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 13:09:27,255 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 13:09:27,256 - werkzeug - INFO - ************** - - [28/Jul/2025 13:09:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 13:10:27,233 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 13:10:27,235 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 13:10:27,244 - map_api - INFO - 找到 3 个目录
2025-07-28 13:10:27,245 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 13:10:27,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 13:10:27,254 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 13:10:27,260 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 13:10:27,261 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 13:10:27,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 13:10:27,268 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 13:10:27,269 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 13:10:27,271 - werkzeug - INFO - ************** - - [28/Jul/2025 13:10:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 14:52:13,398 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 14:52:14,354 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 14:52:14,774 - map_api - INFO - 找到 3 个目录
2025-07-28 14:52:14,780 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 14:52:14,790 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 14:52:14,795 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 14:52:14,803 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 14:52:14,831 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 14:52:14,842 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 14:52:14,864 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 14:52:14,868 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 14:52:14,871 - werkzeug - INFO - ************** - - [28/Jul/2025 14:52:14] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 14:52:14,890 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 14:52:14,894 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 14:52:14,916 - map_api - INFO - 找到 3 个目录
2025-07-28 14:52:14,921 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 14:52:14,978 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 14:52:14,992 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 14:52:15,016 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 14:52:15,029 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 14:52:15,050 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 14:52:15,051 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 14:52:15,057 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 14:52:15,070 - werkzeug - INFO - ************** - - [28/Jul/2025 14:52:15] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 14:52:15,085 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 14:52:15,093 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 14:52:15,108 - map_api - INFO - 找到 3 个目录
2025-07-28 14:52:15,110 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 14:52:15,117 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 14:52:15,120 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 14:52:15,128 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 14:52:15,132 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 14:52:15,140 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 14:52:15,143 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 14:52:15,147 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 14:52:15,153 - werkzeug - INFO - ************** - - [28/Jul/2025 14:52:15] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 14:52:57,942 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 14:52:57,943 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 14:52:57,952 - map_api - INFO - 找到 3 个目录
2025-07-28 14:52:57,954 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 14:52:57,972 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 14:52:57,973 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 14:52:57,977 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 14:52:57,980 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 14:52:57,984 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 14:52:57,985 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 14:52:57,986 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 14:52:57,988 - werkzeug - INFO - ************** - - [28/Jul/2025 14:52:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 14:53:57,937 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 14:53:57,939 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 14:53:57,947 - map_api - INFO - 找到 3 个目录
2025-07-28 14:53:57,948 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 14:53:57,955 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 14:53:57,956 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 14:53:57,963 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 14:53:57,965 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 14:53:57,972 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 14:53:57,973 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 14:53:57,974 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 14:53:57,976 - werkzeug - INFO - ************** - - [28/Jul/2025 14:53:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 14:54:57,943 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 14:54:57,945 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 14:54:57,974 - map_api - INFO - 找到 3 个目录
2025-07-28 14:54:57,976 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 14:54:57,981 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 14:54:57,982 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 14:54:57,985 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 14:54:57,987 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 14:54:57,992 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 14:54:57,995 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 14:54:57,996 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 14:54:57,997 - werkzeug - INFO - ************** - - [28/Jul/2025 14:54:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 14:55:57,946 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 14:55:57,948 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 14:55:57,976 - map_api - INFO - 找到 3 个目录
2025-07-28 14:55:57,977 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 14:55:57,983 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 14:55:57,984 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 14:55:58,004 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 14:55:58,005 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 14:55:58,008 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 14:55:58,009 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 14:55:58,010 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 14:55:58,011 - werkzeug - INFO - ************** - - [28/Jul/2025 14:55:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 14:56:57,936 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 14:56:57,938 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 14:56:57,946 - map_api - INFO - 找到 3 个目录
2025-07-28 14:56:57,948 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 14:56:57,954 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 14:56:57,956 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 14:56:57,981 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 14:56:57,981 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 14:56:57,985 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 14:56:57,985 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 14:56:57,986 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 14:56:57,988 - werkzeug - INFO - ************** - - [28/Jul/2025 14:56:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 14:57:57,938 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 14:57:57,939 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 14:57:57,947 - map_api - INFO - 找到 3 个目录
2025-07-28 14:57:57,949 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 14:57:57,957 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 14:57:57,958 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 14:57:57,963 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 14:57:57,967 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 14:57:57,971 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 14:57:57,972 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 14:57:57,973 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 14:57:57,974 - werkzeug - INFO - ************** - - [28/Jul/2025 14:57:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 14:58:57,933 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 14:58:57,934 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 14:58:57,942 - map_api - INFO - 找到 3 个目录
2025-07-28 14:58:57,944 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 14:58:57,951 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 14:58:57,952 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 14:58:57,958 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 14:58:57,961 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 14:58:57,979 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 14:58:57,982 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 14:58:57,983 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 14:58:57,984 - werkzeug - INFO - ************** - - [28/Jul/2025 14:58:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 14:59:58,202 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 14:59:58,203 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 14:59:58,210 - map_api - INFO - 找到 3 个目录
2025-07-28 14:59:58,211 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 14:59:58,216 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 14:59:58,218 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 14:59:58,222 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 14:59:58,223 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 14:59:58,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 14:59:58,244 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 14:59:58,246 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 14:59:58,248 - werkzeug - INFO - ************** - - [28/Jul/2025 14:59:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:01:27,249 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:01:27,250 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:01:27,261 - map_api - INFO - 找到 3 个目录
2025-07-28 15:01:27,262 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:01:27,269 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:01:27,270 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:01:27,276 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:01:27,277 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:01:27,297 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:01:27,299 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:01:27,300 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:01:27,302 - werkzeug - INFO - ************** - - [28/Jul/2025 15:01:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:02:27,222 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:02:27,232 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:02:27,271 - map_api - INFO - 找到 3 个目录
2025-07-28 15:02:27,275 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:02:27,298 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:02:27,299 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:02:27,306 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:02:27,310 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:02:27,316 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:02:27,320 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:02:27,322 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:02:27,325 - werkzeug - INFO - ************** - - [28/Jul/2025 15:02:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:02:50,287 - map_api - INFO - 请求获取日志: 类型=geolog, ID=73df475a-9777-404f-be70-a349026590b7
2025-07-28 15:02:50,288 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\73df475a-9777-404f-be70-a349026590b7.log
2025-07-28 15:02:50,291 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\73df475a-9777-404f-be70-a349026590b7.log, 内容大小: 829 字节
2025-07-28 15:02:50,295 - werkzeug - INFO - ************** - - [28/Jul/2025 15:02:50] "GET /api/map/logs?log_type=geolog&log_id=73df475a-9777-404f-be70-a349026590b7 HTTP/1.1" 200 -
2025-07-28 15:02:57,930 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:02:57,934 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:02:57,944 - map_api - INFO - 找到 3 个目录
2025-07-28 15:02:57,947 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:02:57,952 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:02:57,953 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:02:57,959 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:02:57,961 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:02:57,967 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:02:57,968 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:02:57,969 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:02:57,971 - werkzeug - INFO - ************** - - [28/Jul/2025 15:02:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:03:15,130 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:03:15,132 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:03:15,141 - map_api - INFO - 找到 3 个目录
2025-07-28 15:03:15,142 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:03:15,148 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:03:15,149 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:03:15,153 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:03:15,155 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:03:15,186 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:03:15,187 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:03:15,188 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:03:15,189 - werkzeug - INFO - ************** - - [28/Jul/2025 15:03:15] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:03:29,907 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:03:29,909 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:03:29,918 - map_api - INFO - 找到 3 个目录
2025-07-28 15:03:29,920 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:03:29,950 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:03:29,951 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:03:29,982 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:03:29,984 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:03:30,013 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:03:30,014 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:03:30,015 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:03:30,017 - werkzeug - INFO - ************** - - [28/Jul/2025 15:03:30] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:03:31,715 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:03:31,716 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:03:31,728 - map_api - INFO - 找到 3 个目录
2025-07-28 15:03:31,729 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:03:31,737 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:03:31,738 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:03:31,745 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:03:31,748 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:03:31,753 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:03:31,755 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:03:31,756 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:03:31,758 - werkzeug - INFO - ************** - - [28/Jul/2025 15:03:31] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:03:58,205 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:03:58,206 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:03:58,232 - map_api - INFO - 找到 3 个目录
2025-07-28 15:03:58,233 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:03:58,260 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:03:58,263 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:03:58,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:03:58,268 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:03:58,291 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:03:58,292 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:03:58,293 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:03:58,294 - werkzeug - INFO - ************** - - [28/Jul/2025 15:03:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:05:27,218 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:05:27,220 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:05:27,230 - map_api - INFO - 找到 3 个目录
2025-07-28 15:05:27,231 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:05:27,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:05:27,238 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:05:27,244 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:05:27,247 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:05:27,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:05:27,255 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:05:27,258 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:05:27,259 - werkzeug - INFO - ************** - - [28/Jul/2025 15:05:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:06:27,220 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:06:27,221 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:06:27,231 - map_api - INFO - 找到 3 个目录
2025-07-28 15:06:27,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:06:27,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:06:27,239 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:06:27,244 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:06:27,247 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:06:27,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:06:27,254 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:06:27,255 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:06:27,257 - werkzeug - INFO - ************** - - [28/Jul/2025 15:06:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:07:23,300 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:07:23,309 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:07:23,323 - map_api - INFO - 找到 3 个目录
2025-07-28 15:07:23,325 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:07:23,332 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:07:23,334 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:07:23,353 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:07:23,372 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:07:23,378 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:07:23,385 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:07:23,387 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:07:23,390 - werkzeug - INFO - ************** - - [28/Jul/2025 15:07:23] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:08:24,196 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:08:24,199 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:08:24,204 - map_api - INFO - 找到 3 个目录
2025-07-28 15:08:24,205 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:08:24,222 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:08:24,224 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:08:24,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:08:24,231 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:08:24,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:08:24,255 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:08:24,256 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:08:24,257 - werkzeug - INFO - ************** - - [28/Jul/2025 15:08:24] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:09:16,468 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:09:16,469 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:09:16,476 - map_api - INFO - 找到 3 个目录
2025-07-28 15:09:16,477 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:09:16,481 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:09:16,483 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:09:16,499 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:09:16,500 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:09:16,504 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:09:16,505 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:09:16,506 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:09:16,507 - werkzeug - INFO - ************** - - [28/Jul/2025 15:09:16] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:10:17,199 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:10:17,200 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:10:17,230 - map_api - INFO - 找到 3 个目录
2025-07-28 15:10:17,231 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:10:17,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:10:17,236 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:10:17,239 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:10:17,242 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:10:17,248 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:10:17,248 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:10:17,249 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:10:17,250 - werkzeug - INFO - ************** - - [28/Jul/2025 15:10:17] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:10:41,021 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:10:41,026 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:10:41,035 - map_api - INFO - 找到 3 个目录
2025-07-28 15:10:41,036 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:10:41,042 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:10:41,043 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:10:41,048 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:10:41,050 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:10:41,054 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:10:41,057 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:10:41,058 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:10:41,060 - werkzeug - INFO - ************** - - [28/Jul/2025 15:10:41] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:11:00,643 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:11:00,645 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:11:00,656 - map_api - INFO - 找到 3 个目录
2025-07-28 15:11:00,657 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:11:00,663 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:11:00,665 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:11:00,673 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:11:00,675 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:11:00,683 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:11:00,684 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:11:00,685 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:11:00,687 - werkzeug - INFO - ************** - - [28/Jul/2025 15:11:00] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:11:58,342 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:11:58,344 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:11:58,351 - map_api - INFO - 找到 3 个目录
2025-07-28 15:11:58,352 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:11:58,356 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:11:58,357 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:11:58,361 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:11:58,362 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:11:58,366 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:11:58,367 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:11:58,367 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:11:58,368 - werkzeug - INFO - ************** - - [28/Jul/2025 15:11:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:12:58,853 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:12:58,855 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:12:58,867 - map_api - INFO - 找到 3 个目录
2025-07-28 15:12:58,872 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:12:58,878 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:12:58,885 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:12:58,921 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:12:58,922 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:12:58,931 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:12:58,932 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:12:58,934 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:12:58,935 - werkzeug - INFO - ************** - - [28/Jul/2025 15:12:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:13:00,731 - map_api - INFO - 请求重命名任务信息文件: 20250705171602
2025-07-28 15:13:00,734 - map_api - INFO - 开始重命名任务信息文件: 20250705171602
2025-07-28 15:13:00,748 - map_api - INFO - 成功读取ODM配置: {'PATHS': {'airflow_data_path': '/opt/airflow/data', 'window_data_path': 'D:/Drone_Project/nginxData'}, 'API': {'geoserver_host': 'host.docker.internal', 'geoserver_hd_port': '5083'}, 'GEOSERVER': {'odm_workspace': 'testodm'}, 'TASK': {'id': 'NONE', 'time': 'NONE'}}
2025-07-28 15:13:00,752 - map_api - INFO - 任务信息文件路径: D:/Drone_Project/nginxData\ODM\Input\20250705171602\TaskInfo.json
2025-07-28 15:13:00,755 - map_api - INFO - 成功将任务信息文件重命名为: D:/Drone_Project/nginxData\ODM\Input\20250705171602\RemoveTask20250728151300.json
2025-07-28 15:13:00,757 - werkzeug - INFO - ************** - - [28/Jul/2025 15:13:00] "GET /api/map/odm/task/rename-info?task_id=20250705171602 HTTP/1.1" 200 -
2025-07-28 15:13:00,769 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:13:00,770 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:13:00,786 - map_api - INFO - 找到 3 个目录
2025-07-28 15:13:00,788 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:13:00,811 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:13:00,813 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:13:00,820 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:13:00,821 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:13:00,827 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:13:00,829 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:13:00,830 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:13:00,831 - werkzeug - INFO - ************** - - [28/Jul/2025 15:13:00] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:13:03,653 - batch_executor - INFO - 启动任务 e828cd9f-7f0f-4e8e-9a49-f7a1cff7d3b5: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
2025-07-28 15:13:03,655 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:13:03] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-28 15:13:03,678 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:13:03] "GET /api/batch/status?task_id=e828cd9f-7f0f-4e8e-9a49-f7a1cff7d3b5 HTTP/1.1" 200 -
2025-07-28 15:13:06,156 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:13:06,158 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:13:06,168 - map_api - INFO - 找到 3 个目录
2025-07-28 15:13:06,183 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:13:06,190 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:13:06,194 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:13:06,201 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:13:06,203 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:13:06,211 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 15:13:06,215 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:13:06,221 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:13:06,223 - werkzeug - INFO - ************** - - [28/Jul/2025 15:13:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:14:05,527 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:14:05] "GET /api/batch/status?task_id=e828cd9f-7f0f-4e8e-9a49-f7a1cff7d3b5 HTTP/1.1" 200 -
2025-07-28 15:14:06,203 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:14:06,203 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:14:06,212 - map_api - INFO - 找到 3 个目录
2025-07-28 15:14:06,213 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:14:06,219 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:14:06,220 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:14:06,225 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:14:06,227 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:14:06,232 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 15:14:06,233 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:14:06,234 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:14:06,235 - werkzeug - INFO - ************** - - [28/Jul/2025 15:14:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:14:14,938 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-28 15:14:14,939 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 15:14:14,940 - tif_api - INFO - 输入文件大小: 237.57 MB
2025-07-28 15:14:14,941 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 15:14:14,942 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 15:14:14,943 - tif_api - INFO - 黑色阈值: 0
2025-07-28 15:14:14,944 - tif_api - INFO - 白色阈值: 255
2025-07-28 15:14:14,946 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-28 15:14:14,950 - tif_executor - INFO - 启动TIF处理任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 15:14:14,951 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - ============ TIF处理任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 开始执行 ============
2025-07-28 15:14:14,952 - tif_api - INFO - 异步任务启动成功，任务ID: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:14:14,953 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 开始时间: 2025-07-28 15:14:14
2025-07-28 15:14:14,954 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:14:14] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-28 15:14:14,955 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 15:14:14,957 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 系统信息:
2025-07-28 15:14:14,958 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 15:14:14,959 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO -   Python版本: 3.8.20
2025-07-28 15:14:14,960 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO -   GDAL版本: 3.9.2
2025-07-28 15:14:14,962 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO -   GPU可用: 否
2025-07-28 15:14:14,964 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 检查参数有效性...
2025-07-28 15:14:14,965 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 15:14:14,966 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 开始执行TIF处理流程...
2025-07-28 15:14:14,966 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 15:14:14,967 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 15:14:14,967 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 15:14:14,976 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:14:14,977 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:14:14,981 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:14:14,984 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:14:14] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:14:19,030 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (15:14:18)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 15:14:19,035 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 15:14:19,351 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:06,  3.18it/s]
2025-07-28 15:14:19,669 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 14.30it/s]
2025-07-28 15:14:19,931 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 19.40it/s]
2025-07-28 15:14:19,979 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 22.27it/s]
2025-07-28 15:14:19,980 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 15:14:19,981 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 15:14:20,014 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 656.24it/s]
2025-07-28 15:14:21,600 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.06 秒 (15:14:21)预计总处理时间: 约 6.19 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (15:14:21)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 15:14:21,605 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 15:14:27,061 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 15:14:27,062 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 15:14:27,064 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:14:27,178 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:01, 17.71it/s]
2025-07-28 15:14:27,179 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:14:27,293 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:00, 27.81it/s]
2025-07-28 15:14:27,293 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:14:27,394 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 32.64it/s]
2025-07-28 15:14:27,395 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:14:27,507 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:00<00:00, 37.41it/s]
2025-07-28 15:14:27,508 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:14:27,631 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:00<00:00, 38.60it/s]
2025-07-28 15:14:27,632 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:14:27,661 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:15:06,304 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:15:06,305 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:15:06,343 - map_api - INFO - 找到 3 个目录
2025-07-28 15:15:06,346 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:15:07,338 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:15:07,339 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:15:07,375 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:15:07,376 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:15:07,397 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:15:07,398 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:15:07,401 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:15:07,403 - werkzeug - INFO - ************** - - [28/Jul/2025 15:15:07] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:15:16,835 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:15:16,835 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:15:16,838 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:15:16,840 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:15:16] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:15:17,285 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:55<01:51, 55.68s/it]
2025-07-28 15:15:22,088 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 15:15:22,090 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 15:15:22,091 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:15:25,587 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:03<01:09,  3.50s/it]
2025-07-28 15:15:25,590 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:15:30,488 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:08<01:22,  4.32s/it]
2025-07-28 15:15:30,489 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:15:35,926 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:13<01:26,  4.83s/it]
2025-07-28 15:15:35,927 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:15:39,519 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:17<01:13,  4.34s/it]
2025-07-28 15:15:39,519 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:15:44,393 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:22<01:12,  4.53s/it]
2025-07-28 15:15:44,394 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:15:49,073 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:26<01:08,  4.58s/it]
2025-07-28 15:15:49,075 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:15:56,944 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:34<01:19,  5.66s/it]
2025-07-28 15:15:56,944 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:16:00,669 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:38<01:05,  5.04s/it]
2025-07-28 15:16:00,670 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:16:04,409 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:42<00:55,  4.64s/it]
2025-07-28 15:16:04,410 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:16:06,207 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:16:06,208 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:16:06,217 - map_api - INFO - 找到 3 个目录
2025-07-28 15:16:06,218 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:16:06,235 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:16:06,236 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:16:06,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:16:06,242 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:16:06,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:16:06,248 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:16:06,249 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:16:06,250 - werkzeug - INFO - ************** - - [28/Jul/2025 15:16:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:16:08,783 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:46<00:50,  4.55s/it]
2025-07-28 15:16:08,783 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:16:08,938 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:46<00:22,  2.46s/it]
2025-07-28 15:16:08,938 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:16:09,100 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:47<00:10,  1.50s/it]
2025-07-28 15:16:09,101 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:16:18,646 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:16:18,648 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:16:19,727 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:16:19,732 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:16:19] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:17:03,170 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [01:41<00:54, 10.80s/it]
2025-07-28 15:17:03,171 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:17:06,154 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:17:06,155 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:17:06,840 - map_api - INFO - 找到 3 个目录
2025-07-28 15:17:08,490 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:17:08,518 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:17:08,520 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:17:08,774 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:17:08,776 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:17:08,783 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:17:08,785 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:17:08,786 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:17:08,788 - werkzeug - INFO - ************** - - [28/Jul/2025 15:17:08] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:17:21,499 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:17:21,501 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:17:21,548 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:17:21,550 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:17:21] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:18:06,201 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:18:06,202 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:18:06,211 - map_api - INFO - 找到 3 个目录
2025-07-28 15:18:06,212 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:18:06,218 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:18:06,219 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:18:06,222 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:18:06,223 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:18:06,228 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:18:06,231 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:18:06,233 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:18:06,234 - werkzeug - INFO - ************** - - [28/Jul/2025 15:18:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:18:23,470 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:18:23,471 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:18:24,943 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:18:24,946 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:18:24] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:18:24,968 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [03:02<01:41, 25.46s/it]
2025-07-28 15:18:24,969 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:19:06,196 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:19:06,197 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:19:06,206 - map_api - INFO - 找到 3 个目录
2025-07-28 15:19:06,207 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:19:06,213 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:19:06,214 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:19:06,220 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:19:06,221 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:19:06,227 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:19:06,229 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:19:06,229 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:19:06,231 - werkzeug - INFO - ************** - - [28/Jul/2025 15:19:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:19:27,466 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:19:27,468 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:19:28,628 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:19:30,170 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:19:30] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:20:05,040 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [04:42<02:07, 42.45s/it]
2025-07-28 15:20:05,041 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:20:27,262 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:20:27,264 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:20:27,274 - map_api - INFO - 找到 3 个目录
2025-07-28 15:20:27,277 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:20:27,282 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:20:27,284 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:20:27,314 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:20:27,316 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:20:27,324 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:20:27,326 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:20:27,328 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:20:27,331 - werkzeug - INFO - ************** - - [28/Jul/2025 15:20:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:20:32,379 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:20:32,380 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:20:34,031 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:20:34,036 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:20:34] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:21:16,590 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [05:54<01:39, 49.60s/it]
2025-07-28 15:21:16,591 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:21:27,246 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:21:27,249 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:21:27,259 - map_api - INFO - 找到 3 个目录
2025-07-28 15:21:27,263 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:21:27,269 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:21:27,271 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:21:27,276 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:21:27,280 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:21:27,288 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:21:27,290 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:21:27,291 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:21:27,297 - werkzeug - INFO - ************** - - [28/Jul/2025 15:21:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:21:36,250 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:21:36,252 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:21:37,287 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:21:37,290 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:21:37] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:21:57,902 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [06:35<00:47, 47.45s/it]
2025-07-28 15:21:57,902 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:21:59,048 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [06:36<00:00, 34.92s/it]
2025-07-28 15:21:59,049 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:21:59,050 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:22:27,232 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:22:27,233 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:22:27,245 - map_api - INFO - 找到 3 个目录
2025-07-28 15:22:27,247 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:22:27,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:22:27,256 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:22:27,263 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:22:27,265 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:22:27,271 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:22:27,275 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:22:27,276 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:22:27,277 - werkzeug - INFO - ************** - - [28/Jul/2025 15:22:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:22:39,806 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:22:39,807 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:22:41,632 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:22:41,634 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:22:41] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:23:27,226 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:23:27,230 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:23:27,240 - map_api - INFO - 找到 3 个目录
2025-07-28 15:23:27,242 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:23:27,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:23:27,249 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:23:27,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:23:27,256 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:23:27,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:23:27,266 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:23:27,267 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:23:27,269 - werkzeug - INFO - ************** - - [28/Jul/2025 15:23:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:23:43,548 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:23:43,549 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:23:43,973 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:23:43,976 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:23:43] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:24:27,219 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:24:27,222 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:24:27,232 - map_api - INFO - 找到 3 个目录
2025-07-28 15:24:27,233 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:24:27,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:24:27,239 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:24:27,245 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:24:27,247 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:24:27,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:24:27,256 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:24:27,258 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:24:27,260 - werkzeug - INFO - ************** - - [28/Jul/2025 15:24:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:24:44,936 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [10:23<05:56, 356.84s/it]
2025-07-28 15:24:46,349 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:24:46,351 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:24:46,364 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:24:46,366 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:24:46] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:24:50,350 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 15:24:50,355 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 15:24:50,356 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:24:52,424 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:02<00:41,  2.07s/it]
2025-07-28 15:24:52,424 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:24:54,572 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:04<00:40,  2.11s/it]
2025-07-28 15:24:54,572 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:24:55,284 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:04<00:26,  1.47s/it]
2025-07-28 15:24:55,284 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:24:55,617 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:05<00:17,  1.02s/it]
2025-07-28 15:24:55,618 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:24:56,059 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:05<00:13,  1.23it/s]
2025-07-28 15:24:56,060 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:24:56,171 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:05<00:03,  3.51it/s]
2025-07-28 15:24:56,172 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:24:56,302 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:05<00:01,  6.20it/s]
2025-07-28 15:24:56,302 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:24:56,410 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
波段 3/3 写入进度:  76%|################################################               | 16/21 [00:06<00:00,  8.46it/s]
2025-07-28 15:24:56,411 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:24:56,489 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - [A
2025-07-28 15:25:27,215 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:25:27,217 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:25:27,226 - map_api - INFO - 找到 3 个目录
2025-07-28 15:25:27,228 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:25:27,320 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:25:27,324 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:25:27,347 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:25:27,349 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:25:27,355 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:25:27,356 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:25:27,357 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:25:27,359 - werkzeug - INFO - ************** - - [28/Jul/2025 15:25:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:25:48,299 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:25:48,300 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:25:48,303 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:25:48,304 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:25:48] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:26:27,260 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:26:27,266 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:26:27,276 - map_api - INFO - 找到 3 个目录
2025-07-28 15:26:27,278 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:26:27,286 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:26:27,287 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:26:27,293 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:26:27,295 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:26:27,301 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:26:27,302 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:26:27,305 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:26:27,307 - werkzeug - INFO - ************** - - [28/Jul/2025 15:26:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:26:34,743 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:26:34,745 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:26:34,755 - map_api - INFO - 找到 3 个目录
2025-07-28 15:26:34,757 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:26:34,765 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:26:34,766 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:26:34,772 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:26:34,776 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:26:34,782 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:26:34,786 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:26:34,790 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:26:34,794 - werkzeug - INFO - ************** - - [28/Jul/2025 15:26:34] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:26:42,263 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [12:20<00:00, 247.47s/it]
2025-07-28 15:26:42,264 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [12:20<00:00, 246.89s/it]
2025-07-28 15:26:50,423 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:26:50,424 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:26:50,480 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:26:50,483 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:26:50] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:26:55,842 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:26:55,852 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:26:56,400 - map_api - INFO - 找到 3 个目录
2025-07-28 15:26:56,401 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:26:56,591 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:26:56,594 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:26:57,189 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:26:57,192 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:26:57,196 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:26:57,198 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:26:57,198 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:26:57,200 - werkzeug - INFO - ************** - - [28/Jul/2025 15:26:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:27:36,138 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 741.63 秒 (15:26:42)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (15:26:42)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-28 15:27:36,158 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-28 15:27:37,871 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:01<00:00,  1.71s/it]
2025-07-28 15:27:37,872 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:01<00:00,  1.71s/it]
2025-07-28 15:27:38,231 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-28 15:27:52,382 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-28 15:27:52,383 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-28 15:27:52,523 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:27:52,526 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:27:52,554 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:02, 11.79it/s]
2025-07-28 15:27:52,759 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理轮廓:  13%|#########4                                                               | 4/31 [00:00<00:02, 10.48it/s]
2025-07-28 15:27:52,794 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:00<00:00, 75.60it/s]
2025-07-28 15:27:52,807 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 正在运行
2025-07-28 15:27:52,810 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:27:52] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:27:53,469 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-28 15:27:53,470 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-28 15:27:53,482 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - ERROR - 
写入多边形: 100%|####################################################################| 35/35 [00:00<00:00, 3182.12it/s]
2025-07-28 15:27:53,643 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 处理完成，耗时: 818.67 秒 (13.64 分钟)
2025-07-28 15:27:53,644 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 处理结果: 成功
2025-07-28 15:27:53,647 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-28 15:27:53,648 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-28 15:27:53,651 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - TIF处理任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 执行成功
2025-07-28 15:27:53,652 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 完成时间: 2025-07-28 15:27:53
2025-07-28 15:27:53,652 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - 状态: 运行成功
2025-07-28 15:27:53,652 - tif_task_e7d4cb74-1671-4847-95f2-47f09a4a87b4 - INFO - ============ 任务执行结束 ============
2025-07-28 15:27:55,640 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:27:55,641 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:27:55,669 - map_api - INFO - 找到 3 个目录
2025-07-28 15:27:55,672 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:27:55,790 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:27:55,792 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:27:55,894 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:27:55,896 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:27:55,900 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 15:27:55,901 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:27:55,904 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:27:55,906 - werkzeug - INFO - ************** - - [28/Jul/2025 15:27:55] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:28:00,786 - map_api - INFO - 请求重命名任务信息文件: 20250705171602
2025-07-28 15:28:00,787 - map_api - INFO - 开始重命名任务信息文件: 20250705171602
2025-07-28 15:28:00,797 - map_api - INFO - 成功读取ODM配置: {'PATHS': {'airflow_data_path': '/opt/airflow/data', 'window_data_path': 'D:/Drone_Project/nginxData'}, 'API': {'geoserver_host': 'host.docker.internal', 'geoserver_hd_port': '5083'}, 'GEOSERVER': {'odm_workspace': 'testodm'}, 'TASK': {'id': '20250705171602', 'time': '2025-07-28 07:13:02'}}
2025-07-28 15:28:00,801 - map_api - INFO - 任务信息文件路径: D:/Drone_Project/nginxData\ODM\Input\20250705171602\TaskInfo.json
2025-07-28 15:28:00,805 - map_api - INFO - 成功将任务信息文件重命名为: D:/Drone_Project/nginxData\ODM\Input\20250705171602\RemoveTask20250728152800.json
2025-07-28 15:28:00,807 - werkzeug - INFO - ************** - - [28/Jul/2025 15:28:00] "GET /api/map/odm/task/rename-info?task_id=20250705171602 HTTP/1.1" 200 -
2025-07-28 15:28:00,816 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:28:00,818 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:28:00,827 - map_api - INFO - 找到 3 个目录
2025-07-28 15:28:00,829 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:28:00,850 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:28:00,853 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:28:00,882 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:28:00,885 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:28:00,911 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:28:00,912 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:28:00,913 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:28:00,916 - werkzeug - INFO - ************** - - [28/Jul/2025 15:28:00] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:28:09,595 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:28:09,596 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:28:09,606 - map_api - INFO - 找到 3 个目录
2025-07-28 15:28:09,607 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:28:09,633 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:28:09,635 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:28:09,664 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:28:09,667 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:28:09,670 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:28:09,671 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:28:09,672 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:28:09,673 - werkzeug - INFO - ************** - - [28/Jul/2025 15:28:09] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:28:35,063 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:28:35,067 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:28:35,081 - map_api - INFO - 找到 3 个目录
2025-07-28 15:28:35,083 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:28:35,106 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:28:35,108 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:28:35,115 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:28:35,119 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:28:35,124 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:28:35,126 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:28:35,127 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:28:35,136 - werkzeug - INFO - ************** - - [28/Jul/2025 15:28:35] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:28:43,839 - map_api - INFO - 请求重命名任务信息文件: 20250705171602
2025-07-28 15:28:43,841 - map_api - INFO - 开始重命名任务信息文件: 20250705171602
2025-07-28 15:28:43,870 - map_api - INFO - 成功读取ODM配置: {'PATHS': {'airflow_data_path': '/opt/airflow/data', 'window_data_path': 'D:/Drone_Project/nginxData'}, 'API': {'geoserver_host': 'host.docker.internal', 'geoserver_hd_port': '5083'}, 'GEOSERVER': {'odm_workspace': 'testodm'}, 'TASK': {'id': '20250705171602', 'time': '2025-07-28 07:13:02'}}
2025-07-28 15:28:43,872 - map_api - INFO - 任务信息文件路径: D:/Drone_Project/nginxData\ODM\Input\20250705171602\TaskInfo.json
2025-07-28 15:28:43,873 - map_api - ERROR - 任务信息文件不存在: D:/Drone_Project/nginxData\ODM\Input\20250705171602\TaskInfo.json
2025-07-28 15:28:43,876 - werkzeug - INFO - ************** - - [28/Jul/2025 15:28:43] "[35m[1mGET /api/map/odm/task/rename-info?task_id=20250705171602 HTTP/1.1[0m" 500 -
2025-07-28 15:28:55,245 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 15:28:55,246 - tif_api - INFO - 查询任务: e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:28:55,249 - tif_api - INFO - 任务 e7d4cb74-1671-4847-95f2-47f09a4a87b4 状态查询成功，当前状态: 运行成功
2025-07-28 15:28:55,250 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:28:55] "GET /api/tif/status?task_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:28:56,828 - geo_publisher - INFO - 启动GeoTIFF发布任务 48028fd8-20e6-4e09-82f4-e2149549087c: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-28 15:28:56,829 - geo_task_48028fd8-20e6-4e09-82f4-e2149549087c - INFO - GeoServer发布任务 48028fd8-20e6-4e09-82f4-e2149549087c 开始执行
2025-07-28 15:28:56,830 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:28:56] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-28 15:28:56,831 - geo_task_48028fd8-20e6-4e09-82f4-e2149549087c - INFO - 开始时间: 2025-07-28 15:28:56
2025-07-28 15:28:56,832 - geo_task_48028fd8-20e6-4e09-82f4-e2149549087c - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-28 15:28:56,833 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-28 15:28:56,835 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-28 15:28:56,855 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:28:56] "GET /api/geo/status?task_id=48028fd8-20e6-4e09-82f4-e2149549087c HTTP/1.1" 200 -
2025-07-28 15:28:58,533 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-28 15:28:58,535 - root - INFO - 未提供存储名称，使用文件名: 20250705171602
2025-07-28 15:28:58,535 - root - INFO - 未提供图层名称，使用存储名称: 20250705171602
2025-07-28 15:28:58,635 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171602\20250705171602.tif
2025-07-28 15:29:35,205 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:29:35,207 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:29:35,216 - map_api - INFO - 找到 3 个目录
2025-07-28 15:29:35,217 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:29:35,221 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:29:35,222 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:29:35,226 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:29:35,227 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:29:35,233 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 15:29:35,234 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:29:35,234 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 15:29:35,236 - werkzeug - INFO - ************** - - [28/Jul/2025 15:29:35] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:29:36,792 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 到 'testodm:20250705171602'
2025-07-28 15:29:37,004 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171602
2025-07-28 15:29:37,005 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 成功发布为图层 'testodm:20250705171602'
2025-07-28 15:29:37,015 - geo_task_48028fd8-20e6-4e09-82f4-e2149549087c - INFO - GeoServer发布任务 48028fd8-20e6-4e09-82f4-e2149549087c 执行成功
2025-07-28 15:29:37,021 - geo_task_48028fd8-20e6-4e09-82f4-e2149549087c - INFO - 完成时间: 2025-07-28 15:29:37
2025-07-28 15:29:37,025 - geo_task_48028fd8-20e6-4e09-82f4-e2149549087c - INFO - 状态: 发布成功
2025-07-28 15:29:58,856 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 15:29:58] "GET /api/geo/status?task_id=48028fd8-20e6-4e09-82f4-e2149549087c HTTP/1.1" 200 -
2025-07-28 15:30:26,323 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:30:26,326 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:30:26,337 - map_api - INFO - 找到 3 个目录
2025-07-28 15:30:26,342 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:30:26,374 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:30:26,376 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:30:26,382 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:30:26,383 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:30:26,405 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:30:26,408 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:30:26,409 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:30:26,412 - werkzeug - INFO - ************** - - [28/Jul/2025 15:30:26] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:30:35,071 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:30:35,076 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:30:35,089 - map_api - INFO - 找到 3 个目录
2025-07-28 15:30:35,090 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:30:35,096 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:30:35,099 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:30:35,127 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:30:35,129 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:30:35,134 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:30:35,135 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:30:35,136 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:30:35,137 - werkzeug - INFO - ************** - - [28/Jul/2025 15:30:35] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:30:46,561 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:30:46,563 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:30:46,573 - map_api - INFO - 找到 3 个目录
2025-07-28 15:30:46,575 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:30:46,580 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:30:46,582 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:30:46,587 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:30:46,588 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:30:46,594 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:30:46,595 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:30:46,595 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:30:46,597 - werkzeug - INFO - ************** - - [28/Jul/2025 15:30:46] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:31:47,203 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:31:47,205 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:31:47,232 - map_api - INFO - 找到 3 个目录
2025-07-28 15:31:47,233 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:31:47,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:31:47,238 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:31:47,245 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:31:47,247 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:31:47,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:31:47,253 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:31:47,253 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:31:47,255 - werkzeug - INFO - ************** - - [28/Jul/2025 15:31:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:32:47,206 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:32:47,209 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:32:47,219 - map_api - INFO - 找到 3 个目录
2025-07-28 15:32:47,220 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:32:47,226 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:32:47,227 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:32:47,233 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:32:47,234 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:32:47,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:32:47,242 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:32:47,244 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:32:47,246 - werkzeug - INFO - ************** - - [28/Jul/2025 15:32:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:33:47,199 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:33:47,201 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:33:47,209 - map_api - INFO - 找到 3 个目录
2025-07-28 15:33:47,212 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:33:47,217 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:33:47,218 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:33:47,223 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:33:47,224 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:33:47,231 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:33:47,232 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:33:47,233 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:33:47,235 - werkzeug - INFO - ************** - - [28/Jul/2025 15:33:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:34:47,194 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:34:47,196 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:34:47,209 - map_api - INFO - 找到 3 个目录
2025-07-28 15:34:47,211 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:34:47,224 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:34:47,226 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:34:47,233 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:34:47,234 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:34:47,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:34:47,241 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:34:47,242 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:34:47,245 - werkzeug - INFO - ************** - - [28/Jul/2025 15:34:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:35:47,197 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:35:47,199 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:35:47,208 - map_api - INFO - 找到 3 个目录
2025-07-28 15:35:47,209 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:35:47,217 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:35:47,217 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:35:47,239 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:35:47,240 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:35:47,271 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:35:47,275 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:35:47,276 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:35:47,280 - werkzeug - INFO - ************** - - [28/Jul/2025 15:35:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:36:17,306 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:36:17,309 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:36:17,319 - map_api - INFO - 找到 3 个目录
2025-07-28 15:36:17,320 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:36:17,326 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:36:17,328 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:36:17,332 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:36:17,334 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:36:17,342 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:36:17,343 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:36:17,344 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:36:17,345 - werkzeug - INFO - ************** - - [28/Jul/2025 15:36:17] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:36:39,993 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:36:39,994 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:36:40,005 - map_api - INFO - 找到 3 个目录
2025-07-28 15:36:40,007 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:36:40,015 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:36:40,017 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:36:40,025 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:36:40,027 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:36:40,033 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:36:40,034 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:36:40,035 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:36:40,036 - werkzeug - INFO - ************** - - [28/Jul/2025 15:36:40] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:37:05,233 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:37:05,242 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:37:05,257 - map_api - INFO - 找到 3 个目录
2025-07-28 15:37:05,259 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:37:05,268 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:37:05,270 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:37:05,277 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:37:05,280 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:37:05,289 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:37:05,292 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:37:05,293 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:37:05,297 - werkzeug - INFO - ************** - - [28/Jul/2025 15:37:05] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:37:36,631 - map_api - INFO - 请求获取日志: 类型=geolog, ID=48028fd8-20e6-4e09-82f4-e2149549087c
2025-07-28 15:37:36,636 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\48028fd8-20e6-4e09-82f4-e2149549087c.log
2025-07-28 15:37:36,638 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\48028fd8-20e6-4e09-82f4-e2149549087c.log, 内容大小: 829 字节
2025-07-28 15:37:36,639 - werkzeug - INFO - ************** - - [28/Jul/2025 15:37:36] "GET /api/map/logs?log_type=geolog&log_id=48028fd8-20e6-4e09-82f4-e2149549087c HTTP/1.1" 200 -
2025-07-28 15:37:40,784 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=e7d4cb74-1671-4847-95f2-47f09a4a87b4
2025-07-28 15:37:40,786 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\e7d4cb74-1671-4847-95f2-47f09a4a87b4.log
2025-07-28 15:37:40,788 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\e7d4cb74-1671-4847-95f2-47f09a4a87b4.log, 内容大小: 16843 字节
2025-07-28 15:37:40,792 - werkzeug - INFO - ************** - - [28/Jul/2025 15:37:40] "GET /api/map/logs?log_type=tiflog&log_id=e7d4cb74-1671-4847-95f2-47f09a4a87b4 HTTP/1.1" 200 -
2025-07-28 15:38:06,208 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:38:06,209 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:38:06,238 - map_api - INFO - 找到 3 个目录
2025-07-28 15:38:06,240 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:38:06,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:38:06,267 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:38:06,282 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:38:06,283 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:38:06,297 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:38:06,298 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:38:06,299 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:38:06,301 - werkzeug - INFO - ************** - - [28/Jul/2025 15:38:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:38:24,140 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:38:24,143 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:38:24,154 - map_api - INFO - 找到 3 个目录
2025-07-28 15:38:24,155 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:38:24,161 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:38:24,162 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:38:24,169 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:38:24,189 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:38:24,199 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:38:24,200 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:38:24,202 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:38:24,203 - werkzeug - INFO - ************** - - [28/Jul/2025 15:38:24] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:38:27,027 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:38:27,028 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:38:27,036 - map_api - INFO - 找到 3 个目录
2025-07-28 15:38:27,037 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:38:27,042 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:38:27,043 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:38:27,047 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:38:27,048 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:38:27,053 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:38:27,054 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:38:27,055 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:38:27,057 - werkzeug - INFO - ************** - - [28/Jul/2025 15:38:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:38:30,596 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:38:30,599 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:38:30,615 - map_api - INFO - 找到 3 个目录
2025-07-28 15:38:30,617 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:38:30,627 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:38:30,630 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:38:30,637 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:38:30,639 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:38:30,653 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:38:30,657 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:38:30,661 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:38:30,666 - werkzeug - INFO - ************** - - [28/Jul/2025 15:38:30] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:38:31,363 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:38:31,365 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:38:31,373 - map_api - INFO - 找到 3 个目录
2025-07-28 15:38:31,374 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:38:31,378 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:38:31,379 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:38:31,383 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:38:31,384 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:38:31,388 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:38:31,389 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:38:31,389 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:38:31,390 - werkzeug - INFO - ************** - - [28/Jul/2025 15:38:31] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:38:38,219 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:38:38,223 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:38:38,258 - map_api - INFO - 找到 3 个目录
2025-07-28 15:38:38,260 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:38:38,268 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:38:38,270 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:38:38,278 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:38:38,280 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:38:38,288 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 15:38:38,290 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:38:38,292 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-28 15:38:38,296 - werkzeug - INFO - ************** - - [28/Jul/2025 15:38:38] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:38:59,015 - map_api - INFO - 请求重命名任务信息文件: 20250705171602
2025-07-28 15:38:59,018 - map_api - INFO - 开始重命名任务信息文件: 20250705171602
2025-07-28 15:38:59,024 - map_api - INFO - 成功读取ODM配置: {'PATHS': {'airflow_data_path': '/opt/airflow/data', 'window_data_path': 'D:/Drone_Project/nginxData'}, 'API': {'geoserver_host': 'host.docker.internal', 'geoserver_hd_port': '5083'}, 'GEOSERVER': {'odm_workspace': 'testodm'}, 'TASK': {'id': 'NONE', 'time': 'NONE'}}
2025-07-28 15:38:59,030 - map_api - INFO - 任务信息文件路径: D:/Drone_Project/nginxData\ODM\Input\20250705171602\TaskInfo.json
2025-07-28 15:38:59,032 - map_api - INFO - 成功将任务信息文件重命名为: D:/Drone_Project/nginxData\ODM\Input\20250705171602\RemoveTask20250728153859.json
2025-07-28 15:38:59,034 - werkzeug - INFO - ************** - - [28/Jul/2025 15:38:59] "GET /api/map/odm/task/rename-info?task_id=20250705171602 HTTP/1.1" 200 -
2025-07-28 15:38:59,048 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:38:59,049 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:38:59,059 - map_api - INFO - 找到 3 个目录
2025-07-28 15:38:59,060 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:38:59,065 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:38:59,066 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:38:59,070 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:38:59,072 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:38:59,091 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:38:59,092 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:38:59,093 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:38:59,094 - werkzeug - INFO - ************** - - [28/Jul/2025 15:38:59] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:39:08,218 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:39:08,220 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:39:08,230 - map_api - INFO - 找到 3 个目录
2025-07-28 15:39:08,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:39:08,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:39:08,259 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:39:08,287 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:39:08,288 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:39:08,294 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:39:08,295 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:39:08,295 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:39:08,297 - werkzeug - INFO - ************** - - [28/Jul/2025 15:39:08] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:39:09,258 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:39:09,261 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:39:09,290 - map_api - INFO - 找到 3 个目录
2025-07-28 15:39:09,291 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:39:09,295 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:39:09,296 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:39:09,317 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:39:09,320 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:39:09,331 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:39:09,334 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:39:09,338 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:39:09,340 - werkzeug - INFO - ************** - - [28/Jul/2025 15:39:09] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:39:25,001 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:39:25,003 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:39:25,034 - map_api - INFO - 找到 3 个目录
2025-07-28 15:39:25,035 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:39:25,061 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:39:25,062 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:39:25,066 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:39:25,067 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:39:25,070 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:39:25,073 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:39:25,075 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:39:25,077 - werkzeug - INFO - ************** - - [28/Jul/2025 15:39:25] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:39:39,199 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:39:39,200 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:39:39,208 - map_api - INFO - 找到 3 个目录
2025-07-28 15:39:39,210 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:39:39,215 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:39:39,216 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:39:39,220 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:39:39,221 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:39:39,224 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:39:39,225 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:39:39,226 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:39:39,228 - werkzeug - INFO - ************** - - [28/Jul/2025 15:39:39] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:40:39,199 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:40:39,201 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:40:39,230 - map_api - INFO - 找到 3 个目录
2025-07-28 15:40:39,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:40:39,235 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:40:39,236 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:40:39,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:40:39,263 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:40:39,290 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:40:39,292 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:40:39,293 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:40:39,294 - werkzeug - INFO - ************** - - [28/Jul/2025 15:40:39] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:40:47,899 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:40:47,929 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:40:47,943 - map_api - INFO - 找到 3 个目录
2025-07-28 15:40:47,945 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:40:47,955 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:40:47,963 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:40:47,968 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:40:47,969 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:40:47,974 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:40:47,975 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:40:47,976 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:40:47,977 - werkzeug - INFO - ************** - - [28/Jul/2025 15:40:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:41:39,210 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:41:39,212 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:41:39,224 - map_api - INFO - 找到 3 个目录
2025-07-28 15:41:39,225 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:41:39,233 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:41:39,233 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:41:39,238 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:41:39,240 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:41:39,244 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:41:39,245 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:41:39,246 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:41:39,249 - werkzeug - INFO - ************** - - [28/Jul/2025 15:41:39] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:42:38,221 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:42:38,222 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:42:38,231 - map_api - INFO - 找到 3 个目录
2025-07-28 15:42:38,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:42:38,238 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:42:38,240 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:42:38,245 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:42:38,246 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:42:38,250 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:42:38,251 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:42:38,252 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:42:38,254 - werkzeug - INFO - ************** - - [28/Jul/2025 15:42:38] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:43:09,281 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 15:43:09,282 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 15:43:09,285 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 15:43:09,287 - werkzeug - INFO - ************** - - [28/Jul/2025 15:43:09] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 15:43:14,034 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-28 15:43:14,035 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log
2025-07-28 15:43:14,037 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log, 内容大小: 17354 字节
2025-07-28 15:43:14,039 - werkzeug - INFO - ************** - - [28/Jul/2025 15:43:14] "GET /api/map/logs?log_type=tiflog&log_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-28 15:43:22,611 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 15:43:22,612 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 15:43:22,614 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 15:43:22,615 - werkzeug - INFO - ************** - - [28/Jul/2025 15:43:22] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 15:43:38,222 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:43:38,223 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:43:38,234 - map_api - INFO - 找到 3 个目录
2025-07-28 15:43:38,235 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:43:38,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:43:38,242 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:43:38,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:43:38,250 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:43:38,256 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:43:38,257 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:43:38,258 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:43:38,264 - werkzeug - INFO - ************** - - [28/Jul/2025 15:43:38] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:43:43,512 - map_api - INFO - 请求获取日志: 类型=geolog, ID=2f2cd194-6d18-437b-a91d-de467720b289
2025-07-28 15:43:43,514 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\2f2cd194-6d18-437b-a91d-de467720b289.log
2025-07-28 15:43:43,516 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\2f2cd194-6d18-437b-a91d-de467720b289.log, 内容大小: 829 字节
2025-07-28 15:43:43,518 - werkzeug - INFO - ************** - - [28/Jul/2025 15:43:43] "GET /api/map/logs?log_type=geolog&log_id=2f2cd194-6d18-437b-a91d-de467720b289 HTTP/1.1" 200 -
2025-07-28 15:44:29,531 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:44:29,534 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:44:29,545 - map_api - INFO - 找到 3 个目录
2025-07-28 15:44:29,547 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:44:29,553 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:44:29,555 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:44:29,563 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:44:29,565 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:44:29,573 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:44:29,579 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:44:29,584 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:44:29,586 - werkzeug - INFO - ************** - - [28/Jul/2025 15:44:29] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:44:47,756 - map_api - INFO - 请求获取日志: 类型=geolog, ID=2f2cd194-6d18-437b-a91d-de467720b289
2025-07-28 15:44:47,757 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\2f2cd194-6d18-437b-a91d-de467720b289.log
2025-07-28 15:44:47,760 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\2f2cd194-6d18-437b-a91d-de467720b289.log, 内容大小: 829 字节
2025-07-28 15:44:47,761 - werkzeug - INFO - ************** - - [28/Jul/2025 15:44:47] "GET /api/map/logs?log_type=geolog&log_id=2f2cd194-6d18-437b-a91d-de467720b289 HTTP/1.1" 200 -
2025-07-28 15:45:29,535 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:45:29,537 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:45:29,546 - map_api - INFO - 找到 3 个目录
2025-07-28 15:45:29,547 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:45:29,563 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:45:29,564 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:45:29,579 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:45:29,580 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:45:29,593 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:45:29,595 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:45:29,596 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:45:29,598 - werkzeug - INFO - ************** - - [28/Jul/2025 15:45:29] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:46:30,253 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:46:30,254 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:46:30,263 - map_api - INFO - 找到 3 个目录
2025-07-28 15:46:30,264 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:46:30,290 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:46:30,292 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:46:30,321 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:46:30,326 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:46:30,331 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:46:30,332 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:46:30,333 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:46:30,334 - werkzeug - INFO - ************** - - [28/Jul/2025 15:46:30] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:47:30,205 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:47:30,207 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:47:30,216 - map_api - INFO - 找到 3 个目录
2025-07-28 15:47:30,218 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:47:30,221 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:47:30,222 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:47:30,245 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:47:30,246 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:47:30,260 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:47:30,261 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:47:30,261 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:47:30,263 - werkzeug - INFO - ************** - - [28/Jul/2025 15:47:30] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:48:21,912 - map_api - INFO - 请求获取日志: 类型=geolog, ID=2f2cd194-6d18-437b-a91d-de467720b289
2025-07-28 15:48:21,913 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\2f2cd194-6d18-437b-a91d-de467720b289.log
2025-07-28 15:48:21,914 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\2f2cd194-6d18-437b-a91d-de467720b289.log, 内容大小: 829 字节
2025-07-28 15:48:21,917 - werkzeug - INFO - ************** - - [28/Jul/2025 15:48:21] "GET /api/map/logs?log_type=geolog&log_id=2f2cd194-6d18-437b-a91d-de467720b289 HTTP/1.1" 200 -
2025-07-28 15:48:25,385 - map_api - INFO - 请求获取日志: 类型=batlog, ID=caee250a-e3c4-4198-8f89-d60ed6ba6aaa
2025-07-28 15:48:25,389 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\caee250a-e3c4-4198-8f89-d60ed6ba6aaa.log
2025-07-28 15:48:25,391 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\caee250a-e3c4-4198-8f89-d60ed6ba6aaa.log, 内容大小: 11524 字节
2025-07-28 15:48:25,392 - werkzeug - INFO - ************** - - [28/Jul/2025 15:48:25] "GET /api/map/logs?log_type=batlog&log_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 15:48:27,475 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 15:48:27,477 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 15:48:27,479 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 15:48:27,481 - werkzeug - INFO - ************** - - [28/Jul/2025 15:48:27] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 15:48:29,541 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:48:29,543 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:48:29,553 - map_api - INFO - 找到 3 个目录
2025-07-28 15:48:29,554 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:48:29,561 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:48:29,562 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:48:29,567 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:48:29,569 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:48:29,577 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:48:29,578 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:48:29,578 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:48:29,580 - werkzeug - INFO - ************** - - [28/Jul/2025 15:48:29] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:48:52,247 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 15:48:52,248 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 15:48:52,250 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 15:48:52,252 - werkzeug - INFO - ************** - - [28/Jul/2025 15:48:52] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 15:48:54,249 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-28 15:48:54,250 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log
2025-07-28 15:48:54,252 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\4acbd746-f075-45ba-b856-04ec2ebb15e0.log, 内容大小: 17354 字节
2025-07-28 15:48:54,253 - werkzeug - INFO - ************** - - [28/Jul/2025 15:48:54] "GET /api/map/logs?log_type=tiflog&log_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-28 15:49:00,420 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1f376117-4639-4e05-8be5-839efea9682a
2025-07-28 15:49:00,421 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log
2025-07-28 15:49:00,422 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1f376117-4639-4e05-8be5-839efea9682a.log, 内容大小: 17438 字节
2025-07-28 15:49:00,423 - werkzeug - INFO - ************** - - [28/Jul/2025 15:49:00] "GET /api/map/logs?log_type=tiflog&log_id=1f376117-4639-4e05-8be5-839efea9682a HTTP/1.1" 200 -
2025-07-28 15:49:06,184 - map_api - INFO - 请求获取日志: 类型=batlog, ID=caee250a-e3c4-4198-8f89-d60ed6ba6aaa
2025-07-28 15:49:06,186 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\caee250a-e3c4-4198-8f89-d60ed6ba6aaa.log
2025-07-28 15:49:06,189 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\caee250a-e3c4-4198-8f89-d60ed6ba6aaa.log, 内容大小: 11524 字节
2025-07-28 15:49:06,191 - werkzeug - INFO - ************** - - [28/Jul/2025 15:49:06] "GET /api/map/logs?log_type=batlog&log_id=caee250a-e3c4-4198-8f89-d60ed6ba6aaa HTTP/1.1" 200 -
2025-07-28 15:49:29,534 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:49:29,535 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:49:29,548 - map_api - INFO - 找到 3 个目录
2025-07-28 15:49:29,550 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:49:29,579 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:49:29,580 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:49:29,609 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:49:29,611 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:49:29,639 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:49:29,640 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:49:29,641 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:49:29,642 - werkzeug - INFO - ************** - - [28/Jul/2025 15:49:29] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:50:29,540 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:50:29,543 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:50:29,552 - map_api - INFO - 找到 3 个目录
2025-07-28 15:50:29,555 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:50:29,560 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:50:29,562 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:50:29,568 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:50:29,569 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:50:29,575 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:50:29,577 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:50:29,578 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:50:29,579 - werkzeug - INFO - ************** - - [28/Jul/2025 15:50:29] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:51:07,752 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:51:07,755 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:51:07,768 - map_api - INFO - 找到 3 个目录
2025-07-28 15:51:07,769 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:51:07,776 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:51:07,780 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:51:07,788 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:51:07,792 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:51:07,798 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:51:07,800 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:51:07,802 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:51:07,805 - werkzeug - INFO - ************** - - [28/Jul/2025 15:51:07] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:52:08,215 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:52:08,216 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:52:08,225 - map_api - INFO - 找到 3 个目录
2025-07-28 15:52:08,229 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:52:08,235 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:52:08,236 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:52:08,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:52:08,241 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:52:08,246 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:52:08,250 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:52:08,251 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:52:08,252 - werkzeug - INFO - ************** - - [28/Jul/2025 15:52:08] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:53:08,217 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:53:08,218 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:53:08,248 - map_api - INFO - 找到 3 个目录
2025-07-28 15:53:08,249 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:53:08,277 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:53:08,279 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:53:08,292 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:53:08,295 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:53:08,299 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:53:08,300 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:53:08,301 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:53:08,302 - werkzeug - INFO - ************** - - [28/Jul/2025 15:53:08] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:54:08,217 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:54:08,219 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:54:08,239 - map_api - INFO - 找到 3 个目录
2025-07-28 15:54:08,240 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:54:08,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:54:08,247 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:54:08,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:54:08,268 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:54:08,271 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:54:08,272 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:54:08,273 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:54:08,274 - werkzeug - INFO - ************** - - [28/Jul/2025 15:54:08] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:55:08,221 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:55:08,223 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:55:08,232 - map_api - INFO - 找到 3 个目录
2025-07-28 15:55:08,233 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:55:08,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:55:08,253 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:55:08,257 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:55:08,259 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:55:08,262 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:55:08,264 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:55:08,264 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:55:08,266 - werkzeug - INFO - ************** - - [28/Jul/2025 15:55:08] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:56:08,206 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:56:08,208 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:56:08,230 - map_api - INFO - 找到 3 个目录
2025-07-28 15:56:08,231 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:56:08,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:56:08,235 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:56:08,239 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:56:08,240 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:56:08,257 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:56:08,259 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:56:08,260 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:56:08,262 - werkzeug - INFO - ************** - - [28/Jul/2025 15:56:08] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 15:57:08,227 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 15:57:08,231 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 15:57:08,244 - map_api - INFO - 找到 3 个目录
2025-07-28 15:57:08,246 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 15:57:08,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-28 15:57:08,263 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 15:57:08,276 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 15:57:08,280 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 15:57:08,285 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 15:57:08,286 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 15:57:08,288 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(2)
2025-07-28 15:57:08,294 - werkzeug - INFO - ************** - - [28/Jul/2025 15:57:08] "GET /api/map/odm/tasks HTTP/1.1" 200 -
