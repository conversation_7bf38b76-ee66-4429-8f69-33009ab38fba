2025-07-31 16:54:41,216 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250731_165441.log
2025-07-31 16:54:41,232 - geo_publisher - INFO - 加载了 32 个任务状态
2025-07-31 16:54:41,342 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-31 16:54:41,343 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-31 16:54:41,356 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-31 16:54:41,366 - root - INFO - === GeoServer REST API服务 ===
2025-07-31 16:54:41,368 - root - INFO - 主机: 0.0.0.0
2025-07-31 16:54:41,369 - root - INFO - 端口: 5083
2025-07-31 16:54:41,370 - root - INFO - 调试模式: 禁用
2025-07-31 16:54:41,371 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-31 16:54:41,392 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-31 16:54:41,393 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 16:54:44,022 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:54:44,029 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:54:44,043 - map_api - INFO - 找到 3 个目录
2025-07-31 16:54:44,047 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:54:44,054 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:54:44,059 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:54:44,088 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:54:44,093 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:54:44,100 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:54:44,103 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:54:44,107 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:54:44,111 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:54:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-31 16:55:04,031 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:55:04] "[33mGET /api/layer_intersections?workspace=testodm&layer=20250705171602 HTTP/1.1[0m" 404 -
2025-07-31 16:55:04,101 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:55:04] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-31 16:55:12,227 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:55:12] "[33mGET /api/layer_intersections?workspace=testodm&layer=20250705171602 HTTP/1.1[0m" 404 -
2025-07-31 16:55:34,862 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:34] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-31 16:55:34,884 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-31 16:55:34,906 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-31 16:55:34,907 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:34] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-31 16:55:37,179 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:37] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:55:37,248 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:55:37,273 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:55:37,274 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:37] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:55:40,570 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:55:40,589 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:55:40,590 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:40] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:55:44,006 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:55:44,010 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:55:44,040 - map_api - INFO - 找到 3 个目录
2025-07-31 16:55:44,044 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:55:44,048 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:55:44,051 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:55:44,059 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:55:44,061 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:55:44,084 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:55:44,087 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:55:44,088 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:55:44,090 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:55:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-31 16:55:44,585 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:55:44,586 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:55:44,595 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:55:44,595 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:55:44,600 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:44] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:55:44,601 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:44] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:55:44,637 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:55:44,642 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-31 16:55:44,647 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:55:44,650 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:44] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:55:44,651 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-31 16:55:44,653 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:44] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-31 16:55:44,694 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:55:44,694 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-31 16:55:44,703 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:55:44,705 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:44] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:55:44,706 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-31 16:55:44,708 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:44] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-31 16:55:45,046 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:45] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:55:45,079 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:55:45,104 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:55:45,105 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:45] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:55:46,308 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:55:46,332 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:55:46,334 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:46] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:55:50,454 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:50] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:55:50,489 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:55:50,510 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:55:50,511 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:50] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:55:52,496 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:52] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-31 16:55:52,518 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-31 16:55:52,542 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-31 16:55:52,544 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:52] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-31 16:55:54,857 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:55:54,882 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:55:54,883 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:55:54] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:55:56,701 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:55:56] "[33mGET /api/layer_intersections?workspace=testodm&layer=20250705171602 HTTP/1.1[0m" 404 -
2025-07-31 16:56:00,801 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:56:00] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-31 16:56:01,762 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-31 16:56:01,784 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-31 16:56:01,785 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:56:01] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-31 16:56:54,036 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:56:54,039 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:56:54,050 - map_api - INFO - 找到 3 个目录
2025-07-31 16:56:54,052 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:56:54,063 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:56:54,066 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:56:54,073 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:56:54,074 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:56:54,084 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:56:54,089 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:56:54,093 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:56:54,096 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:56:54] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-31 16:57:54,028 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:57:54,032 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:57:54,042 - map_api - INFO - 找到 3 个目录
2025-07-31 16:57:54,044 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:57:54,048 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:57:54,051 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:57:54,081 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:57:54,083 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:57:54,090 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:57:54,092 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:57:54,092 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:57:54,094 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:57:54] "GET /api/map/odm/tasks HTTP/1.1" 200 -
