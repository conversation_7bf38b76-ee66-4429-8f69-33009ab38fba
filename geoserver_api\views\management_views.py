#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
管理视图 - GeoServer管理相关功能
"""

import os
import logging
import glob
from django.http import JsonResponse

# 导入核心模块
from ..core.manager import GeoServerManager

# 获取日志记录器
logger = logging.getLogger('geoserver_api')

# 初始化全局实例
manager = GeoServerManager()


def get_workspaces(request):
    """获取所有工作区"""
    try:
        workspaces = manager.get_workspaces()
        return JsonResponse({
            'status': 'success',
            'count': len(workspaces),
            'workspaces': workspaces
        })
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def create_workspace(request):
    """
    创建工作区
    
    查询参数:
        name: 工作区名称
    """
    try:
        name = request.GET.get('name')
        if not name:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: name'
            }, status=400)
        
        result = manager.create_workspace(name)
        if result:
            return JsonResponse({
                'status': 'success',
                'message': f'工作区 {name} 创建成功'
            })
        else:
            return JsonResponse({
                'status': 'error',
                'message': f'工作区 {name} 创建失败'
            }, status=500)
            
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def delete_workspace(request):
    """
    删除工作区
    
    查询参数:
        name: 工作区名称
        recurse: 是否递归删除 (默认: true)
    """
    try:
        name = request.GET.get('name')
        if not name:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: name'
            }, status=400)
        
        recurse = request.GET.get('recurse', 'true').lower() == 'true'
        
        result = manager.delete_workspace(name, recurse=recurse)
        if result:
            return JsonResponse({
                'status': 'success',
                'message': f'工作区 {name} 删除成功'
            })
        else:
            return JsonResponse({
                'status': 'error',
                'message': f'工作区 {name} 删除失败'
            }, status=500)
            
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_management_layers(request):
    """
    获取图层列表
    
    查询参数:
        workspace: 工作区名称 (可选)
    """
    try:
        workspace = request.GET.get('workspace')
        layers = manager.get_layers(workspace)
        
        return JsonResponse({
            'status': 'success',
            'workspace': workspace,
            'count': len(layers),
            'layers': layers
        })
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_datastores(request):
    """
    获取指定工作区中的数据存储
    
    查询参数:
        workspace: 工作区名称
    """
    try:
        workspace = request.GET.get('workspace')
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: workspace'
            }, status=400)
            
        datastores = manager.get_datastores(workspace)
        return JsonResponse({
            'status': 'success',
            'count': len(datastores),
            'datastores': datastores
        })
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def create_datastore(request):
    """
    创建PostGIS数据存储
    
    查询参数:
        name: 数据存储名称
        workspace: 工作区名称
        host: 数据库主机地址
        port: 数据库端口
        database: 数据库名称
        username: 用户名
        password: 密码
        schema: 模式名称 (默认: public)
    """
    try:
        name = request.GET.get('name')
        workspace = request.GET.get('workspace')
        host = request.GET.get('host')
        port = request.GET.get('port')
        database = request.GET.get('database')
        username = request.GET.get('username')
        password = request.GET.get('password')
        schema = request.GET.get('schema', 'public')
        
        if not all([name, workspace, host, port, database, username, password]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: name, workspace, host, port, database, username, password'
            }, status=400)
        
        try:
            port = int(port)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '端口必须是有效的数字'
            }, status=400)
        
        result = manager.create_datastore(name, workspace, host, port, database, username, password, schema)
        
        if result:
            return JsonResponse({
                'status': 'success',
                'message': f'PostGIS数据存储 {workspace}:{name} 创建成功'
            })
        else:
            return JsonResponse({
                'status': 'error',
                'message': f'PostGIS数据存储 {workspace}:{name} 创建失败'
            }, status=500)
            
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def publish_shapefile(request):
    """
    发布Shapefile到GeoServer
    
    查询参数:
        file: Shapefile文件路径
        workspace: 工作区名称
        store: 存储名称 (可选)
        layer: 图层名称 (可选)
        charset: 字符集 (默认: UTF-8)
    """
    try:
        file_path = request.GET.get('file')
        workspace = request.GET.get('workspace')
        store = request.GET.get('store')
        layer = request.GET.get('layer')
        charset = request.GET.get('charset', 'UTF-8')
        
        if not all([file_path, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: file, workspace'
            }, status=400)
        
        if not os.path.exists(file_path):
            return JsonResponse({
                'status': 'error',
                'message': f'文件不存在: {file_path}'
            }, status=404)
        
        # 发布Shapefile
        result = manager.publish_shapefile(file_path, workspace, store, layer, charset)
        
        if result:
            return JsonResponse({
                'status': 'success',
                'message': f'Shapefile {os.path.basename(file_path)} 发布成功为 {workspace}:{layer or store or os.path.splitext(os.path.basename(file_path))[0]}'
            })
        else:
            return JsonResponse({
                'status': 'error',
                'message': f'Shapefile {file_path} 发布失败'
            }, status=500)
            
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def publish_shapefile_directory(request):
    """
    发布目录中所有Shapefile
    
    查询参数:
        directory: Shapefile目录路径
        workspace: 工作区名称
        store: 存储名称前缀 (可选)
        charset: 字符集 (默认: UTF-8)
    """
    try:
        directory = request.GET.get('directory')
        workspace = request.GET.get('workspace')
        store_prefix = request.GET.get('store', 'shapefiles')
        charset = request.GET.get('charset', 'UTF-8')
        
        if not all([directory, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: directory, workspace'
            }, status=400)
        
        if not os.path.exists(directory):
            return JsonResponse({
                'status': 'error',
                'message': f'目录不存在: {directory}'
            }, status=404)
        
        # 查找目录中的所有Shapefile
        shapefiles = glob.glob(os.path.join(directory, "*.shp"))
        
        if not shapefiles:
            return JsonResponse({
                'status': 'error',
                'message': f'目录中没有找到Shapefile: {directory}'
            }, status=404)
        
        results = []
        success_count = 0
        
        for shapefile in shapefiles:
            try:
                filename = os.path.splitext(os.path.basename(shapefile))[0]
                store_name = f"{store_prefix}_{filename}"
                
                result = manager.publish_shapefile(shapefile, workspace, store_name, filename, charset)
                
                if result:
                    success_count += 1
                    results.append({
                        'file': shapefile,
                        'status': 'success',
                        'layer': f'{workspace}:{filename}'
                    })
                else:
                    results.append({
                        'file': shapefile,
                        'status': 'failed',
                        'error': '发布失败'
                    })
                    
            except Exception as e:
                results.append({
                    'file': shapefile,
                    'status': 'failed',
                    'error': str(e)
                })
        
        return JsonResponse({
            'status': 'success',
            'message': f'处理完成，成功发布 {success_count}/{len(shapefiles)} 个Shapefile',
            'total': len(shapefiles),
            'success': success_count,
            'results': results
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def publish_geotiff(request):
    """
    发布GeoTIFF到GeoServer

    查询参数:
        file: GeoTIFF文件路径
        workspace: 工作区名称
        store: 存储名称 (可选)
        layer: 图层名称 (可选)
    """
    try:
        file_path = request.GET.get('file')
        workspace = request.GET.get('workspace')
        store = request.GET.get('store')
        layer = request.GET.get('layer')

        if not all([file_path, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: file, workspace'
            }, status=400)

        if not os.path.exists(file_path):
            return JsonResponse({
                'status': 'error',
                'message': f'文件不存在: {file_path}'
            }, status=404)

        # 发布GeoTIFF
        result = manager.publish_geotiff(file_path, workspace, store, layer)

        if result:
            return JsonResponse({
                'status': 'success',
                'message': f'GeoTIFF {os.path.basename(file_path)} 发布成功为 {workspace}:{layer or store or os.path.splitext(os.path.basename(file_path))[0]}'
            })
        else:
            return JsonResponse({
                'status': 'error',
                'message': f'GeoTIFF {file_path} 发布失败'
            }, status=500)

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def publish_geotiff_directory(request):
    """
    发布目录中所有GeoTIFF

    查询参数:
        directory: GeoTIFF目录路径
        workspace: 工作区名称
        store: 存储名称前缀 (可选)
    """
    try:
        directory = request.GET.get('directory')
        workspace = request.GET.get('workspace')
        store_prefix = request.GET.get('store', 'geotiffs')

        if not all([directory, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: directory, workspace'
            }, status=400)

        if not os.path.exists(directory):
            return JsonResponse({
                'status': 'error',
                'message': f'目录不存在: {directory}'
            }, status=404)

        # 查找目录中的所有GeoTIFF文件
        geotiffs = []
        for ext in ['*.tif', '*.tiff', '*.TIF', '*.TIFF']:
            geotiffs.extend(glob.glob(os.path.join(directory, ext)))

        if not geotiffs:
            return JsonResponse({
                'status': 'error',
                'message': f'目录中没有找到GeoTIFF文件: {directory}'
            }, status=404)

        results = []
        success_count = 0

        for geotiff in geotiffs:
            try:
                filename = os.path.splitext(os.path.basename(geotiff))[0]
                store_name = f"{store_prefix}_{filename}"

                result = manager.publish_geotiff(geotiff, workspace, store_name, filename)

                if result:
                    success_count += 1
                    results.append({
                        'file': geotiff,
                        'status': 'success',
                        'layer': f'{workspace}:{filename}'
                    })
                else:
                    results.append({
                        'file': geotiff,
                        'status': 'failed',
                        'error': '发布失败'
                    })

            except Exception as e:
                results.append({
                    'file': geotiff,
                    'status': 'failed',
                    'error': str(e)
                })

        return JsonResponse({
            'status': 'success',
            'message': f'处理完成，成功发布 {success_count}/{len(geotiffs)} 个GeoTIFF',
            'total': len(geotiffs),
            'success': success_count,
            'results': results
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def publish_postgis(request):
    """发布PostGIS表 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def delete_layer(request):
    """删除图层 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def set_layer_style(request):
    """设置图层样式 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def execute_batch_file(request):
    """执行批处理文件 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def diagnose_geoserver(request):
    """诊断GeoServer连接和图层状态"""
    try:
        # 检查GeoServer连接
        connection_ok = manager.check_connection()

        # 获取基本信息
        workspaces = manager.get_workspaces()

        diagnosis = {
            'geoserver_connection': 'ok' if connection_ok else 'failed',
            'geoserver_url': manager.url,
            'workspaces_count': len(workspaces),
            'workspaces': workspaces[:10],  # 只返回前10个工作区
        }

        return JsonResponse({
            'status': 'success',
            'diagnosis': diagnosis
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)
