#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Django API功能
"""

import requests
import json
import time

def test_api_endpoint(url, description):
    """测试API端点"""
    print(f"\n测试: {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                return True
            except json.JSONDecodeError:
                print(f"响应文本: {response.text}")
                return True
        else:
            print(f"错误响应: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    base_url = "http://127.0.0.1:5000"
    
    print("=" * 60)
    print("Django GeoServer API 测试")
    print("=" * 60)
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(2)
    
    # 测试基础端点
    test_cases = [
        (f"{base_url}/health/", "健康检查"),
        (f"{base_url}/api/management/workspaces/", "获取工作区列表"),
        (f"{base_url}/api/batch/all/", "获取所有批处理任务状态"),
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for url, description in test_cases:
        if test_api_endpoint(url, description):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试完成: {success_count}/{total_count} 个测试通过")
    print("=" * 60)
    
    if success_count == total_count:
        print("✅ 所有测试通过！Django API迁移成功！")
        return True
    else:
        print("❌ 部分测试失败，请检查配置")
        return False

if __name__ == "__main__":
    main()
