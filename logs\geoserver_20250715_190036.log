2025-07-15 19:00:37,058 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250715_190036.log
2025-07-15 19:00:37,060 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-15 19:00:37,060 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-15 19:00:37,145 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-15 19:00:37,219 - batch_executor - INFO - 加载了 35 个任务状态
2025-07-15 19:00:47,439 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-15 19:00:47,439 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-15 19:00:49,001 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-15 19:00:49,007 - root - INFO - === GeoServer REST API服务 ===
2025-07-15 19:00:49,008 - root - INFO - 主机: 0.0.0.0
2025-07-15 19:00:49,009 - root - INFO - 端口: 5083
2025-07-15 19:00:49,009 - root - INFO - 调试模式: 禁用
2025-07-15 19:00:49,010 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-15 19:00:49,720 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-15 19:00:49,721 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-15 20:01:53,032 - werkzeug - INFO - ************** - - [15/Jul/2025 20:01:53] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 20:01:53,046 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 20:02:13,380 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 20:02:13,383 - werkzeug - INFO - ************** - - [15/Jul/2025 20:02:13] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 20:02:30,535 - werkzeug - INFO - ************** - - [15/Jul/2025 20:02:30] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=id1 HTTP/1.1" 200 -
2025-07-15 20:02:30,545 - root - INFO - 获取图层 test_myworkspace:id1 的信息
2025-07-15 20:02:30,566 - root - INFO - 成功获取图层 test_myworkspace:id1 的边界框信息
2025-07-15 20:02:30,569 - werkzeug - INFO - ************** - - [15/Jul/2025 20:02:30] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id1 HTTP/1.1" 200 -
2025-07-15 20:02:35,033 - root - INFO - 获取图层 test_myworkspace:id1 的信息
2025-07-15 20:02:35,121 - root - INFO - 成功获取图层 test_myworkspace:id1 的边界框信息
2025-07-15 20:02:35,122 - werkzeug - INFO - ************** - - [15/Jul/2025 20:02:35] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id1 HTTP/1.1" 200 -
2025-07-15 20:21:09,795 - root - INFO - 开始查询坐标点 (22.893028968276994, 108.35756264284271) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 20:21:27,757 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 20:21:27,826 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:21:27,960 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:21:27,961 - root - INFO - 在坐标点 (22.893028968276994, 108.35756264284271) 处找到 2 个有有效数据的栅格图层
2025-07-15 20:21:27,962 - werkzeug - INFO - ************** - - [15/Jul/2025 20:21:27] "GET /api/query_values?lat=22.893028968276994&lon=108.35756264284271&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 20:21:42,895 - werkzeug - INFO - ************** - - [15/Jul/2025 20:21:42] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 20:21:42,902 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 20:21:44,152 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 20:21:44,154 - werkzeug - INFO - ************** - - [15/Jul/2025 20:21:44] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 20:21:46,969 - root - INFO - 开始查询坐标点 (22.87207086776091, 108.39029884957839) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 20:21:47,375 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 20:21:47,395 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:21:47,426 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:21:47,428 - root - INFO - 在坐标点 (22.87207086776091, 108.39029884957839) 处找到 2 个有有效数据的栅格图层
2025-07-15 20:21:47,430 - werkzeug - INFO - ************** - - [15/Jul/2025 20:21:47] "GET /api/query_values?lat=22.87207086776091&lon=108.39029884957839&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 20:21:47,544 - root - INFO - 开始查询坐标点 (22.80172338779579, 108.36132514217746) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 20:21:47,848 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 20:21:47,876 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:21:47,986 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:21:47,987 - root - INFO - 在坐标点 (22.80172338779579, 108.36132514217746) 处找到 2 个有有效数据的栅格图层
2025-07-15 20:21:47,988 - werkzeug - INFO - ************** - - [15/Jul/2025 20:21:47] "GET /api/query_values?lat=22.80172338779579&lon=108.36132514217746&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 20:26:29,211 - root - INFO - 开始查询坐标点 (22.7950082895413, 108.44640649843049) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 20:26:29,487 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 20:26:29,513 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:26:29,629 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:26:29,632 - root - INFO - 在坐标点 (22.7950082895413, 108.44640649843049) 处找到 2 个有有效数据的栅格图层
2025-07-15 20:26:29,635 - werkzeug - INFO - ************** - - [15/Jul/2025 20:26:29] "GET /api/query_values?lat=22.7950082895413&lon=108.44640649843049&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 20:26:37,473 - root - INFO - 开始查询坐标点 (22.79603737859989, 108.34326244083273) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 20:26:37,739 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 20:26:37,764 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:26:37,862 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:26:37,862 - root - INFO - 在坐标点 (22.79603737859989, 108.34326244083273) 处找到 2 个有有效数据的栅格图层
2025-07-15 20:26:37,870 - werkzeug - INFO - ************** - - [15/Jul/2025 20:26:37] "GET /api/query_values?lat=22.79603737859989&lon=108.34326244083273&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 20:28:23,216 - root - INFO - 开始查询坐标点 (22.89015424824771, 108.34176332226546) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 20:28:23,476 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 20:28:23,509 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:28:23,631 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:28:23,632 - root - INFO - 在坐标点 (22.89015424824771, 108.34176332226546) 处找到 2 个有有效数据的栅格图层
2025-07-15 20:28:23,681 - werkzeug - INFO - ************** - - [15/Jul/2025 20:28:23] "GET /api/query_values?lat=22.89015424824771&lon=108.34176332226546&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 20:28:49,375 - root - INFO - 开始查询坐标点 (22.968904761971984, 108.35270284352583) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 20:28:49,643 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 20:28:49,644 - root - INFO - 在坐标点 (22.968904761971984, 108.35270284352583) 处找到 0 个有有效数据的栅格图层
2025-07-15 20:28:49,649 - werkzeug - INFO - ************** - - [15/Jul/2025 20:28:49] "GET /api/query_values?lat=22.968904761971984&lon=108.35270284352583&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 20:28:51,125 - root - INFO - 开始查询坐标点 (22.9524593526408, 108.36185632049879) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 20:28:51,375 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 20:28:51,376 - root - INFO - 在坐标点 (22.9524593526408, 108.36185632049879) 处找到 0 个有有效数据的栅格图层
2025-07-15 20:28:51,378 - werkzeug - INFO - ************** - - [15/Jul/2025 20:28:51] "GET /api/query_values?lat=22.9524593526408&lon=108.36185632049879&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 20:28:52,696 - root - INFO - 开始查询坐标点 (22.905990273322956, 108.4239213594862) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 20:28:53,048 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 20:28:53,091 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:28:53,216 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 20:28:53,217 - root - INFO - 在坐标点 (22.905990273322956, 108.4239213594862) 处找到 2 个有有效数据的栅格图层
2025-07-15 20:28:53,222 - werkzeug - INFO - ************** - - [15/Jul/2025 20:28:53] "GET /api/query_values?lat=22.905990273322956&lon=108.4239213594862&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 20:32:02,520 - werkzeug - INFO - ************** - - [15/Jul/2025 20:32:02] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 20:32:02,528 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 20:32:02,589 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 20:32:02,591 - werkzeug - INFO - ************** - - [15/Jul/2025 20:32:02] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 20:34:46,957 - werkzeug - INFO - ************** - - [15/Jul/2025 20:34:46] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 20:34:46,967 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 20:34:46,990 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 20:34:46,991 - werkzeug - INFO - ************** - - [15/Jul/2025 20:34:46] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
