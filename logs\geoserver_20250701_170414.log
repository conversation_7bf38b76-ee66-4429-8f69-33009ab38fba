2025-07-01 17:04:14,456 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250701_170414.log
2025-07-01 17:04:14,476 - root - INFO - 开始执行命令: publish-geotiff
2025-07-01 17:04:14,477 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-01 17:04:14,477 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-01 17:04:14,496 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-01 17:04:14,511 - root - INFO - 工作区 'tif_workspace3' 不存在，正在创建...
2025-07-01 17:04:14,557 - root - INFO - 成功创建工作区 'tif_workspace3'
2025-07-01 17:04:14,559 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\nanning.tif
2025-07-01 17:04:14,797 - root - INFO - 发布GeoTIFF 'data/20250701/nanning.tif' 到 'tif_workspace3:nanning_store'
2025-07-01 17:04:14,797 - root - INFO - 图层名称与存储名称不同，尝试重命名图层...
2025-07-01 17:04:14,840 - root - INFO - 成功将图层名称从 'nanning_store' 更改为 'nanning_layer'
2025-07-01 17:04:14,888 - root - INFO - 图层验证成功: 直接图层路径: tif_workspace3:nanning_layer
2025-07-01 17:04:14,888 - root - INFO - GeoTIFF 'data/20250701/nanning.tif' 成功发布为图层 'tif_workspace3:nanning_layer'
2025-07-01 17:04:14,889 - root - INFO - 命令执行完成: publish-geotiff
