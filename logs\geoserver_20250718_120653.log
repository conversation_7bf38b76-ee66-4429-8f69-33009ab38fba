2025-07-18 12:06:53,271 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250718_120653.log
2025-07-18 12:06:53,272 - geo_publisher - INFO - 加载了 13 个任务状态
2025-07-18 12:06:53,293 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 12:06:53,293 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 12:06:53,324 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 12:06:53,332 - root - INFO - === GeoServer REST API服务 ===
2025-07-18 12:06:53,332 - root - INFO - 主机: 0.0.0.0
2025-07-18 12:06:53,333 - root - INFO - 端口: 5083
2025-07-18 12:06:53,911 - root - INFO - 调试模式: 禁用
2025-07-18 12:06:53,916 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-18 12:06:54,019 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-18 12:06:54,020 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 12:15:35,589 - batch_executor - INFO - 启动任务 4d238329-a277-4cee-8b55-99037586a151: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
2025-07-18 12:15:35,591 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:15:35] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-18 12:15:35,626 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:15:35] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:16:37,784 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:16:37] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:17:41,548 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:17:41] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:18:43,144 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:18:43] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:19:02,070 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:19:02] "[33mGET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=testodm&charset=UTF-8 HTTP/1.1[0m" 404 -
2025-07-18 12:19:47,064 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:19:47] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:20:51,239 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:20:51] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:21:07,732 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:21:07] "[33mGET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=testodm&charset=UTF-8 HTTP/1.1[0m" 404 -
2025-07-18 12:21:52,708 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:21:52] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:22:54,482 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:22:54] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:23:56,205 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:23:56] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:24:12,878 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:24:12] "[33mGET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=testodm&charset=UTF-8 HTTP/1.1[0m" 404 -
2025-07-18 12:24:58,059 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:24:58] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:25:59,835 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:25:59] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:27:01,657 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:27:01] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:28:03,375 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:28:03] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:28:19,980 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:28:19] "[33mGET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=testodm&charset=UTF-8 HTTP/1.1[0m" 404 -
2025-07-18 12:29:05,196 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:29:05] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:30:07,018 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:30:07] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:31:09,129 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:31:09] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:32:11,715 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:32:11] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:33:13,605 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:33:13] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:33:28,408 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:33:28] "[33mGET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=testodm&charset=UTF-8 HTTP/1.1[0m" 404 -
2025-07-18 12:34:15,972 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:34:15] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:35:17,912 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:35:17] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:36:20,283 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:36:20] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:37:22,562 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:37:22] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:38:24,868 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:38:24] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:38:40,663 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:38:40] "[33mGET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=testodm&charset=UTF-8 HTTP/1.1[0m" 404 -
2025-07-18 12:39:26,952 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:39:26] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:40:29,038 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:40:29] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:41:31,122 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:41:31] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:42:33,210 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:42:33] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:43:35,381 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:43:35] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:43:51,323 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:43:51] "[33mGET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=testodm&charset=UTF-8 HTTP/1.1[0m" 404 -
2025-07-18 12:44:38,198 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:44:38] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:45:38,771 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:45:38] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:46:39,661 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:46:39] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:47:40,367 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:47:40] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:48:42,557 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:48:42] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:48:57,735 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:48:57] "[33mGET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=testodm&charset=UTF-8 HTTP/1.1[0m" 404 -
2025-07-18 12:49:44,509 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:49:44] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:50:46,558 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:50:46] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:51:48,572 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:51:48] "GET /api/batch/status?task_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-07-18 12:51:50,476 - tif_executor - INFO - 启动TIF处理任务 a413982e-ed7d-4158-b465-872ed73274df: D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp
2025-07-18 12:51:50,480 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - INFO - TIF处理任务 a413982e-ed7d-4158-b465-872ed73274df 开始执行
2025-07-18 12:51:50,481 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:51:50] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-18 12:51:50,486 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - INFO - 开始时间: 2025-07-18 12:51:50
2025-07-18 12:51:50,496 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - INFO - 处理参数: {
  "input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-18 12:51:50,517 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:51:50] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 12:51:50,522 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-18 12:51:59,068 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 30192x36956x3估计内存使用: 总计 16.63 GB, 单个处理块 460.69 MB开始创建掩码... (12:51:58)使用全图检测无效区域模式...将处理 37 个数据块...使用多线程处理 (7 线程)...
2025-07-18 12:51:59,074 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理数据块:   0%|                                                                               | 0/37 [00:00<?, ?it/s]
2025-07-18 12:51:59,502 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理数据块:   3%|#9                                                                     | 1/37 [00:00<00:14,  2.54it/s]
2025-07-18 12:51:59,848 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理数据块:  22%|###############3                                                       | 8/37 [00:00<00:02, 12.38it/s]
2025-07-18 12:52:00,097 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理数据块:  27%|##################9                                                   | 10/37 [00:00<00:02, 10.79it/s]
2025-07-18 12:52:00,480 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理数据块:  41%|############################3                                         | 15/37 [00:01<00:01, 11.80it/s]
2025-07-18 12:52:00,662 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理数据块:  51%|###################################9                                  | 19/37 [00:01<00:01, 14.17it/s]
2025-07-18 12:52:01,075 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理数据块:  59%|#########################################6                            | 22/37 [00:01<00:01, 11.20it/s]
2025-07-18 12:52:01,258 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理数据块:  70%|#################################################1                    | 26/37 [00:02<00:00, 13.50it/s]
2025-07-18 12:52:01,431 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理数据块:  76%|####################################################9                 | 28/37 [00:02<00:00, 13.06it/s]
2025-07-18 12:52:01,636 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理数据块:  81%|########################################################7             | 30/37 [00:02<00:00, 12.13it/s]
2025-07-18 12:52:01,743 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理数据块:  89%|##############################################################4       | 33/37 [00:02<00:00, 14.84it/s]
2025-07-18 12:52:01,878 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理数据块:  95%|##################################################################2   | 35/37 [00:02<00:00, 14.85it/s]
2025-07-18 12:52:01,909 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理数据块: 100%|######################################################################| 37/37 [00:02<00:00, 13.21it/s]
2025-07-18 12:52:01,913 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - INFO - 已完成: 10/37 块 (27.0%)已完成: 20/37 块 (54.1%)已完成: 30/37 块 (81.1%)已完成: 37/37 块 (100.0%)合并处理结果...
2025-07-18 12:52:01,914 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
合并结果:   0%|                                                                                 | 0/37 [00:00<?, ?it/s]
2025-07-18 12:52:02,018 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
合并结果:  78%|#######################################################6               | 29/37 [00:00<00:00, 288.47it/s]
2025-07-18 12:52:02,045 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
合并结果: 100%|#######################################################################| 37/37 [00:00<00:00, 290.13it/s]
2025-07-18 12:52:05,101 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - INFO - 合并进度: 20/37 块 (54.1%)合并进度: 37/37 块 (100.0%)统计结果: 总像素 1115775552, 有效像素 250536219 (22.45%), 无效像素 865239333 (77.55%)掩码创建完成，耗时: 6.32 秒 (12:52:05)预计总处理时间: 约 18.96 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif (12:52:05)写入带有nodata值的影像...
2025-07-18 12:52:05,102 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-18 12:52:35,492 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/37 [00:00<?, ?it/s]
2025-07-18 12:52:35,493 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:52:35,613 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:   5%|###4                                                            | 2/37 [00:00<00:02, 17.39it/s]
2025-07-18 12:52:35,613 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:52:35,714 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  22%|#############8                                                  | 8/37 [00:00<00:00, 40.72it/s]
2025-07-18 12:52:35,715 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:52:35,827 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  38%|#######################8                                       | 14/37 [00:00<00:00, 46.35it/s]
2025-07-18 12:52:35,828 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:52:49,094 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  38%|#######################8                                       | 14/37 [00:13<00:00, 46.35it/s]
2025-07-18 12:52:49,100 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:52:50,901 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  49%|##############################6                                | 18/37 [00:15<00:24,  1.29s/it]
2025-07-18 12:52:50,902 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:52:52,646 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:52:52] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 12:52:56,761 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  51%|################################3                              | 19/37 [00:21<00:32,  1.78s/it]
2025-07-18 12:52:56,762 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:53:07,785 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  59%|#####################################4                         | 22/37 [00:32<00:35,  2.38s/it]
2025-07-18 12:53:07,785 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:53:12,995 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  62%|#######################################1                       | 23/37 [00:37<00:38,  2.75s/it]
2025-07-18 12:53:12,997 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:53:19,428 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  68%|##########################################5                    | 25/37 [00:43<00:34,  2.88s/it]
2025-07-18 12:53:19,428 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:53:39,166 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  68%|##########################################5                    | 25/37 [01:03<00:34,  2.88s/it]
2025-07-18 12:53:39,295 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:53:54,809 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:53:54] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 12:54:08,690 - geo_publisher - INFO - 启动GeoTIFF发布任务 216b7e9c-d10d-43de-844b-d046e24c2205: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=testodm
2025-07-18 12:54:08,696 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:54:08] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-18 12:54:09,576 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - INFO - GeoServer发布任务 216b7e9c-d10d-43de-844b-d046e24c2205 开始执行
2025-07-18 12:54:09,577 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - INFO - 开始时间: 2025-07-18 12:54:08
2025-07-18 12:54:09,582 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-18 12:54:09,587 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 12:54:09,601 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 12:54:09,604 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:54:09] "GET /api/geo/status?task_id=216b7e9c-d10d-43de-844b-d046e24c2205 HTTP/1.1" 200 -
2025-07-18 12:54:14,951 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  70%|############################################2                  | 26/37 [01:39<02:05, 11.44s/it]
2025-07-18 12:54:14,951 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:54:18,028 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 12:54:20,654 - root - INFO - 未提供存储名称，使用文件名: 20250705171600
2025-07-18 12:54:20,657 - root - INFO - 未提供图层名称，使用存储名称: 20250705171600
2025-07-18 12:54:35,325 - root - INFO - 工作区 'testodm' 不存在，正在创建...
2025-07-18 12:54:41,305 - root - INFO - 成功创建工作区 'testodm'
2025-07-18 12:54:41,305 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171600\20250705171600.tif
2025-07-18 12:54:58,403 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:54:58] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 12:55:11,750 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:55:11] "GET /api/geo/status?task_id=216b7e9c-d10d-43de-844b-d046e24c2205 HTTP/1.1" 200 -
2025-07-18 12:55:39,579 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  73%|#############################################9                 | 27/37 [03:04<04:12, 25.24s/it]
2025-07-18 12:55:39,580 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:56:00,931 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:56:00] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 12:56:13,826 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:56:13] "GET /api/geo/status?task_id=216b7e9c-d10d-43de-844b-d046e24c2205 HTTP/1.1" 200 -
2025-07-18 12:56:41,926 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  76%|###############################################6               | 28/37 [04:06<04:58, 33.12s/it]
2025-07-18 12:56:41,927 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:56:54,134 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  78%|#################################################3             | 29/37 [04:18<03:46, 28.25s/it]
2025-07-18 12:56:54,135 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:57:02,996 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:57:02] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 12:57:05,705 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 30/37 [04:30<02:48, 24.09s/it]
2025-07-18 12:57:05,708 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:57:15,874 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:57:15] "GET /api/geo/status?task_id=216b7e9c-d10d-43de-844b-d046e24c2205 HTTP/1.1" 200 -
2025-07-18 12:57:20,815 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  84%|####################################################7          | 31/37 [04:45<02:10, 21.73s/it]
2025-07-18 12:57:20,816 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:57:32,628 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  86%|######################################################4        | 32/37 [04:57<01:35, 19.02s/it]
2025-07-18 12:57:32,629 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:57:45,309 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  89%|########################################################1      | 33/37 [05:09<01:08, 17.25s/it]
2025-07-18 12:57:45,311 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:58:00,503 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  92%|#########################################################8     | 34/37 [05:25<00:49, 16.66s/it]
2025-07-18 12:58:00,504 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:58:05,053 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:58:05] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 12:58:12,775 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  95%|###########################################################5   | 35/37 [05:37<00:30, 15.39s/it]
2025-07-18 12:58:12,786 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:58:17,948 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:58:17] "GET /api/geo/status?task_id=216b7e9c-d10d-43de-844b-d046e24c2205 HTTP/1.1" 200 -
2025-07-18 12:58:27,662 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度:  97%|#############################################################2 | 36/37 [05:52<00:15, 15.24s/it]
2025-07-18 12:58:27,663 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:58:35,674 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
波段 1/3 写入进度: 100%|###############################################################| 37/37 [06:00<00:00, 13.11s/it]
2025-07-18 12:58:35,675 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:58:35,678 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - [A
2025-07-18 12:59:07,147 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:59:07] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 12:59:20,086 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:59:20] "GET /api/geo/status?task_id=216b7e9c-d10d-43de-844b-d046e24c2205 HTTP/1.1" 200 -
2025-07-18 12:59:51,276 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理波段:  33%|########################3                                                | 1/3 [07:46<15:32, 466.17s/it]
2025-07-18 13:00:01,761 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/37 [00:00<?, ?it/s]
2025-07-18 13:00:01,774 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:00:02,299 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:   3%|#7                                                              | 1/37 [00:00<00:18,  1.92it/s]
2025-07-18 13:00:02,300 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:00:02,442 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:   8%|#####1                                                          | 3/37 [00:00<00:06,  5.31it/s]
2025-07-18 13:00:02,443 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:00:02,572 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  14%|########6                                                       | 5/37 [00:00<00:03,  8.02it/s]
2025-07-18 13:00:02,573 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:00:02,695 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 7/37 [00:00<00:02, 10.19it/s]
2025-07-18 13:00:02,697 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:00:02,837 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  24%|###############5                                                | 9/37 [00:01<00:02, 11.41it/s]
2025-07-18 13:00:02,838 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:00:02,979 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  30%|##################7                                            | 11/37 [00:01<00:02, 12.19it/s]
2025-07-18 13:00:02,980 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:00:03,149 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  35%|######################1                                        | 13/37 [00:01<00:01, 12.04it/s]
2025-07-18 13:00:03,150 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:00:09,404 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:00:09] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:00:10,006 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  41%|#########################5                                     | 15/37 [00:08<00:25,  1.16s/it]
2025-07-18 13:00:10,008 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:00:21,474 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  43%|###########################2                                   | 16/37 [00:19<01:04,  3.09s/it]
2025-07-18 13:00:21,476 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:00:22,125 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:00:22] "GET /api/geo/status?task_id=216b7e9c-d10d-43de-844b-d046e24c2205 HTTP/1.1" 200 -
2025-07-18 13:00:30,793 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  46%|############################9                                  | 17/37 [00:29<01:28,  4.41s/it]
2025-07-18 13:00:30,794 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:00:44,817 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  49%|##############################6                                | 18/37 [00:43<02:06,  6.63s/it]
2025-07-18 13:00:44,817 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:00:54,496 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  51%|################################3                              | 19/37 [00:52<02:13,  7.39s/it]
2025-07-18 13:00:54,497 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:01:08,380 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  54%|##################################                             | 20/37 [01:06<02:34,  9.09s/it]
2025-07-18 13:01:08,381 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:01:11,685 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:01:11] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:01:18,794 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  57%|###################################7                           | 21/37 [01:17<02:31,  9.45s/it]
2025-07-18 13:01:18,795 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:01:24,635 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:01:24] "GET /api/geo/status?task_id=216b7e9c-d10d-43de-844b-d046e24c2205 HTTP/1.1" 200 -
2025-07-18 13:01:30,171 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  59%|#####################################4                         | 22/37 [01:28<02:29,  9.99s/it]
2025-07-18 13:01:30,172 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:01:43,061 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  62%|#######################################1                       | 23/37 [01:41<02:31, 10.82s/it]
2025-07-18 13:01:43,061 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:01:51,523 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  65%|########################################8                      | 24/37 [01:49<02:11, 10.14s/it]
2025-07-18 13:01:51,523 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:02:04,320 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  68%|##########################################5                    | 25/37 [02:02<02:10, 10.92s/it]
2025-07-18 13:02:04,320 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:02:13,190 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:02:13] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:02:18,211 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  70%|############################################2                  | 26/37 [02:16<02:09, 11.79s/it]
2025-07-18 13:02:18,211 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:02:25,795 - root - ERROR - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif' 失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-18 13:02:25,806 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - INFO - GeoServer发布任务 216b7e9c-d10d-43de-844b-d046e24c2205 执行失败
2025-07-18 13:02:25,807 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - INFO - 完成时间: 2025-07-18 13:02:25
2025-07-18 13:02:25,808 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - INFO - 状态: 发布失败
2025-07-18 13:02:26,117 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:02:26] "GET /api/geo/status?task_id=216b7e9c-d10d-43de-844b-d046e24c2205 HTTP/1.1" 200 -
2025-07-18 13:02:32,576 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  73%|#############################################9                 | 27/37 [02:30<02:05, 12.56s/it]
2025-07-18 13:02:32,577 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:02:42,358 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  76%|###############################################6               | 28/37 [02:40<01:45, 11.73s/it]
2025-07-18 13:02:42,359 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:02:54,573 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  78%|#################################################3             | 29/37 [02:52<01:35, 11.88s/it]
2025-07-18 13:02:54,574 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:03:08,937 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 30/37 [03:07<01:28, 12.62s/it]
2025-07-18 13:03:08,938 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:03:15,040 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:03:15] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:03:30,668 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  84%|####################################################7          | 31/37 [03:28<01:32, 15.34s/it]
2025-07-18 13:03:30,669 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:04:17,208 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:04:17] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:05:19,864 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:05:19] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:06:22,095 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:06:22] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:06:37,764 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  86%|######################################################4        | 32/37 [06:35<05:33, 66.77s/it]
2025-07-18 13:06:37,764 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:07:04,293 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  89%|########################################################1      | 33/37 [07:02<03:38, 54.71s/it]
2025-07-18 13:07:04,294 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:07:24,438 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:07:24] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:07:29,220 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  92%|#########################################################8     | 34/37 [07:27<02:17, 45.79s/it]
2025-07-18 13:07:29,221 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:07:37,440 - geo_publisher - INFO - 启动GeoTIFF发布任务 75c92676-e2f1-4faa-91b3-65087ac759e8: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=testodm
2025-07-18 13:07:37,461 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:07:37] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-18 13:07:37,485 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:07:37] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:07:37,505 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - INFO - GeoServer发布任务 75c92676-e2f1-4faa-91b3-65087ac759e8 开始执行
2025-07-18 13:07:37,506 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - INFO - 开始时间: 2025-07-18 13:07:37
2025-07-18 13:07:37,514 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-18 13:07:37,517 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 13:07:37,520 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 13:07:37,641 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 13:07:38,243 - root - INFO - 未提供存储名称，使用文件名: 20250705171600
2025-07-18 13:07:38,243 - root - INFO - 未提供图层名称，使用存储名称: 20250705171600
2025-07-18 13:07:38,279 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171600\20250705171600.tif
2025-07-18 13:07:54,712 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  95%|###########################################################5   | 35/37 [07:52<01:19, 39.70s/it]
2025-07-18 13:07:54,715 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:08:19,590 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度:  97%|#############################################################2 | 36/37 [08:17<00:35, 35.26s/it]
2025-07-18 13:08:19,590 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:08:26,517 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:08:26] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:08:39,533 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:08:39] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:08:45,368 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 37/37 [08:43<00:00, 32.41s/it]
2025-07-18 13:08:45,369 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:08:45,372 - geo_task_216b7e9c-d10d-43de-844b-d046e24c2205 - ERROR - [A
2025-07-18 13:09:28,658 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:09:28] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:09:41,693 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:09:41] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:10:30,773 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:10:30] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:10:43,786 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:10:43] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:11:32,868 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:11:32] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:11:45,889 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:11:45] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:12:34,991 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:12:34] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:12:48,008 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:12:48] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:13:03,592 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [20:58<10:58, 658.02s/it]
2025-07-18 13:13:10,128 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/37 [00:00<?, ?it/s]
2025-07-18 13:13:10,129 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:13:10,614 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:   3%|#7                                                              | 1/37 [00:00<00:17,  2.08it/s]
2025-07-18 13:13:10,614 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:13:10,754 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:   8%|#####1                                                          | 3/37 [00:00<00:05,  5.67it/s]
2025-07-18 13:13:10,754 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:13:10,859 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  11%|######9                                                         | 4/37 [00:00<00:05,  6.60it/s]
2025-07-18 13:13:10,859 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:13:11,017 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  16%|##########3                                                     | 6/37 [00:00<00:03,  8.64it/s]
2025-07-18 13:13:11,017 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:13:11,163 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  22%|#############8                                                  | 8/37 [00:01<00:02, 10.19it/s]
2025-07-18 13:13:11,164 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:13:11,311 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  27%|#################                                              | 10/37 [00:01<00:02, 11.24it/s]
2025-07-18 13:13:11,311 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:13:11,441 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  32%|####################4                                          | 12/37 [00:01<00:02, 12.39it/s]
2025-07-18 13:13:11,441 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:13:11,568 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  38%|#######################8                                       | 14/37 [00:01<00:01, 13.33it/s]
2025-07-18 13:13:11,569 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:13:31,066 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  38%|#######################8                                       | 14/37 [00:20<00:01, 13.33it/s]
2025-07-18 13:13:31,067 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:13:37,147 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:13:37] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:13:50,371 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:13:50] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:14:39,794 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:14:39] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:14:52,560 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:14:52] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:15:42,064 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:15:42] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:15:55,075 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:15:55] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:16:23,135 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  43%|###########################2                                   | 16/37 [03:13<10:47, 30.84s/it]
2025-07-18 13:16:23,137 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:16:44,328 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:16:44] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:16:47,752 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  46%|############################9                                  | 17/37 [03:37<09:53, 29.68s/it]
2025-07-18 13:16:47,752 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:16:57,191 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:16:57] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:17:02,072 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  49%|##############################6                                | 18/37 [03:51<08:22, 26.44s/it]
2025-07-18 13:17:02,073 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:17:09,021 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  51%|################################3                              | 19/37 [03:58<06:34, 21.93s/it]
2025-07-18 13:17:09,022 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:17:36,729 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  54%|##################################                             | 20/37 [04:26<06:37, 23.37s/it]
2025-07-18 13:17:36,732 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:17:46,659 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:17:46] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:17:59,932 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:17:59] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:17:59,997 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  57%|###################################7                           | 21/37 [04:49<06:13, 23.34s/it]
2025-07-18 13:17:59,999 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:18:22,966 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  59%|#####################################4                         | 22/37 [05:12<05:48, 23.24s/it]
2025-07-18 13:18:22,967 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:18:49,265 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:18:49] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:18:51,379 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  62%|#######################################1                       | 23/37 [05:41<05:45, 24.69s/it]
2025-07-18 13:18:51,380 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:19:02,927 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:19:02] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:19:20,976 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  65%|########################################8                      | 24/37 [06:10<05:39, 26.09s/it]
2025-07-18 13:19:20,977 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:19:40,181 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  68%|##########################################5                    | 25/37 [06:30<04:49, 24.09s/it]
2025-07-18 13:19:40,182 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:19:50,322 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  70%|############################################2                  | 26/37 [06:40<03:40, 20.01s/it]
2025-07-18 13:19:50,323 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:19:52,619 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:19:52] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:20:04,006 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  73%|#############################################9                 | 27/37 [06:53<03:01, 18.14s/it]
2025-07-18 13:20:04,007 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:20:05,912 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:20:05] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:20:18,509 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  76%|###############################################6               | 28/37 [07:08<02:33, 17.06s/it]
2025-07-18 13:20:18,511 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:20:38,042 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  78%|#################################################3             | 29/37 [07:27<02:22, 17.80s/it]
2025-07-18 13:20:38,043 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:20:49,693 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 30/37 [07:39<01:51, 15.97s/it]
2025-07-18 13:20:49,694 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:20:55,222 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:20:55] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:21:03,868 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  84%|####################################################7          | 31/37 [07:53<01:32, 15.43s/it]
2025-07-18 13:21:03,869 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:21:08,106 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:21:08] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:21:17,997 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  86%|######################################################4        | 32/37 [08:07<01:15, 15.04s/it]
2025-07-18 13:21:17,998 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:21:27,989 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  89%|########################################################1      | 33/37 [08:17<00:54, 13.53s/it]
2025-07-18 13:21:27,989 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:21:41,150 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  92%|#########################################################8     | 34/37 [08:31<00:40, 13.42s/it]
2025-07-18 13:21:41,151 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:21:51,853 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  95%|###########################################################5   | 35/37 [08:41<00:25, 12.61s/it]
2025-07-18 13:21:51,854 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:21:57,598 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:21:57] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:22:04,461 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度:  97%|#############################################################2 | 36/37 [08:54<00:12, 12.61s/it]
2025-07-18 13:22:04,461 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:22:10,528 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:22:10] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:22:15,065 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - 
波段 3/3 写入进度: 100%|###############################################################| 37/37 [09:04<00:00, 12.01s/it]
2025-07-18 13:22:15,066 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:22:15,069 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - ERROR - [A
2025-07-18 13:23:00,043 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:23:00] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:23:12,945 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:23:12] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:24:02,399 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:24:02] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:24:15,306 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:24:15] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:25:05,446 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:25:05] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:25:17,704 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:25:17] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:26:07,761 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:26:07] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:26:20,168 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:26:20] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:27:11,828 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:27:11] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:27:22,548 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:27:22] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:28:14,168 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:28:14] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:28:24,888 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:28:24] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:28:45,047 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [36:39<00:00, 787.44s/it]
2025-07-18 13:28:45,048 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [36:39<00:00, 733.31s/it]
2025-07-18 13:28:45,990 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif' 到 'testodm:20250705171600'
2025-07-18 13:28:46,518 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171600
2025-07-18 13:28:46,519 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif' 成功发布为图层 'testodm:20250705171600'
2025-07-18 13:28:46,527 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - INFO - GeoServer发布任务 75c92676-e2f1-4faa-91b3-65087ac759e8 执行成功
2025-07-18 13:28:46,528 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - INFO - 完成时间: 2025-07-18 13:28:46
2025-07-18 13:28:46,529 - geo_task_75c92676-e2f1-4faa-91b3-65087ac759e8 - INFO - 状态: 发布成功
2025-07-18 13:29:16,505 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:29:16] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:29:27,223 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:29:27] "GET /api/geo/status?task_id=75c92676-e2f1-4faa-91b3-65087ac759e8 HTTP/1.1" 200 -
2025-07-18 13:30:18,573 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:30:18] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:31:20,324 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:31:20] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:32:23,549 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:32:23] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:32:50,737 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - INFO - 创建掩码，识别有效区域...轻微平滑处理掩码，减少边缘锯齿...寻找有效区域轮廓...找到 2 个初始轮廓使用所有 2 个轮廓，不进行面积过滤转换轮廓为地理坐标...
2025-07-18 13:32:51,009 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理轮廓:   0%|                                                                                  | 0/2 [00:00<?, ?it/s]
2025-07-18 13:32:52,059 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理轮廓: 100%|##########################################################################| 2/2 [00:01<00:00,  1.91it/s]
2025-07-18 13:32:52,060 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
处理轮廓: 100%|##########################################################################| 2/2 [00:01<00:00,  1.91it/s]
2025-07-18 13:32:52,266 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - INFO - 成功创建 2 个多边形合并多边形...创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp写入shapefile特征...处理MultiPolygon，包含 2 个多边形
2025-07-18 13:32:52,268 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
写入多边形:   0%|                                                                                | 0/2 [00:00<?, ?it/s]
2025-07-18 13:32:52,302 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - ERROR - 
写入多边形: 100%|########################################################################| 2/2 [00:00<00:00, 70.71it/s]
2025-07-18 13:32:57,902 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - INFO - TIF处理任务 a413982e-ed7d-4158-b465-872ed73274df 执行成功
2025-07-18 13:32:57,902 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - INFO - 完成时间: 2025-07-18 13:32:57
2025-07-18 13:32:57,905 - tif_task_a413982e-ed7d-4158-b465-872ed73274df - INFO - 状态: 运行成功
2025-07-18 13:33:26,301 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:33:26] "GET /api/tif/status?task_id=a413982e-ed7d-4158-b465-872ed73274df HTTP/1.1" 200 -
2025-07-18 13:33:28,424 - geo_publisher - INFO - 启动GeoTIFF发布任务 ecbe12a5-d49f-4d85-abfe-5019a10a238e: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=testodm
2025-07-18 13:33:28,426 - geo_task_ecbe12a5-d49f-4d85-abfe-5019a10a238e - INFO - GeoServer发布任务 ecbe12a5-d49f-4d85-abfe-5019a10a238e 开始执行
2025-07-18 13:33:28,428 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:33:28] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-18 13:33:28,431 - geo_task_ecbe12a5-d49f-4d85-abfe-5019a10a238e - INFO - 开始时间: 2025-07-18 13:33:28
2025-07-18 13:33:28,433 - geo_task_ecbe12a5-d49f-4d85-abfe-5019a10a238e - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-18 13:33:28,437 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 13:33:28,440 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 13:33:28,477 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:33:28] "GET /api/geo/status?task_id=ecbe12a5-d49f-4d85-abfe-5019a10a238e HTTP/1.1" 200 -
2025-07-18 13:33:32,793 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 13:33:32,795 - root - INFO - 未提供存储名称，使用文件名: 20250705171600
2025-07-18 13:33:32,797 - root - INFO - 未提供图层名称，使用存储名称: 20250705171600
2025-07-18 13:33:32,874 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171600\20250705171600.tif
2025-07-18 13:34:30,886 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:34:30] "GET /api/geo/status?task_id=ecbe12a5-d49f-4d85-abfe-5019a10a238e HTTP/1.1" 200 -
2025-07-18 13:35:33,414 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:35:33] "GET /api/geo/status?task_id=ecbe12a5-d49f-4d85-abfe-5019a10a238e HTTP/1.1" 200 -
2025-07-18 13:36:36,031 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:36:36] "GET /api/geo/status?task_id=ecbe12a5-d49f-4d85-abfe-5019a10a238e HTTP/1.1" 200 -
2025-07-18 13:37:39,111 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:37:39] "GET /api/geo/status?task_id=ecbe12a5-d49f-4d85-abfe-5019a10a238e HTTP/1.1" 200 -
2025-07-18 13:38:41,592 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:38:41] "GET /api/geo/status?task_id=ecbe12a5-d49f-4d85-abfe-5019a10a238e HTTP/1.1" 200 -
2025-07-18 13:39:44,564 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:39:44] "GET /api/geo/status?task_id=ecbe12a5-d49f-4d85-abfe-5019a10a238e HTTP/1.1" 200 -
2025-07-18 13:40:24,050 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif' 到 'testodm:20250705171600'
2025-07-18 13:40:24,253 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171600
2025-07-18 13:40:24,253 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif' 成功发布为图层 'testodm:20250705171600'
2025-07-18 13:40:24,259 - geo_task_ecbe12a5-d49f-4d85-abfe-5019a10a238e - INFO - GeoServer发布任务 ecbe12a5-d49f-4d85-abfe-5019a10a238e 执行成功
2025-07-18 13:40:24,262 - geo_task_ecbe12a5-d49f-4d85-abfe-5019a10a238e - INFO - 完成时间: 2025-07-18 13:40:24
2025-07-18 13:40:24,263 - geo_task_ecbe12a5-d49f-4d85-abfe-5019a10a238e - INFO - 状态: 发布成功
2025-07-18 13:40:47,167 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 13:40:47] "GET /api/geo/status?task_id=ecbe12a5-d49f-4d85-abfe-5019a10a238e HTTP/1.1" 200 -
2025-07-18 15:53:24,437 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 15:53:24] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-18 15:53:24,449 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-18 15:53:24,579 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-18 15:53:24,582 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 15:53:24] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-18 15:53:32,772 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 15:53:32] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171600 HTTP/1.1" 200 -
2025-07-18 15:53:32,778 - root - INFO - 获取图层 testodm:20250705171600 的信息
2025-07-18 15:53:32,819 - root - INFO - 成功获取图层 testodm:20250705171600 的边界框信息
2025-07-18 15:53:32,822 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 15:53:32] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171600 HTTP/1.1" 200 -
2025-07-18 16:00:39,329 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 16:00:39] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-18 16:00:39,379 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-18 16:00:39,494 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-18 16:00:39,498 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 16:00:39] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-18 16:03:05,603 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 16:03:05] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-18 16:03:05,610 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-18 16:03:05,664 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-18 16:03:05,738 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 16:03:05] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-18 16:16:53,966 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 16:16:53] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-18 16:16:54,044 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-18 16:16:54,159 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-18 16:16:54,161 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 16:16:54] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-18 16:17:41,947 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 16:17:41] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-18 16:17:41,961 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-18 16:17:42,012 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-18 16:17:42,015 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 16:17:42] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-18 16:17:45,792 - root - INFO - 开始查询坐标点 (22.837376865504254, 108.36010664777044) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-18 16:17:46,468 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-18 16:17:47,212 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-18 16:17:47,331 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-18 16:17:47,332 - root - INFO - 在坐标点 (22.837376865504254, 108.36010664777044) 处找到 2 个有有效数据的栅格图层
2025-07-18 16:17:47,333 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 16:17:47] "GET /api/query_values?lat=22.837376865504254&lon=108.36010664777044&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-18 16:17:54,037 - root - INFO - 开始查询坐标点 (22.873157448581637, 108.44541302570688) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-18 16:17:54,675 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-18 16:17:54,876 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-18 16:17:55,040 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-18 16:17:55,044 - root - INFO - 在坐标点 (22.873157448581637, 108.44541302570688) 处找到 2 个有有效数据的栅格图层
2025-07-18 16:17:55,053 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 16:17:55] "GET /api/query_values?lat=22.873157448581637&lon=108.44541302570688&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-18 16:18:35,518 - root - INFO - 开始查询坐标点 (22.888147117115906, 108.3469206036421) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-18 16:18:35,924 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-18 16:18:35,953 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-18 16:18:36,072 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-18 16:18:36,073 - root - INFO - 在坐标点 (22.888147117115906, 108.3469206036421) 处找到 2 个有有效数据的栅格图层
2025-07-18 16:18:36,074 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 16:18:36] "GET /api/query_values?lat=22.888147117115906&lon=108.3469206036421&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-18 16:18:39,037 - root - INFO - 开始查询坐标点 (22.797102340310715, 108.42965080515451) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-18 16:18:39,520 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-18 16:18:39,556 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-18 16:18:39,672 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-18 16:18:39,672 - root - INFO - 在坐标点 (22.797102340310715, 108.42965080515451) 处找到 2 个有有效数据的栅格图层
2025-07-18 16:18:39,673 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 16:18:39] "GET /api/query_values?lat=22.797102340310715&lon=108.42965080515451&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-18 16:18:42,294 - root - INFO - 开始查询坐标点 (22.888147117115906, 108.34432988751959) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-18 16:18:42,678 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-18 16:18:42,708 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-18 16:18:42,741 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-18 16:18:42,741 - root - INFO - 在坐标点 (22.888147117115906, 108.34432988751959) 处找到 2 个有有效数据的栅格图层
2025-07-18 16:18:42,742 - werkzeug - INFO - 192.168.43.148 - - [18/Jul/2025 16:18:42] "GET /api/query_values?lat=22.888147117115906&lon=108.34432988751959&workspace=test_myworkspace HTTP/1.1" 200 -
