2025-08-04 11:41:11,145 - INFO - ============ TIF处理任务 add2250d-60e9-44b3-a6ac-ade953e72018 开始执行 ============
2025-08-04 11:41:11,147 - INFO - 开始时间: 2025-08-04 11:41:11
2025-08-04 11:41:11,166 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-08-04 11:41:11,176 - INFO - 系统信息:
2025-08-04 11:41:11,177 - INFO -   操作系统: Windows 10.0.19045
2025-08-04 11:41:11,178 - INFO -   Python版本: 3.8.20
2025-08-04 11:41:11,178 - INFO -   GDAL版本: 3.9.2
2025-08-04 11:41:11,179 - INFO -   GPU可用: 否
2025-08-04 11:41:11,180 - INFO - 检查参数有效性...
2025-08-04 11:41:11,182 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 11:41:11,191 - INFO - 开始执行TIF处理流程...
2025-08-04 11:41:11,198 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 11:41:11,200 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-04 11:41:11,201 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-08-04 11:41:17,573 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17007, 高=20364, 波段数=4开始读取影像数据，大小约: 5284.58 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (11:41:17)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-08-04 11:41:17,598 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-08-04 11:41:17,989 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:07,  2.59it/s]
2025-08-04 11:41:18,325 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:01, 12.71it/s]
2025-08-04 11:41:18,599 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 17.72it/s]
2025-08-04 11:41:18,705 - ERROR - 
处理数据块:  95%|##################################################################6   | 20/21 [00:01<00:00, 22.96it/s]
2025-08-04 11:41:18,707 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:01<00:00, 19.02it/s]
2025-08-04 11:41:18,712 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-08-04 11:41:18,713 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-08-04 11:41:18,766 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 411.76it/s]
2025-08-04 11:41:25,896 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 2.87 秒 (11:41:20)预计总处理时间: 约 8.61 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif (11:41:20)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif使用已处理过的影像信息影像信息: 宽=17007, 高=20364, 波段数=3掩码形状: (20364, 17007), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 尺寸: 17007x20364x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-08-04 11:41:25,898 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-08-04 11:41:35,018 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=42.58, std=63.34应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-08-04 11:41:35,023 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-08-04 11:41:35,025 - ERROR - [A
2025-08-04 11:41:35,156 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:00, 23.14it/s]
2025-08-04 11:41:35,157 - ERROR - [A
2025-08-04 11:41:35,366 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:00, 16.91it/s]
2025-08-04 11:41:35,367 - ERROR - [A
2025-08-04 11:41:35,551 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:00<00:00, 14.06it/s]
2025-08-04 11:41:35,552 - ERROR - [A
2025-08-04 11:41:35,773 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 11.80it/s]
2025-08-04 11:41:35,774 - ERROR - [A
2025-08-04 11:41:35,952 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:00<00:00, 11.59it/s]
2025-08-04 11:41:35,953 - ERROR - [A
2025-08-04 11:41:36,230 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:01<00:00,  9.72it/s]
2025-08-04 11:41:36,231 - ERROR - [A
2025-08-04 11:41:36,494 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:01<00:00,  8.93it/s]
2025-08-04 11:41:36,494 - ERROR - [A
2025-08-04 11:41:36,645 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:01<00:00,  8.40it/s]
2025-08-04 11:41:36,649 - ERROR - [A
2025-08-04 11:41:36,806 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:01<00:00,  7.83it/s]
2025-08-04 11:41:36,807 - ERROR - [A
2025-08-04 11:41:36,967 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:01<00:00,  7.39it/s]
2025-08-04 11:41:36,967 - ERROR - [A
2025-08-04 11:41:37,163 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:02<00:00,  6.65it/s]
2025-08-04 11:41:37,164 - ERROR - [A
2025-08-04 11:41:37,209 - ERROR - [A
2025-08-04 11:42:26,906 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:00<02:01, 61.00s/it]
2025-08-04 11:42:33,453 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=45.79, std=65.41应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-08-04 11:42:33,472 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-08-04 11:42:33,473 - ERROR - [A
2025-08-04 11:42:34,681 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:01<00:24,  1.21s/it]
2025-08-04 11:42:34,682 - ERROR - [A
2025-08-04 11:42:35,003 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:01<00:13,  1.46it/s]
2025-08-04 11:42:35,003 - ERROR - [A
2025-08-04 11:42:35,243 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:08,  2.07it/s]
2025-08-04 11:42:35,244 - ERROR - [A
2025-08-04 11:42:35,607 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:02<00:07,  2.30it/s]
2025-08-04 11:42:35,608 - ERROR - [A
2025-08-04 11:42:35,736 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:02<00:05,  3.08it/s]
2025-08-04 11:42:35,737 - ERROR - [A
2025-08-04 11:42:35,868 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:03,  3.85it/s]
2025-08-04 11:42:35,869 - ERROR - [A
2025-08-04 11:42:36,081 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:02<00:03,  4.09it/s]
2025-08-04 11:42:36,082 - ERROR - [A
2025-08-04 11:42:36,248 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:02<00:02,  4.55it/s]
2025-08-04 11:42:36,249 - ERROR - [A
2025-08-04 11:42:36,390 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:02<00:02,  5.13it/s]
2025-08-04 11:42:36,390 - ERROR - [A
2025-08-04 11:42:36,667 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:03<00:02,  4.53it/s]
2025-08-04 11:42:36,667 - ERROR - [A
2025-08-04 11:42:37,115 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:03<00:02,  3.45it/s]
2025-08-04 11:42:37,133 - ERROR - [A
2025-08-04 11:42:37,430 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:03<00:02,  3.36it/s]
2025-08-04 11:42:37,431 - ERROR - [A
2025-08-04 11:42:37,776 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:04<00:02,  3.20it/s]
2025-08-04 11:42:37,777 - ERROR - [A
2025-08-04 11:42:38,151 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:04<00:02,  3.02it/s]
2025-08-04 11:42:38,151 - ERROR - [A
2025-08-04 11:42:38,436 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:04<00:01,  3.15it/s]
2025-08-04 11:42:38,437 - ERROR - [A
2025-08-04 11:42:38,739 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:05<00:01,  3.19it/s]
2025-08-04 11:42:38,740 - ERROR - [A
2025-08-04 11:42:40,882 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:07<00:03,  1.16it/s]
2025-08-04 11:42:40,882 - ERROR - [A
2025-08-04 11:42:44,396 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:10<00:04,  1.66s/it]
2025-08-04 11:42:44,397 - ERROR - [A
2025-08-04 11:42:50,067 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:16<00:05,  2.86s/it]
2025-08-04 11:42:50,068 - ERROR - [A
2025-08-04 11:42:55,377 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:21<00:03,  3.60s/it]
2025-08-04 11:42:55,377 - ERROR - [A
2025-08-04 11:42:55,384 - ERROR - [A
2025-08-04 11:44:13,124 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [02:47<01:27, 87.60s/it]
2025-08-04 11:44:23,758 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=35.76, std=51.77应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-08-04 11:44:23,763 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-08-04 11:44:23,765 - ERROR - [A
2025-08-04 11:44:24,482 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:14,  1.40it/s]
2025-08-04 11:44:24,483 - ERROR - [A
2025-08-04 11:44:24,809 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:01<00:09,  2.06it/s]
2025-08-04 11:44:24,810 - ERROR - [A
2025-08-04 11:44:25,189 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:07,  2.28it/s]
2025-08-04 11:44:25,190 - ERROR - [A
2025-08-04 11:44:25,550 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:01<00:06,  2.46it/s]
2025-08-04 11:44:25,550 - ERROR - [A
2025-08-04 11:44:25,653 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  5.78it/s]
2025-08-04 11:44:25,653 - ERROR - [A
2025-08-04 11:44:25,773 - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:02<00:00, 10.51it/s]
2025-08-04 11:44:25,776 - ERROR - [A
2025-08-04 11:44:25,882 - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:00, 13.63it/s]
2025-08-04 11:44:25,884 - ERROR - [A
2025-08-04 11:44:26,004 - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 18/21 [00:02<00:00, 17.91it/s]
2025-08-04 11:44:26,005 - ERROR - [A
2025-08-04 11:44:26,102 - ERROR - [A
2025-08-04 11:46:12,907 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [04:47<00:00, 102.29s/it]
2025-08-04 11:46:12,908 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:47<00:00, 95.67s/it]
2025-08-04 11:46:52,886 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif影像保存完成，耗时: 293.51 秒 (11:46:13)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp (11:46:13)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tifTIF文件信息: 宽=17007, 高=20364, 波段数=3开始读取影像数据，大小约: 3963.44 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif提取TIF文件信息...TIF文件信息: 宽=17007, 高=20364, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 218929698 个无效像素检查波段 2...波段 2 检测到 218929698 个无效像素检查波段 3...波段 3 检测到 218929698 个无效像素掩码统计: 总像素数 346330548, 有效像素数 127400850 (36.79%), 无效像素数 218929698 (63.21%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 1 个初始轮廓轮廓面积统计: 最小=127380512.50, 最大=127380512.50, 平均=127380512.50, 总数=1将 1 个轮廓转换为地理坐标...
2025-08-04 11:46:52,909 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-08-04 11:46:53,425 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.95it/s]
2025-08-04 11:46:53,426 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.94it/s]
2025-08-04 11:46:53,433 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-08-04 11:47:05,835 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-08-04 11:47:05,836 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-08-04 11:47:07,032 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:01<00:17,  1.67it/s]
2025-08-04 11:47:07,167 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:11,  2.47it/s]
2025-08-04 11:47:07,211 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 22.59it/s]
2025-08-04 11:47:07,870 - INFO - 处理了 82263 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp写入shapefile特征...处理MultiPolygon，包含 19 个多边形
2025-08-04 11:47:07,873 - ERROR - 
写入多边形:   0%|                                                                               | 0/19 [00:00<?, ?it/s]
2025-08-04 11:47:07,978 - ERROR - 
写入多边形: 100%|#####################################################################| 19/19 [00:00<00:00, 190.85it/s]
2025-08-04 11:47:08,238 - INFO - 处理完成，耗时: 357.04 秒 (5.95 分钟)
2025-08-04 11:47:08,239 - INFO - 处理结果: 成功
2025-08-04 11:47:08,244 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 大小: 740.84 MB
2025-08-04 11:47:08,247 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp, 大小: 22.81 KB
2025-08-04 11:47:08,272 - INFO - TIF处理任务 add2250d-60e9-44b3-a6ac-ade953e72018 执行成功
2025-08-04 11:47:08,273 - INFO - 完成时间: 2025-08-04 11:47:08
2025-08-04 11:47:08,274 - INFO - 状态: 运行成功
2025-08-04 11:47:08,278 - INFO - ============ 任务执行结束 ============
