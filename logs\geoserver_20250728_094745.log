2025-07-28 09:47:45,614 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250728_094745.log
2025-07-28 09:47:45,617 - geo_publisher - INFO - 加载了 25 个任务状态
2025-07-28 09:47:45,971 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-28 09:47:45,971 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-28 09:47:46,009 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-28 09:47:46,020 - root - INFO - === GeoServer REST API服务 ===
2025-07-28 09:47:46,021 - root - INFO - 主机: 0.0.0.0
2025-07-28 09:47:46,023 - root - INFO - 端口: 5083
2025-07-28 09:47:46,024 - root - INFO - 调试模式: 禁用
2025-07-28 09:47:46,025 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-28 09:47:46,059 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-28 09:47:46,060 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-28 09:47:48,690 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:47:48,711 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:47:48,736 - map_api - INFO - 找到 3 个目录
2025-07-28 09:47:48,741 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:47:48,755 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:47:48,762 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:47:48,777 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:47:48,782 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:47:48,809 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1921 字节
2025-07-28 09:47:48,816 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:47:48,818 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:47:48,829 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:47:48] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:47:54,838 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:47:54] "[33mGET /api/map/logs/tiflog/f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1[0m" 404 -
2025-07-28 09:47:57,716 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:47:57] "[33mGET /api/map/logs/tiflog/f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1[0m" 404 -
2025-07-28 09:48:41,072 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:48:41] "[33mGET /api/map/logs/log_type=tiflog&log_id=f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1[0m" 404 -
2025-07-28 09:48:44,194 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:48:44,203 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:48:44,225 - map_api - INFO - 找到 3 个目录
2025-07-28 09:48:44,226 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:48:44,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:48:44,239 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:48:44,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:48:44,245 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:48:44,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1921 字节
2025-07-28 09:48:44,269 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:48:44,270 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:48:44,272 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:48:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:48:45,149 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:48:45] "[33mGET /api/map/logs/log_type=tiflog&log_id=f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1[0m" 404 -
2025-07-28 09:48:46,502 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-28 09:48:50,226 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:48:50,231 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:48:50,236 - tif_api - INFO - 输入文件大小: 237.55 MB
2025-07-28 09:48:50,250 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:48:50,252 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 09:48:50,264 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:48:50,279 - map_api - INFO - 找到 3 个目录
2025-07-28 09:48:50,281 - tif_api - INFO - 黑色阈值: 0
2025-07-28 09:48:50,303 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:48:50,313 - tif_api - INFO - 白色阈值: 255
2025-07-28 09:48:50,319 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-28 09:48:50,341 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:48:50,349 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:48:50,351 - tif_executor - INFO - 启动TIF处理任务 fa4b06bc-66c8-4de0-a0bb-703173c8023c: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:48:50,363 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:48:50,378 - tif_api - INFO - 异步任务启动成功，任务ID: fa4b06bc-66c8-4de0-a0bb-703173c8023c
2025-07-28 09:48:50,384 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:48:50] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-28 09:48:50,383 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:48:50,417 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2032 字节
2025-07-28 09:48:50,426 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:48:50,429 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:48:50,432 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:48:50] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:48:50,457 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:48:50,458 - tif_api - INFO - 查询任务: fa4b06bc-66c8-4de0-a0bb-703173c8023c
2025-07-28 09:48:50,818 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - ============ TIF处理任务 fa4b06bc-66c8-4de0-a0bb-703173c8023c 开始执行 ============
2025-07-28 09:48:50,831 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - 开始时间: 2025-07-28 09:48:50
2025-07-28 09:48:50,820 - tif_api - INFO - 任务 fa4b06bc-66c8-4de0-a0bb-703173c8023c 状态查询成功，当前状态: 正在运行
2025-07-28 09:48:50,835 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 09:48:50,837 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:48:50] "GET /api/tif/status?task_id=fa4b06bc-66c8-4de0-a0bb-703173c8023c HTTP/1.1" 200 -
2025-07-28 09:48:50,840 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - 系统信息:
2025-07-28 09:48:50,843 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO -   操作系统: Windows 10.0.19045
2025-07-28 09:48:50,856 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO -   Python版本: 3.8.20
2025-07-28 09:48:50,857 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO -   GDAL版本: 3.9.2
2025-07-28 09:48:50,860 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO -   GPU可用: 否
2025-07-28 09:48:50,864 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - 检查参数有效性...
2025-07-28 09:48:50,865 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:48:50,868 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - 开始执行TIF处理流程...
2025-07-28 09:48:50,872 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:48:50,874 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 09:48:50,888 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:48:50,900 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-28 09:48:58,780 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (09:48:58)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 09:48:58,814 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 09:48:58,987 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
处理数据块:  29%|####################2                                                  | 6/21 [00:00<00:00, 44.28it/s]
2025-07-28 09:48:59,530 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
处理数据块:  52%|####################################6                                 | 11/21 [00:00<00:00, 14.42it/s]
2025-07-28 09:48:59,643 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
处理数据块:  86%|############################################################          | 18/21 [00:00<00:00, 24.02it/s]
2025-07-28 09:48:59,702 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 24.65it/s]
2025-07-28 09:48:59,709 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 09:48:59,713 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 09:48:59,794 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 276.95it/s]
2025-07-28 09:49:02,963 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 3.40 秒 (09:49:01)预计总处理时间: 约 10.20 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (09:49:01)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 09:49:02,965 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 09:49:04,945 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=f3a51a13-d851-4c11-8cac-ab5a5c40a9d8
2025-07-28 09:49:04,952 - map_api - INFO - 请求文件内容: tiflog/f3a51a13-d851-4c11-8cac-ab5a5c40a9d8.log
2025-07-28 09:49:04,964 - map_api - ERROR - 获取文件内容失败: tiflog/f3a51a13-d851-4c11-8cac-ab5a5c40a9d8.log, 状态码: 404
2025-07-28 09:49:04,972 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:49:04] "[33mGET /api/map/logs?log_type=tiflog&log_id=f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1[0m" 404 -
2025-07-28 09:49:29,788 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=3fb3bacd-fccd-47a5-90b0-0a8231300ecb
2025-07-28 09:49:29,812 - map_api - INFO - 请求文件内容: tiflog/3fb3bacd-fccd-47a5-90b0-0a8231300ecb.log
2025-07-28 09:49:29,836 - map_api - ERROR - 获取文件内容失败: tiflog/3fb3bacd-fccd-47a5-90b0-0a8231300ecb.log, 状态码: 404
2025-07-28 09:49:29,840 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:49:29] "[33mGET /api/map/logs?log_type=tiflog&log_id=3fb3bacd-fccd-47a5-90b0-0a8231300ecb HTTP/1.1[0m" 404 -
2025-07-28 09:49:44,227 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:49:44,235 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:49:44,266 - map_api - INFO - 找到 3 个目录
2025-07-28 09:49:44,272 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:49:44,603 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:49:44,609 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:49:44,683 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:49:44,696 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:49:44,709 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:49:44,720 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:49:44,726 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:49:44,736 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:49:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:49:52,305 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:49:52,320 - tif_api - INFO - 查询任务: fa4b06bc-66c8-4de0-a0bb-703173c8023c
2025-07-28 09:49:52,334 - tif_api - INFO - 任务 fa4b06bc-66c8-4de0-a0bb-703173c8023c 状态查询成功，当前状态: 正在运行
2025-07-28 09:49:52,344 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:49:52] "GET /api/tif/status?task_id=fa4b06bc-66c8-4de0-a0bb-703173c8023c HTTP/1.1" 200 -
2025-07-28 09:50:14,754 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 09:50:14,758 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:50:14,767 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - [A
2025-07-28 09:50:16,843 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:02<00:41,  2.07s/it]
2025-07-28 09:50:16,844 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - [A
2025-07-28 09:50:18,724 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:03<00:37,  1.96s/it]
2025-07-28 09:50:18,736 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - [A
2025-07-28 09:50:19,174 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:04<00:22,  1.27s/it]
2025-07-28 09:50:19,175 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - [A
2025-07-28 09:50:19,293 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:04<00:05,  2.68it/s]
2025-07-28 09:50:19,298 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - [A
2025-07-28 09:50:19,404 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:04<00:01,  5.69it/s]
2025-07-28 09:50:19,405 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - [A
2025-07-28 09:50:19,521 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:04<00:00, 10.10it/s]
2025-07-28 09:50:19,522 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - [A
2025-07-28 09:50:19,888 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - [A
2025-07-28 09:50:44,207 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:50:44,216 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:50:44,235 - map_api - INFO - 找到 3 个目录
2025-07-28 09:50:44,241 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:50:44,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:50:44,254 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:50:44,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:50:44,264 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:50:44,271 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:50:44,276 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:50:44,287 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:50:44,292 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:50:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:50:53,877 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:50:53,889 - tif_api - INFO - 查询任务: fa4b06bc-66c8-4de0-a0bb-703173c8023c
2025-07-28 09:50:53,893 - tif_api - INFO - 任务 fa4b06bc-66c8-4de0-a0bb-703173c8023c 状态查询成功，当前状态: 正在运行
2025-07-28 09:50:53,898 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:50:53] "GET /api/tif/status?task_id=fa4b06bc-66c8-4de0-a0bb-703173c8023c HTTP/1.1" 200 -
2025-07-28 09:51:10,705 - map_api - INFO - 请求删除任务信息: 20250705171601
2025-07-28 09:51:10,716 - map_api - INFO - 请求删除文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:51:10,731 - map_api - ERROR - 删除文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 405
2025-07-28 09:51:10,749 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:51:10] "[31m[1mGET /api/map/odm/task/delete-info?task_id=20250705171601 HTTP/1.1[0m" 405 -
2025-07-28 09:51:44,209 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:51:44,214 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:51:44,239 - map_api - INFO - 找到 3 个目录
2025-07-28 09:51:44,241 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:51:44,248 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:51:44,253 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:51:44,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:51:44,262 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:51:44,269 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:51:44,271 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:51:44,273 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:51:44,277 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:51:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:51:51,709 - tif_task_fa4b06bc-66c8-4de0-a0bb-703173c8023c - ERROR - 
处理波段:  33%|########################3                                                | 1/3 [02:48<05:37, 168.73s/it]
2025-07-28 09:51:55,346 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:51:55,350 - tif_api - INFO - 查询任务: fa4b06bc-66c8-4de0-a0bb-703173c8023c
2025-07-28 09:51:55,354 - tif_api - INFO - 任务 fa4b06bc-66c8-4de0-a0bb-703173c8023c 状态查询成功，当前状态: 正在运行
2025-07-28 09:51:55,355 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:51:55] "GET /api/tif/status?task_id=fa4b06bc-66c8-4de0-a0bb-703173c8023c HTTP/1.1" 200 -
