2025-07-28 09:10:20,989 - INFO - ============ TIF处理任务 b6e90861-407a-4faf-9b63-e854fc59d3b3 开始执行 ============
2025-07-28 09:10:20,999 - INFO - 开始时间: 2025-07-28 09:10:20
2025-07-28 09:10:21,001 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 09:10:21,003 - INFO - 系统信息:
2025-07-28 09:10:21,005 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 09:10:21,007 - INFO -   Python版本: 3.8.20
2025-07-28 09:10:21,009 - INFO -   GDAL版本: 3.9.2
2025-07-28 09:10:21,010 - INFO -   GPU可用: 否
2025-07-28 09:10:21,011 - INFO - 检查参数有效性...
2025-07-28 09:10:21,011 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:10:21,013 - INFO - 开始执行TIF处理流程...
2025-07-28 09:10:21,030 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:10:21,033 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 09:10:21,033 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:10:21,043 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-28 09:10:40,576 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (09:10:39)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 09:10:40,667 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 09:10:40,913 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:04,  4.24it/s]
2025-07-28 09:10:41,530 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:01,  9.83it/s]
2025-07-28 09:10:41,714 - ERROR - 
处理数据块:  43%|##############################4                                        | 9/21 [00:01<00:01,  8.73it/s]
2025-07-28 09:10:41,834 - ERROR - 
处理数据块:  48%|#################################3                                    | 10/21 [00:01<00:01,  8.65it/s]
2025-07-28 09:10:41,924 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:01<00:00, 16.82it/s]
2025-07-28 09:10:41,931 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 09:10:41,932 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 09:10:41,974 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 512.19it/s]
2025-07-28 09:10:47,972 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 3.50 秒 (09:10:43)预计总处理时间: 约 10.51 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (09:10:43)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 09:10:47,975 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 09:11:11,486 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 09:11:11,510 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:11:11,511 - ERROR - [A
2025-07-28 09:11:11,647 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:01, 14.95it/s]
2025-07-28 09:11:11,648 - ERROR - [A
2025-07-28 09:11:11,814 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:01, 12.96it/s]
2025-07-28 09:11:11,815 - ERROR - [A
2025-07-28 09:11:12,059 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:01, 10.25it/s]
2025-07-28 09:11:12,059 - ERROR - [A
2025-07-28 09:11:12,322 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:00<00:01,  9.00it/s]
2025-07-28 09:11:12,322 - ERROR - [A
2025-07-28 09:11:12,440 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:01,  8.89it/s]
2025-07-28 09:11:12,440 - ERROR - [A
2025-07-28 09:11:12,551 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  8.91it/s]
2025-07-28 09:11:12,552 - ERROR - [A
2025-07-28 09:11:12,742 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:01<00:00,  9.50it/s]
2025-07-28 09:11:12,742 - ERROR - [A
2025-07-28 09:11:12,842 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:00,  9.59it/s]
2025-07-28 09:11:12,843 - ERROR - [A
2025-07-28 09:11:12,954 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:01<00:00,  9.41it/s]
2025-07-28 09:11:12,955 - ERROR - [A
2025-07-28 09:11:13,071 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:01<00:00,  9.17it/s]
2025-07-28 09:11:13,072 - ERROR - [A
2025-07-28 09:11:13,240 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:01<00:00,  8.00it/s]
2025-07-28 09:11:13,241 - ERROR - [A
2025-07-28 09:11:13,465 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:01<00:00,  6.55it/s]
2025-07-28 09:11:13,465 - ERROR - [A
2025-07-28 09:11:13,686 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:02<00:00,  5.81it/s]
2025-07-28 09:11:13,686 - ERROR - [A
2025-07-28 09:11:13,966 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:02<00:00,  4.91it/s]
2025-07-28 09:11:13,967 - ERROR - [A
2025-07-28 09:11:14,034 - ERROR - [A
2025-07-28 09:12:20,165 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:32<03:04, 92.18s/it]
2025-07-28 09:12:37,722 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 09:12:37,738 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:12:37,739 - ERROR - [A
2025-07-28 09:12:38,213 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:09,  2.11it/s]
2025-07-28 09:12:38,214 - ERROR - [A
2025-07-28 09:12:38,374 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:05,  3.45it/s]
2025-07-28 09:12:38,374 - ERROR - [A
2025-07-28 09:12:38,591 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:04,  3.90it/s]
2025-07-28 09:12:38,591 - ERROR - [A
2025-07-28 09:12:38,771 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:01<00:03,  4.42it/s]
2025-07-28 09:12:38,772 - ERROR - [A
2025-07-28 09:12:38,921 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:01<00:03,  5.02it/s]
2025-07-28 09:12:38,923 - ERROR - [A
2025-07-28 09:12:39,059 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:01<00:02,  5.61it/s]
2025-07-28 09:12:39,060 - ERROR - [A
2025-07-28 09:12:39,300 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  5.03it/s]
2025-07-28 09:12:39,301 - ERROR - [A
2025-07-28 09:12:39,476 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:02,  5.22it/s]
2025-07-28 09:12:39,476 - ERROR - [A
2025-07-28 09:12:39,621 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:02,  5.65it/s]
2025-07-28 09:12:39,622 - ERROR - [A
2025-07-28 09:12:39,771 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:02<00:01,  5.93it/s]
2025-07-28 09:12:39,773 - ERROR - [A
2025-07-28 09:12:40,006 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:02<00:01,  5.29it/s]
2025-07-28 09:12:40,007 - ERROR - [A
2025-07-28 09:12:40,184 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:02<00:01,  5.39it/s]
2025-07-28 09:12:40,184 - ERROR - [A
2025-07-28 09:12:40,410 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:02<00:01,  5.06it/s]
2025-07-28 09:12:40,412 - ERROR - [A
2025-07-28 09:12:40,572 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:01,  5.35it/s]
2025-07-28 09:12:40,573 - ERROR - [A
2025-07-28 09:12:40,937 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:03<00:01,  4.15it/s]
2025-07-28 09:12:40,938 - ERROR - [A
2025-07-28 09:12:42,733 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:04<00:03,  1.41it/s]
2025-07-28 09:12:42,734 - ERROR - [A
2025-07-28 09:12:46,663 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:08<00:06,  1.68s/it]
2025-07-28 09:12:46,664 - ERROR - [A
2025-07-28 09:12:51,879 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:14<00:08,  2.74s/it]
2025-07-28 09:12:51,879 - ERROR - [A
2025-07-28 09:12:58,428 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:20<00:07,  3.88s/it]
2025-07-28 09:12:58,429 - ERROR - [A
2025-07-28 09:13:03,980 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:26<00:04,  4.38s/it]
2025-07-28 09:13:03,981 - ERROR - [A
2025-07-28 09:13:05,173 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:27<00:00,  3.43s/it]
2025-07-28 09:13:05,174 - ERROR - [A
2025-07-28 09:13:05,177 - ERROR - [A
