2025-07-13 15:58:12,091 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250713_155812.log
2025-07-13 15:58:12,117 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-13 15:58:12,117 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-13 15:58:13,307 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-13 15:58:13,372 - batch_executor - INFO - 加载了 13 个任务状态
2025-07-13 15:58:13,389 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-13 15:58:13,390 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-13 15:58:13,412 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-13 15:58:13,422 - root - INFO - === GeoServer REST API服务 ===
2025-07-13 15:58:13,423 - root - INFO - 主机: 0.0.0.0
2025-07-13 15:58:13,423 - root - INFO - 端口: 5083
2025-07-13 15:58:13,423 - root - INFO - 调试模式: 禁用
2025-07-13 15:58:13,424 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-13 15:58:13,500 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://************:5083
2025-07-13 15:58:13,507 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 16:05:05,941 - werkzeug - INFO - ************ - - [13/Jul/2025 16:05:05] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:05:05,949 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:05:06,165 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:05:06,187 - werkzeug - INFO - ************ - - [13/Jul/2025 16:05:06] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:06:00,058 - werkzeug - INFO - ************ - - [13/Jul/2025 16:06:00] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:06:00,066 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:06:00,114 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:06:00,115 - werkzeug - INFO - ************ - - [13/Jul/2025 16:06:00] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:17:32,900 - werkzeug - INFO - ************ - - [13/Jul/2025 16:17:32] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:17:32,907 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:17:33,000 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:17:33,002 - werkzeug - INFO - ************ - - [13/Jul/2025 16:17:33] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:20:41,162 - werkzeug - INFO - ************ - - [13/Jul/2025 16:20:41] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:20:41,170 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:20:41,215 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:20:41,217 - werkzeug - INFO - ************ - - [13/Jul/2025 16:20:41] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:24:30,482 - werkzeug - INFO - ************ - - [13/Jul/2025 16:24:30] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:24:30,491 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:24:30,537 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:24:30,544 - werkzeug - INFO - ************ - - [13/Jul/2025 16:24:30] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:24:48,007 - werkzeug - INFO - ************ - - [13/Jul/2025 16:24:48] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:24:48,014 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:24:48,056 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:24:48,058 - werkzeug - INFO - ************ - - [13/Jul/2025 16:24:48] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:25:22,945 - werkzeug - INFO - ************ - - [13/Jul/2025 16:25:22] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:25:22,952 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:25:22,995 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:25:23,004 - werkzeug - INFO - ************ - - [13/Jul/2025 16:25:23] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:25:49,031 - werkzeug - INFO - ************ - - [13/Jul/2025 16:25:49] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:25:49,042 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:25:49,096 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:25:49,112 - werkzeug - INFO - ************ - - [13/Jul/2025 16:25:49] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:32:54,979 - werkzeug - INFO - ************ - - [13/Jul/2025 16:32:54] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:32:55,012 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:32:55,096 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:32:55,098 - werkzeug - INFO - ************ - - [13/Jul/2025 16:32:55] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:33:34,728 - werkzeug - INFO - ************ - - [13/Jul/2025 16:33:34] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:33:34,739 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:33:34,782 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:33:34,785 - werkzeug - INFO - ************ - - [13/Jul/2025 16:33:34] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:34:12,450 - werkzeug - INFO - ************ - - [13/Jul/2025 16:34:12] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:34:12,462 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:34:12,503 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:34:12,526 - werkzeug - INFO - ************ - - [13/Jul/2025 16:34:12] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:34:53,732 - werkzeug - INFO - ************ - - [13/Jul/2025 16:34:53] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:34:53,786 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:34:53,826 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:34:53,841 - werkzeug - INFO - ************ - - [13/Jul/2025 16:34:53] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:46:23,420 - werkzeug - INFO - ************ - - [13/Jul/2025 16:46:23] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:46:23,427 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:46:23,484 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:46:23,486 - werkzeug - INFO - ************ - - [13/Jul/2025 16:46:23] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:46:31,101 - werkzeug - INFO - ************ - - [13/Jul/2025 16:46:31] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:46:31,112 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:46:31,152 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:46:31,155 - werkzeug - INFO - ************ - - [13/Jul/2025 16:46:31] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:48:43,269 - werkzeug - INFO - ************ - - [13/Jul/2025 16:48:43] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:48:43,274 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:48:43,305 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:48:43,307 - werkzeug - INFO - ************ - - [13/Jul/2025 16:48:43] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:48:55,360 - werkzeug - INFO - ************ - - [13/Jul/2025 16:48:55] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:48:55,366 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:48:55,404 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:48:55,405 - werkzeug - INFO - ************ - - [13/Jul/2025 16:48:55] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:49:02,360 - werkzeug - INFO - ************ - - [13/Jul/2025 16:49:02] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:49:02,368 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:49:02,401 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:49:02,404 - werkzeug - INFO - ************ - - [13/Jul/2025 16:49:02] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:50:31,861 - werkzeug - INFO - ************ - - [13/Jul/2025 16:50:31] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:50:31,868 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:50:31,906 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:50:31,908 - werkzeug - INFO - ************ - - [13/Jul/2025 16:50:31] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:51:21,603 - werkzeug - INFO - ************ - - [13/Jul/2025 16:51:21] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:51:21,610 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:51:21,637 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:51:21,639 - werkzeug - INFO - ************ - - [13/Jul/2025 16:51:21] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:52:11,356 - werkzeug - INFO - ************ - - [13/Jul/2025 16:52:11] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:52:11,365 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:52:11,402 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:52:11,403 - werkzeug - INFO - ************ - - [13/Jul/2025 16:52:11] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:53:31,780 - werkzeug - INFO - ************ - - [13/Jul/2025 16:53:31] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:53:31,793 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:53:31,839 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:53:31,841 - werkzeug - INFO - ************ - - [13/Jul/2025 16:53:31] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:53:51,887 - werkzeug - INFO - ************ - - [13/Jul/2025 16:53:51] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:53:51,908 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:53:51,948 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:53:51,951 - werkzeug - INFO - ************ - - [13/Jul/2025 16:53:51] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:57:02,454 - werkzeug - INFO - ************ - - [13/Jul/2025 16:57:02] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:57:02,469 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:57:02,557 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:57:02,559 - werkzeug - INFO - ************ - - [13/Jul/2025 16:57:02] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:58:05,776 - werkzeug - INFO - ************ - - [13/Jul/2025 16:58:05] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:58:05,786 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:58:05,826 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:58:05,831 - werkzeug - INFO - ************ - - [13/Jul/2025 16:58:05] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:58:23,441 - werkzeug - INFO - ************ - - [13/Jul/2025 16:58:23] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:58:23,447 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:58:23,486 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:58:23,487 - werkzeug - INFO - ************ - - [13/Jul/2025 16:58:23] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:58:33,730 - werkzeug - INFO - ************ - - [13/Jul/2025 16:58:33] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 16:58:33,752 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 16:58:33,790 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 16:58:33,822 - werkzeug - INFO - ************ - - [13/Jul/2025 16:58:33] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:01:11,088 - werkzeug - INFO - ************ - - [13/Jul/2025 17:01:11] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:01:11,100 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 17:01:11,142 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 17:01:11,183 - werkzeug - INFO - ************ - - [13/Jul/2025 17:01:11] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:07:08,341 - werkzeug - INFO - ************ - - [13/Jul/2025 17:07:08] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:07:08,349 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 17:07:08,438 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 17:07:08,441 - werkzeug - INFO - ************ - - [13/Jul/2025 17:07:08] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:07:46,405 - werkzeug - INFO - ************ - - [13/Jul/2025 17:07:46] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:07:46,412 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 17:07:46,454 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 17:07:46,456 - werkzeug - INFO - ************ - - [13/Jul/2025 17:07:46] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:07:57,697 - werkzeug - INFO - ************ - - [13/Jul/2025 17:07:57] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:07:57,704 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 17:07:57,743 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 17:07:57,745 - werkzeug - INFO - ************ - - [13/Jul/2025 17:07:57] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:08:04,414 - werkzeug - INFO - ************ - - [13/Jul/2025 17:08:04] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:08:04,421 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 17:08:04,459 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 17:08:04,461 - werkzeug - INFO - ************ - - [13/Jul/2025 17:08:04] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:08:36,196 - werkzeug - INFO - ************ - - [13/Jul/2025 17:08:36] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:08:36,203 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 17:08:36,243 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 17:08:36,245 - werkzeug - INFO - ************ - - [13/Jul/2025 17:08:36] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:14:29,712 - werkzeug - INFO - ************ - - [13/Jul/2025 17:14:29] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:14:29,720 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 17:14:29,806 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 17:14:29,815 - werkzeug - INFO - ************ - - [13/Jul/2025 17:14:29] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:30:52,661 - werkzeug - INFO - ************ - - [13/Jul/2025 17:30:52] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:30:52,681 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 17:30:52,788 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 17:30:52,796 - werkzeug - INFO - ************ - - [13/Jul/2025 17:30:52] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:31:03,464 - werkzeug - INFO - ************ - - [13/Jul/2025 17:31:03] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:31:03,483 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 17:31:03,527 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 17:31:03,546 - werkzeug - INFO - ************ - - [13/Jul/2025 17:31:03] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:31:31,214 - werkzeug - INFO - ************ - - [13/Jul/2025 17:31:31] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:31:31,260 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 17:31:31,308 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 17:31:31,315 - werkzeug - INFO - ************ - - [13/Jul/2025 17:31:31] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:37:53,758 - werkzeug - INFO - ************ - - [13/Jul/2025 17:37:53] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:37:53,766 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 17:37:53,841 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 17:37:53,843 - werkzeug - INFO - ************ - - [13/Jul/2025 17:37:53] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:45:06,051 - werkzeug - INFO - ************ - - [13/Jul/2025 17:45:06] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 17:45:06,064 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 17:45:06,160 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 17:45:06,162 - werkzeug - INFO - ************ - - [13/Jul/2025 17:45:06] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 20:41:10,862 - werkzeug - INFO - ************ - - [13/Jul/2025 20:41:10] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 20:41:10,872 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 20:41:10,957 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 20:41:10,960 - werkzeug - INFO - ************ - - [13/Jul/2025 20:41:10] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 20:42:08,253 - werkzeug - INFO - ************ - - [13/Jul/2025 20:42:08] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 20:42:08,259 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 20:42:08,294 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 20:42:08,296 - werkzeug - INFO - ************ - - [13/Jul/2025 20:42:08] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 20:50:28,713 - werkzeug - INFO - ************ - - [13/Jul/2025 20:50:28] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 20:50:28,718 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 20:50:28,794 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 20:50:28,795 - werkzeug - INFO - ************ - - [13/Jul/2025 20:50:28] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 20:51:25,248 - werkzeug - INFO - ************ - - [13/Jul/2025 20:51:25] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 20:51:25,256 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 20:51:25,294 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 20:51:25,306 - werkzeug - INFO - ************ - - [13/Jul/2025 20:51:25] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 20:51:45,966 - werkzeug - INFO - ************ - - [13/Jul/2025 20:51:45] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 20:51:45,972 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 20:51:46,009 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 20:51:46,011 - werkzeug - INFO - ************ - - [13/Jul/2025 20:51:46] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:09:48,526 - werkzeug - INFO - ************ - - [13/Jul/2025 21:09:48] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:09:48,531 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:09:48,587 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:09:48,589 - werkzeug - INFO - ************ - - [13/Jul/2025 21:09:48] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:17:19,738 - werkzeug - INFO - ************ - - [13/Jul/2025 21:17:19] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:17:19,746 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:17:19,818 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:17:19,820 - werkzeug - INFO - ************ - - [13/Jul/2025 21:17:19] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:17:38,655 - werkzeug - INFO - ************ - - [13/Jul/2025 21:17:38] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:17:38,659 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:17:38,681 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:17:38,683 - werkzeug - INFO - ************ - - [13/Jul/2025 21:17:38] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:21:55,425 - werkzeug - INFO - ************ - - [13/Jul/2025 21:21:55] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:21:55,429 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:21:55,451 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:21:55,452 - werkzeug - INFO - ************ - - [13/Jul/2025 21:21:55] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:22:27,180 - werkzeug - INFO - ************ - - [13/Jul/2025 21:22:27] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:22:27,187 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:22:27,212 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:22:27,213 - werkzeug - INFO - ************ - - [13/Jul/2025 21:22:27] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:24:43,699 - werkzeug - INFO - ************ - - [13/Jul/2025 21:24:43] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:24:43,703 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:24:43,728 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:24:43,730 - werkzeug - INFO - ************ - - [13/Jul/2025 21:24:43] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:28:11,549 - werkzeug - INFO - ************ - - [13/Jul/2025 21:28:11] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:28:11,554 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:28:11,610 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:28:11,611 - werkzeug - INFO - ************ - - [13/Jul/2025 21:28:11] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:36:56,804 - werkzeug - INFO - ************ - - [13/Jul/2025 21:36:56] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:36:56,809 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:36:56,867 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:36:56,869 - werkzeug - INFO - ************ - - [13/Jul/2025 21:36:56] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:40:58,900 - werkzeug - INFO - ************ - - [13/Jul/2025 21:40:58] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:40:58,908 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:40:58,941 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:40:58,948 - werkzeug - INFO - ************ - - [13/Jul/2025 21:40:58] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:44:45,806 - werkzeug - INFO - ************ - - [13/Jul/2025 21:44:45] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:44:45,811 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:44:45,836 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:44:45,838 - werkzeug - INFO - ************ - - [13/Jul/2025 21:44:45] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:52:43,017 - werkzeug - INFO - ************ - - [13/Jul/2025 21:52:43] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:52:43,021 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:52:43,074 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:52:43,075 - werkzeug - INFO - ************ - - [13/Jul/2025 21:52:43] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:53:08,537 - werkzeug - INFO - ************ - - [13/Jul/2025 21:53:08] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:53:08,541 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:53:08,565 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:53:08,567 - werkzeug - INFO - ************ - - [13/Jul/2025 21:53:08] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:53:19,548 - werkzeug - INFO - ************ - - [13/Jul/2025 21:53:19] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:53:19,552 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:53:19,577 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:53:19,578 - werkzeug - INFO - ************ - - [13/Jul/2025 21:53:19] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:54:32,173 - werkzeug - INFO - ************ - - [13/Jul/2025 21:54:32] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:54:32,177 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:54:32,202 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:54:32,203 - werkzeug - INFO - ************ - - [13/Jul/2025 21:54:32] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:54:42,487 - werkzeug - INFO - ************ - - [13/Jul/2025 21:54:42] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:54:42,492 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:54:42,518 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:54:42,519 - werkzeug - INFO - ************ - - [13/Jul/2025 21:54:42] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:56:03,534 - werkzeug - INFO - ************ - - [13/Jul/2025 21:56:03] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:56:03,538 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:56:03,566 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:56:03,568 - werkzeug - INFO - ************ - - [13/Jul/2025 21:56:03] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:57:27,438 - werkzeug - INFO - ************ - - [13/Jul/2025 21:57:27] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:57:27,444 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:57:27,478 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:57:27,479 - werkzeug - INFO - ************ - - [13/Jul/2025 21:57:27] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:58:17,071 - werkzeug - INFO - ************ - - [13/Jul/2025 21:58:17] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:58:17,077 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:58:17,118 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:58:17,120 - werkzeug - INFO - ************ - - [13/Jul/2025 21:58:17] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:58:56,793 - werkzeug - INFO - ************ - - [13/Jul/2025 21:58:56] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:58:56,848 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:58:56,894 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:58:56,896 - werkzeug - INFO - ************ - - [13/Jul/2025 21:58:56] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:59:28,575 - werkzeug - INFO - ************ - - [13/Jul/2025 21:59:28] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 21:59:28,584 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 21:59:28,622 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 21:59:28,624 - werkzeug - INFO - ************ - - [13/Jul/2025 21:59:28] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:06:01,336 - werkzeug - INFO - ************ - - [13/Jul/2025 22:06:01] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:06:01,347 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 22:06:01,430 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 22:06:01,431 - werkzeug - INFO - ************ - - [13/Jul/2025 22:06:01] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:08:29,259 - werkzeug - INFO - ************ - - [13/Jul/2025 22:08:29] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:08:29,265 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 22:08:29,300 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 22:08:29,301 - werkzeug - INFO - ************ - - [13/Jul/2025 22:08:29] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:09:15,242 - werkzeug - INFO - ************ - - [13/Jul/2025 22:09:15] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:09:15,251 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 22:09:15,290 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 22:09:15,292 - werkzeug - INFO - ************ - - [13/Jul/2025 22:09:15] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:09:43,524 - werkzeug - INFO - ************ - - [13/Jul/2025 22:09:43] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:09:43,538 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 22:09:43,569 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 22:09:43,572 - werkzeug - INFO - ************ - - [13/Jul/2025 22:09:43] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:14:43,782 - werkzeug - INFO - ************ - - [13/Jul/2025 22:14:43] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:14:43,790 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 22:14:43,885 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 22:14:43,888 - werkzeug - INFO - ************ - - [13/Jul/2025 22:14:43] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:16:06,486 - werkzeug - INFO - ************ - - [13/Jul/2025 22:16:06] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:16:06,499 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 22:16:06,536 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 22:16:06,538 - werkzeug - INFO - ************ - - [13/Jul/2025 22:16:06] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:17:12,764 - werkzeug - INFO - ************ - - [13/Jul/2025 22:17:12] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:17:12,771 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 22:17:12,808 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 22:17:12,810 - werkzeug - INFO - ************ - - [13/Jul/2025 22:17:12] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:19:09,135 - werkzeug - INFO - ************ - - [13/Jul/2025 22:19:09] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:19:09,199 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 22:19:09,246 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 22:19:09,252 - werkzeug - INFO - ************ - - [13/Jul/2025 22:19:09] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:19:54,882 - werkzeug - INFO - ************ - - [13/Jul/2025 22:19:54] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:19:54,891 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 22:19:54,955 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 22:19:54,958 - werkzeug - INFO - ************ - - [13/Jul/2025 22:19:54] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:20:48,761 - werkzeug - INFO - ************ - - [13/Jul/2025 22:20:48] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-13 22:20:48,782 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-13 22:20:48,822 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-13 22:20:48,823 - werkzeug - INFO - ************ - - [13/Jul/2025 22:20:48] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
