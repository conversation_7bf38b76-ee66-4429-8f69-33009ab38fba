执行命令: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
开始时间: 2025-07-17 11:36:04

[INFO]    Initializing ODM 3.5.5 - Thu Jul 17 11:36:33  2025
[INFO]    ==============
[INFO]    3d_tiles: False
[INFO]    align: None
[INFO]    auto_boundary: False
[INFO]    auto_boundary_distance: 0
[INFO]    bg_removal: False
[INFO]    boundary: {}
[INFO]    build_overviews: False
[INFO]    camera_lens: auto
[INFO]    cameras: {}
[INFO]    cog: False
[INFO]    copy_to: None
[INFO]    crop: 3
[INFO]    dem_decimation: 1
[INFO]    dem_euclidean_map: False
[INFO]    dem_gapfill_steps: 3
[INFO]    dem_resolution: 5
[INFO]    dsm: False
[INFO]    dtm: False
[INFO]    end_with: odm_postprocess
[INFO]    fast_orthophoto: False
[INFO]    feature_quality: high
[INFO]    feature_type: dspsift
[INFO]    force_gps: False
[INFO]    gcp: None
[INFO]    geo: None
[INFO]    gltf: False
[INFO]    gps_accuracy: 3
[INFO]    ignore_gsd: False
[INFO]    matcher_neighbors: 0
[INFO]    matcher_order: 0
[INFO]    matcher_type: flann
[INFO]    max_concurrency: 8
[INFO]    merge: all
[INFO]    mesh_octree_depth: 11
[INFO]    mesh_size: 200000
[INFO]    min_num_features: 10000
[INFO]    name: D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
[INFO]    no_gpu: False
[INFO]    optimize_disk_space: False
[INFO]    orthophoto_compression: DEFLATE
[INFO]    orthophoto_cutline: False
[INFO]    orthophoto_kmz: False
[INFO]    orthophoto_no_tiled: False
[INFO]    orthophoto_png: False
[INFO]    orthophoto_resolution: 5
[INFO]    pc_classify: False
[INFO]    pc_copc: False
[INFO]    pc_csv: False
[INFO]    pc_ept: False
[INFO]    pc_filter: 5
[INFO]    pc_las: False
[INFO]    pc_quality: medium
[INFO]    pc_rectify: False
[INFO]    pc_sample: 0
[INFO]    pc_skip_geometric: False
[INFO]    primary_band: auto
[INFO]    project_path: D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
[INFO]    radiometric_calibration: none
[INFO]    rerun: None
[INFO]    rerun_all: False
[INFO]    rerun_from: None
[INFO]    rolling_shutter: False
[INFO]    rolling_shutter_readout: 0
[INFO]    sfm_algorithm: incremental
[INFO]    sfm_no_partial: False
[INFO]    skip_3dmodel: False
[INFO]    skip_band_alignment: False
[INFO]    skip_orthophoto: False
[INFO]    skip_report: False
[INFO]    sky_removal: False
[INFO]    sm_cluster: None
[INFO]    sm_no_align: False
[INFO]    smrf_scalar: 1.25
[INFO]    smrf_slope: 0.15
[INFO]    smrf_threshold: 0.5
[INFO]    smrf_window: 18.0
[INFO]    split: 999999
[INFO]    split_image_groups: None
[INFO]    split_overlap: 150
[INFO]    texturing_keep_unseen_faces: False
[INFO]    texturing_single_material: False
[INFO]    texturing_skip_global_seam_leveling: False
[INFO]    tiles: False
[INFO]    use_3dmesh: False
[INFO]    use_exif: False
[INFO]    use_fixed_camera_params: False
[INFO]    use_hybrid_bundle_adjustment: False
[INFO]    video_limit: 500
[INFO]    video_resolution: 4000
[INFO]    ==============
[INFO]    Running dataset stage
[INFO]    Loading dataset from: D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\images
[INFO]    Loading 98 images
[INFO]    Wrote images database: D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\images.json
[INFO]    Found 98 usable images
[INFO]    Model geo file already exist: D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\odm_georeferencing\odm_georeferencing_model_geo.txt
[INFO]    Finished dataset stage
[INFO]    Running split stage
[INFO]    Normal dataset, will process all at once.
[INFO]    Finished split stage
[INFO]    Running merge stage
[INFO]    Normal dataset, nothing to merge.
[INFO]    Finished merge stage
[INFO]    Running opensfm stage
[WARNING] D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm\image_list.txt already exists, not rerunning OpenSfM setup
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" detect_features "D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm"
2025-07-17 11:36:56,964 INFO: Planning to use 4513.130859375 MB of RAM for both processing queue and parallel processing.
2025-07-17 11:36:56,964 INFO: Scale-space expected size of a single image : 158.89835357666016 MB
2025-07-17 11:36:56,964 INFO: Expecting to queue at most 28 images while parallel processing of 8 images.
2025-07-17 11:36:57,006 INFO: Reading data for image DJI_20250705171600_0001_V.jpeg (queue-size=0)
2025-07-17 11:36:57,007 INFO: Reading data for image DJI_20250705171919_0027_V.jpeg (queue-size=0)
2025-07-17 11:36:57,007 INFO: Reading data for image DJI_20250705172237_0052_V.jpeg (queue-size=0)
2025-07-17 11:36:57,008 INFO: Reading data for image DJI_20250705172557_0077_V.jpeg (queue-size=0)
2025-07-17 11:36:58,090 INFO: Reading data for image DJI_20250705171611_0003_V.jpeg (queue-size=1)
2025-07-17 11:36:58,091 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171600_0001_V.jpeg
2025-07-17 11:36:58,175 INFO: Reading data for image DJI_20250705171927_0028_V.jpeg (queue-size=1)
2025-07-17 11:36:58,176 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171919_0027_V.jpeg
2025-07-17 11:36:58,453 INFO: Reading data for image DJI_20250705172245_0053_V.jpeg (queue-size=1)
2025-07-17 11:36:58,453 INFO: Reading data for image DJI_20250705172605_0078_V.jpeg (queue-size=2)
2025-07-17 11:36:58,454 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172557_0077_V.jpeg
2025-07-17 11:36:58,454 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172237_0052_V.jpeg
2025-07-17 11:36:59,618 INFO: Reading data for image DJI_20250705171619_0004_V.jpeg (queue-size=1)
2025-07-17 11:36:59,677 INFO: Reading data for image DJI_20250705171935_0029_V.jpeg (queue-size=2)
2025-07-17 11:37:00,272 INFO: Reading data for image DJI_20250705172254_0054_V.jpeg (queue-size=3)
2025-07-17 11:37:00,504 INFO: Reading data for image DJI_20250705172612_0079_V.jpeg (queue-size=4)
2025-07-17 11:37:01,631 INFO: Reading data for image DJI_20250705171944_0030_V.jpeg (queue-size=5)
2025-07-17 11:37:01,632 INFO: Reading data for image DJI_20250705171626_0005_V.jpeg (queue-size=6)
2025-07-17 11:37:03,418 INFO: Reading data for image DJI_20250705171952_0031_V.jpeg (queue-size=7)
2025-07-17 11:37:03,453 INFO: Reading data for image DJI_20250705172302_0055_V.jpeg (queue-size=8)
2025-07-17 11:37:03,494 INFO: Reading data for image DJI_20250705171634_0006_V.jpeg (queue-size=9)
2025-07-17 11:37:03,697 INFO: Reading data for image DJI_20250705172620_0080_V.jpeg (queue-size=10)
2025-07-17 11:37:05,534 INFO: Reading data for image DJI_20250705171642_0007_V.jpeg (queue-size=11)
2025-07-17 11:37:05,628 INFO: Reading data for image DJI_20250705172000_0032_V.jpeg (queue-size=12)
2025-07-17 11:37:07,331 INFO: Reading data for image DJI_20250705171650_0008_V.jpeg (queue-size=13)
2025-07-17 11:37:07,460 INFO: Reading data for image DJI_20250705172007_0033_V.jpeg (queue-size=14)
2025-07-17 11:37:07,758 INFO: Reading data for image DJI_20250705172310_0056_V.jpeg (queue-size=15)
2025-07-17 11:37:07,867 INFO: Reading data for image DJI_20250705172628_0081_V.jpeg (queue-size=16)
2025-07-17 11:37:08,902 INFO: Reading data for image DJI_20250705171658_0009_V.jpeg (queue-size=17)
2025-07-17 11:37:10,882 INFO: Reading data for image DJI_20250705172015_0034_V.jpeg (queue-size=18)
2025-07-17 11:37:10,909 INFO: Reading data for image DJI_20250705172318_0057_V.jpeg (queue-size=19)
2025-07-17 11:37:11,964 INFO: Reading data for image DJI_20250705171706_0010_V.jpeg (queue-size=20)
2025-07-17 11:37:12,564 INFO: Reading data for image DJI_20250705172636_0082_V.jpeg (queue-size=21)
2025-07-17 11:37:13,083 INFO: Reading data for image DJI_20250705172023_0035_V.jpeg (queue-size=22)
2025-07-17 11:37:13,924 INFO: Reading data for image DJI_20250705171714_0011_V.jpeg (queue-size=23)
2025-07-17 11:37:14,144 INFO: Reading data for image DJI_20250705172326_0058_V.jpeg (queue-size=24)
2025-07-17 11:37:15,518 INFO: Reading data for image DJI_20250705172031_0036_V.jpeg (queue-size=25)
2025-07-17 11:37:16,075 INFO: Reading data for image DJI_20250705172644_0083_V.jpeg (queue-size=26)
2025-07-17 11:37:16,233 INFO: Reading data for image DJI_20250705171722_0012_V.jpeg (queue-size=27)
2025-07-17 11:37:17,465 INFO: Reading data for image DJI_20250705172039_0037_V.jpeg (queue-size=28)
2025-07-17 11:37:29,433 DEBUG: Found 10000 points in 31.320025205612183s
2025-07-17 11:37:30,845 DEBUG: Found 10000 points in 32.647937297821045s
2025-07-17 11:37:32,786 INFO: Reading data for image DJI_20250705172334_0059_V.jpeg (queue-size=28)
2025-07-17 11:37:32,786 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171611_0003_V.jpeg
2025-07-17 11:37:33,351 INFO: Reading data for image DJI_20250705171730_0013_V.jpeg (queue-size=28)
2025-07-17 11:37:33,352 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171927_0028_V.jpeg
2025-07-17 11:37:49,087 DEBUG: Found 10000 points in 50.607441902160645s
2025-07-17 11:37:50,625 DEBUG: Found 10000 points in 52.14629530906677s
2025-07-17 11:37:54,024 INFO: Reading data for image DJI_20250705172046_0038_V.jpeg (queue-size=28)
2025-07-17 11:37:54,024 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172245_0053_V.jpeg
2025-07-17 11:37:54,180 INFO: Reading data for image DJI_20250705172652_0084_V.jpeg (queue-size=28)
2025-07-17 11:37:54,181 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172605_0078_V.jpeg
2025-07-17 11:37:57,064 DEBUG: Found 10000 points in 23.671476125717163s
2025-07-17 11:37:59,179 DEBUG: Found 10000 points in 26.339841842651367s
2025-07-17 11:38:00,030 INFO: Reading data for image DJI_20250705172342_0060_V.jpeg (queue-size=28)
2025-07-17 11:38:00,030 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171935_0029_V.jpeg
2025-07-17 11:38:02,401 INFO: Reading data for image DJI_20250705171738_0014_V.jpeg (queue-size=28)
2025-07-17 11:38:02,401 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171619_0004_V.jpeg
2025-07-17 11:38:26,313 DEBUG: Found 10000 points in 26.207518339157104s
2025-07-17 11:38:28,807 INFO: Reading data for image DJI_20250705172054_0039_V.jpeg (queue-size=28)
2025-07-17 11:38:28,810 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171944_0030_V.jpeg
2025-07-17 11:38:30,698 DEBUG: Found 10000 points in 28.260172367095947s
2025-07-17 11:38:32,538 INFO: Reading data for image DJI_20250705172700_0085_V.jpeg (queue-size=28)
2025-07-17 11:38:32,538 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172254_0054_V.jpeg
2025-07-17 11:38:43,470 DEBUG: Found 10000 points in 49.26169681549072s
2025-07-17 11:38:46,668 DEBUG: Found 10000 points in 52.602641344070435s
2025-07-17 11:38:49,187 INFO: Reading data for image DJI_20250705172350_0061_V.jpeg (queue-size=28)
2025-07-17 11:38:49,188 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171626_0005_V.jpeg
2025-07-17 11:38:51,272 INFO: Reading data for image DJI_20250705171745_0015_V.jpeg (queue-size=28)
2025-07-17 11:38:51,272 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172612_0079_V.jpeg
2025-07-17 11:38:54,181 DEBUG: Found 10000 points in 25.29141330718994s
2025-07-17 11:38:57,208 DEBUG: Found 10000 points in 24.61430597305298s
2025-07-17 11:38:59,011 INFO: Reading data for image DJI_20250705172102_0040_V.jpeg (queue-size=28)
2025-07-17 11:38:59,012 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171634_0006_V.jpeg
2025-07-17 11:39:01,793 INFO: Reading data for image DJI_20250705172708_0086_V.jpeg (queue-size=28)
2025-07-17 11:39:01,793 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171952_0031_V.jpeg
2025-07-17 11:39:24,740 DEBUG: Found 10000 points in 25.703595399856567s
2025-07-17 11:39:26,179 DEBUG: Found 10000 points in 24.364070892333984s
2025-07-17 11:39:27,868 DEBUG: Found 10000 points in 36.53118443489075s
2025-07-17 11:39:28,598 DEBUG: Found 10000 points in 39.37093496322632s
2025-07-17 11:39:29,393 INFO: Reading data for image DJI_20250705171753_0016_V.jpeg (queue-size=28)
2025-07-17 11:39:29,394 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171642_0007_V.jpeg
2025-07-17 11:39:36,498 INFO: Reading data for image DJI_20250705172358_0062_V.jpeg (queue-size=28)
2025-07-17 11:39:39,823 INFO: Reading data for image DJI_20250705172110_0041_V.jpeg (queue-size=28)
2025-07-17 11:39:39,823 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172000_0032_V.jpeg
2025-07-17 11:39:39,824 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172302_0055_V.jpeg
2025-07-17 11:39:39,958 INFO: Reading data for image DJI_20250705172716_0087_V.jpeg (queue-size=28)
2025-07-17 11:39:39,959 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172620_0080_V.jpeg
2025-07-17 11:39:51,090 DEBUG: Found 10000 points in 21.661500692367554s
2025-07-17 11:39:57,073 INFO: Reading data for image DJI_20250705171801_0017_V.jpeg (queue-size=28)
2025-07-17 11:39:57,073 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171650_0008_V.jpeg
2025-07-17 11:40:03,808 DEBUG: Found 10000 points in 23.95932102203369s
2025-07-17 11:40:06,063 INFO: Reading data for image DJI_20250705172406_0063_V.jpeg (queue-size=28)
2025-07-17 11:40:06,064 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172007_0033_V.jpeg
2025-07-17 11:40:16,854 DEBUG: Found 10000 points in 36.87182569503784s
2025-07-17 11:40:19,209 DEBUG: Found 10000 points in 39.34433960914612s
2025-07-17 11:40:20,780 DEBUG: Found 10000 points in 23.67208695411682s
2025-07-17 11:40:20,958 INFO: Reading data for image DJI_20250705172118_0042_V.jpeg (queue-size=28)
2025-07-17 11:40:20,958 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172310_0056_V.jpeg
2025-07-17 11:40:22,679 INFO: Reading data for image DJI_20250705172724_0088_V.jpeg (queue-size=28)
2025-07-17 11:40:22,726 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171658_0009_V.jpeg
2025-07-17 11:40:22,775 INFO: Reading data for image DJI_20250705171808_0018_V.jpeg (queue-size=28)
2025-07-17 11:40:22,779 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172628_0081_V.jpeg
2025-07-17 11:40:29,910 DEBUG: Found 10000 points in 23.811636447906494s
2025-07-17 11:40:31,686 INFO: Reading data for image DJI_20250705172414_0064_V.jpeg (queue-size=28)
2025-07-17 11:40:31,687 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172015_0034_V.jpeg
2025-07-17 11:40:44,000 DEBUG: Found 10000 points in 21.177905797958374s
2025-07-17 11:40:46,103 INFO: Reading data for image DJI_20250705172126_0043_V.jpeg (queue-size=28)
2025-07-17 11:40:46,103 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171706_0010_V.jpeg
2025-07-17 11:40:54,857 DEBUG: Found 10000 points in 23.13973379135132s
2025-07-17 11:40:56,127 INFO: Reading data for image DJI_20250705171816_0019_V.jpeg (queue-size=28)
2025-07-17 11:40:56,127 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172318_0057_V.jpeg
2025-07-17 11:41:03,894 DEBUG: Found 10000 points in 42.87038278579712s
2025-07-17 11:41:05,895 DEBUG: Found 10000 points in 43.14293813705444s
2025-07-17 11:41:06,456 INFO: Reading data for image DJI_20250705172732_0089_V.jpeg (queue-size=28)
2025-07-17 11:41:06,465 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172023_0035_V.jpeg
2025-07-17 11:41:08,348 DEBUG: Found 10000 points in 22.212315320968628s
2025-07-17 11:41:09,469 INFO: Reading data for image DJI_20250705172422_0065_V.jpeg (queue-size=28)
2025-07-17 11:41:09,470 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172636_0082_V.jpeg
2025-07-17 11:41:10,758 INFO: Reading data for image DJI_20250705172134_0044_V.jpeg (queue-size=28)
2025-07-17 11:41:10,761 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171714_0011_V.jpeg
2025-07-17 11:41:18,625 DEBUG: Found 10000 points in 22.45779252052307s
2025-07-17 11:41:20,140 INFO: Reading data for image DJI_20250705171824_0020_V.jpeg (queue-size=28)
2025-07-17 11:41:20,140 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172031_0036_V.jpeg
2025-07-17 11:41:33,637 DEBUG: Found 10000 points in 22.847238540649414s
2025-07-17 11:41:35,264 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172326_0058_V.jpeg
2025-07-17 11:41:35,264 INFO: Reading data for image DJI_20250705172739_0090_V.jpeg (queue-size=28)
2025-07-17 11:41:43,607 DEBUG: Found 10000 points in 23.438080310821533s
2025-07-17 11:41:44,766 INFO: Reading data for image DJI_20250705172429_0066_V.jpeg (queue-size=28)
2025-07-17 11:41:44,768 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171722_0012_V.jpeg
2025-07-17 11:41:46,785 DEBUG: Found 10000 points in 40.26301574707031s
2025-07-17 11:41:46,958 DEBUG: Found 10000 points in 37.45956349372864s
2025-07-17 11:41:50,034 INFO: Reading data for image DJI_20250705172143_0045_V.jpeg (queue-size=28)
2025-07-17 11:41:50,034 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172039_0037_V.jpeg
2025-07-17 11:41:50,222 INFO: Reading data for image DJI_20250705171832_0021_V.jpeg (queue-size=28)
2025-07-17 11:41:50,223 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172644_0083_V.jpeg
2025-07-17 11:41:58,361 DEBUG: Found 10000 points in 23.066115856170654s
2025-07-17 11:41:59,590 INFO: Reading data for image DJI_20250705172748_0091_V.jpeg (queue-size=28)
2025-07-17 11:41:59,590 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172334_0059_V.jpeg
2025-07-17 11:42:09,458 DEBUG: Found 10000 points in 24.66298770904541s
2025-07-17 11:42:12,350 INFO: Reading data for image DJI_20250705172437_0067_V.jpeg (queue-size=28)
2025-07-17 11:42:12,351 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171730_0013_V.jpeg
2025-07-17 11:42:24,624 DEBUG: Found 10000 points in 25.003490686416626s
2025-07-17 11:42:26,509 INFO: Reading data for image DJI_20250705172150_0046_V.jpeg (queue-size=28)
2025-07-17 11:42:26,509 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172046_0038_V.jpeg
2025-07-17 11:42:36,816 DEBUG: Found 10000 points in 46.493287801742554s
2025-07-17 11:42:38,870 DEBUG: Found 10000 points in 48.801517963409424s
2025-07-17 11:42:38,933 DEBUG: Found 10000 points in 26.554516553878784s
2025-07-17 11:42:39,555 INFO: Reading data for image DJI_20250705171840_0022_V.jpeg (queue-size=28)
2025-07-17 11:42:39,556 INFO: Skip recomputing ROOT_DSPSIFT features for image DJI_20250705172652_0084_V.jpeg
2025-07-17 11:42:39,563 INFO: Reading data for image DJI_20250705172756_0092_V.jpeg (queue-size=28)
2025-07-17 11:42:39,563 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172342_0060_V.jpeg
2025-07-17 11:42:43,685 INFO: Reading data for image DJI_20250705172445_0068_V.jpeg (queue-size=28)
2025-07-17 11:42:43,686 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171738_0014_V.jpeg
2025-07-17 11:42:43,695 INFO: Reading data for image DJI_20250705172158_0047_V.jpeg (queue-size=28)
2025-07-17 11:42:43,695 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172054_0039_V.jpeg
2025-07-17 11:42:52,438 DEBUG: Found 10000 points in 25.87069797515869s
2025-07-17 11:42:56,407 INFO: Reading data for image DJI_20250705171848_0023_V.jpeg (queue-size=28)
2025-07-17 11:42:56,408 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172700_0085_V.jpeg
2025-07-17 11:43:13,743 DEBUG: Found 10000 points in 30.025020122528076s
2025-07-17 11:43:19,761 INFO: Reading data for image DJI_20250705172804_0093_V.jpeg (queue-size=28)
2025-07-17 11:43:19,761 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171745_0015_V.jpeg
2025-07-17 11:43:19,762 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172350_0061_V.jpeg
2025-07-17 11:43:19,763 INFO: Reading data for image DJI_20250705172205_0048_V.jpeg (queue-size=27)
2025-07-17 11:43:19,763 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172102_0040_V.jpeg
2025-07-17 11:43:19,765 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172708_0086_V.jpeg
2025-07-17 11:43:19,765 INFO: Reading data for image DJI_20250705171856_0024_V.jpeg (queue-size=27)
2025-07-17 11:43:19,767 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171753_0016_V.jpeg
2025-07-17 11:43:19,767 INFO: Reading data for image DJI_20250705172453_0069_V.jpeg (queue-size=27)
2025-07-17 11:43:23,021 INFO: Reading data for image DJI_20250705172811_0094_V.jpeg (queue-size=28)
2025-07-17 11:43:24,385 DEBUG: Found 10000 points in 27.907576084136963s
2025-07-17 11:43:26,774 DEBUG: Found 10000 points in 47.123053312301636s
2025-07-17 11:43:30,405 DEBUG: Found 10000 points in 46.654895067214966s
2025-07-17 11:43:34,088 INFO: Reading data for image DJI_20250705172213_0049_V.jpeg (queue-size=28)
2025-07-17 11:43:34,088 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172358_0062_V.jpeg
2025-07-17 11:43:37,983 INFO: Reading data for image DJI_20250705171904_0025_V.jpeg (queue-size=28)
2025-07-17 11:43:37,984 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172110_0041_V.jpeg
2025-07-17 11:43:38,112 INFO: Reading data for image DJI_20250705172501_0070_V.jpeg (queue-size=28)
2025-07-17 11:43:38,113 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172716_0087_V.jpeg
Traceback (most recent call last):
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 25, in <module>
    commands.command_runner(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command_runner.py", line 38, in command_runner
    command.run(data, args)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command.py", line 13, in run
    self.run_impl(data, args)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\detect_features.py", line 13, in run_impl
    detect_features.run_dataset(dataset)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\actions\detect_features.py", line 15, in run_dataset
    features_processing.run_features_processing(data, data.images(), False)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\features_processing.py", line 79, in run_features_processing
    parallel_map(process, arguments, processes, 1)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\context.py", line 59, in parallel_map
    res = Parallel(batch_size=batch_size)(delayed(func)(arg) for arg in args)
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\joblib\parallel.py", line 1056, in __call__
    self.retrieve()
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\joblib\parallel.py", line 935, in retrieve
    self._output.extend(job.get(timeout=self.timeout))
  File "multiprocessing\pool.py", line 768, in get
  File "multiprocessing\pool.py", line 125, in worker
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\joblib\_parallel_backends.py", line 595, in __call__
    return self.func(*args, **kwargs)
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\joblib\parallel.py", line 262, in __call__
    return [func(*args, **kwargs)
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\joblib\parallel.py", line 262, in <listcomp>
    return [func(*args, **kwargs)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\features_processing.py", line 146, in process
    read_images(queue, data, images, counter, expected, force)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\features_processing.py", line 170, in read_images
    queue.put(args, block=True, timeout=full_queue_timeout)
  File "queue.py", line 147, in put
queue.Full
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" match_features "D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm"
Traceback (most recent call last):
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 25, in <module>
    commands.command_runner(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command_runner.py", line 34, in command_runner
    with dataset_factory(args.dataset, args.dataset_type) as data:
  File "contextlib.py", line 113, in __enter__
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 17, in create_default_dataset_context
    dataset = DataSet(dataset_path)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 53, in __init__
    self.load_image_list()
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 83, in load_image_list
    raise IOError("No Images found in {}".format(image_list_path))
OSError: No Images found in D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm\images
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" create_tracks "D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm"
Traceback (most recent call last):
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 25, in <module>
    commands.command_runner(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command_runner.py", line 34, in command_runner
    with dataset_factory(args.dataset, args.dataset_type) as data:
  File "contextlib.py", line 113, in __enter__
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 17, in create_default_dataset_context
    dataset = DataSet(dataset_path)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 53, in __init__
    self.load_image_list()
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 83, in load_image_list
    raise IOError("No Images found in {}".format(image_list_path))
OSError: No Images found in D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm\images
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" reconstruct "D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm"
Traceback (most recent call last):
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 25, in <module>
    commands.command_runner(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command_runner.py", line 34, in command_runner
    with dataset_factory(args.dataset, args.dataset_type) as data:
  File "contextlib.py", line 113, in __enter__
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 17, in create_default_dataset_context
    dataset = DataSet(dataset_path)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 53, in __init__
    self.load_image_list()
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 83, in load_image_list
    raise IOError("No Images found in {}".format(image_list_path))
OSError: No Images found in D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm\images
[ERROR]   The program could not process this dataset using the current settings. Check that the images have enough overlap, that there are enough recognizable features and that the images are in focus. The program will now exit.

完成时间: 2025-07-17 12:43:55
返回代码: 0
状态: 运行成功
