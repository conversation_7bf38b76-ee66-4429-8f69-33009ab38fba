2025-07-17 15:12:43,934 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250717_151243.log
2025-07-17 15:12:43,937 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-17 15:12:43,942 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-17 15:12:46,727 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-17 15:12:46,791 - batch_executor - INFO - 加载了 40 个任务状态
2025-07-17 15:12:56,099 - tif_executor - INFO - 加载了 5 个任务状态
2025-07-17 15:12:56,181 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-17 15:12:56,195 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-17 15:12:56,222 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-17 15:12:56,234 - root - INFO - === GeoServer REST API服务 ===
2025-07-17 15:12:56,237 - root - INFO - 主机: 0.0.0.0
2025-07-17 15:12:56,242 - root - INFO - 端口: 5083
2025-07-17 15:12:56,254 - root - INFO - 调试模式: 禁用
2025-07-17 15:12:56,258 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-17 15:12:56,323 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-17 15:12:56,361 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-17 17:30:01,757 - batch_executor - INFO - 启动任务 0f79f411-135d-42fa-8272-dd88ef9bfdb3: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
2025-07-17 17:30:01,759 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:30:01] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-17 17:30:01,798 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:30:01] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:31:05,387 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:31:05] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:31:43,412 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:31:43] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:31:43,538 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:31:43] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-17 17:32:09,252 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:32:09] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:33:13,485 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:33:13] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:34:17,124 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:34:17] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:34:42,012 - batch_executor - INFO - 启动任务 0100fe85-cb66-4ac7-9ac5-c1a41c1436f2: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
2025-07-17 17:34:42,014 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:34:42] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-17 17:34:42,036 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:34:42] "GET /api/batch/status?task_id=0100fe85-cb66-4ac7-9ac5-c1a41c1436f2 HTTP/1.1" 200 -
2025-07-17 17:34:43,754 - batch_executor - INFO - 启动任务 775837a8-4aeb-4f08-87df-0f7d1506b0b9: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
2025-07-17 17:34:43,756 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:34:43] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-17 17:34:44,401 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:34:44] "GET /api/batch/status?task_id=775837a8-4aeb-4f08-87df-0f7d1506b0b9 HTTP/1.1" 200 -
2025-07-17 17:35:21,094 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:35:21] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:35:45,507 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:35:45] "GET /api/batch/status?task_id=0100fe85-cb66-4ac7-9ac5-c1a41c1436f2 HTTP/1.1" 200 -
2025-07-17 17:35:47,938 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:35:47] "GET /api/batch/status?task_id=775837a8-4aeb-4f08-87df-0f7d1506b0b9 HTTP/1.1" 200 -
2025-07-17 17:36:25,406 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:36:25] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:36:49,948 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:36:49] "GET /api/batch/status?task_id=0100fe85-cb66-4ac7-9ac5-c1a41c1436f2 HTTP/1.1" 200 -
2025-07-17 17:36:52,586 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:36:52] "GET /api/batch/status?task_id=775837a8-4aeb-4f08-87df-0f7d1506b0b9 HTTP/1.1" 200 -
2025-07-17 17:36:53,960 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:36:53] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:36:56,565 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:36:56] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:37:29,816 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:37:29] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:37:57,708 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:37:57] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:38:00,437 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:38:00] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:38:33,881 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:38:33] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:39:01,585 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:39:01] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:39:04,578 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:39:04] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:39:39,985 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:39:39] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:40:05,657 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:40:05] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:40:08,109 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:40:08] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:40:42,156 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:40:42] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:41:08,007 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:41:08] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:41:10,613 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:41:10] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:41:44,699 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:41:44] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:42:11,894 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:42:11] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:42:14,185 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:42:14] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:42:48,793 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:42:48] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:43:15,910 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:43:15] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:43:18,009 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:43:18] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:43:52,298 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:43:52] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:44:19,512 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:44:19] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:44:21,634 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:44:21] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:44:55,772 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:44:55] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:45:23,231 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:45:23] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:45:25,021 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:45:25] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:45:59,198 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:45:59] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:46:26,637 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:46:26] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:46:28,466 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:46:28] "[33mGET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
2025-07-17 17:47:03,049 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:47:03] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:48:06,603 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:48:06] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:49:10,058 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:49:10] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:50:14,011 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:50:14] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:51:18,720 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:51:18] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:52:22,330 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:52:22] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:53:25,821 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:53:25] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:54:29,307 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:54:29] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:55:32,756 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:55:32] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:56:36,206 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:56:36] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:57:39,606 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:57:39] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:58:42,968 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:58:42] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 17:59:46,347 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 17:59:46] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:00:49,662 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:00:49] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:01:51,182 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:01:51] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:02:52,746 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:02:52] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:03:53,821 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:03:53] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:04:54,965 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:04:54] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:05:56,532 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:05:56] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:06:57,570 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:06:57] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:07:58,639 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:07:58] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:08:59,344 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:08:59] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:09:59,895 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:09:59] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:11:00,348 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:11:00] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:12:00,825 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:12:00] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:13:01,355 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:13:01] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:14:02,114 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:14:02] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:15:03,308 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:15:03] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:16:03,590 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:16:03] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:17:03,617 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:17:03] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:18:03,689 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:18:03] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:19:03,726 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:19:03] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:20:03,741 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:20:03] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:21:03,760 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:21:03] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:22:04,067 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:22:04] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:23:05,049 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:23:05] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:24:05,937 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:24:05] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:25:05,955 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:25:05] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:26:05,974 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:26:05] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:27:05,991 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:27:05] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:28:06,005 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:28:06] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:29:06,023 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:29:06] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:30:06,043 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:30:06] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:31:06,074 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:31:06] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:32:06,164 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:32:06] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:33:06,454 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:33:06] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:34:06,477 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:34:06] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:35:07,264 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:35:07] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:36:07,605 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:36:07] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:37:07,626 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:37:07] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:38:07,642 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:38:07] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:39:07,658 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:39:07] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:40:07,727 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:40:07] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:41:07,826 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:41:07] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:42:07,852 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:42:07] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:43:07,875 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:43:07] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:44:07,893 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:44:07] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:45:07,907 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:45:07] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:46:07,924 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:46:07] "GET /api/batch/status?task_id=0f79f411-135d-42fa-8272-dd88ef9bfdb3 HTTP/1.1" 200 -
2025-07-17 18:46:09,618 - tif_executor - INFO - 启动TIF处理任务 5f3f6493-e2d8-451c-a8a2-3745ce521c0b: D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp
2025-07-17 18:46:09,622 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - INFO - TIF处理任务 5f3f6493-e2d8-451c-a8a2-3745ce521c0b 开始执行
2025-07-17 18:46:09,625 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:46:09] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-17 18:46:09,630 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - INFO - 开始时间: 2025-07-17 18:46:09
2025-07-17 18:46:09,635 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - INFO - 处理参数: {
  "input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-17 18:46:09,666 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-17 18:46:09,669 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:46:09] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:46:24,609 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 38572x48224x3估计内存使用: 总计 27.72 GB, 单个处理块 588.56 MB开始创建掩码... (18:46:24)使用全图检测无效区域模式...将处理 49 个数据块...使用多线程处理 (7 线程)...
2025-07-17 18:46:24,619 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:   0%|                                                                               | 0/49 [00:00<?, ?it/s]
2025-07-17 18:46:25,207 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:   2%|#4                                                                     | 1/49 [00:00<00:26,  1.79it/s]
2025-07-17 18:46:25,725 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  16%|###########5                                                           | 8/49 [00:01<00:04,  8.46it/s]
2025-07-17 18:46:25,837 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  18%|#############                                                          | 9/49 [00:01<00:04,  8.54it/s]
2025-07-17 18:46:26,346 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  31%|#####################4                                                | 15/49 [00:01<00:03, 10.15it/s]
2025-07-17 18:46:26,644 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  33%|######################8                                               | 16/49 [00:01<00:04,  8.16it/s]
2025-07-17 18:46:26,914 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  45%|###############################4                                      | 22/49 [00:02<00:02, 12.01it/s]
2025-07-17 18:46:27,234 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  49%|##################################2                                   | 24/49 [00:02<00:02, 10.18it/s]
2025-07-17 18:46:27,397 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  53%|#####################################1                                | 26/49 [00:02<00:02, 10.57it/s]
2025-07-17 18:46:27,627 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  59%|#########################################4                            | 29/49 [00:02<00:01, 11.26it/s]
2025-07-17 18:46:27,908 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  63%|############################################2                         | 31/49 [00:03<00:01,  9.91it/s]
2025-07-17 18:46:28,075 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  69%|################################################5                     | 34/49 [00:03<00:01, 11.65it/s]
2025-07-17 18:46:28,306 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  73%|###################################################4                  | 36/49 [00:03<00:01, 10.78it/s]
2025-07-17 18:46:28,570 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  78%|######################################################2               | 38/49 [00:03<00:01,  9.72it/s]
2025-07-17 18:46:28,672 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  82%|#########################################################1            | 40/49 [00:04<00:00, 11.22it/s]
2025-07-17 18:46:28,899 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  88%|#############################################################4        | 43/49 [00:04<00:00, 11.86it/s]
2025-07-17 18:46:30,833 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块:  92%|################################################################2     | 45/49 [00:06<00:01,  3.21it/s]
2025-07-17 18:46:30,895 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理数据块: 100%|######################################################################| 49/49 [00:06<00:00,  7.84it/s]
2025-07-17 18:46:30,899 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - INFO - 已完成: 10/49 块 (20.4%)已完成: 20/49 块 (40.8%)已完成: 30/49 块 (61.2%)已完成: 40/49 块 (81.6%)已完成: 49/49 块 (100.0%)合并处理结果...
2025-07-17 18:46:30,900 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
合并结果:   0%|                                                                                 | 0/49 [00:00<?, ?it/s]
2025-07-17 18:46:31,002 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
合并结果:  24%|#################3                                                     | 12/49 [00:00<00:00, 119.99it/s]
2025-07-17 18:46:31,107 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
合并结果:  51%|####################################2                                  | 25/49 [00:00<00:00, 122.27it/s]
2025-07-17 18:46:31,213 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
合并结果:  78%|#######################################################                | 38/49 [00:00<00:00, 122.98it/s]
2025-07-17 18:46:31,325 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
合并结果: 100%|#######################################################################| 49/49 [00:00<00:00, 115.95it/s]
2025-07-17 18:46:37,394 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - INFO - 合并进度: 20/49 块 (40.8%)合并进度: 40/49 块 (81.6%)合并进度: 49/49 块 (100.0%)统计结果: 总像素 1860096128, 有效像素 439865224 (23.65%), 无效像素 1420230904 (76.35%)掩码创建完成，耗时: 13.15 秒 (18:46:37)预计总处理时间: 约 39.44 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif (18:46:37)写入带有nodata值的影像...
2025-07-17 18:46:37,395 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-17 18:46:51,591 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/49 [00:00<?, ?it/s]
2025-07-17 18:46:51,591 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:46:51,707 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:   6%|###9                                                            | 3/49 [00:00<00:01, 26.78it/s]
2025-07-17 18:46:51,708 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:46:51,837 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  12%|#######8                                                        | 6/49 [00:00<00:01, 24.54it/s]
2025-07-17 18:46:51,840 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:46:51,988 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  18%|###########7                                                    | 9/49 [00:00<00:01, 22.19it/s]
2025-07-17 18:46:51,988 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:46:54,169 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  24%|###############4                                               | 12/49 [00:02<00:11,  3.18it/s]
2025-07-17 18:46:54,169 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:47:04,626 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  24%|###############4                                               | 12/49 [00:13<00:11,  3.18it/s]
2025-07-17 18:47:04,697 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:47:06,253 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  29%|##################                                             | 14/49 [00:14<01:05,  1.88s/it]
2025-07-17 18:47:06,254 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:47:09,802 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:47:09] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:47:12,836 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  31%|###################2                                           | 15/49 [00:21<01:30,  2.65s/it]
2025-07-17 18:47:12,838 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:47:17,911 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  33%|####################5                                          | 16/49 [00:26<01:42,  3.11s/it]
2025-07-17 18:47:17,912 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:47:23,917 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  35%|#####################8                                         | 17/49 [00:32<01:59,  3.72s/it]
2025-07-17 18:47:23,919 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:47:29,638 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  37%|#######################1                                       | 18/49 [00:38<02:09,  4.19s/it]
2025-07-17 18:47:29,639 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:47:34,571 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  39%|########################4                                      | 19/49 [00:42<02:11,  4.38s/it]
2025-07-17 18:47:34,572 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:47:40,891 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  41%|#########################7                                     | 20/49 [00:49<02:21,  4.89s/it]
2025-07-17 18:47:40,891 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:47:45,486 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  43%|###########################                                    | 21/49 [00:53<02:14,  4.81s/it]
2025-07-17 18:47:45,487 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:47:49,854 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  45%|############################2                                  | 22/49 [00:58<02:06,  4.68s/it]
2025-07-17 18:47:49,855 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:47:55,087 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  47%|#############################5                                 | 23/49 [01:03<02:05,  4.84s/it]
2025-07-17 18:47:55,088 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:48:01,004 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  49%|##############################8                                | 24/49 [01:09<02:08,  5.15s/it]
2025-07-17 18:48:01,004 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:48:06,673 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  51%|################################1                              | 25/49 [01:15<02:07,  5.30s/it]
2025-07-17 18:48:06,674 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:48:09,947 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:48:09] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:48:12,223 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  53%|#################################4                             | 26/49 [01:20<02:03,  5.38s/it]
2025-07-17 18:48:12,223 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:48:17,384 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  55%|##################################7                            | 27/49 [01:25<01:56,  5.31s/it]
2025-07-17 18:48:17,384 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:48:21,683 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 28/49 [01:30<01:45,  5.01s/it]
2025-07-17 18:48:21,683 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:48:28,691 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  59%|#####################################2                         | 29/49 [01:37<01:52,  5.61s/it]
2025-07-17 18:48:28,691 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:48:32,569 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  61%|######################################5                        | 30/49 [01:40<01:36,  5.09s/it]
2025-07-17 18:48:32,569 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:48:37,766 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  63%|#######################################8                       | 31/49 [01:46<01:32,  5.12s/it]
2025-07-17 18:48:37,767 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:48:43,266 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  65%|#########################################1                     | 32/49 [01:51<01:29,  5.24s/it]
2025-07-17 18:48:43,267 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:48:47,504 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  67%|##########################################4                    | 33/49 [01:55<01:18,  4.94s/it]
2025-07-17 18:48:47,504 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:48:52,242 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  69%|###########################################7                   | 34/49 [02:00<01:13,  4.88s/it]
2025-07-17 18:48:52,243 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:48:57,459 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 35/49 [02:05<01:09,  4.98s/it]
2025-07-17 18:48:57,459 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:49:01,668 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  73%|##############################################2                | 36/49 [02:10<01:01,  4.75s/it]
2025-07-17 18:49:01,669 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:49:07,367 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  76%|###############################################5               | 37/49 [02:15<01:00,  5.03s/it]
2025-07-17 18:49:07,368 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:49:09,971 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:49:09] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:49:13,582 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  78%|################################################8              | 38/49 [02:21<00:59,  5.39s/it]
2025-07-17 18:49:13,582 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:49:17,518 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  80%|##################################################1            | 39/49 [02:25<00:49,  4.95s/it]
2025-07-17 18:49:17,519 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:49:21,620 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  82%|###################################################4           | 40/49 [02:30<00:42,  4.70s/it]
2025-07-17 18:49:21,621 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:49:26,315 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  84%|####################################################7          | 41/49 [02:34<00:37,  4.70s/it]
2025-07-17 18:49:26,316 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:49:31,419 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 42/49 [02:39<00:33,  4.82s/it]
2025-07-17 18:49:31,420 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:49:37,004 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  88%|#######################################################2       | 43/49 [02:45<00:30,  5.05s/it]
2025-07-17 18:49:37,005 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:49:43,398 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  90%|########################################################5      | 44/49 [02:51<00:27,  5.45s/it]
2025-07-17 18:49:43,399 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:49:47,924 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  92%|#########################################################8     | 45/49 [02:56<00:20,  5.17s/it]
2025-07-17 18:49:47,924 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:49:52,299 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  94%|###########################################################1   | 46/49 [03:00<00:14,  4.93s/it]
2025-07-17 18:49:52,300 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:49:57,742 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  96%|############################################################4  | 47/49 [03:06<00:10,  5.09s/it]
2025-07-17 18:49:57,744 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:50:02,991 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度:  98%|#############################################################7 | 48/49 [03:11<00:05,  5.14s/it]
2025-07-17 18:50:02,992 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:50:04,832 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 1/3 写入进度: 100%|###############################################################| 49/49 [03:13<00:00,  4.15s/it]
2025-07-17 18:50:04,832 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:50:04,837 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:50:09,997 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:50:09] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:50:56,394 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理波段:  33%|########################3                                                | 1/3 [04:18<08:37, 258.99s/it]
2025-07-17 18:51:10,657 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:51:10] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:51:25,574 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/49 [00:00<?, ?it/s]
2025-07-17 18:51:25,590 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:51:26,193 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:   2%|#3                                                              | 1/49 [00:00<00:28,  1.67it/s]
2025-07-17 18:51:26,194 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:51:26,339 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:   6%|###9                                                            | 3/49 [00:00<00:09,  4.77it/s]
2025-07-17 18:51:26,340 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:51:26,492 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  10%|######5                                                         | 5/49 [00:00<00:06,  7.09it/s]
2025-07-17 18:51:26,493 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:51:26,662 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 7/49 [00:01<00:04,  8.53it/s]
2025-07-17 18:51:26,662 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:51:26,810 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  18%|###########7                                                    | 9/49 [00:01<00:04,  9.89it/s]
2025-07-17 18:51:26,810 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:51:26,972 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  22%|##############1                                                | 11/49 [00:01<00:03, 10.65it/s]
2025-07-17 18:51:26,972 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:51:45,030 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  22%|##############1                                                | 11/49 [00:19<00:03, 10.65it/s]
2025-07-17 18:51:45,030 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:51:51,804 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  27%|################7                                              | 13/49 [00:26<02:31,  4.20s/it]
2025-07-17 18:51:51,805 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:52:07,389 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  29%|##################                                             | 14/49 [00:41<03:43,  6.39s/it]
2025-07-17 18:52:07,390 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:52:10,928 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:52:10] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:52:23,478 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  31%|###################2                                           | 15/49 [00:57<04:48,  8.48s/it]
2025-07-17 18:52:23,479 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:52:38,231 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  33%|####################5                                          | 16/49 [01:12<05:28,  9.96s/it]
2025-07-17 18:52:38,231 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:52:55,280 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  35%|#####################8                                         | 17/49 [01:29<06:15, 11.74s/it]
2025-07-17 18:52:55,281 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:53:10,949 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:53:10] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:53:12,029 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  37%|#######################1                                       | 18/49 [01:46<06:45, 13.07s/it]
2025-07-17 18:53:12,029 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:54:10,967 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:54:10] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:54:22,267 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  39%|########################4                                      | 19/49 [02:56<14:22, 28.74s/it]
2025-07-17 18:54:22,268 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:55:10,983 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:55:10] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:55:55,279 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  41%|#########################7                                     | 20/49 [04:29<22:38, 46.83s/it]
2025-07-17 18:55:55,281 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:56:11,002 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:56:11] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:56:17,099 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  43%|###########################                                    | 21/49 [04:51<18:30, 39.66s/it]
2025-07-17 18:56:17,101 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:56:32,025 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  45%|############################2                                  | 22/49 [05:06<14:36, 32.47s/it]
2025-07-17 18:56:32,026 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:56:46,529 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  47%|#############################5                                 | 23/49 [05:20<11:47, 27.20s/it]
2025-07-17 18:56:46,531 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:57:01,916 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  49%|##############################8                                | 24/49 [05:36<09:52, 23.71s/it]
2025-07-17 18:57:01,916 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:57:11,026 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:57:11] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:57:16,731 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  51%|################################1                              | 25/49 [05:51<08:25, 21.07s/it]
2025-07-17 18:57:16,732 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:57:32,454 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  53%|#################################4                             | 26/49 [06:06<07:28, 19.48s/it]
2025-07-17 18:57:32,455 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:57:49,054 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  55%|##################################7                            | 27/49 [06:23<06:49, 18.62s/it]
2025-07-17 18:57:49,055 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:58:04,887 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 28/49 [06:39<06:13, 17.79s/it]
2025-07-17 18:58:04,888 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:58:11,045 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:58:11] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:58:20,811 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  59%|#####################################2                         | 29/49 [06:55<05:44, 17.23s/it]
2025-07-17 18:58:20,812 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:58:35,353 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  61%|######################################5                        | 30/49 [07:09<05:12, 16.42s/it]
2025-07-17 18:58:35,353 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:58:49,934 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  63%|#######################################8                       | 31/49 [07:24<04:45, 15.87s/it]
2025-07-17 18:58:49,935 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:59:04,863 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  65%|#########################################1                     | 32/49 [07:39<04:25, 15.59s/it]
2025-07-17 18:59:04,864 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:59:11,065 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 18:59:11] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 18:59:19,601 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  67%|##########################################4                    | 33/49 [07:54<04:05, 15.33s/it]
2025-07-17 18:59:19,602 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:59:34,099 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  69%|###########################################7                   | 34/49 [08:08<03:46, 15.08s/it]
2025-07-17 18:59:34,100 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 18:59:48,574 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 35/49 [08:22<03:28, 14.90s/it]
2025-07-17 18:59:48,574 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:00:02,943 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  73%|##############################################2                | 36/49 [08:37<03:11, 14.74s/it]
2025-07-17 19:00:02,945 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:00:11,084 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:00:11] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:00:17,768 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  76%|###############################################5               | 37/49 [08:52<02:57, 14.77s/it]
2025-07-17 19:00:17,769 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:00:32,630 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  78%|################################################8              | 38/49 [09:07<02:42, 14.80s/it]
2025-07-17 19:00:32,630 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:00:47,158 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  80%|##################################################1            | 39/49 [09:21<02:27, 14.72s/it]
2025-07-17 19:00:47,159 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:01:01,387 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  82%|###################################################4           | 40/49 [09:35<02:11, 14.57s/it]
2025-07-17 19:01:01,387 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:01:11,106 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:01:11] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:01:16,448 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  84%|####################################################7          | 41/49 [09:50<01:57, 14.72s/it]
2025-07-17 19:01:16,448 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:01:31,087 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 42/49 [10:05<01:42, 14.69s/it]
2025-07-17 19:01:31,087 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:01:46,412 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  88%|#######################################################2       | 43/49 [10:20<01:29, 14.88s/it]
2025-07-17 19:01:46,412 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:02:02,724 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  90%|########################################################5      | 44/49 [10:37<01:16, 15.31s/it]
2025-07-17 19:02:02,724 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:02:11,124 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:02:11] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:02:19,956 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  92%|#########################################################8     | 45/49 [10:54<01:03, 15.89s/it]
2025-07-17 19:02:19,956 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:02:36,693 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  94%|###########################################################1   | 46/49 [11:11<00:48, 16.14s/it]
2025-07-17 19:02:36,694 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:02:51,757 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  96%|############################################################4  | 47/49 [11:26<00:31, 15.82s/it]
2025-07-17 19:02:51,758 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:03:07,471 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度:  98%|#############################################################7 | 48/49 [11:41<00:15, 15.79s/it]
2025-07-17 19:03:07,472 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:03:11,063 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 49/49 [11:45<00:00, 12.13s/it]
2025-07-17 19:03:11,064 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:03:11,068 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:03:11,142 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:03:11] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:04:11,160 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:04:11] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:05:11,181 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:05:11] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:06:16,623 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:06:16] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:07:16,643 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:07:16] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:08:15,416 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [21:38<11:57, 717.83s/it]
2025-07-17 19:08:16,662 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:08:16] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:08:48,996 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/49 [00:00<?, ?it/s]
2025-07-17 19:08:49,018 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:08:49,778 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:   2%|#3                                                              | 1/49 [00:00<00:36,  1.32it/s]
2025-07-17 19:08:49,778 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:08:49,995 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:   4%|##6                                                             | 2/49 [00:00<00:20,  2.28it/s]
2025-07-17 19:08:49,996 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:08:50,209 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:   6%|###9                                                            | 3/49 [00:01<00:15,  2.98it/s]
2025-07-17 19:08:50,209 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:08:50,449 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:   8%|#####2                                                          | 4/49 [00:01<00:13,  3.35it/s]
2025-07-17 19:08:50,449 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:08:50,709 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  10%|######5                                                         | 5/49 [00:01<00:12,  3.52it/s]
2025-07-17 19:08:50,710 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:08:51,005 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  12%|#######8                                                        | 6/49 [00:01<00:12,  3.47it/s]
2025-07-17 19:08:51,006 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:08:51,258 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 7/49 [00:02<00:11,  3.61it/s]
2025-07-17 19:08:51,259 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:08:51,521 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  16%|##########4                                                     | 8/49 [00:02<00:11,  3.67it/s]
2025-07-17 19:08:51,521 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:08:51,800 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  18%|###########7                                                    | 9/49 [00:02<00:10,  3.65it/s]
2025-07-17 19:08:51,800 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:08:52,086 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  20%|############8                                                  | 10/49 [00:03<00:10,  3.60it/s]
2025-07-17 19:08:52,086 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:08:52,367 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  22%|##############1                                                | 11/49 [00:03<00:10,  3.59it/s]
2025-07-17 19:08:52,367 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:09:06,342 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  24%|###############4                                               | 12/49 [00:17<02:44,  4.45s/it]
2025-07-17 19:09:06,343 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:09:16,893 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:09:16] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:09:21,329 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  27%|################7                                              | 13/49 [00:32<04:34,  7.64s/it]
2025-07-17 19:09:21,329 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:09:33,002 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  29%|##################                                             | 14/49 [00:43<05:09,  8.86s/it]
2025-07-17 19:09:33,003 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:09:48,091 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  31%|###################2                                           | 15/49 [00:59<06:05, 10.74s/it]
2025-07-17 19:09:48,092 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:10:01,999 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  33%|####################5                                          | 16/49 [01:12<06:25, 11.69s/it]
2025-07-17 19:10:02,000 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:10:16,911 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:10:16] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:10:17,846 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  35%|#####################8                                         | 17/49 [01:28<06:54, 12.94s/it]
2025-07-17 19:10:17,847 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:10:33,835 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  37%|#######################1                                       | 18/49 [01:44<07:09, 13.86s/it]
2025-07-17 19:10:33,836 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:10:49,866 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  39%|########################4                                      | 19/49 [02:00<07:15, 14.51s/it]
2025-07-17 19:10:49,866 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:11:01,382 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  41%|#########################7                                     | 20/49 [02:12<06:34, 13.61s/it]
2025-07-17 19:11:01,383 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:11:16,233 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  43%|###########################                                    | 21/49 [02:27<06:31, 13.98s/it]
2025-07-17 19:11:16,234 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:11:16,933 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:11:16] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:11:31,000 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  45%|############################2                                  | 22/49 [02:41<06:23, 14.22s/it]
2025-07-17 19:11:31,000 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:11:45,649 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  47%|#############################5                                 | 23/49 [02:56<06:13, 14.35s/it]
2025-07-17 19:11:45,650 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:12:01,548 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  49%|##############################8                                | 24/49 [03:12<06:10, 14.81s/it]
2025-07-17 19:12:01,549 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:12:16,965 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:12:16] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:12:17,032 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  51%|################################1                              | 25/49 [03:28<06:00, 15.01s/it]
2025-07-17 19:12:17,032 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:12:32,288 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  53%|#################################4                             | 26/49 [03:43<05:46, 15.09s/it]
2025-07-17 19:12:32,288 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:12:48,549 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  55%|##################################7                            | 27/49 [03:59<05:39, 15.44s/it]
2025-07-17 19:12:48,550 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:13:03,820 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 28/49 [04:14<05:23, 15.39s/it]
2025-07-17 19:13:03,821 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:13:16,990 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:13:16] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:13:19,550 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  59%|#####################################2                         | 29/49 [04:30<05:09, 15.49s/it]
2025-07-17 19:13:19,550 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:14:17,008 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:14:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:15:17,028 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:15:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:15:44,167 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  61%|######################################5                        | 30/49 [06:55<17:10, 54.23s/it]
2025-07-17 19:15:44,168 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:16:09,631 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  63%|#######################################8                       | 31/49 [07:20<13:40, 45.60s/it]
2025-07-17 19:16:09,632 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:16:17,050 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:16:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:16:27,962 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  65%|#########################################1                     | 32/49 [07:38<10:36, 37.42s/it]
2025-07-17 19:16:27,962 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:16:42,736 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  67%|##########################################4                    | 33/49 [07:53<08:10, 30.63s/it]
2025-07-17 19:16:42,737 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:16:58,599 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  69%|###########################################7                   | 34/49 [08:09<06:32, 26.20s/it]
2025-07-17 19:16:58,600 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:17:13,881 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 35/49 [08:24<05:20, 22.92s/it]
2025-07-17 19:17:13,882 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:17:17,071 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:17:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:17:28,546 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  73%|##############################################2                | 36/49 [08:39<04:25, 20.45s/it]
2025-07-17 19:17:28,548 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:17:43,074 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  76%|###############################################5               | 37/49 [08:54<03:44, 18.67s/it]
2025-07-17 19:17:43,075 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:17:58,891 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  78%|################################################8              | 38/49 [09:09<03:15, 17.81s/it]
2025-07-17 19:17:58,892 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:18:13,648 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  80%|##################################################1            | 39/49 [09:24<02:48, 16.90s/it]
2025-07-17 19:18:13,650 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:18:17,091 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:18:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:18:28,136 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  82%|###################################################4           | 40/49 [09:39<02:25, 16.17s/it]
2025-07-17 19:18:28,137 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:18:43,293 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  84%|####################################################7          | 41/49 [09:54<02:06, 15.87s/it]
2025-07-17 19:18:43,294 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:18:57,609 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 42/49 [10:08<01:47, 15.40s/it]
2025-07-17 19:18:57,609 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:19:13,223 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  88%|#######################################################2       | 43/49 [10:24<01:32, 15.47s/it]
2025-07-17 19:19:13,224 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:19:17,109 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:19:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:19:29,165 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  90%|########################################################5      | 44/49 [10:40<01:18, 15.61s/it]
2025-07-17 19:19:29,198 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:19:44,990 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  92%|#########################################################8     | 45/49 [10:55<01:02, 15.67s/it]
2025-07-17 19:19:44,991 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:20:00,700 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  94%|###########################################################1   | 46/49 [11:11<00:47, 15.68s/it]
2025-07-17 19:20:00,701 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:20:15,533 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  96%|############################################################4  | 47/49 [11:26<00:30, 15.43s/it]
2025-07-17 19:20:15,533 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:20:17,126 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:20:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:20:31,730 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度:  98%|#############################################################7 | 48/49 [11:42<00:15, 15.66s/it]
2025-07-17 19:20:31,731 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:20:35,450 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
波段 3/3 写入进度: 100%|###############################################################| 49/49 [11:46<00:00, 12.08s/it]
2025-07-17 19:20:35,450 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:20:35,456 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - [A
2025-07-17 19:21:17,143 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:21:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:22:17,163 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:22:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:23:17,183 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:23:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:23:21,168 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [36:43<00:00, 803.64s/it]
2025-07-17 19:23:21,169 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [36:43<00:00, 734.59s/it]
2025-07-17 19:24:17,205 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:24:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:25:17,223 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:25:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:26:17,240 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:26:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:27:17,256 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:27:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:28:17,658 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:28:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:29:17,723 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:29:17] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:30:21,579 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:30:21] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:31:22,054 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:31:22] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:32:22,664 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:32:22] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:33:23,001 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:33:22] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:34:23,369 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:34:23] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:34:54,747 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - INFO - 影像保存完成，耗时: 2204.76 秒 (19:23:22)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp (19:23:22)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif创建掩码，识别有效区域...轻微平滑处理掩码，减少边缘锯齿...寻找有效区域轮廓...找到 3 个初始轮廓使用所有 3 个轮廓，不进行面积过滤转换轮廓为地理坐标...
2025-07-17 19:34:54,755 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理轮廓:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-17 19:34:55,773 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理轮廓: 100%|##########################################################################| 3/3 [00:01<00:00,  3.00it/s]
2025-07-17 19:34:55,775 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
处理轮廓: 100%|##########################################################################| 3/3 [00:01<00:00,  2.99it/s]
2025-07-17 19:34:57,100 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - INFO - 成功创建 3 个多边形合并多边形...创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp写入shapefile特征...处理MultiPolygon，包含 3 个多边形
2025-07-17 19:34:57,101 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
写入多边形:   0%|                                                                                | 0/3 [00:00<?, ?it/s]
2025-07-17 19:34:57,128 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - ERROR - 
写入多边形: 100%|#######################################################################| 3/3 [00:00<00:00, 130.43it/s]
2025-07-17 19:35:02,406 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - INFO - TIF处理任务 5f3f6493-e2d8-451c-a8a2-3745ce521c0b 执行成功
2025-07-17 19:35:02,407 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - INFO - 完成时间: 2025-07-17 19:35:02
2025-07-17 19:35:02,410 - tif_task_5f3f6493-e2d8-451c-a8a2-3745ce521c0b - INFO - 状态: 运行成功
2025-07-17 19:35:24,273 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:35:24] "GET /api/tif/status?task_id=5f3f6493-e2d8-451c-a8a2-3745ce521c0b HTTP/1.1" 200 -
2025-07-17 19:35:30,110 - root - INFO - 工作区 '"tttt"' 不存在，正在创建...
2025-07-17 19:35:31,126 - root - ERROR - 创建工作区 "tttt" 时出错: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'
2025-07-17 19:35:31,129 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:35:31] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-17 19:41:55,116 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/"tttt"/coveragestores/20250705171600/file.geotiff
2025-07-17 19:42:04,333 - root - ERROR - 发布GeoTIFF时出错: ('Connection aborted.', ConnectionAbortedError(10053, '你的主机中的软件中止了一个已建立的连接。', None, 10053, None))
2025-07-17 19:42:04,378 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:42:04] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-17 19:51:04,160 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/"tttt"/coveragestores/20250705171600/file.geotiff
2025-07-17 19:51:14,674 - root - ERROR - 发布GeoTIFF时出错: ('Connection aborted.', ConnectionAbortedError(10053, '你的主机中的软件中止了一个已建立的连接。', None, 10053, None))
2025-07-17 19:51:14,718 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:51:14] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-17 19:59:08,209 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/"tttt"/coveragestores/20250705171600/file.geotiff
2025-07-17 19:59:18,328 - root - ERROR - 发布GeoTIFF时出错: ('Connection aborted.', ConnectionAbortedError(10053, '你的主机中的软件中止了一个已建立的连接。', None, 10053, None))
2025-07-17 19:59:18,377 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 19:59:18] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-17 20:09:20,421 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/"tttt"/coveragestores/20250705171600/file.geotiff
2025-07-17 20:09:29,928 - root - ERROR - 发布GeoTIFF时出错: ('Connection aborted.', ConnectionAbortedError(10053, '你的主机中的软件中止了一个已建立的连接。', None, 10053, None))
2025-07-17 20:09:29,976 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 20:09:29] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-17 20:19:56,954 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/"tttt"/coveragestores/20250705171600/file.geotiff
2025-07-17 20:20:07,242 - root - ERROR - 发布GeoTIFF时出错: ('Connection aborted.', ConnectionAbortedError(10053, '你的主机中的软件中止了一个已建立的连接。', None, 10053, None))
2025-07-17 20:20:07,283 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 20:20:07] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-17 20:29:01,800 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/"tttt"/coveragestores/20250705171600/file.geotiff
2025-07-17 20:29:11,027 - root - ERROR - 发布GeoTIFF时出错: ('Connection aborted.', ConnectionAbortedError(10053, '你的主机中的软件中止了一个已建立的连接。', None, 10053, None))
2025-07-17 20:29:11,066 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 20:29:11] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-17 20:38:37,276 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/"tttt"/coveragestores/20250705171600/file.geotiff
2025-07-17 20:38:47,256 - root - ERROR - 发布GeoTIFF时出错: ('Connection aborted.', ConnectionAbortedError(10053, '你的主机中的软件中止了一个已建立的连接。', None, 10053, None))
2025-07-17 20:38:47,296 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 20:38:47] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-17 20:49:02,313 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/"tttt"/coveragestores/20250705171600/file.geotiff
2025-07-17 20:49:12,039 - root - ERROR - 发布GeoTIFF时出错: ('Connection aborted.', ConnectionAbortedError(10053, '你的主机中的软件中止了一个已建立的连接。', None, 10053, None))
2025-07-17 20:49:12,465 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 20:49:12] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-17 20:58:55,429 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/"tttt"/coveragestores/20250705171600/file.geotiff
2025-07-17 20:59:04,291 - root - ERROR - 发布GeoTIFF时出错: ('Connection aborted.', ConnectionAbortedError(10053, '你的主机中的软件中止了一个已建立的连接。', None, 10053, None))
2025-07-17 20:59:04,333 - werkzeug - INFO - 127.0.0.1 - - [17/Jul/2025 20:59:04] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1[0m" 500 -
