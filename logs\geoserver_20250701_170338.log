2025-07-01 17:03:38,442 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250701_170338.log
2025-07-01 17:03:38,480 - root - INFO - 开始执行命令: publish-geotiff
2025-07-01 17:03:38,480 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-01 17:03:38,481 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-01 17:03:38,505 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-01 17:03:38,506 - root - INFO - 未提供存储名称，使用文件名: nanning
2025-07-01 17:03:38,507 - root - INFO - 未提供图层名称，使用存储名称: nanning
2025-07-01 17:03:38,573 - root - INFO - 工作区 'tif_workspace2' 不存在，正在创建...
2025-07-01 17:03:38,634 - root - INFO - 成功创建工作区 'tif_workspace2'
2025-07-01 17:03:38,638 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\nanning.tif
2025-07-01 17:03:39,360 - root - INFO - 发布GeoTIFF 'data/20250701/nanning.tif' 到 'tif_workspace2:nanning'
2025-07-01 17:03:39,442 - root - INFO - 图层验证成功: 直接图层路径: tif_workspace2:nanning
2025-07-01 17:03:39,442 - root - INFO - GeoTIFF 'data/20250701/nanning.tif' 成功发布为图层 'tif_workspace2:nanning'
2025-07-01 17:03:39,443 - root - INFO - 命令执行完成: publish-geotiff
