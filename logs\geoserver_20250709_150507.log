2025-07-09 15:05:07,086 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250709_150507.log
2025-07-09 15:05:07,089 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-09 15:05:07,090 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-09 15:05:07,108 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-09 15:05:07,114 - root - INFO - === GeoServer REST API服务 ===
2025-07-09 15:05:07,115 - root - INFO - 主机: 0.0.0.0
2025-07-09 15:05:07,115 - root - INFO - 端口: 5083
2025-07-09 15:05:07,116 - root - INFO - 调试模式: 禁用
2025-07-09 15:05:07,117 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-09 15:05:07,133 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-09 15:05:07,134 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 15:05:18,168 - root - INFO - 开始查询坐标点 (22.851294156930834, 108.3952307340426) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-09 15:05:18,517 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-09 15:05:18,545 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-09 15:05:18,587 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-09 15:05:18,587 - root - INFO - 在坐标点 (22.851294156930834, 108.3952307340426) 处找到 2 个有有效数据的栅格图层
2025-07-09 15:05:18,588 - werkzeug - INFO - ************** - - [09/Jul/2025 15:05:18] "GET /api/query_values?lat=22.851294156930834&lon=108.3952307340426&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-09 15:05:23,671 - root - INFO - 开始查询坐标点 (22.80886875373946, 108.44399544536971) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-09 15:05:23,971 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-09 15:05:23,997 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-09 15:05:24,092 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-09 15:05:24,092 - root - INFO - 在坐标点 (22.80886875373946, 108.44399544536971) 处找到 2 个有有效数据的栅格图层
2025-07-09 15:05:24,093 - werkzeug - INFO - ************** - - [09/Jul/2025 15:05:24] "GET /api/query_values?lat=22.80886875373946&lon=108.44399544536971&workspace=test_myworkspace HTTP/1.1" 200 -
