# GeoServer管理与API工具

本项目提供了用于管理GeoServer服务的命令行工具和RESTful API服务，实现了自动化操作GeoServer和栅格数据查询功能。

## 项目重构

为了提高代码的可维护性和结构清晰度，项目已经进行了重构，将所有功能集中在`geoserverRest`包中。重构主要包括以下方面：

1. 将`coordinate_query`模块的功能集成到`geoserverRest`中
2. 将命令行工具功能集成到`geoserverRest`中
3. 统一API访问入口，通过同一个端口提供所有服务

## 项目结构

```
geoserverRest/              # 主包
  ├── api/                  # RESTful API服务模块
  ├── cli/                  # 命令行工具模块
  ├── core/                 # 核心功能模块
  │   ├── manager.py        # GeoServer管理器
  │   └── raster_query.py   # 栅格数据查询
  └── utils/                # 实用工具模块

geoserver_manager_cli.py    # 命令行工具入口点
start_geoserver_api.bat     # API服务启动批处理文件
```

## 使用方法

### 启动API服务

```bash
# 使用批处理文件启动
start_geoserver_api.bat

# 或者使用Python模块启动
python -m geoserverRest.run [--host HOST] [--port PORT] [--debug]
```

### 使用命令行工具

```bash
# 使用根目录入口脚本
python geoserver_manager_cli.py [命令] [参数]

# 或者使用模块入口
python -m geoserverRest.geoserver_cli [命令] [参数]
```

更多详细信息请参考`geoserverRest/README.md`文件。 