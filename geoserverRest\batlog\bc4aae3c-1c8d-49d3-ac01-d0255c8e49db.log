执行命令: D:\Drone_Project\ODM\ODM\run.bat D:\Drone_Project\dataset\DJITESTIMAGE\20250714140500\project --fast-orthophoto
工作目录: D:\Drone_Project\ODM\ODM
开始时间: 2025-07-14 11:35:04

检测到ODM项目，使用特殊方法执行
创建执行批处理文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\exec_bc4aae3c-1c8d-49d3-ac01-d0255c8e49db.bat
批处理文件内容:
@echo off
cd /d D:\Drone_Project\ODM\ODM
echo 正在运行: run.bat D:\Drone_Project\dataset\DJITESTIMAGE\20250714140500\project --fast-orthophoto
call run.bat D:\Drone_Project\dataset\DJITESTIMAGE\20250714140500\project --fast-orthophoto
echo 命令执行完成，退出代码: %ERRORLEVEL%


开始执行批处理文件...

执行出错: 'utf-8' codec can't decode byte 0xd5 in position 0: invalid continuation byte
完成时间: 2025-07-14 11:35:04
状态: 运行失败
