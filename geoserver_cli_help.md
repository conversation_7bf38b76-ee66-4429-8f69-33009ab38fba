# GeoServer命令行工具使用手册

GeoServer命令行工具是一个用于自动化操作GeoServer的工具，支持通过命令行或批处理文件执行各种GeoServer操作。

## 目录

- [1. 全局选项](#1-全局选项)
- [2. 命令分类](#2-命令分类)
  - [2.1 工作区管理](#21-工作区管理)
  - [2.2 矢量数据发布](#22-矢量数据发布)
  - [2.3 栅格数据发布](#23-栅格数据发布)
  - [2.4 PostGIS数据管理](#24-postgis数据管理)
  - [2.5 图层样式与管理](#25-图层样式与管理)
  - [2.6 批处理操作](#26-批处理操作)
- [3. 批处理文件格式](#3-批处理文件格式)
- [4. 日志管理](#4-日志管理)

## 1. 全局选项

以下选项适用于所有命令：

* `--log-level`: 设置日志级别，可选值为DEBUG、INFO、WARNING、ERROR、CRITICAL，默认为INFO
* `--log-file`: 设置日志文件路径，默认为logs目录下带时间戳的文件

## 2. 命令分类

### 2.1 工作区管理

#### 2.1.1 `create-workspace`: 创建GeoServer工作区

**参数**：

* `--name`: 工作区名称（必需）

**示例**：

```bash
python geoserver_cli.py create-workspace --name test_workspace
```

#### 2.1.2 `delete-workspace`: 删除工作区

**参数**：

* `--name`: 工作区名称（必需）

**示例**：

```bash
python geoserver_cli.py delete-workspace --name test_workspace
```

### 2.2 矢量数据发布

#### 2.2.1 `publish-shapefile`: 发布Shapefile到GeoServer

**参数**：

* `--file`: Shapefile文件路径（必需）
* `--workspace`: 工作区名称（必需）
* `--store`: 存储名称，默认使用文件名
* `--layer`: 图层名称，默认使用存储名

**示例**：

```bash
python geoserver_cli.py publish-shapefile --file data/example.shp --workspace test_workspace --store example_store --layer example_layer
```

#### 2.2.2 `publish-shapefile-directory`: 发布目录中所有Shapefile到GeoServer

**参数**：

* `--directory`: 包含Shapefile文件的目录路径（必需）
* `--workspace`: 工作区名称（必需）
* `--store`: 存储名称，如果不提供则使用目录名称
* `--charset`: DBF文件字符集，默认为UTF-8以支持中文

**示例**：

```bash
python geoserver_cli.py publish-shapefile-directory --directory data/shapefiles --workspace test_workspace --store shp_store

python geoserver_cli.py publish-shapefile-directory --directory data/20250701/testshp --workspace test_workspace --charset UTF-8
```

### 2.3 栅格数据发布

#### 2.3.1 `publish-geotiff`: 发布GeoTIFF到GeoServer

**参数**：

* `--file`: GeoTIFF文件路径（必需）
* `--workspace`: 工作区名称（必需）
* `--store`: 存储名称，默认使用文件名
* `--layer`: 图层名称，默认使用存储名

**示例**：

```bash
python geoserver_cli.py publish-geotiff --file data/example.tif --workspace test_workspace --store example_raster --layer example_raster_layer

python geoserver_cli.py publish-geotiff --file data/20250701/nanning.tif --workspace tif_workspace3
```

#### 2.3.2 `publish-geotiff-directory`: 发布目录中所有GeoTIFF到GeoServer

**参数**：

* `--directory`: 包含GeoTIFF文件的目录路径（必需）
* `--workspace`: 工作区名称（必需）
* `--store`: 存储名称前缀，如果不提供则使用目录名称

**示例**：

```bash
python geoserver_cli.py publish-geotiff-directory --directory data/rasters --workspace raster_workspace --store raster_data

python geoserver_cli.py publish-geotiff-directory --directory D:/Drone_Project/rasterTest --workspace test_myworkspace
```

### 2.4 PostGIS数据管理

#### 2.4.1 `create-datastore`: 创建PostGIS数据存储

**参数**：

* `--name`: 数据存储名称（必需）
* `--workspace`: 工作区名称（必需）
* `--host`: 数据库主机，默认为localhost
* `--port`: 数据库端口，默认为5432
* `--database`: 数据库名称（必需）
* `--username`: 数据库用户名（必需）
* `--password`: 数据库密码（必需）

**示例**：

```bash
python geoserver_cli.py create-datastore --name postgis_store --workspace test_workspace --database spatial_db --username postgres --password postgres
```

#### 2.4.2 `publish-postgis`: 发布PostGIS表到GeoServer

**参数**：

* `--table`: 数据表名称（必需）
* `--workspace`: 工作区名称（必需）
* `--store`: 数据存储名称（必需）
* `--layer`: 图层名称，默认使用表名
* `--geometry-column`: 几何列名称，默认为geom
* `--srid`: 空间参考系统ID，默认为4326

**示例**：

```bash
python geoserver_cli.py publish-postgis --table spatial_table --workspace test_workspace --store postgis_store --layer postgis_layer --geometry-column geom --srid 4326
```

### 2.5 图层样式与管理

#### 2.5.1 `set-style`: 设置图层样式

**参数**：

* `--layer`: 图层名称（必需）
* `--style`: 样式名称（必需）
* `--workspace`: 工作区名称（必需）

**示例**：

```bash
python geoserver_cli.py set-style --layer example_layer --style polygon_style --workspace test_workspace
```

#### 2.5.2 `delete-layer`: 删除图层

**参数**：

* `--name`: 图层名称（必需）
* `--workspace`: 工作区名称（必需）

**示例**：

```bash
python geoserver_cli.py delete-layer --name example_layer --workspace test_workspace
```

### 2.6 批处理操作

#### 2.6.1 `batch`: 批量处理操作

**参数**：

* `--file`: JSON格式的批处理配置文件（必需）

**示例**：

```bash
python geoserver_cli.py batch --file batch_commands.json

python geoserver_cli.py batch --file batch.json
```

## 3. 批处理文件格式

批处理文件是一个JSON格式的文件，包含一系列操作，每个操作包含类型和参数。例如：

```json
[
  {
    "type": "create_workspace",
    "params": {
      "name": "test_workspace"
    }
  },
  {
    "type": "publish_shapefile",
    "params": {
      "file": "data/example.shp",
      "workspace": "test_workspace",
      "store": "example_store",
      "layer": "example_layer"
    }
  },
  {
    "type": "publish_shapefile_directory",
    "params": {
      "directory": "data/shapefiles",
      "workspace": "test_workspace",
      "store": "shp"
    }
  },
  {
    "type": "publish_geotiff_directory",
    "params": {
      "directory": "data/rasters",
      "workspace": "test_workspace",
      "store": "raster_data"
    }
  }
]
```

可用的操作类型有：

* `create_workspace`
* `delete_workspace`
* `publish_shapefile`
* `publish_shapefile_directory`
* `publish_geotiff`
* `publish_geotiff_directory`
* `publish_postgis_layer`
* `create_datastore`
* `set_layer_style`
* `delete_layer`

## 4. 日志管理

命令执行过程中的所有日志会同时输出到控制台和日志文件。默认情况下，日志文件保存在`logs`目录下，文件名格式为`geoserver_YYYYMMDD_HHMMSS.log`。

可以通过`--log-level`参数设置日志级别，可选值有：

* `DEBUG`: 调试信息，最详细的日志
* `INFO`: 一般信息，默认级别
* `WARNING`: 警告信息
* `ERROR`: 错误信息
* `CRITICAL`: 严重错误信息

可以通过`--log-file`参数指定日志文件的路径。

