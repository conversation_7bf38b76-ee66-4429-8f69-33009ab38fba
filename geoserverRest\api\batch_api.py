#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer REST API服务 - 批处理执行 API 模块
提供批处理文件执行和状态查询的 API 端点
"""

import os
import sys
from flask import Blueprint, request, jsonify

# 导入核心模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from core.batch_executor import executor, batch_logger

# 创建 API 蓝图
batch_api = Blueprint('batch_api', __name__)

@batch_api.route('/execute', methods=['GET'])
def execute_batch():
    """
    执行批处理文件
    
    查询参数:
        batch_path: 批处理文件路径 (可选，如果不提供则使用默认路径)
        arg1: 参数1 (可选)
        arg2: 参数2 (可选)
        [其他参数]: 其他参数 (可选)
    
    返回:
        执行结果，包含任务 ID
    """
    try:
        # 获取批处理文件路径 D:\Drone_Project\ODM\ODM
        batch_path = request.args.get('batch_path', 'D:\\Drone_Project\\ODM\\ODM\\run.bat')
        
        # 检查批处理文件是否存在
        if not os.path.exists(batch_path):
            return jsonify({
                'status': 'error',
                'message': f'批处理文件不存在: {batch_path}'
            }), 404
        
        # 收集所有其他参数
        args = {}
        for key, value in request.args.items():
            if key != 'batch_path' and value:
                args[key] = value
                
        # 如果没有提供任何参数，使用默认参数
        if not args:
            args = {
                'project_path': 'c://user/20250714140500/project',
                'fast-orthophoto': ''
            }
        
        # 执行批处理文件
        task_id = executor.execute_batch(batch_path, args)
        
        return jsonify({
            'status': 'success',
            'message': '批处理文件执行已启动',
            'task_id': task_id
        })
        
    except Exception as e:
        batch_logger.error(f"API错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@batch_api.route('/status', methods=['GET'])
def get_batch_status():
    """
    获取批处理文件执行状态
    
    查询参数:
        task_id: 任务ID
    
    返回:
        任务状态信息
    """
    try:
        # 获取任务ID
        task_id = request.args.get('task_id')
        
        # 检查任务ID是否提供
        if not task_id:
            return jsonify({
                'status': 'error',
                'message': '缺少必要参数: task_id'
            }), 400
        
        # 获取任务状态
        task_status = executor.get_task_status(task_id)
        
        if not task_status:
            return jsonify({
                'status': 'error',
                'message': f'未找到任务: {task_id}'
            }), 404
        
        return jsonify({
            'status': 'success',
            'task': task_status
        })
        
    except Exception as e:
        batch_logger.error(f"API错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@batch_api.route('/all', methods=['GET'])
def get_all_batch_status():
    """
    获取所有批处理文件执行状态
    
    返回:
        所有任务状态信息
    """
    try:
        # 获取所有任务状态
        tasks = executor.get_all_tasks()
        
        return jsonify({
            'status': 'success',
            'count': len(tasks),
            'tasks': tasks
        })
        
    except Exception as e:
        batch_logger.error(f"API错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500 