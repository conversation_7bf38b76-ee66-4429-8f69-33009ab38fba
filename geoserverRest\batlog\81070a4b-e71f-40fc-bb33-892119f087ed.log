执行命令: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
开始时间: 2025-07-17 11:36:04

[INFO]    Initializing ODM 3.5.5 - Thu Jul 17 11:36:33  2025
[INFO]    ==============
[INFO]    3d_tiles: False
[INFO]    align: None
[INFO]    auto_boundary: False
[INFO]    auto_boundary_distance: 0
[INFO]    bg_removal: False
[INFO]    boundary: {}
[INFO]    build_overviews: False
[INFO]    camera_lens: auto
[INFO]    cameras: {}
[INFO]    cog: False
[INFO]    copy_to: None
[INFO]    crop: 3
[INFO]    dem_decimation: 1
[INFO]    dem_euclidean_map: False
[INFO]    dem_gapfill_steps: 3
[INFO]    dem_resolution: 5
[INFO]    dsm: False
[INFO]    dtm: False
[INFO]    end_with: odm_postprocess
[INFO]    fast_orthophoto: False
[INFO]    feature_quality: high
[INFO]    feature_type: dspsift
[INFO]    force_gps: False
[INFO]    gcp: None
[INFO]    geo: None
[INFO]    gltf: False
[INFO]    gps_accuracy: 3
[INFO]    ignore_gsd: False
[INFO]    matcher_neighbors: 0
[INFO]    matcher_order: 0
[INFO]    matcher_type: flann
[INFO]    max_concurrency: 8
[INFO]    merge: all
[INFO]    mesh_octree_depth: 11
[INFO]    mesh_size: 200000
[INFO]    min_num_features: 10000
[INFO]    name: D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
[INFO]    no_gpu: False
[INFO]    optimize_disk_space: False
[INFO]    orthophoto_compression: DEFLATE
[INFO]    orthophoto_cutline: False
[INFO]    orthophoto_kmz: False
[INFO]    orthophoto_no_tiled: False
[INFO]    orthophoto_png: False
[INFO]    orthophoto_resolution: 5
[INFO]    pc_classify: False
[INFO]    pc_copc: False
[INFO]    pc_csv: False
[INFO]    pc_ept: False
[INFO]    pc_filter: 5
[INFO]    pc_las: False
[INFO]    pc_quality: medium
[INFO]    pc_rectify: False
[INFO]    pc_sample: 0
[INFO]    pc_skip_geometric: False
[INFO]    primary_band: auto
[INFO]    project_path: D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
[INFO]    radiometric_calibration: none
[INFO]    rerun: None
[INFO]    rerun_all: False
[INFO]    rerun_from: None
[INFO]    rolling_shutter: False
[INFO]    rolling_shutter_readout: 0
[INFO]    sfm_algorithm: incremental
[INFO]    sfm_no_partial: False
[INFO]    skip_3dmodel: False
[INFO]    skip_band_alignment: False
[INFO]    skip_orthophoto: False
[INFO]    skip_report: False
[INFO]    sky_removal: False
[INFO]    sm_cluster: None
[INFO]    sm_no_align: False
[INFO]    smrf_scalar: 1.25
[INFO]    smrf_slope: 0.15
[INFO]    smrf_threshold: 0.5
[INFO]    smrf_window: 18.0
[INFO]    split: 999999
[INFO]    split_image_groups: None
[INFO]    split_overlap: 150
[INFO]    texturing_keep_unseen_faces: False
[INFO]    texturing_single_material: False
[INFO]    texturing_skip_global_seam_leveling: False
[INFO]    tiles: False
[INFO]    use_3dmesh: False
[INFO]    use_exif: False
[INFO]    use_fixed_camera_params: False
[INFO]    use_hybrid_bundle_adjustment: False
[INFO]    video_limit: 500
[INFO]    video_resolution: 4000
[INFO]    ==============
[INFO]    Running dataset stage
[INFO]    Loading dataset from: D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\images
[INFO]    Loading 98 images
[INFO]    Wrote images database: D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\images.json
[INFO]    Found 98 usable images
[INFO]    Finished dataset stage
[INFO]    Running split stage
[INFO]    Normal dataset, will process all at once.
[INFO]    Finished split stage
[INFO]    Running merge stage
[INFO]    Normal dataset, nothing to merge.
[INFO]    Finished merge stage
[INFO]    Running opensfm stage
[INFO]    Maximum photo dimensions: 5280px
[INFO]    Photo dimensions for feature extraction: 2640px
[INFO]    CUDA drivers detected
[INFO]    Altitude data detected, enabling it for GPS alignment
[INFO]    ['use_exif_size: no', 'flann_algorithm: KDTREE', 'feature_process_size: 2640', 'feature_min_frames: 10000', 'processes: 8', 'matching_gps_neighbors: 0', 'matching_gps_distance: 0', 'matching_graph_rounds: 50', 'optimize_camera_parameters: yes', 'reconstruction_algorithm: incremental', 'undistorted_image_format: tif', 'bundle_outlier_filtering_type: AUTO', 'sift_peak_threshold: 0.066', 'align_orientation_prior: vertical', 'triangulation_type: ROBUST', 'retriangulation_ratio: 2', 'matcher_type: FLANN', 'feature_type: DSPSIFT', 'use_altitude_tag: yes', 'align_method: auto', 'local_bundle_radius: 0']
[INFO]    Wrote reference_lla.json
[WARNING] D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm\exif already exists, not rerunning photo to metadata
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" detect_features "D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm"
2025-07-17 11:36:57,345 INFO: Planning to use 4346.9015625 MB of RAM for both processing queue and parallel processing.
2025-07-17 11:36:57,346 INFO: Scale-space expected size of a single image : 158.89835357666016 MB
2025-07-17 11:36:57,346 INFO: Expecting to queue at most 27 images while parallel processing of 8 images.
2025-07-17 11:36:57,365 INFO: Reading data for image DJI_20250705171600_0001_V.jpeg (queue-size=0)
2025-07-17 11:36:57,366 INFO: Reading data for image DJI_20250705171919_0027_V.jpeg (queue-size=0)
2025-07-17 11:36:57,367 INFO: Reading data for image DJI_20250705172237_0052_V.jpeg (queue-size=0)
2025-07-17 11:36:57,368 INFO: Reading data for image DJI_20250705172557_0077_V.jpeg (queue-size=0)
2025-07-17 11:36:58,523 INFO: Reading data for image DJI_20250705171927_0028_V.jpeg (queue-size=1)
2025-07-17 11:36:58,524 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171919_0027_V.jpeg
2025-07-17 11:36:58,572 INFO: Reading data for image DJI_20250705171611_0003_V.jpeg (queue-size=1)
2025-07-17 11:36:58,573 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171600_0001_V.jpeg
2025-07-17 11:36:58,978 INFO: Reading data for image DJI_20250705172605_0078_V.jpeg (queue-size=1)
2025-07-17 11:36:58,979 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172557_0077_V.jpeg
2025-07-17 11:36:59,067 INFO: Reading data for image DJI_20250705172245_0053_V.jpeg (queue-size=1)
2025-07-17 11:36:59,068 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172237_0052_V.jpeg
2025-07-17 11:37:00,140 INFO: Reading data for image DJI_20250705171935_0029_V.jpeg (queue-size=1)
2025-07-17 11:37:00,244 INFO: Reading data for image DJI_20250705171619_0004_V.jpeg (queue-size=2)
2025-07-17 11:37:01,352 INFO: Reading data for image DJI_20250705172612_0079_V.jpeg (queue-size=3)
2025-07-17 11:37:01,719 INFO: Reading data for image DJI_20250705172254_0054_V.jpeg (queue-size=4)
2025-07-17 11:37:02,172 INFO: Reading data for image DJI_20250705171944_0030_V.jpeg (queue-size=5)
2025-07-17 11:37:02,391 INFO: Reading data for image DJI_20250705171626_0005_V.jpeg (queue-size=6)
2025-07-17 11:37:03,938 INFO: Reading data for image DJI_20250705171952_0031_V.jpeg (queue-size=7)
2025-07-17 11:37:04,140 INFO: Reading data for image DJI_20250705171634_0006_V.jpeg (queue-size=8)
2025-07-17 11:37:05,240 INFO: Reading data for image DJI_20250705172620_0080_V.jpeg (queue-size=9)
2025-07-17 11:37:05,467 INFO: Reading data for image DJI_20250705172302_0055_V.jpeg (queue-size=10)
2025-07-17 11:37:06,072 INFO: Reading data for image DJI_20250705172000_0032_V.jpeg (queue-size=11)
2025-07-17 11:37:06,632 INFO: Reading data for image DJI_20250705171642_0007_V.jpeg (queue-size=12)
2025-07-17 11:37:08,035 INFO: Reading data for image DJI_20250705172007_0033_V.jpeg (queue-size=13)
2025-07-17 11:37:08,179 INFO: Reading data for image DJI_20250705171650_0008_V.jpeg (queue-size=14)
2025-07-17 11:37:08,656 INFO: Reading data for image DJI_20250705172628_0081_V.jpeg (queue-size=15)
2025-07-17 11:37:08,834 INFO: Reading data for image DJI_20250705172310_0056_V.jpeg (queue-size=16)
2025-07-17 11:37:10,929 INFO: Reading data for image DJI_20250705171658_0009_V.jpeg (queue-size=17)
2025-07-17 11:37:11,359 INFO: Reading data for image DJI_20250705172015_0034_V.jpeg (queue-size=18)
2025-07-17 11:37:12,888 INFO: Reading data for image DJI_20250705172636_0082_V.jpeg (queue-size=19)
2025-07-17 11:37:13,022 INFO: Reading data for image DJI_20250705172318_0057_V.jpeg (queue-size=20)
2025-07-17 11:37:13,091 INFO: Reading data for image DJI_20250705171706_0010_V.jpeg (queue-size=21)
2025-07-17 11:37:13,421 INFO: Reading data for image DJI_20250705172023_0035_V.jpeg (queue-size=22)
2025-07-17 11:37:15,259 INFO: Reading data for image DJI_20250705171714_0011_V.jpeg (queue-size=23)
2025-07-17 11:37:15,843 INFO: Reading data for image DJI_20250705172031_0036_V.jpeg (queue-size=24)
2025-07-17 11:37:16,247 INFO: Reading data for image DJI_20250705172644_0083_V.jpeg (queue-size=25)
2025-07-17 11:37:16,403 INFO: Reading data for image DJI_20250705172326_0058_V.jpeg (queue-size=26)
2025-07-17 11:37:17,307 INFO: Reading data for image DJI_20250705171722_0012_V.jpeg (queue-size=27)
2025-07-17 11:37:29,509 DEBUG: Found 10000 points in 30.914968729019165s
2025-07-17 11:37:31,206 DEBUG: Found 10000 points in 32.65093636512756s
2025-07-17 11:37:33,353 INFO: Reading data for image DJI_20250705172039_0037_V.jpeg (queue-size=27)
2025-07-17 11:37:33,354 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171927_0028_V.jpeg
2025-07-17 11:37:33,570 INFO: Reading data for image DJI_20250705171730_0013_V.jpeg (queue-size=27)
2025-07-17 11:37:33,571 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171611_0003_V.jpeg
2025-07-17 11:37:48,982 DEBUG: Found 10000 points in 49.98329520225525s
2025-07-17 11:37:50,654 DEBUG: Found 10000 points in 51.56309223175049s
2025-07-17 11:37:51,623 INFO: Reading data for image DJI_20250705172652_0084_V.jpeg (queue-size=27)
2025-07-17 11:37:51,624 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172605_0078_V.jpeg
2025-07-17 11:37:54,823 INFO: Reading data for image DJI_20250705172334_0059_V.jpeg (queue-size=27)
2025-07-17 11:37:54,824 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172245_0053_V.jpeg
2025-07-17 11:37:56,956 DEBUG: Found 10000 points in 23.564894676208496s
2025-07-17 11:37:58,436 INFO: Reading data for image DJI_20250705172046_0038_V.jpeg (queue-size=27)
2025-07-17 11:37:58,437 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171935_0029_V.jpeg
2025-07-17 11:37:59,719 DEBUG: Found 10000 points in 26.122286796569824s
2025-07-17 11:38:03,482 INFO: Reading data for image DJI_20250705171738_0014_V.jpeg (queue-size=27)
2025-07-17 11:38:03,492 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171619_0004_V.jpeg
2025-07-17 11:38:24,573 DEBUG: Found 10000 points in 26.08894109725952s
2025-07-17 11:38:25,912 INFO: Reading data for image DJI_20250705172700_0085_V.jpeg (queue-size=27)
2025-07-17 11:38:25,912 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171944_0030_V.jpeg
2025-07-17 11:38:31,941 DEBUG: Found 10000 points in 28.403903245925903s
2025-07-17 11:38:34,528 INFO: Reading data for image DJI_20250705172342_0060_V.jpeg (queue-size=27)
2025-07-17 11:38:34,529 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171626_0005_V.jpeg
2025-07-17 11:38:40,015 DEBUG: Found 10000 points in 48.31329655647278s
2025-07-17 11:38:42,592 INFO: Reading data for image DJI_20250705172054_0039_V.jpeg (queue-size=27)
2025-07-17 11:38:42,592 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172612_0079_V.jpeg
2025-07-17 11:38:46,984 DEBUG: Found 10000 points in 52.06379413604736s
2025-07-17 11:38:50,977 DEBUG: Found 10000 points in 25.04124927520752s
2025-07-17 11:38:53,629 INFO: Reading data for image DJI_20250705171745_0015_V.jpeg (queue-size=27)
2025-07-17 11:38:53,630 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172254_0054_V.jpeg
2025-07-17 11:39:00,254 DEBUG: Found 10000 points in 25.696622610092163s
2025-07-17 11:39:01,433 INFO: Reading data for image DJI_20250705172708_0086_V.jpeg (queue-size=27)
2025-07-17 11:39:01,433 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171952_0031_V.jpeg
2025-07-17 11:39:03,737 INFO: Reading data for image DJI_20250705172350_0061_V.jpeg (queue-size=27)
2025-07-17 11:39:03,738 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171634_0006_V.jpeg
2025-07-17 11:39:18,674 DEBUG: Found 10000 points in 24.9898681640625s
2025-07-17 11:39:19,024 DEBUG: Found 10000 points in 36.40105438232422s
2025-07-17 11:39:22,677 INFO: Reading data for image DJI_20250705172102_0040_V.jpeg (queue-size=27)
2025-07-17 11:39:22,678 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172000_0032_V.jpeg
2025-07-17 11:39:25,404 INFO: Reading data for image DJI_20250705171753_0016_V.jpeg (queue-size=27)
2025-07-17 11:39:25,404 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171642_0007_V.jpeg
2025-07-17 11:39:28,427 DEBUG: Found 10000 points in 24.669230937957764s
2025-07-17 11:39:35,708 DEBUG: Found 10000 points in 34.24997425079346s
2025-07-17 11:39:39,824 INFO: Reading data for image DJI_20250705172716_0087_V.jpeg (queue-size=27)
2025-07-17 11:39:39,824 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172620_0080_V.jpeg
2025-07-17 11:39:45,445 INFO: Reading data for image DJI_20250705172358_0062_V.jpeg (queue-size=27)
2025-07-17 11:39:45,446 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172302_0055_V.jpeg
2025-07-17 11:39:46,784 DEBUG: Found 10000 points in 21.33853507041931s
2025-07-17 11:39:47,994 INFO: Reading data for image DJI_20250705172110_0041_V.jpeg (queue-size=27)
2025-07-17 11:39:47,994 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171650_0008_V.jpeg
2025-07-17 11:39:50,479 DEBUG: Found 10000 points in 27.756173133850098s
2025-07-17 11:39:54,552 INFO: Reading data for image DJI_20250705171801_0017_V.jpeg (queue-size=27)
2025-07-17 11:39:54,553 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172007_0033_V.jpeg
2025-07-17 11:40:00,861 DEBUG: Found 10000 points in 21.01168465614319s
2025-07-17 11:40:03,297 INFO: Reading data for image DJI_20250705172724_0088_V.jpeg (queue-size=27)
2025-07-17 11:40:03,298 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172628_0081_V.jpeg
2025-07-17 11:40:11,842 DEBUG: Found 10000 points in 23.799673795700073s
2025-07-17 11:40:13,286 INFO: Reading data for image DJI_20250705172406_0063_V.jpeg (queue-size=27)
2025-07-17 11:40:13,286 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172310_0056_V.jpeg
2025-07-17 11:40:25,469 DEBUG: Found 10000 points in 22.139477968215942s
2025-07-17 11:40:26,749 INFO: Reading data for image DJI_20250705172118_0042_V.jpeg (queue-size=27)
2025-07-17 11:40:26,750 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171658_0009_V.jpeg
2025-07-17 11:40:29,690 DEBUG: Found 10000 points in 44.17637872695923s
2025-07-17 11:40:34,316 INFO: Reading data for image DJI_20250705171808_0018_V.jpeg (queue-size=27)
2025-07-17 11:40:34,317 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172015_0034_V.jpeg
2025-07-17 11:40:35,388 DEBUG: Found 10000 points in 22.075870275497437s
2025-07-17 11:40:36,826 INFO: Reading data for image DJI_20250705172732_0089_V.jpeg (queue-size=27)
2025-07-17 11:40:36,826 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171706_0010_V.jpeg
2025-07-17 11:40:42,905 DEBUG: Found 10000 points in 48.32023572921753s
2025-07-17 11:40:46,358 INFO: Reading data for image DJI_20250705172414_0064_V.jpeg (queue-size=27)
2025-07-17 11:40:46,360 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172023_0035_V.jpeg
2025-07-17 11:40:47,829 DEBUG: Found 10000 points in 21.037217140197754s
2025-07-17 11:40:49,366 INFO: Reading data for image DJI_20250705172126_0043_V.jpeg (queue-size=27)
2025-07-17 11:40:49,366 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172636_0082_V.jpeg
2025-07-17 11:40:57,811 DEBUG: Found 10000 points in 20.949546575546265s
2025-07-17 11:40:59,278 INFO: Reading data for image DJI_20250705171816_0019_V.jpeg (queue-size=27)
2025-07-17 11:40:59,278 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172318_0057_V.jpeg
2025-07-17 11:41:11,756 DEBUG: Found 10000 points in 22.322863578796387s
2025-07-17 11:41:13,156 INFO: Reading data for image DJI_20250705172739_0090_V.jpeg (queue-size=27)
2025-07-17 11:41:13,156 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171714_0011_V.jpeg
2025-07-17 11:41:15,930 DEBUG: Found 10000 points in 41.496270179748535s
2025-07-17 11:41:20,018 INFO: Reading data for image DJI_20250705172422_0065_V.jpeg (queue-size=27)
2025-07-17 11:41:20,019 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172031_0036_V.jpeg
2025-07-17 11:41:22,141 DEBUG: Found 10000 points in 22.836062908172607s
2025-07-17 11:41:23,915 INFO: Reading data for image DJI_20250705172134_0044_V.jpeg (queue-size=27)
2025-07-17 11:41:23,915 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171722_0012_V.jpeg
2025-07-17 11:41:27,489 DEBUG: Found 10000 points in 41.03546071052551s
2025-07-17 11:41:29,044 INFO: Reading data for image DJI_20250705171824_0020_V.jpeg (queue-size=27)
2025-07-17 11:41:29,044 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172644_0083_V.jpeg
2025-07-17 11:41:35,534 DEBUG: Found 10000 points in 22.34848165512085s
2025-07-17 11:41:37,362 INFO: Reading data for image DJI_20250705172748_0091_V.jpeg (queue-size=27)
2025-07-17 11:41:37,362 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172326_0058_V.jpeg
2025-07-17 11:41:46,496 DEBUG: Found 10000 points in 22.53928780555725s
2025-07-17 11:41:48,423 INFO: Reading data for image DJI_20250705172429_0066_V.jpeg (queue-size=27)
2025-07-17 11:41:48,423 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172039_0037_V.jpeg
2025-07-17 11:41:58,710 DEBUG: Found 10000 points in 38.611948013305664s
2025-07-17 11:42:00,440 DEBUG: Found 10000 points in 23.05028986930847s
2025-07-17 11:42:03,006 INFO: Reading data for image DJI_20250705172143_0045_V.jpeg (queue-size=27)
2025-07-17 11:42:03,006 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171730_0013_V.jpeg
2025-07-17 11:42:03,015 INFO: Reading data for image DJI_20250705171832_0021_V.jpeg (queue-size=27)
2025-07-17 11:42:03,019 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172652_0084_V.jpeg
2025-07-17 11:42:04,320 DEBUG: Found 10000 points in 35.22973990440369s
2025-07-17 11:42:06,868 INFO: Reading data for image DJI_20250705172756_0092_V.jpeg (queue-size=27)
2025-07-17 11:42:06,869 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172334_0059_V.jpeg
2025-07-17 11:42:13,863 DEBUG: Found 10000 points in 25.407544136047363s
2025-07-17 11:42:15,520 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172046_0038_V.jpeg
2025-07-17 11:42:28,147 DEBUG: Found 10000 points in 25.056243419647217s
2025-07-17 11:42:29,753 INFO: Reading data for image DJI_20250705172150_0046_V.jpeg (queue-size=27)
2025-07-17 11:42:29,761 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171738_0014_V.jpeg
2025-07-17 11:42:42,239 DEBUG: Found 10000 points in 26.681723833084106s
2025-07-17 11:42:46,206 INFO: Reading data for image DJI_20250705171840_0022_V.jpeg (queue-size=27)
2025-07-17 11:42:46,206 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172700_0085_V.jpeg
2025-07-17 11:42:53,013 DEBUG: Found 10000 points in 49.940715312957764s
2025-07-17 11:42:55,669 DEBUG: Found 10000 points in 25.856728553771973s
2025-07-17 11:42:55,728 DEBUG: Found 10000 points in 48.8240020275116s
2025-07-17 11:42:56,663 INFO: Reading data for image DJI_20250705172804_0093_V.jpeg (queue-size=27)
2025-07-17 11:42:56,665 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172342_0060_V.jpeg
2025-07-17 11:42:58,220 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172054_0039_V.jpeg
2025-07-17 11:42:58,221 INFO: Reading data for image DJI_20250705172445_0068_V.jpeg (queue-size=27)
2025-07-17 11:42:59,655 INFO: Reading data for image DJI_20250705172158_0047_V.jpeg (queue-size=27)
2025-07-17 11:42:59,656 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171745_0015_V.jpeg
2025-07-17 11:43:15,490 DEBUG: Found 10000 points in 29.24074649810791s
2025-07-17 11:43:19,763 INFO: Reading data for image DJI_20250705171848_0023_V.jpeg (queue-size=27)
2025-07-17 11:43:19,763 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172708_0086_V.jpeg
2025-07-17 11:43:19,768 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172350_0061_V.jpeg
2025-07-17 11:43:19,769 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172102_0040_V.jpeg
2025-07-17 11:43:19,774 INFO: Reading data for image DJI_20250705172811_0094_V.jpeg (queue-size=26)
2025-07-17 11:43:19,775 INFO: Reading data for image DJI_20250705172453_0069_V.jpeg (queue-size=27)
2025-07-17 11:43:19,775 INFO: Reading data for image DJI_20250705172205_0048_V.jpeg (queue-size=27)
2025-07-17 11:43:19,810 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171753_0016_V.jpeg
2025-07-17 11:43:19,812 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172716_0087_V.jpeg
2025-07-17 11:43:20,715 INFO: Reading data for image DJI_20250705171856_0024_V.jpeg (queue-size=27)
2025-07-17 11:43:26,584 DEBUG: Found 10000 points in 28.331562519073486s
2025-07-17 11:43:34,087 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172358_0062_V.jpeg
2025-07-17 11:43:34,087 INFO: Reading data for image DJI_20250705172213_0049_V.jpeg (queue-size=27)
2025-07-17 11:43:36,501 DEBUG: Found 10000 points in 39.76045513153076s
2025-07-17 11:43:39,346 DEBUG: Found 10000 points in 39.616321325302124s
2025-07-17 11:43:43,291 INFO: Reading data for image DJI_20250705171904_0025_V.jpeg (queue-size=27)
2025-07-17 11:43:43,292 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172110_0041_V.jpeg
2025-07-17 11:43:44,613 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171801_0017_V.jpeg
2025-07-17 11:43:44,614 INFO: Reading data for image DJI_20250705172501_0070_V.jpeg (queue-size=27)
Traceback (most recent call last):
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 25, in <module>
    commands.command_runner(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command_runner.py", line 38, in command_runner
    command.run(data, args)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command.py", line 13, in run
    self.run_impl(data, args)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\detect_features.py", line 13, in run_impl
    detect_features.run_dataset(dataset)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\actions\detect_features.py", line 15, in run_dataset
    features_processing.run_features_processing(data, data.images(), False)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\features_processing.py", line 79, in run_features_processing
    parallel_map(process, arguments, processes, 1)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\context.py", line 59, in parallel_map
    res = Parallel(batch_size=batch_size)(delayed(func)(arg) for arg in args)
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\joblib\parallel.py", line 1056, in __call__
    self.retrieve()
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\joblib\parallel.py", line 935, in retrieve
    self._output.extend(job.get(timeout=self.timeout))
  File "multiprocessing\pool.py", line 768, in get
  File "multiprocessing\pool.py", line 125, in worker
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\joblib\_parallel_backends.py", line 595, in __call__
    return self.func(*args, **kwargs)
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\joblib\parallel.py", line 262, in __call__
    return [func(*args, **kwargs)
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\joblib\parallel.py", line 262, in <listcomp>
    return [func(*args, **kwargs)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\features_processing.py", line 146, in process
    read_images(queue, data, images, counter, expected, force)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\features_processing.py", line 170, in read_images
    queue.put(args, block=True, timeout=full_queue_timeout)
  File "queue.py", line 147, in put
queue.Full
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" match_features "D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm"
Traceback (most recent call last):
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 25, in <module>
    commands.command_runner(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command_runner.py", line 34, in command_runner
    with dataset_factory(args.dataset, args.dataset_type) as data:
  File "contextlib.py", line 113, in __enter__
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 17, in create_default_dataset_context
    dataset = DataSet(dataset_path)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 53, in __init__
    self.load_image_list()
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 83, in load_image_list
    raise IOError("No Images found in {}".format(image_list_path))
OSError: No Images found in D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm\images
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" create_tracks "D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm"
Traceback (most recent call last):
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 25, in <module>
    commands.command_runner(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command_runner.py", line 34, in command_runner
    with dataset_factory(args.dataset, args.dataset_type) as data:
  File "contextlib.py", line 113, in __enter__
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 17, in create_default_dataset_context
    dataset = DataSet(dataset_path)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 53, in __init__
    self.load_image_list()
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 83, in load_image_list
    raise IOError("No Images found in {}".format(image_list_path))
OSError: No Images found in D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm\images
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" reconstruct "D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm"
Traceback (most recent call last):
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 25, in <module>
    commands.command_runner(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command_runner.py", line 34, in command_runner
    with dataset_factory(args.dataset, args.dataset_type) as data:
  File "contextlib.py", line 113, in __enter__
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 17, in create_default_dataset_context
    dataset = DataSet(dataset_path)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 53, in __init__
    self.load_image_list()
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 83, in load_image_list
    raise IOError("No Images found in {}".format(image_list_path))
OSError: No Images found in D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm\images
[ERROR]   The program could not process this dataset using the current settings. Check that the images have enough overlap, that there are enough recognizable features and that the images are in focus. The program will now exit.

完成时间: 2025-07-17 12:43:55
返回代码: 0
状态: 运行成功
