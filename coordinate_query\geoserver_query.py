#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
Description: GeoServer栅格数据坐标查询模块
'''

import os
import sys
import logging
import requests
from urllib.parse import urljoin
import json
import numpy as np
from osgeo import gdal
import xml.etree.ElementTree as ET

# 添加父目录到系统路径，以便导入geoserver_manager
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from geoserver_manager import GeoServerManager, logger

class GeoServerRasterQuery:
    """用于在GeoServer上查询栅格数据的类"""
    
    def __init__(self, geoserver_manager=None):
        """
        初始化查询类
        
        Args:
            geoserver_manager: GeoServerManager实例，如果不提供则创建新的实例
        """
        self.manager = geoserver_manager if geoserver_manager else GeoServerManager()
        self.logger = logger
    
    def get_raster_layers(self, workspace):
        """
        获取指定工作区中的所有栅格图层
        
        Args:
            workspace: 工作区名称
            
        Returns:
            栅格图层列表
        """
        try:
            # 检查工作区是否存在
            if not self.manager.check_workspace_exists(workspace):
                self.logger.error(f"工作区 '{workspace}' 不存在")
                return []
            
            # 使用GeoServer REST API获取图层列表
            base_url = self.manager.geo.service_url
            if not base_url.endswith('/rest'):
                base_url = f"{base_url}/rest"
            
            auth = (self.manager.geo.username, self.manager.geo.password)
            url = f"{base_url}/workspaces/{workspace}/coveragestores.json"
            
            self.logger.debug(f"正在请求覆盖层存储列表: {url}")
            response = requests.get(url, auth=auth)
            
            if response.status_code != 200:
                self.logger.error(f"获取栅格存储列表失败，状态码: {response.status_code}")
                return []
                
            data = response.json()
            self.logger.debug(f"覆盖层存储响应: {json.dumps(data, ensure_ascii=False)}")
            
            raster_layers = []
            
            # 处理覆盖层存储列表
            if 'coverageStores' in data and 'coverageStore' in data['coverageStores']:
                for store in data['coverageStores']['coverageStore']:
                    store_name = store['name']
                    self.logger.debug(f"找到覆盖层存储: {store_name}")
                    
                    # 获取存储下的图层列表
                    store_url = f"{base_url}/workspaces/{workspace}/coveragestores/{store_name}/coverages.json"
                    self.logger.debug(f"正在请求覆盖层列表: {store_url}")
                    store_response = requests.get(store_url, auth=auth)
                    
                    if store_response.status_code == 200:
                        store_data = store_response.json()
                        self.logger.debug(f"覆盖层响应: {json.dumps(store_data, ensure_ascii=False)}")
                        
                        if 'coverages' in store_data and 'coverage' in store_data['coverages']:
                            for coverage in store_data['coverages']['coverage']:
                                layer_info = {
                                    'name': coverage['name'],
                                    'store': store_name,
                                    'workspace': workspace,
                                    'id': f"{workspace}:{coverage['name']}",
                                    'bbox': coverage.get('latLonBoundingBox', {})
                                }
                                self.logger.debug(f"添加图层: {layer_info['name']}, 边界框: {layer_info['bbox']}")
                                raster_layers.append(layer_info)
                    else:
                        self.logger.warning(f"获取存储 '{store_name}' 的覆盖层失败，状态码: {store_response.status_code}")
            else:
                self.logger.warning(f"在工作区 '{workspace}' 中未找到覆盖层存储")
            
            # 如果有任何图层没有边界框，尝试从WMS GetCapabilities获取
            self._fetch_missing_bboxes(raster_layers, workspace)
            
            self.logger.info(f"在工作区 '{workspace}' 中找到 {len(raster_layers)} 个栅格图层")
            return raster_layers
            
        except Exception as e:
            self.logger.error(f"获取栅格图层列表时出错: {str(e)}")
            return []
    
    def get_vector_layers(self, workspace):
        """
        获取指定工作区中的所有矢量图层
        
        Args:
            workspace: 工作区名称
            
        Returns:
            矢量图层列表
        """
        try:
            # 检查工作区是否存在
            if not self.manager.check_workspace_exists(workspace):
                self.logger.error(f"工作区 '{workspace}' 不存在")
                return []
            
            # 使用GeoServer REST API获取矢量图层列表
            base_url = self.manager.geo.service_url
            if not base_url.endswith('/rest'):
                base_url = f"{base_url}/rest"
            
            auth = (self.manager.geo.username, self.manager.geo.password)
            url = f"{base_url}/workspaces/{workspace}/datastores.json"
            
            self.logger.debug(f"正在请求数据存储列表: {url}")
            response = requests.get(url, auth=auth)
            
            if response.status_code != 200:
                self.logger.error(f"获取数据存储列表失败，状态码: {response.status_code}")
                return []
                
            data = response.json()
            self.logger.debug(f"数据存储响应: {json.dumps(data, ensure_ascii=False)}")
            
            vector_layers = []
            
            # 处理数据存储列表
            if 'dataStores' in data and 'dataStore' in data['dataStores']:
                for store in data['dataStores']['dataStore']:
                    store_name = store['name']
                    self.logger.debug(f"找到数据存储: {store_name}")
                    
                    # 获取存储下的特征类型列表
                    store_url = f"{base_url}/workspaces/{workspace}/datastores/{store_name}/featuretypes.json"
                    self.logger.debug(f"正在请求特征类型列表: {store_url}")
                    store_response = requests.get(store_url, auth=auth)
                    
                    if store_response.status_code == 200:
                        store_data = store_response.json()
                        self.logger.debug(f"特征类型响应: {json.dumps(store_data, ensure_ascii=False)}")
                        
                        if 'featureTypes' in store_data and 'featureType' in store_data['featureTypes']:
                            for feature_type in store_data['featureTypes']['featureType']:
                                # 获取特征类型的详细信息
                                feature_url = f"{base_url}/workspaces/{workspace}/datastores/{store_name}/featuretypes/{feature_type['name']}.json"
                                feature_response = requests.get(feature_url, auth=auth)
                                
                                if feature_response.status_code == 200:
                                    feature_data = feature_response.json()
                                    feature_detail = feature_data.get('featureType', {})
                                    
                                    # 获取几何类型
                                    geometry_type = "Unknown"
                                    attributes = feature_detail.get('attributes', {}).get('attribute', [])
                                    for attr in attributes:
                                        binding = attr.get('binding', '').lower()
                                        if 'geometry' in binding:
                                            # 尝试从binding中获取更详细的几何类型
                                            if 'polygon' in binding:
                                                geometry_type = 'MultiPolygon'
                                            elif 'linestring' in binding or 'line' in binding:
                                                geometry_type = 'MultiLineString'
                                            elif 'point' in binding:
                                                geometry_type = 'MultiPoint'
                                            else:
                                                geometry_type = attr.get('binding', '').split('.')[-1]
                                            self.logger.debug(f"为图层 {feature_detail.get('name', '')} 找到几何类型: {geometry_type}")
                                            break
                                    
                                    # 如果无法从属性中确定几何类型，尝试从其他字段获取
                                    if geometry_type == "Unknown":
                                        # 从schema或keywords中尝试获取信息
                                        keywords = feature_detail.get('keywords', {}).get('string', [])
                                        for keyword in keywords:
                                            keyword_lower = keyword.lower()
                                            if 'polygon' in keyword_lower:
                                                geometry_type = 'MultiPolygon'
                                                break
                                            elif 'linestring' in keyword_lower or 'line' in keyword_lower:
                                                geometry_type = 'MultiLineString'
                                                break
                                            elif 'point' in keyword_lower:
                                                geometry_type = 'MultiPoint'
                                                break
                                    
                                    # 如果仍然无法确定，则默认为MultiPolygon
                                    if geometry_type == "Unknown":
                                        geometry_type = "MultiPolygon"
                                        self.logger.debug(f"无法确定图层 {feature_detail.get('name', '')} 的几何类型，默认设置为MultiPolygon")
                                    
                                    layer_info = {
                                        'name': feature_detail.get('name', ''),
                                        'store': store_name,
                                        'workspace': workspace,
                                        'id': f"{workspace}:{feature_detail.get('name', '')}",
                                        'bbox': feature_detail.get('latLonBoundingBox', {}),
                                        'type': 'vector',
                                        'geometry_type': geometry_type
                                    }
                                    
                                    # 获取默认样式信息
                                    style_url = f"{base_url}/layers/{workspace}:{feature_detail.get('name', '')}.json"
                                    style_response = requests.get(style_url, auth=auth)
                                    if style_response.status_code == 200:
                                        style_data = style_response.json()
                                        layer_data = style_data.get('layer', {})
                                        default_style = layer_data.get('defaultStyle', {})
                                        if default_style:
                                            layer_info['default_style'] = default_style.get('name', 'default_polygon')
                                    
                                    self.logger.debug(f"添加矢量图层: {layer_info['name']}")
                                    vector_layers.append(layer_info)
                                else:
                                    self.logger.warning(f"获取特征类型 '{feature_type['name']}' 详情失败，状态码: {feature_response.status_code}")
                    else:
                        self.logger.warning(f"获取存储 '{store_name}' 的特征类型列表失败，状态码: {store_response.status_code}")
            else:
                self.logger.warning(f"在工作区 '{workspace}' 中未找到数据存储")
            
            self.logger.info(f"在工作区 '{workspace}' 中找到 {len(vector_layers)} 个矢量图层")
            return vector_layers
            
        except Exception as e:
            self.logger.error(f"获取矢量图层列表时出错: {str(e)}")
            return []
    
    def get_all_layers(self, workspace):
        """
        获取工作区中的所有图层（栅格和矢量）并按照前端需要的格式返回
        
        Args:
            workspace: 工作区名称
            
        Returns:
            格式化后的图层信息列表
        """
        try:
            # 获取栅格和矢量图层
            raster_layers = self.get_raster_layers(workspace)
            vector_layers = self.get_vector_layers(workspace)
            
            # 格式化图层信息
            formatted_layers = []
            
            # 处理栅格图层
            for layer in raster_layers:
                formatted_layer = {
                    "id": layer.get('id', ''),
                    "name": layer.get('name', ''),
                    "type": "raster",
                    "initialLoad": False,
                    "protocol": "WMTS",
                    "workspace": workspace,
                    "layerName": layer.get('name', ''),
                    "theme": ""
                }
                formatted_layers.append(formatted_layer)
            
            # 处理矢量图层
            for layer in vector_layers:
                formatted_layer = {
                    "id": layer.get('id', ''),
                    "name": layer.get('name', ''),
                    "type": "vector",
                    "protocol": "WFS",
                    "workspace": workspace,
                    "layerName": layer.get('name', ''),
                    "initialLoad": False,
                    "theme": ""
                }
                
                # 添加默认样式信息（如果有）
                if 'default_style' in layer:
                    formatted_layer["defaultStyle"] = layer.get('default_style')
                else:
                    formatted_layer["defaultStyle"] = "default_polygon"
                
                # 添加几何类型信息（如果有）并规范化类型名称
                if 'geometry_type' in layer:
                    # 获取几何类型，此时应该已经是规范化的
                    formatted_layer["geometryType"] = layer.get('geometry_type')
                else:
                    # 默认为MultiPolygon类型
                    formatted_layer["geometryType"] = "MultiPolygon"
                
                formatted_layers.append(formatted_layer)
            
            self.logger.info(f"在工作区 '{workspace}' 中找到 {len(formatted_layers)} 个图层（{len(raster_layers)} 个栅格，{len(vector_layers)} 个矢量）")
            return formatted_layers
            
        except Exception as e:
            self.logger.error(f"获取所有图层时出错: {str(e)}")
            return []
    
    def _fetch_missing_bboxes(self, layers, workspace):
        """
        从WMS GetCapabilities获取缺失的边界框信息
        
        Args:
            layers: 图层列表
            workspace: 工作区名称
        """
        try:
            # 检查是否有图层缺少边界框
            missing_bbox_layers = [layer for layer in layers if not layer.get('bbox')]
            if not missing_bbox_layers:
                return
                
            self.logger.info(f"尝试从WMS GetCapabilities获取 {len(missing_bbox_layers)} 个图层的边界框")
            
            # 构建WMS GetCapabilities请求
            base_url = self.manager.geo.service_url
            if base_url.endswith('/rest'):
                base_url = base_url[:-5]  # 移除'/rest'以获取GeoServer基础URL
                
            # 确保URL正确
            if not base_url.endswith('/'):
                base_url += '/'
                
            wms_url = f"{base_url}wms"
            params = {
                'SERVICE': 'WMS',
                'VERSION': '1.3.0',
                'REQUEST': 'GetCapabilities'
            }
            
            self.logger.debug(f"请求WMS能力文档: {wms_url}")
            
            auth = (self.manager.geo.username, self.manager.geo.password)
            response = requests.get(wms_url, params=params, auth=auth)
            
            if response.status_code != 200:
                self.logger.warning(f"获取WMS能力文档失败，状态码: {response.status_code}")
                
                # 尝试使用GeoServer REST API获取每个图层的详细信息
                self.logger.info("尝试使用REST API获取图层边界框")
                self._fetch_bboxes_from_rest_api(missing_bbox_layers, workspace)
                return
                
            # 解析XML响应
            try:
                root = ET.fromstring(response.content)
                
                # 定义命名空间
                namespaces = {
                    'wms': 'http://www.opengis.net/wms',
                    'xlink': 'http://www.w3.org/1999/xlink'
                }
                
                # 查找所有Layer元素
                layer_elements = root.findall('.//wms:Layer', namespaces)
                
                for layer in missing_bbox_layers:
                    layer_name = f"{workspace}:{layer['name']}"
                    
                    for layer_elem in layer_elements:
                        name_elem = layer_elem.find('./wms:Name', namespaces)
                        if name_elem is not None and name_elem.text == layer_name:
                            # 找到匹配的图层，提取边界框
                            bbox_elem = layer_elem.find('./wms:EX_GeographicBoundingBox', namespaces)
                            if bbox_elem is not None:
                                try:
                                    minx = float(bbox_elem.find('./wms:westBoundLongitude', namespaces).text)
                                    miny = float(bbox_elem.find('./wms:southBoundLatitude', namespaces).text)
                                    maxx = float(bbox_elem.find('./wms:eastBoundLongitude', namespaces).text)
                                    maxy = float(bbox_elem.find('./wms:northBoundLatitude', namespaces).text)
                                    
                                    # 更新图层的边界框信息
                                    layer['bbox'] = {
                                        'minx': minx,
                                        'miny': miny,
                                        'maxx': maxx,
                                        'maxy': maxy
                                    }
                                    
                                    self.logger.info(f"成功从WMS获取图层 '{layer['name']}' 的边界框: ({minx}, {miny}) - ({maxx}, {maxy})")
                                except Exception as e:
                                    self.logger.warning(f"解析图层 '{layer['name']}' 的边界框时出错: {str(e)}")
                            break
                
                # 对于仍然没有边界框的图层，尝试使用REST API
                still_missing = [layer for layer in layers if not layer.get('bbox')]
                if still_missing:
                    self.logger.info(f"仍有 {len(still_missing)} 个图层没有边界框，尝试使用REST API")
                    self._fetch_bboxes_from_rest_api(still_missing, workspace)
                            
            except Exception as e:
                self.logger.warning(f"解析WMS能力文档时出错: {str(e)}")
                # 尝试使用REST API作为备选方法
                self._fetch_bboxes_from_rest_api(missing_bbox_layers, workspace)
                
        except Exception as e:
            self.logger.error(f"获取缺失边界框时出错: {str(e)}")
            
    def _fetch_bboxes_from_rest_api(self, layers, workspace):
        """
        使用GeoServer REST API获取图层边界框
        
        Args:
            layers: 图层列表
            workspace: 工作区名称
        """
        try:
            base_url = self.manager.geo.service_url
            if not base_url.endswith('/rest'):
                base_url = f"{base_url}/rest"
                
            auth = (self.manager.geo.username, self.manager.geo.password)
            
            for layer in layers:
                # 获取图层详细信息
                layer_url = f"{base_url}/workspaces/{workspace}/coverages/{layer['name']}.json"
                self.logger.debug(f"获取图层详细信息: {layer_url}")
                
                response = requests.get(layer_url, auth=auth)
                if response.status_code == 200:
                    try:
                        data = response.json()
                        coverage = data.get('coverage', {})
                        bbox = coverage.get('latLonBoundingBox', {})
                        
                        if bbox:
                            layer['bbox'] = bbox
                            self.logger.info(f"成功从REST API获取图层 '{layer['name']}' 的边界框")
                        else:
                            # 尝试获取原始边界框并转换
                            native_bbox = coverage.get('nativeBoundingBox', {})
                            if native_bbox:
                                # 这里应该有坐标系转换，但简化处理
                                layer['bbox'] = native_bbox
                                self.logger.info(f"使用原生边界框作为图层 '{layer['name']}' 的边界框")
                    except Exception as e:
                        self.logger.warning(f"解析图层 '{layer['name']}' 的REST响应时出错: {str(e)}")
                else:
                    self.logger.warning(f"获取图层 '{layer['name']}' 详细信息失败，状态码: {response.status_code}")
                    
                    # 如果仍然没有边界框，设置一个默认的全球范围边界框
                    if not layer.get('bbox'):
                        self.logger.warning(f"为图层 '{layer['name']}' 设置默认全球范围边界框")
                        layer['bbox'] = {
                            'minx': -180.0,
                            'miny': -90.0,
                            'maxx': 180.0,
                            'maxy': 90.0
                        }
                        
        except Exception as e:
            self.logger.error(f"从REST API获取边界框时出错: {str(e)}")
    
    def query_point(self, lat, lon, workspace):
        """
        查询给定坐标点处的栅格图层
        
        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称
            
        Returns:
            包含该坐标点有效数据的图层信息列表
        """
        try:
            self.logger.info(f"开始查询坐标点 ({lat}, {lon}) 在工作区 '{workspace}' 中的图层")
            
            # 获取工作区中的所有栅格图层
            raster_layers = self.get_raster_layers(workspace)
            if not raster_layers:
                self.logger.warning(f"工作区 '{workspace}' 中没有找到栅格图层")
                return []
                
            result_layers = []
            
            # 对每个栅格图层进行检查
            for layer in raster_layers:
                # 首先检查坐标点是否在图层的边界框内
                bbox = layer.get('bbox', {})
                if bbox:
                    try:
                        minx = float(bbox.get('minx', 0))
                        miny = float(bbox.get('miny', 0))
                        maxx = float(bbox.get('maxx', 0))
                        maxy = float(bbox.get('maxy', 0))
                        
                        self.logger.debug(f"检查图层 '{layer['name']}' 的边界框: ({minx}, {miny}) - ({maxx}, {maxy})")
                        
                        # 如果点不在边界框内，则跳过
                        if not (minx <= lon <= maxx and miny <= lat <= maxy):
                            self.logger.debug(f"坐标点 ({lat}, {lon}) 不在图层 '{layer['name']}' 的边界框内，跳过")
                            continue
                            
                        self.logger.debug(f"坐标点 ({lat}, {lon}) 在图层 '{layer['name']}' 的边界框内，检查数据有效性")
                        
                        # 使用WMS GetFeatureInfo检查该点是否有有效数据
                        has_data = self._check_point_data(lat, lon, layer['workspace'], layer['name'])
                        if has_data:
                            self.logger.info(f"在图层 '{layer['name']}' 中找到有效数据")
                            result_layers.append(layer)
                        else:
                            self.logger.debug(f"图层 '{layer['name']}' 在坐标点处没有有效数据")
                    except (ValueError, TypeError) as e:
                        self.logger.warning(f"处理图层 '{layer['name']}' 的边界框时出错: {str(e)}")
                else:
                    self.logger.warning(f"图层 '{layer['name']}' 没有边界框信息")
                    
                    # 尝试直接检查该点是否有数据，即使没有边界框
                    self.logger.debug(f"尝试直接检查图层 '{layer['name']}' 在坐标点处是否有数据")
                    has_data = self._check_point_data(lat, lon, layer['workspace'], layer['name'])
                    if has_data:
                        self.logger.info(f"在图层 '{layer['name']}' 中找到有效数据")
                        result_layers.append(layer)
            
            self.logger.info(f"在坐标点 ({lat}, {lon}) 处找到 {len(result_layers)} 个有效栅格图层")
            return result_layers
            
        except Exception as e:
            self.logger.error(f"查询坐标点 ({lat}, {lon}) 时出错: {str(e)}")
            return []
            
    def _check_point_data(self, lat, lon, workspace, layer_name):
        """
        检查栅格图层在指定坐标点是否有有效数据
        
        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区
            layer_name: 图层名称
            
        Returns:
            布尔值，表示是否有有效数据
        """
        try:
            base_url = self.manager.geo.service_url
            if base_url.endswith('/rest'):
                base_url = base_url[:-5]  # 移除'/rest'以获取GeoServer基础URL
                
            # 确保URL正确
            if not base_url.endswith('/'):
                base_url += '/'
                
            # 使用WMS GetFeatureInfo查询坐标点
            wms_url = f"{base_url}wms"
            
            # 使用较小的BBOX以提高精度
            bbox_width = 0.0002  # 约20米
            bbox_height = 0.0002  # 约20米
            
            # 首先尝试使用application/json格式
            params = {
                'SERVICE': 'WMS',
                'VERSION': '1.1.1',
                'REQUEST': 'GetFeatureInfo',
                'LAYERS': f'{workspace}:{layer_name}',
                'QUERY_LAYERS': f'{workspace}:{layer_name}',
                'STYLES': '',
                'SRS': 'EPSG:4326',
                'BBOX': f"{lon-bbox_width/2},{lat-bbox_height/2},{lon+bbox_width/2},{lat+bbox_height/2}",
                'WIDTH': 10,
                'HEIGHT': 10,
                'FORMAT': 'image/png',
                'INFO_FORMAT': 'application/json',
                'X': 5,
                'Y': 5,
                'FEATURE_COUNT': 10  # 增加返回的特征数量
            }
            
            self.logger.debug(f"发送WMS GetFeatureInfo请求(JSON): {wms_url}，参数: {params}")
            
            auth = (self.manager.geo.username, self.manager.geo.password)
            response = requests.get(wms_url, params=params, auth=auth)
            
            if response.status_code != 200:
                self.logger.warning(f"获取坐标点 ({lat}, {lon}) 的栅格数据失败，状态码: {response.status_code}")
                
                # 尝试获取图层的基本信息，检查图层是否可访问
                try:
                    capability_params = {
                        'SERVICE': 'WMS',
                        'VERSION': '1.1.1',
                        'REQUEST': 'GetCapabilities',
                        'LAYERS': f'{workspace}:{layer_name}'
                    }
                    
                    self.logger.debug(f"检查图层可访问性: {wms_url}，参数: {capability_params}")
                    capability_response = requests.get(wms_url, params=capability_params, auth=auth)
                    
                    if capability_response.status_code != 200:
                        self.logger.warning(f"图层 '{workspace}:{layer_name}' 不可访问，状态码: {capability_response.status_code}")
                        return False
                        
                    self.logger.info(f"图层 '{workspace}:{layer_name}' 可访问，但在坐标点 ({lat}, {lon}) 处没有数据")
                except Exception as e:
                    self.logger.warning(f"检查图层可访问性时出错: {str(e)}")
                
                return False
                
            # 记录原始响应内容
            self.logger.debug(f"GetFeatureInfo响应(JSON): {response.text[:1000]}")
            
            # 尝试解析JSON响应
            try:
                data = response.json()
                features = data.get('features', [])
                
                if features:
                    # 检查是否存在有效数据（非空值）
                    for feature in features:
                        properties = feature.get('properties', {})
                        self.logger.debug(f"特征属性: {properties}")
                        
                        # 检查是否有任何非空属性
                        for key, value in properties.items():
                            if key.lower() in ('gray_index', 'band1', 'band2', 'band3', 'red', 'green', 'blue', 'value'):
                                # 对于已知的栅格值属性，检查是否有有效值
                                if value not in (None, "nodata", 0, "0", ""):
                                    self.logger.debug(f"找到有效栅格值: {key}={value}")
                                    return True
                            elif value not in (None, "nodata", "", "0"):
                                # 对于其他属性，如果有任何非空值，也认为有效
                                self.logger.debug(f"找到有效属性值: {key}={value}")
                                return True
            except json.JSONDecodeError:
                self.logger.warning(f"解析JSON响应失败，尝试其他格式")
            
            # 如果JSON格式没有返回有效数据，尝试使用text/plain格式
            params['INFO_FORMAT'] = 'text/plain'
            self.logger.debug(f"发送WMS GetFeatureInfo请求(TEXT): {wms_url}，参数: {params}")
            
            text_response = requests.get(wms_url, params=params, auth=auth)
            if text_response.status_code == 200:
                self.logger.debug(f"GetFeatureInfo响应(TEXT): {text_response.text[:1000]}")
                
                # 检查文本响应是否包含有意义的数据
                text_content = text_response.text.strip()
                if text_content and "no feature" not in text_content.lower() and "no results" not in text_content.lower():
                    # 检查是否包含数值
                    import re
                    # 查找形如 "Value: 123" 的模式
                    value_matches = re.findall(r'value:?\s*(-?\d+\.?\d*)', text_content, re.IGNORECASE)
                    if value_matches:
                        for value in value_matches:
                            try:
                                num_value = float(value)
                                if num_value != 0:  # 非零值视为有效数据
                                    self.logger.debug(f"在文本响应中找到有效数值: {num_value}")
                                    return True
                            except ValueError:
                                pass
                    
                    # 如果没有找到数值模式但文本非空，也可能是有效数据
                    if len(text_content) > 10:  # 避免只有简单的"no data"之类的响应
                        self.logger.debug(f"在文本响应中找到非空内容，可能有有效数据")
                        return True
            
            # 最后尝试使用WCS GetCoverage进行像素值检查
            try:
                # 构建WCS请求URL
                wcs_url = f"{base_url}wcs"
                wcs_params = {
                    'SERVICE': 'WCS',
                    'VERSION': '2.0.1',
                    'REQUEST': 'GetCoverage',
                    'COVERAGEID': f'{workspace}:{layer_name}',
                    'FORMAT': 'image/tiff',
                    'SUBSET': [f'Long({lon})', f'Lat({lat})'],
                    'SUBSETTINGCRS': 'http://www.opengis.net/def/crs/EPSG/0/4326'
                }
                
                self.logger.debug(f"发送WCS GetCoverage请求: {wcs_url}，参数: {wcs_params}")
                
                # 这里不实际发送请求，因为需要处理二进制响应并解析GeoTIFF，
                # 这超出了当前实现的范围。但在实际应用中，可以考虑实现这部分。
                
                # 如果前面的方法都没有检测到有效数据，默认返回False
                self.logger.debug(f"坐标点 ({lat}, {lon}) 在图层 '{workspace}:{layer_name}' 中没有检测到有效数据")
                return False
                                
            except Exception as e:
                self.logger.warning(f"WCS请求出错: {str(e)}")
                return False
            
        except Exception as e:
            self.logger.error(f"检查坐标点 ({lat}, {lon}) 的栅格数据时出错: {str(e)}")
            return False

    def get_raster_info(self, workspace, layer_name):
        """
        获取栅格图层的详细信息
        
        Args:
            workspace: 工作区名称
            layer_name: 图层名称
            
        Returns:
            图层详细信息字典
        """
        try:
            base_url = self.manager.geo.service_url
            if not base_url.endswith('/rest'):
                base_url = f"{base_url}/rest"
                
            auth = (self.manager.geo.username, self.manager.geo.password)
            
            # 尝试获取图层详细信息
            layer_url = f"{base_url}/workspaces/{workspace}/coverages/{layer_name}.json"
            self.logger.debug(f"获取图层详细信息: {layer_url}")
            
            response = requests.get(layer_url, auth=auth)
            if response.status_code != 200:
                self.logger.warning(f"获取图层 '{workspace}:{layer_name}' 详细信息失败，状态码: {response.status_code}")
                return None
                
            data = response.json()
            return data.get('coverage', {})
            
        except Exception as e:
            self.logger.error(f"获取图层 '{workspace}:{layer_name}' 详细信息时出错: {str(e)}")
            return None
            
    def get_pixel_value(self, lat, lon, workspace, layer_name):
        """
        获取栅格图层在指定坐标点的像素值
        
        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称
            layer_name: 图层名称
            
        Returns:
            像素值字典，包含波段及其对应的值
        """
        try:
            # 验证参数
            if not all([lat, lon, workspace, layer_name]):
                self.logger.error("获取像素值需要提供纬度、经度、工作区和图层名")
                return None
                
            # 构建WMS GetFeatureInfo请求
            base_url = self.manager.geo.service_url
            if base_url.endswith('/rest'):
                base_url = base_url[:-5]  # 移除'/rest'以获取GeoServer基础URL
                
            # 确保URL正确
            if not base_url.endswith('/'):
                base_url += '/'
                
            wms_url = f"{base_url}wms"
            
            # 使用较小的BBOX以提高精度
            bbox_width = 0.0001  # 约10米
            bbox_height = 0.0001  # 约10米
            
            params = {
                'SERVICE': 'WMS',
                'VERSION': '1.1.1',
                'REQUEST': 'GetFeatureInfo',
                'LAYERS': f'{workspace}:{layer_name}',
                'QUERY_LAYERS': f'{workspace}:{layer_name}',
                'STYLES': '',
                'SRS': 'EPSG:4326',
                'BBOX': f"{lon-bbox_width/2},{lat-bbox_height/2},{lon+bbox_width/2},{lat+bbox_height/2}",
                'WIDTH': 1,
                'HEIGHT': 1,
                'FORMAT': 'image/png',
                'INFO_FORMAT': 'application/json',
                'X': 0,
                'Y': 0,
                'FEATURE_COUNT': 1
            }
            
            self.logger.debug(f"发送GetFeatureInfo请求获取像素值: {wms_url}")
            
            auth = (self.manager.geo.username, self.manager.geo.password)
            response = requests.get(wms_url, params=params, auth=auth)
            
            if response.status_code != 200:
                self.logger.error(f"获取像素值失败，状态码: {response.status_code}")
                return None
                
            try:
                # 解析JSON响应
                data = response.json()
                self.logger.debug(f"GetFeatureInfo响应: {data}")
                
                if 'features' in data and len(data['features']) > 0:
                    feature = data['features'][0]
                    if 'properties' in feature:
                        # 提取所有属性作为像素值
                        pixel_values = feature['properties']
                        
                        # 过滤掉非数值属性
                        filtered_values = {}
                        for key, value in pixel_values.items():
                            # 尝试将值转换为数字
                            try:
                                if value is not None:
                                    if isinstance(value, (int, float)):
                                        filtered_values[key] = value
                                    else:
                                        try:
                                            filtered_values[key] = float(value)
                                        except (ValueError, TypeError):
                                            # 保留非数值属性
                                            filtered_values[key] = value
                            except Exception:
                                filtered_values[key] = value
                        
                        return {
                            'coordinates': {'lat': lat, 'lon': lon},
                            'layer': f"{workspace}:{layer_name}",
                            'values': filtered_values
                        }
                    
                # 如果无法获取像素值，尝试使用text/plain格式
                return self._get_pixel_value_text_format(lat, lon, workspace, layer_name)
                
            except json.JSONDecodeError:
                # JSON解析失败，尝试使用text/plain格式
                return self._get_pixel_value_text_format(lat, lon, workspace, layer_name)
                
        except Exception as e:
            self.logger.error(f"获取像素值时出错: {str(e)}")
            return None
            
    def _get_pixel_value_text_format(self, lat, lon, workspace, layer_name):
        """
        使用text/plain格式获取栅格图层在指定坐标点的像素值
        
        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称
            layer_name: 图层名称
            
        Returns:
            像素值字典
        """
        try:
            # 构建WMS GetFeatureInfo请求
            base_url = self.manager.geo.service_url
            if base_url.endswith('/rest'):
                base_url = base_url[:-5]
                
            if not base_url.endswith('/'):
                base_url += '/'
                
            wms_url = f"{base_url}wms"
            
            bbox_width = 0.0001
            bbox_height = 0.0001
            
            params = {
                'SERVICE': 'WMS',
                'VERSION': '1.1.1',
                'REQUEST': 'GetFeatureInfo',
                'LAYERS': f'{workspace}:{layer_name}',
                'QUERY_LAYERS': f'{workspace}:{layer_name}',
                'STYLES': '',
                'SRS': 'EPSG:4326',
                'BBOX': f"{lon-bbox_width/2},{lat-bbox_height/2},{lon+bbox_width/2},{lat+bbox_height/2}",
                'WIDTH': 1,
                'HEIGHT': 1,
                'FORMAT': 'image/png',
                'INFO_FORMAT': 'text/plain',
                'X': 0,
                'Y': 0,
                'FEATURE_COUNT': 1
            }
            
            self.logger.debug("使用text/plain格式尝试获取像素值")
            
            auth = (self.manager.geo.username, self.manager.geo.password)
            response = requests.get(wms_url, params=params, auth=auth)
            
            if response.status_code != 200:
                self.logger.error(f"获取像素值失败，状态码: {response.status_code}")
                return None
                
            # 解析文本响应
            text_content = response.text.strip()
            self.logger.debug(f"GetFeatureInfo文本响应: {text_content}")
            
            if not text_content or "no feature" in text_content.lower() or "no results" in text_content.lower():
                self.logger.warning("未找到像素值")
                return {
                    'coordinates': {'lat': lat, 'lon': lon},
                    'layer': f"{workspace}:{layer_name}",
                    'values': {},
                    'message': "该坐标点无有效数据"
                }
                
            # 尝试从文本响应中提取像素值
            import re
            values = {}
            
            # 查找形如 "band1 = 123.45" 的模式
            band_pattern = re.compile(r'(?:band|Band|BAND)(\d+)\s*=\s*([0-9.-]+)')
            band_matches = band_pattern.findall(text_content)
            
            if band_matches:
                for band, value in band_matches:
                    try:
                        values[f"band{band}"] = float(value)
                    except ValueError:
                        values[f"band{band}"] = value
                
            # 查找形如 "Red = 123" 的模式
            color_pattern = re.compile(r'(?:Red|GREEN|Blue|Gray|R|G|B)\s*=\s*([0-9.-]+)')
            color_matches = color_pattern.findall(text_content)
            
            if color_matches:
                colors = ['Red', 'Green', 'Blue', 'Gray']
                for i, value in enumerate(color_matches):
                    if i < len(colors):
                        try:
                            values[colors[i]] = float(value)
                        except ValueError:
                            values[colors[i]] = value
            
            # 通用键值对模式
            kv_pattern = re.compile(r'([a-zA-Z0-9_]+)\s*[:=]\s*([0-9.-]+)')
            kv_matches = kv_pattern.findall(text_content)
            
            if kv_matches:
                for key, value in kv_matches:
                    if key.lower() not in [k.lower() for k in values.keys()]:
                        try:
                            values[key] = float(value)
                        except ValueError:
                            values[key] = value
            
            # 如果没有找到任何匹配，但有内容，则返回原始文本
            if not values and text_content:
                values = {"raw_text": text_content}
                
            return {
                'coordinates': {'lat': lat, 'lon': lon},
                'layer': f"{workspace}:{layer_name}",
                'values': values
            }
            
        except Exception as e:
            self.logger.error(f"解析文本格式像素值时出错: {str(e)}")
            return None
            
    def query_point_with_values(self, lat, lon, workspace):
        """
        查询给定坐标点处的栅格图层，并返回有效像素值的图层
        
        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称
            
        Returns:
            包含该坐标点有效数据的图层信息列表，每个图层包含像素值
        """
        try:
            self.logger.info(f"开始查询坐标点 ({lat}, {lon}) 在工作区 '{workspace}' 中的图层及像素值")
            
            # 获取工作区中的所有栅格图层
            raster_layers = self.get_raster_layers(workspace)
            if not raster_layers:
                self.logger.warning(f"工作区 '{workspace}' 中没有找到栅格图层")
                return []
                
            result_layers = []
            
            # 对每个栅格图层进行检查
            for layer in raster_layers:
                # 首先检查坐标点是否在图层的边界框内
                bbox = layer.get('bbox', {})
                if not bbox:
                    self.logger.warning(f"图层 '{layer['name']}' 没有边界框信息，跳过")
                    continue
                    
                try:
                    minx = float(bbox.get('minx', 0))
                    miny = float(bbox.get('miny', 0))
                    maxx = float(bbox.get('maxx', 0))
                    maxy = float(bbox.get('maxy', 0))
                    
                    self.logger.debug(f"检查图层 '{layer['name']}' 的边界框: ({minx}, {miny}) - ({maxx}, {maxy})")
                    
                    # 如果点不在边界框内，则跳过
                    if not (minx <= lon <= maxx and miny <= lat <= maxy):
                        self.logger.debug(f"坐标点 ({lat}, {lon}) 不在图层 '{layer['name']}' 的边界框内，跳过")
                        continue
                        
                    self.logger.debug(f"坐标点 ({lat}, {lon}) 在图层 '{layer['name']}' 的边界框内，获取像素值")
                    
                    # 获取该点的像素值
                    pixel_data = self.get_pixel_value(lat, lon, workspace, layer['name'])
                    
                    # 如果获取像素值失败或没有像素值，跳过该图层
                    if not pixel_data or not pixel_data.get('values'):
                        self.logger.debug(f"图层 '{layer['name']}' 在该坐标点无有效像素值，跳过")
                        continue
                        
                    pixel_values = pixel_data.get('values', {})
                    
                    # 检查是否为全黑(0,0,0)或全白(255,255,255)
                    is_black = False
                    is_white = False
                    has_transparent_alpha = False
                    
                    # 检查是否有透明alpha通道
                    if 'ALPHA_BAND' in pixel_values and pixel_values['ALPHA_BAND'] == 0:
                        has_transparent_alpha = True
                        self.logger.debug(f"图层 '{layer['name']}' 在该坐标点ALPHA_BAND为0（完全透明），跳过")
                    
                    # 检查是否有RGB值
                    if all(key in pixel_values for key in ['Red', 'Green', 'Blue']):
                        rgb_values = [pixel_values.get('Red', 0), pixel_values.get('Green', 0), pixel_values.get('Blue', 0)]
                        is_black = all(v == 0 for v in rgb_values)
                        is_white = all(v == 255 for v in rgb_values)
                        
                    # 检查是否有band值（多波段栅格）
                    elif any(key.lower().startswith('band') for key in pixel_values):
                        band_values = [v for k, v in pixel_values.items() if k.lower().startswith('band')]
                        is_black = all(v == 0 for v in band_values)
                        is_white = all(v == 255 for v in band_values)
                        
                    # 检查其他可能的像素值格式
                    elif len(pixel_values) == 1:
                        single_value = list(pixel_values.values())[0]
                        is_black = single_value == 0
                        is_white = single_value == 255
                    
                    # 如果是全黑或全白或透明，跳过
                    if is_black:
                        self.logger.debug(f"图层 '{layer['name']}' 在该坐标点像素值为全黑，跳过")
                        continue
                    elif is_white:
                        self.logger.debug(f"图层 '{layer['name']}' 在该坐标点像素值为全白，跳过")
                        continue
                    elif has_transparent_alpha:
                        continue
                    
                    # 构建结果对象
                    layer_result = {
                        'name': layer['name'],
                        'store': layer['store'],
                        'workspace': layer['workspace'],
                        'id': layer['id'],
                        'bbox': layer['bbox'],
                        'pixel_values': pixel_values
                    }
                    
                    result_layers.append(layer_result)
                    self.logger.info(f"图层 '{layer['name']}' 在该坐标点有有效数据，添加到结果中")
                    
                except (ValueError, TypeError) as e:
                    self.logger.warning(f"处理图层 '{layer['name']}' 时出错: {str(e)}")
            
            self.logger.info(f"在坐标点 ({lat}, {lon}) 处找到 {len(result_layers)} 个有有效数据的栅格图层")
            return result_layers
            
        except Exception as e:
            self.logger.error(f"查询坐标点 ({lat}, {lon}) 时出错: {str(e)}")
            return [] 