#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer管理器 - 用于管理GeoServer服务的核心功能
"""

import os
import logging
import glob
import datetime
from geo.Geoserver import Geoserver
import requests
from requests.auth import HTTPBasicAuth

# 导入配置
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
from config import (
    GEOSERVER_URL, 
    GEOSERVER_USER, 
    GEOSERVER_PASSWORD,
    DEFAULT_WORKSPACE,
    DEFAULT_DATASTORE,
    LOG_LEVEL,
    LOG_FILE
)

# 配置日志
def setup_logging():
    """配置并返回日志系统"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 创建logs目录（如果不存在）
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # 如果没有指定日志文件，使用带时间戳的文件名
    if not LOG_FILE or LOG_FILE == "geoserver_api.log":
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file_path = os.path.join(log_dir, f'geoserver_{timestamp}.log')
    else:
        log_file_path = os.path.join(log_dir, LOG_FILE)
    
    # 创建日志处理器
    file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
    console_handler = logging.StreamHandler()
    
    # 设置格式
    formatter = logging.Formatter(log_format)
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, LOG_LEVEL))
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 添加处理器
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # 记录初始化日志
    root_logger.info(f"日志系统初始化完成，日志文件：{log_file_path}")
    return root_logger

# 初始化日志系统
logger = setup_logging()

class GeoServerManager:
    """用于管理GeoServer操作的类，如发布图层、创建工作区等。"""
    
    def __init__(self, url=GEOSERVER_URL, username=GEOSERVER_USER, password=GEOSERVER_PASSWORD):
        """初始化GeoServer连接。"""
        self.geo = Geoserver(url, username=username, password=password)
        logger.info(f"已连接到GeoServer：{url}")
        self.check_connection()
        
    def check_connection(self):
        """检查GeoServer连接是否正常，并获取GeoServer信息。"""
        try:
            # 构建REST API URL
            base_url = self.geo.service_url
            if not base_url.endswith('/rest'):
                base_url = f"{base_url}/rest"
                
            # 尝试访问about/version.json获取GeoServer版本信息
            url = f"{base_url}/about/version.json"
            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            headers = {"Accept": "application/json"}
            
            logger.info(f"正在检查GeoServer连接: {url}")
            
            response = requests.get(url, auth=auth, headers=headers)
            
            if response.status_code == 200:
                try:
                    info = response.json()
                    version = info.get('about', {}).get('resource', [{}])[0].get('Version', 'Unknown')
                    logger.info(f"成功连接到GeoServer，版本: {version}")
                except Exception as e:
                    logger.warning(f"解析GeoServer版本信息失败: {str(e)}")
            else:
                logger.warning(f"连接GeoServer返回错误状态码: {response.status_code}")
                logger.warning(f"尝试列出工作区以进一步验证连接...")
                
                # 如果获取版本信息失败，尝试列出工作区
                try:
                    workspaces = self.geo.get_workspaces()
                    if workspaces and 'workspaces' in workspaces:
                        ws_count = len(workspaces.get('workspaces', {}).get('workspace', []))
                        logger.info(f"成功获取工作区列表，共有 {ws_count} 个工作区")
                    else:
                        logger.warning("工作区列表为空或格式不正确")
                except Exception as e:
                    logger.error(f"获取工作区列表失败，可能无法正常连接GeoServer: {str(e)}")
                    raise ConnectionError(f"无法连接到GeoServer: {str(e)}")
                
        except Exception as e:
            logger.error(f"检查GeoServer连接失败: {str(e)}")
            raise ConnectionError(f"无法连接到GeoServer: {str(e)}")
        
    def check_workspace_exists(self, workspace):
        """
        检查工作区是否存在
        
        Args:
            workspace: 工作区名称
            
        Returns:
            工作区是否存在
        """
        try:
            all_workspaces = self.geo.get_workspaces()
            if all_workspaces and 'workspaces' in all_workspaces:
                for ws in all_workspaces.get('workspaces', {}).get('workspace', []):
                    if ws.get('name') == workspace:
                        return True
            return False
        except Exception as e:
            logger.error(f"检查工作区 '{workspace}' 是否存在时出错: {str(e)}")
            return False
            
    def verify_layer(self, workspace, store_name=None, layer_name=None):
        """
        验证图层是否存在
        
        Args:
            workspace: 工作区名称
            store_name: 存储名称，如果不提供则使用layer_name
            layer_name: 图层名称
            
        Returns:
            图层是否存在
        """
        if not layer_name:
            logger.warning("验证图层需要提供图层名称")
            return False
            
        try:
            base_url = self.geo.service_url
            if not base_url.endswith('/rest'):
                base_url = f"{base_url}/rest"
                
            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            headers = {"Accept": "application/json"}
            
            # 尝试多种可能的URL格式
            urls_to_try = []
            
            # 1. 使用完整路径: /workspaces/{ws}/datastores/{store}/featuretypes/{layer}
            if store_name:
                urls_to_try.append({
                    "url": f"{base_url}/workspaces/{workspace}/datastores/{store_name}/featuretypes/{layer_name}",
                    "description": f"完整路径 - 工作区/存储/图层: {workspace}/{store_name}/{layer_name}"
                })
                
            # 2. 使用图层名称可能与存储名称相同: /workspaces/{ws}/datastores/{store}/featuretypes/{store}
            if store_name:
                urls_to_try.append({
                    "url": f"{base_url}/workspaces/{workspace}/datastores/{store_name}/featuretypes/{store_name}",
                    "description": f"存储名称作为图层: {workspace}/{store_name}/{store_name}"
                })
                
            # 3. 直接使用图层路径: /layers/{ws}:{layer}
            urls_to_try.append({
                "url": f"{base_url}/layers/{workspace}:{layer_name}",
                "description": f"直接图层路径: {workspace}:{layer_name}"
            })
            
            # 4. 使用原始文件名
            urls_to_try.append({
                "url": f"{base_url}/workspaces/{workspace}/featuretypes/{layer_name}",
                "description": f"工作区/图层直接路径: {workspace}/{layer_name}"
            })
            
            for url_info in urls_to_try:
                logger.debug(f"尝试验证图层: {url_info['description']}")
                response = requests.get(url_info["url"], auth=auth, headers=headers)
                if response.status_code == 200:
                    logger.info(f"图层验证成功: {url_info['description']}")
                    return True
                else:
                    logger.debug(f"图层验证失败 ({response.status_code}): {url_info['description']}")
            
            logger.warning(f"无法验证图层存在性: {workspace}:{layer_name}")
            return False
            
        except Exception as e:
            logger.error(f"验证图层 '{layer_name}' 时出错: {str(e)}")
            return False
            
    def create_workspace(self, workspace=DEFAULT_WORKSPACE):
        """创建工作区"""
        try:
            if not self.check_workspace_exists(workspace):
                self.geo.create_workspace(workspace=workspace)
                logger.info(f"成功创建工作区: {workspace}")
                return True
            else:
                logger.info(f"工作区 {workspace} 已存在，无需创建")
                return True
        except Exception as e:
            logger.error(f"创建工作区 {workspace} 时出错: {str(e)}")
            return False
            
    def delete_workspace(self, workspace):
        """删除工作区"""
        try:
            self.geo.delete_workspace(workspace=workspace)
            logger.info(f"成功删除工作区: {workspace}")
            return True
        except Exception as e:
            logger.error(f"删除工作区 {workspace} 时出错: {str(e)}")
            return False
            
    def create_datastore(self, name, workspace=DEFAULT_WORKSPACE, conn_params=None):
        """
        创建数据存储
        
        Args:
            name: 数据存储名称
            workspace: 工作区名称
            conn_params: 连接参数，包括host、port、database、username、password
            
        Returns:
            创建是否成功
        """
        try:
            # 确保工作区存在
            if not self.check_workspace_exists(workspace):
                logger.info(f"工作区 {workspace} 不存在，正在创建")
                self.create_workspace(workspace)
            
            # 构建连接参数
            if not conn_params:
                logger.error("创建数据存储需要连接参数")
                return False
                
            # 创建PostGIS数据存储
            self.geo.create_pg_store(
                store_name=name,
                workspace=workspace,
                host=conn_params.get('host', 'localhost'),
                port=conn_params.get('port', 5432),
                database=conn_params.get('database'),
                user=conn_params.get('username'),
                passwd=conn_params.get('password')
            )
            
            logger.info(f"成功创建数据存储: {workspace}:{name}")
            return True
        except Exception as e:
            logger.error(f"创建数据存储 {workspace}:{name} 时出错: {str(e)}")
            return False 

    def publish_shapefile(self, shapefile_path, workspace=DEFAULT_WORKSPACE, store_name=None, layer_name=None, charset="UTF-8"):
        """
        发布Shapefile到GeoServer
        
        Args:
            shapefile_path: Shapefile文件路径
            workspace: 工作区名称
            store_name: 存储名称，如果不提供则使用文件名
            layer_name: 图层名称，如果不提供则使用存储名
            charset: 字符集，默认为UTF-8以支持中文
            
        Returns:
            发布是否成功
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(shapefile_path):
                logger.error(f"Shapefile文件不存在: {shapefile_path}")
                return False
                
            # 确保工作区存在
            if not self.check_workspace_exists(workspace):
                logger.info(f"工作区 {workspace} 不存在，正在创建")
                self.create_workspace(workspace)
                
            # 提取文件名（不带扩展名）作为默认存储名称
            base_name = os.path.splitext(os.path.basename(shapefile_path))[0]
            
            # 如果未提供存储名称，使用文件名
            if not store_name:
                store_name = base_name
                
            # 如果未提供图层名称，使用存储名称
            if not layer_name:
                layer_name = store_name
                
            # 使用REST API直接发布Shapefile，避免权限问题
            import zipfile
            import io
            import requests
            from requests.auth import HTTPBasicAuth
            
            # 创建包含所有Shapefile相关文件的ZIP内存文件
            zip_buffer = io.BytesIO()
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zipf:
                base_dir = os.path.dirname(shapefile_path)
                base_name = os.path.splitext(os.path.basename(shapefile_path))[0]
                
                # 添加所有相关文件
                for ext in ['.shp', '.dbf', '.shx', '.prj', '.qix', '.cpg', '.qmd']:
                    full_path = os.path.join(base_dir, base_name + ext)
                    if os.path.exists(full_path):
                        with open(full_path, 'rb') as f:
                            file_content = f.read()
                            zipf.writestr(os.path.basename(full_path), file_content)
                            logger.info(f"添加文件到ZIP: {os.path.basename(full_path)}")
            
            # 获取API URL
            base_url = self.geo.service_url
            if not base_url.endswith('/rest'):
                base_url = f"{base_url}/rest"
                
            # 构建上传URL
            upload_url = f"{base_url}/workspaces/{workspace}/datastores/{store_name}/file.shp"
            
            # 设置REST API请求头和参数
            headers = {'Content-type': 'application/zip'}
            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            params = {'charset': charset}  # 通过URL参数传递字符集
            
            # 发送ZIP数据
            zip_buffer.seek(0)
            response = requests.put(
                upload_url, 
                headers=headers,
                data=zip_buffer.getvalue(),
                auth=auth,
                params=params
            )
            
            if response.status_code >= 200 and response.status_code < 300:
                logger.info(f"成功通过REST API发布Shapefile: {response.status_code}")
            else:
                logger.error(f"REST API发布失败: {response.status_code} - {response.text}")
                return False
            
            logger.info(f"成功发布Shapefile: {workspace}:{store_name}:{layer_name}")
            
            # 验证图层是否成功发布
            if self.verify_layer(workspace, store_name, layer_name):
                logger.info(f"图层验证成功: {workspace}:{layer_name}")
                return True
            else:
                logger.warning(f"无法验证图层是否成功发布: {workspace}:{layer_name}")
                # 尝试使用文件名作为图层名称验证
                if self.verify_layer(workspace, store_name, base_name):
                    logger.info(f"图层验证成功(使用文件名): {workspace}:{base_name}")
                    return True
                else:
                    logger.warning(f"即使使用文件名也无法验证图层: {workspace}:{base_name}")
                    # 返回True，因为发布操作本身没有抛出异常
                    return True
                    
        except Exception as e:
            logger.error(f"发布Shapefile时出错: {str(e)}")
            return False
            
    def list_layers(self, workspace=None):
        """
        获取图层列表
        
        Args:
            workspace: 工作区名称，如果不提供则获取所有工作区的图层
            
        Returns:
            图层列表
        """
        try:
            base_url = self.geo.service_url
            if not base_url.endswith('/rest'):
                base_url = f"{base_url}/rest"
                
            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            headers = {"Accept": "application/json"}
            
            if workspace:
                # 获取特定工作区的图层
                url = f"{base_url}/workspaces/{workspace}/layers.json"
                logger.info(f"获取工作区 {workspace} 的图层列表")
            else:
                # 获取所有图层
                url = f"{base_url}/layers.json"
                logger.info("获取所有图层列表")
                
            response = requests.get(url, auth=auth, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                
                # 处理响应数据
                layers = []
                if "layers" in data and "layer" in data["layers"]:
                    for layer_info in data["layers"]["layer"]:
                        # 获取每个图层的详细信息
                        layer_name = layer_info.get("name", "")
                        layer_url = layer_info.get("href", "")
                        
                        # 提取工作区名称（通常在图层名称中，格式为workspace:layer_name）
                        ws_name = None
                        if ":" in layer_name:
                            ws_name = layer_name.split(":")[0]
                            layer_short_name = layer_name.split(":")[1]
                        else:
                            layer_short_name = layer_name
                            
                        # 如果指定了工作区，但这个图层不属于该工作区，则跳过
                        if workspace and ws_name != workspace:
                            continue
                            
                        # 请求图层详细信息
                        detail_response = requests.get(layer_url, auth=auth, headers=headers)
                        if detail_response.status_code == 200:
                            detail_data = detail_response.json()
                            layer_detail = detail_data.get("layer", {})
                            
                            # 构造图层信息
                            layer_item = {
                                "name": layer_short_name,
                                "full_name": layer_name,
                                "workspace": ws_name or "",
                                "type": layer_detail.get("type", ""),
                                "defaultStyle": layer_detail.get("defaultStyle", {}).get("name", ""),
                                "enabled": layer_detail.get("enabled", True),
                                "resource": layer_detail.get("resource", {}).get("href", ""),
                            }
                            
                            # 获取边界框信息
                            if "resource" in layer_detail:
                                resource_url = layer_detail["resource"]["href"]
                                resource_response = requests.get(resource_url, auth=auth, headers=headers)
                                if resource_response.status_code == 200:
                                    resource_data = resource_response.json()
                                    feature_type = resource_data.get("featureType") or resource_data.get("coverage", {})
                                    
                                    # 提取边界框
                                    if "nativeBoundingBox" in feature_type:
                                        layer_item["bbox"] = {
                                            "minx": feature_type["nativeBoundingBox"].get("minx"),
                                            "miny": feature_type["nativeBoundingBox"].get("miny"),
                                            "maxx": feature_type["nativeBoundingBox"].get("maxx"),
                                            "maxy": feature_type["nativeBoundingBox"].get("maxy"),
                                            "crs": feature_type["nativeBoundingBox"].get("crs")
                                        }
                            
                            layers.append(layer_item)
                
                logger.info(f"成功获取 {len(layers)} 个图层")
                return layers
            else:
                logger.error(f"获取图层列表失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"获取图层列表时出错: {str(e)}")
            return []
            
    def publish_shapefile_directory(self, directory_path, workspace=DEFAULT_WORKSPACE, store_name=None, charset="UTF-8"):
        """
        发布目录中所有Shapefile到GeoServer
        
        Args:
            directory_path: 包含Shapefile文件的目录路径
            workspace: 工作区名称
            store_name: 存储名称前缀，如果不提供则使用目录名称
            charset: 字符集，默认为UTF-8以支持中文
            
        Returns:
            包含成功和失败文件列表的字典
        """
        try:
            # 检查目录是否存在
            if not os.path.isdir(directory_path):
                logger.error(f"目录不存在: {directory_path}")
                return {'success': [], 'failed': []}
                
            # 确保工作区存在
            if not self.check_workspace_exists(workspace):
                logger.info(f"工作区 {workspace} 不存在，正在创建")
                self.create_workspace(workspace)
                
            # 如果未提供存储名称，使用目录名
            if not store_name:
                store_name = os.path.basename(os.path.normpath(directory_path))
                
            # 查找目录中所有.shp文件
            shapefile_pattern = os.path.join(directory_path, "*.shp")
            shapefile_paths = glob.glob(shapefile_pattern)
            
            if not shapefile_paths:
                logger.warning(f"目录中未找到Shapefile文件: {directory_path}")
                return {'success': [], 'failed': []}
                
            logger.info(f"在目录 {directory_path} 中找到 {len(shapefile_paths)} 个Shapefile文件")
            
            # 发布每个Shapefile
            success_files = []
            failed_files = []
            
            for shapefile_path in shapefile_paths:
                # 使用文件名作为图层名
                file_name = os.path.splitext(os.path.basename(shapefile_path))[0]
                # 创建唯一的存储名称
                unique_store_name = f"{store_name}_{file_name}"
                
                logger.info(f"正在发布Shapefile: {shapefile_path}")
                try:
                    result = self.publish_shapefile(
                        shapefile_path=shapefile_path,
                        workspace=workspace,
                        store_name=unique_store_name,
                        layer_name=file_name,
                        charset=charset
                    )
                    
                    if result:
                        success_files.append(os.path.basename(shapefile_path))
                    else:
                        failed_files.append(os.path.basename(shapefile_path))
                        
                except Exception as e:
                    logger.error(f"发布文件 {shapefile_path} 时出错: {str(e)}")
                    failed_files.append(os.path.basename(shapefile_path))
            
            # 日志记录结果
            logger.info(f"成功发布 {len(success_files)}/{len(shapefile_paths)} 个Shapefile文件")
            if failed_files:
                logger.warning(f"失败文件: {', '.join(failed_files)}")
                
            return {
                'success': success_files,
                'failed': failed_files
            }
            
        except Exception as e:
            logger.error(f"发布Shapefile目录时出错: {str(e)}")
            return {'success': [], 'failed': list(map(os.path.basename, glob.glob(os.path.join(directory_path, "*.shp"))))}
            
    def publish_geotiff(self, geotiff_path, workspace=DEFAULT_WORKSPACE, store_name=None, layer_name=None):
        """
        发布GeoTIFF到GeoServer
        
        Args:
            geotiff_path: GeoTIFF文件路径
            workspace: 工作区名称
            store_name: 存储名称，如果不提供则使用文件名
            layer_name: 图层名称，如果不提供则使用存储名
            
        Returns:
            发布是否成功
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(geotiff_path):
                logger.error(f"GeoTIFF文件不存在: {geotiff_path}")
                return False
                
            # 确保工作区存在
            if not self.check_workspace_exists(workspace):
                logger.info(f"工作区 {workspace} 不存在，正在创建")
                self.create_workspace(workspace)
                
            # 提取文件名（不带扩展名）作为默认存储名称
            base_name = os.path.splitext(os.path.basename(geotiff_path))[0]
            
            # 如果未提供存储名称，使用文件名
            if not store_name:
                store_name = base_name
                
            # 如果未提供图层名称，使用存储名称
            if not layer_name:
                layer_name = store_name
                
            # 使用REST API直接发布GeoTIFF，避免权限问题
            import requests
            from requests.auth import HTTPBasicAuth
            
            # 读取GeoTIFF文件内容
            with open(geotiff_path, 'rb') as f:
                geotiff_data = f.read()
                
            # 获取API URL
            base_url = self.geo.service_url
            if not base_url.endswith('/rest'):
                base_url = f"{base_url}/rest"
                
            # 构建上传URL
            upload_url = f"{base_url}/workspaces/{workspace}/coveragestores/{store_name}/file.geotiff"
            
            # 设置REST API请求头和参数
            headers = {'Content-type': 'image/tiff'}
            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            
            # 发送GeoTIFF数据
            logger.info(f"通过REST API发布GeoTIFF: {upload_url}")
            
            # 添加参数确保图层名称设置正确
            params = {}
            if layer_name and layer_name != store_name:
                params['coverageName'] = layer_name
                
            response = requests.put(
                upload_url, 
                headers=headers,
                data=geotiff_data,
                auth=auth,
                params=params
            )
            
            if response.status_code >= 200 and response.status_code < 300:
                logger.info(f"成功通过REST API发布GeoTIFF: {response.status_code}")
            else:
                logger.error(f"REST API发布失败: {response.status_code} - {response.text}")
                return False
            
            logger.info(f"成功发布GeoTIFF: {workspace}:{store_name}:{layer_name}")
            
            # 验证图层是否成功发布
            try:
                # 构建coverage URL
                coverage_url = f"{base_url}/workspaces/{workspace}/coveragestores/{store_name}/coverages/{layer_name}.json"
                
                headers = {"Accept": "application/json"}
                response = requests.get(coverage_url, auth=auth, headers=headers)
                if response.status_code == 200:
                    logger.info(f"图层验证成功: {workspace}:{layer_name}")
                    return True
                else:
                    # 尝试使用store_name作为图层名
                    coverage_url = f"{base_url}/workspaces/{workspace}/coveragestores/{store_name}/coverages/{store_name}.json"
                    response = requests.get(coverage_url, auth=auth, headers=headers)
                    if response.status_code == 200:
                        logger.info(f"图层验证成功(使用存储名): {workspace}:{store_name}")
                        return True
                    else:
                        logger.warning(f"无法验证图层是否成功发布: {workspace}:{layer_name}")
                        # 返回True，因为发布操作本身成功
                        return True
            except Exception as e:
                logger.warning(f"验证图层时出错: {str(e)}")
                # 返回True，因为发布操作本身成功
                return True
                
        except Exception as e:
            logger.error(f"发布GeoTIFF时出错: {str(e)}")
            return False 

    def publish_geotiff_directory(self, directory_path, workspace=DEFAULT_WORKSPACE, store_name=None):
        """
        发布目录中所有GeoTIFF到GeoServer
        
        Args:
            directory_path: 包含GeoTIFF文件的目录路径
            workspace: 工作区名称
            store_name: 存储名称前缀，如果不提供则使用目录名称
            
        Returns:
            包含成功和失败文件列表的字典
        """
        try:
            # 检查目录是否存在
            if not os.path.isdir(directory_path):
                logger.error(f"目录不存在: {directory_path}")
                return {'success': [], 'failed': []}
                
            # 确保工作区存在
            if not self.check_workspace_exists(workspace):
                logger.info(f"工作区 {workspace} 不存在，正在创建")
                self.create_workspace(workspace)
                
            # 如果未提供存储名称，使用目录名
            if not store_name:
                store_name = os.path.basename(os.path.normpath(directory_path))
                
            # 查找目录中所有GeoTIFF文件（.tif, .tiff）
            geotiff_patterns = [
                os.path.join(directory_path, "*.tif"),
                os.path.join(directory_path, "*.tiff")
            ]
            geotiff_paths = []
            for pattern in geotiff_patterns:
                geotiff_paths.extend(glob.glob(pattern))
                
            if not geotiff_paths:
                logger.warning(f"目录中未找到GeoTIFF文件: {directory_path}")
                return {'success': [], 'failed': []}
                
            logger.info(f"在目录 {directory_path} 中找到 {len(geotiff_paths)} 个GeoTIFF文件")
            
            # 发布每个GeoTIFF
            success_files = []
            failed_files = []
            
            for geotiff_path in geotiff_paths:
                # 使用文件名作为图层名
                file_name = os.path.splitext(os.path.basename(geotiff_path))[0]
                # 不再为每个文件创建唯一存储名称，直接使用目录名作为存储名称
                # 这样最终图层名为 workspace:file_name
                
                logger.info(f"正在发布GeoTIFF: {geotiff_path}")
                try:
                    result = self.publish_geotiff(
                        geotiff_path=geotiff_path,
                        workspace=workspace,
                        store_name=store_name,  # 直接使用目录名作为存储名称
                        layer_name=file_name
                    )
                    
                    if result:
                        success_files.append(os.path.basename(geotiff_path))
                    else:
                        failed_files.append(os.path.basename(geotiff_path))
                except Exception as e:
                    logger.error(f"发布文件 {geotiff_path} 时出错: {str(e)}")
                    failed_files.append(os.path.basename(geotiff_path))
                    
            # 日志记录结果
            logger.info(f"成功发布 {len(success_files)}/{len(geotiff_paths)} 个GeoTIFF文件")
            if failed_files:
                logger.warning(f"失败文件: {', '.join(failed_files)}")
                
            return {
                'success': success_files,
                'failed': failed_files
            }
            
        except Exception as e:
            logger.error(f"发布GeoTIFF目录时出错: {str(e)}")
            # 尝试获取目录中的所有GeoTIFF文件作为失败列表
            failed_files = []
            try:
                for pattern in [os.path.join(directory_path, "*.tif"), os.path.join(directory_path, "*.tiff")]:
                    failed_files.extend(map(os.path.basename, glob.glob(pattern)))
            except:
                pass
                
            return {'success': [], 'failed': failed_files}
            
    def publish_postgis_layer(self, table_name, workspace=DEFAULT_WORKSPACE, store_name=DEFAULT_DATASTORE, 
                             layer_name=None, geometry_column='geom', srid=4326):
        """
        发布PostGIS表到GeoServer
        
        Args:
            table_name: 数据表名称
            workspace: 工作区名称
            store_name: 数据存储名称
            layer_name: 图层名称，如果不提供则使用表名
            geometry_column: 几何列名称，默认为geom
            srid: 空间参考系统ID，默认为4326
            
        Returns:
            发布是否成功
        """
        try:
            # 如果未提供图层名称，使用表名
            if not layer_name:
                layer_name = table_name
                
            # 发布PostGIS图层
            self.geo.publish_featurestore_sqlview(
                store_name=store_name,
                name=layer_name,
                sql=f"SELECT * FROM {table_name}",
                geom_name=geometry_column,
                geom_type="Geometry",  # 这里使用通用的Geometry类型
                workspace=workspace,
                srid=srid
            )
            
            logger.info(f"成功发布PostGIS图层: {workspace}:{layer_name} (从表 {table_name})")
            return True
            
        except Exception as e:
            logger.error(f"发布PostGIS图层时出错: {str(e)}")
            return False
            
    def set_layer_style(self, layer_name, style_name, workspace=DEFAULT_WORKSPACE):
        """设置图层样式"""
        try:
            self.geo.publish_style(layer_name=layer_name, style_name=style_name, workspace=workspace)
            logger.info(f"成功设置图层 {workspace}:{layer_name} 的样式为 {style_name}")
            return True
        except Exception as e:
            logger.error(f"设置图层样式时出错: {str(e)}")
            return False
            
    def delete_layer(self, layer_name, workspace=DEFAULT_WORKSPACE):
        """删除图层"""
        try:
            self.geo.delete_layer(layer_name=layer_name, workspace=workspace)
            logger.info(f"成功删除图层: {workspace}:{layer_name}")
            return True
        except Exception as e:
            logger.error(f"删除图层时出错: {str(e)}")
            return False
            
    def get_layer_bbox(self, layer_name, workspace=DEFAULT_WORKSPACE):
        """
        获取图层的边界框信息
        
        Args:
            layer_name: 图层名称
            workspace: 工作区名称
            
        Returns:
            包含边界框信息的字典，如果获取失败则返回None
        """
        try:
            base_url = self.geo.service_url
            if not base_url.endswith('/rest'):
                base_url = f"{base_url}/rest"
                
            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            headers = {"Accept": "application/json"}
            
            # 构建图层URL
            layer_url = f"{base_url}/layers/{workspace}:{layer_name}.json"
            logger.info(f"获取图层 {workspace}:{layer_name} 的信息")
            
            response = requests.get(layer_url, auth=auth, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                layer_detail = data.get("layer", {})
                
                # 获取资源URL
                if "resource" in layer_detail:
                    resource_url = layer_detail["resource"]["href"]
                    resource_response = requests.get(resource_url, auth=auth, headers=headers)
                    
                    if resource_response.status_code == 200:
                        resource_data = resource_response.json()
                        
                        # 根据图层类型获取边界框信息
                        if "featureType" in resource_data:
                            feature_type = resource_data["featureType"]
                        elif "coverage" in resource_data:
                            feature_type = resource_data["coverage"]
                        else:
                            logger.warning(f"无法识别图层 {workspace}:{layer_name} 的类型")
                            return None
                        
                        # 提取边界框信息
                        bbox_info = {}
                        
                        # 原生边界框
                        if "nativeBoundingBox" in feature_type:
                            bbox_info["native"] = {
                                "minx": feature_type["nativeBoundingBox"].get("minx"),
                                "miny": feature_type["nativeBoundingBox"].get("miny"),
                                "maxx": feature_type["nativeBoundingBox"].get("maxx"),
                                "maxy": feature_type["nativeBoundingBox"].get("maxy"),
                                "crs": feature_type["nativeBoundingBox"].get("crs")
                            }
                        
                        # 经纬度边界框
                        if "latLonBoundingBox" in feature_type:
                            bbox_info["latLon"] = {
                                "minx": feature_type["latLonBoundingBox"].get("minx"),
                                "miny": feature_type["latLonBoundingBox"].get("miny"),
                                "maxx": feature_type["latLonBoundingBox"].get("maxx"),
                                "maxy": feature_type["latLonBoundingBox"].get("maxy"),
                                "crs": feature_type["latLonBoundingBox"].get("crs")
                            }
                        
                        # 如果找到边界框信息，返回结果
                        if bbox_info:
                            logger.info(f"成功获取图层 {workspace}:{layer_name} 的边界框信息")
                            return {
                                "layer": layer_name,
                                "workspace": workspace,
                                "bbox": bbox_info
                            }
                
                logger.warning(f"无法获取图层 {workspace}:{layer_name} 的边界框信息")
                return None
            else:
                logger.error(f"获取图层信息失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取图层 {workspace}:{layer_name} 的边界框信息时出错: {str(e)}")
            return None
            
    def list_datastores(self, workspace):
        """
        获取工作区中的数据存储列表
        
        Args:
            workspace: 工作区名称
            
        Returns:
            数据存储列表
        """
        try:
            base_url = self.geo.service_url
            if not base_url.endswith('/rest'):
                base_url = f"{base_url}/rest"
                
            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            headers = {"Accept": "application/json"}
            
            # 获取矢量数据存储
            vector_url = f"{base_url}/workspaces/{workspace}/datastores.json"
            # 获取栅格数据存储
            raster_url = f"{base_url}/workspaces/{workspace}/coveragestores.json"
            
            datastores = []
            
            # 处理矢量数据存储
            response = requests.get(vector_url, auth=auth, headers=headers)
            if response.status_code == 200:
                data = response.json()
                if "dataStores" in data and "dataStore" in data["dataStores"]:
                    for store in data["dataStores"]["dataStore"]:
                        store_info = {
                            "name": store.get("name", ""),
                            "type": "vector",
                            "workspace": workspace,
                            "href": store.get("href", "")
                        }
                        datastores.append(store_info)
                        
            # 处理栅格数据存储
            response = requests.get(raster_url, auth=auth, headers=headers)
            if response.status_code == 200:
                data = response.json()
                if "coverageStores" in data and "coverageStore" in data["coverageStores"]:
                    for store in data["coverageStores"]["coverageStore"]:
                        store_info = {
                            "name": store.get("name", ""),
                            "type": "raster",
                            "workspace": workspace,
                            "href": store.get("href", "")
                        }
                        datastores.append(store_info)
                        
            logger.info(f"在工作区 {workspace} 中找到 {len(datastores)} 个数据存储")
            return datastores
            
        except Exception as e:
            logger.error(f"获取数据存储列表时出错: {str(e)}")
            return []
            
    def diagnose(self, workspace=None, detail=False):
        """
        诊断GeoServer连接和图层状态
        
        Args:
            workspace: 要检查的特定工作区，不提供则检查所有工作区
            detail: 是否显示详细诊断信息
            
        Returns:
            诊断结果字典
        """
        results = {
            "connection": False,
            "version": "Unknown",
            "workspaces": [],
            "layers_count": 0,
            "datastores_count": 0,
            "issues": []
        }
        
        try:
            # 1. 检查连接
            import requests
            from requests.auth import HTTPBasicAuth
            
            base_url = self.geo.service_url
            if not base_url.endswith('/rest'):
                base_url = f"{base_url}/rest"
                
            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            headers = {"Accept": "application/json"}
            
            # 获取版本信息
            url = f"{base_url}/about/version.json"
            response = requests.get(url, auth=auth, headers=headers)
            
            if response.status_code == 200:
                results["connection"] = True
                try:
                    info = response.json()
                    version = info.get('about', {}).get('resource', [{}])[0].get('Version', 'Unknown')
                    results["version"] = version
                except Exception as e:
                    results["issues"].append(f"解析版本信息出错: {str(e)}")
            else:
                results["issues"].append(f"连接GeoServer失败，状态码: {response.status_code}")
                # 如果连接失败，直接返回结果
                return results
                
            # 2. 获取工作区列表
            workspaces_url = f"{base_url}/workspaces.json"
            response = requests.get(workspaces_url, auth=auth, headers=headers)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if "workspaces" in data and "workspace" in data["workspaces"]:
                        all_workspaces = [ws.get("name") for ws in data["workspaces"]["workspace"]]
                        
                        # 如果指定了工作区，仅检查该工作区
                        if workspace:
                            if workspace in all_workspaces:
                                workspaces_to_check = [workspace]
                            else:
                                results["issues"].append(f"指定的工作区 {workspace} 不存在")
                                workspaces_to_check = []
                        else:
                            workspaces_to_check = all_workspaces
                            
                        # 处理每个工作区
                        for ws in workspaces_to_check:
                            ws_info = {"name": ws, "layers": [], "datastores": []}
                            
                            # 获取数据存储
                            if detail:
                                datastores = self.list_datastores(ws)
                                ws_info["datastores"] = datastores
                                results["datastores_count"] += len(datastores)
                                
                            # 获取图层
                            layers = self.list_layers(ws)
                            if detail:
                                ws_info["layers"] = layers
                            ws_info["layers_count"] = len(layers)
                            results["layers_count"] += len(layers)
                            
                            results["workspaces"].append(ws_info)
                            
                except Exception as e:
                    results["issues"].append(f"处理工作区信息时出错: {str(e)}")
            else:
                results["issues"].append(f"获取工作区列表失败，状态码: {response.status_code}")
                
            # 添加总结
            results["workspaces_count"] = len(results["workspaces"])
            
            return results
            
        except Exception as e:
            results["issues"].append(f"诊断过程中出错: {str(e)}")
            return results 