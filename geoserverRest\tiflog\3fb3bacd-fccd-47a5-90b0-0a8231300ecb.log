2025-07-21 11:10:16,711 - INFO - TIF处理任务 3fb3bacd-fccd-47a5-90b0-0a8231300ecb 开始执行
2025-07-21 11:10:16,718 - INFO - 开始时间: 2025-07-21 11:10:16
2025-07-21 11:10:16,723 - INFO - 处理参数: {
  "input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-21 11:10:16,734 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-21 11:10:20,390 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (11:10:20)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-21 11:10:20,395 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-21 11:10:20,642 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:04,  4.79it/s]
2025-07-21 11:10:20,884 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 19.82it/s]
2025-07-21 11:10:20,985 - ERROR - 
处理数据块:  48%|#################################3                                    | 10/21 [00:00<00:00, 19.84it/s]
2025-07-21 11:10:21,169 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 22.80it/s]
2025-07-21 11:10:21,255 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 25.51it/s]
2025-07-21 11:10:21,263 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-21 11:10:21,264 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-21 11:10:21,311 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 475.96it/s]
2025-07-21 11:10:22,298 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 1.97 秒 (11:10:22)预计总处理时间: 约 5.92 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif (11:10:22)写入带有nodata值的影像...
2025-07-21 11:10:22,299 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-21 11:10:29,539 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-21 11:10:29,543 - ERROR - [A
2025-07-21 11:10:30,721 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:01<00:23,  1.17s/it]
2025-07-21 11:10:30,722 - ERROR - [A
2025-07-21 11:10:31,582 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:02<00:18,  1.01it/s]
2025-07-21 11:10:31,583 - ERROR - [A
2025-07-21 11:10:32,786 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:03<00:19,  1.09s/it]
2025-07-21 11:10:32,787 - ERROR - [A
2025-07-21 11:10:33,582 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:04<00:16,  1.03it/s]
2025-07-21 11:10:33,583 - ERROR - [A
2025-07-21 11:10:33,811 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:04<00:11,  1.42it/s]
2025-07-21 11:10:33,811 - ERROR - [A
2025-07-21 11:10:33,918 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:04<00:01,  6.06it/s]
2025-07-21 11:10:33,919 - ERROR - [A
2025-07-21 11:10:34,019 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:04<00:00,  9.94it/s]
2025-07-21 11:10:34,020 - ERROR - [A
2025-07-21 11:10:34,077 - ERROR - [A
2025-07-21 11:11:33,468 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:11<02:22, 71.16s/it]
2025-07-21 11:11:43,220 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-21 11:11:43,223 - ERROR - [A
2025-07-21 11:11:43,383 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  6.43it/s]
2025-07-21 11:11:43,385 - ERROR - [A
2025-07-21 11:11:43,500 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:01, 16.38it/s]
2025-07-21 11:11:43,500 - ERROR - [A
2025-07-21 11:11:43,624 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:00, 19.76it/s]
2025-07-21 11:11:43,625 - ERROR - [A
2025-07-21 11:11:43,736 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 22.26it/s]
2025-07-21 11:11:43,737 - ERROR - [A
2025-07-21 11:11:43,840 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:00<00:00, 24.40it/s]
2025-07-21 11:11:43,841 - ERROR - [A
2025-07-21 11:11:43,969 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:00<00:00, 26.82it/s]
2025-07-21 11:11:43,970 - ERROR - [A
2025-07-21 11:11:45,395 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:02<00:00,  5.76it/s]
2025-07-21 11:11:45,395 - ERROR - [A
2025-07-21 11:11:45,701 - ERROR - [A
2025-07-21 11:14:01,235 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [03:38<01:56, 116.22s/it]
2025-07-21 11:14:07,917 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-21 11:14:07,917 - ERROR - [A
2025-07-21 11:14:08,080 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  6.25it/s]
2025-07-21 11:14:08,081 - ERROR - [A
2025-07-21 11:14:08,182 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:00, 17.32it/s]
2025-07-21 11:14:08,183 - ERROR - [A
2025-07-21 11:14:08,316 - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:00<00:00, 23.19it/s]
2025-07-21 11:14:08,317 - ERROR - [A
2025-07-21 11:14:08,429 - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:00<00:00, 24.41it/s]
2025-07-21 11:14:08,430 - ERROR - [A
2025-07-21 11:14:08,535 - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:00<00:00, 25.71it/s]
2025-07-21 11:14:08,536 - ERROR - [A
2025-07-21 11:14:08,640 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [00:00<00:00, 26.70it/s]
2025-07-21 11:14:08,640 - ERROR - [A
2025-07-21 11:14:08,750 - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [00:00<00:00, 26.82it/s]
2025-07-21 11:14:08,751 - ERROR - [A
2025-07-21 11:14:08,947 - ERROR - [A
2025-07-21 11:16:27,741 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [06:05<00:00, 130.05s/it]
2025-07-21 11:16:27,742 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [06:05<00:00, 121.81s/it]
2025-07-21 11:17:49,910 - INFO - 影像保存完成，耗时: 365.64 秒 (11:16:27)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp (11:16:27)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif创建掩码，识别有效区域...轻微平滑处理掩码，减少边缘锯齿...寻找有效区域轮廓...找到 1 个初始轮廓使用所有 1 个轮廓，不进行面积过滤转换轮廓为地理坐标...
2025-07-21 11:17:49,933 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-21 11:17:50,515 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.75it/s]
2025-07-21 11:17:50,517 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.75it/s]
2025-07-21 11:17:50,631 - INFO - 成功创建 1 个多边形合并多边形...创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp写入shapefile特征...处理单个Polygon
2025-07-21 11:17:50,632 - ERROR - 
写入多边形:   0%|                                                                                | 0/1 [00:00<?, ?it/s]
2025-07-21 11:17:50,644 - ERROR - 
写入多边形: 100%|#######################################################################| 1/1 [00:00<00:00, 109.65it/s]
2025-07-21 11:18:00,669 - INFO - TIF处理任务 3fb3bacd-fccd-47a5-90b0-0a8231300ecb 执行成功
2025-07-21 11:18:00,671 - INFO - 完成时间: 2025-07-21 11:18:00
2025-07-21 11:18:00,675 - INFO - 状态: 运行成功
