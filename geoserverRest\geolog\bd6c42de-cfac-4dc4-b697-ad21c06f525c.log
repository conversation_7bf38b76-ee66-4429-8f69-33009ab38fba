2025-08-04 10:04:51,355 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - INFO - GeoServer发布任务 bd6c42de-cfac-4dc4-b697-ad21c06f525c 开始执行
2025-08-04 10:04:51,361 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - INFO - 开始时间: 2025-08-04 10:04:51
2025-08-04 10:04:51,362 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - INFO - 参数: {
  "root_directory": "D:/Drone_Project/nginxData/ODM/Output",
  "workspace": "testodm",
  "store_name": null
}
2025-08-04 10:04:51,462 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - ERROR - 执行出错: name 'task_logger' is not defined
2025-08-04 10:04:51,462 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 410, in _run_with_logging
    result = wrapper_func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 408, in wrapper_func
    return func(*args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 587, in publish_func
    task_logger.info(f"开始扫描根目录: {root_dir}")
NameError: name 'task_logger' is not defined

2025-08-04 10:04:51,465 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - ERROR - 完成时间: 2025-08-04 10:04:51
2025-08-04 10:04:51,480 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - ERROR - 状态: 发布失败
