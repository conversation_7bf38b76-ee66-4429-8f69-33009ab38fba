@echo off
echo ======= GeoServer and API Service Startup Script =======

REM Set GeoServer path (modify according to actual installation)
set GEOSERVER_HOME=C:\Program Files\GeoServer

REM Check if GeoServer path exists
if not exist "%GEOSERVER_HOME%" (
    echo Error: GeoServer path does not exist: %GEOSERVER_HOME%
    echo Please edit this batch file and set the correct GeoServer path
    pause
    exit /b 1
)

echo Starting GeoServer service...

REM Start GeoServer by changing to its directory first
cd /d "%GEOSERVER_HOME%\bin"
start "" startup.bat

REM Wait for GeoServer to start
echo Waiting for GeoServer to start (30 seconds)...
timeout /t 30 /nobreak

echo Starting GeoServer API service...

REM Return to original directory
cd /d "%~dp0"

REM Initialize conda and activate environment
call conda.bat activate geoserverapi

REM Display current environment
echo Current Python environment:
where python
python --version

REM Run server
cd /d D:\Drone_Project\geoserverapi
python -m geoserverRest.run --host 0.0.0.0 --port 5083

REM Pause to view error messages if server exits
pause 