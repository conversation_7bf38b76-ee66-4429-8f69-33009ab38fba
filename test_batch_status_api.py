#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试batch status接口的状态返回一致性
"""

import requests
import json

def test_flask_batch_status():
    """测试Flask版本的batch status API"""
    try:
        # 先获取所有任务
        url = "http://127.0.0.1:5000/api/batch/all"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("Flask API - 所有任务:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 如果有任务，测试获取单个任务状态
            if data.get("tasks") and len(data["tasks"]) > 0:
                task_id = data["tasks"][0].get("task_id")
                if task_id:
                    status_url = f"http://127.0.0.1:5000/api/batch/status"
                    status_response = requests.get(status_url, params={"task_id": task_id}, timeout=10)
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        print(f"\nFlask API - 任务 {task_id} 状态:")
                        print(json.dumps(status_data, indent=2, ensure_ascii=False))
                        return status_data
                    else:
                        print(f"Flask status API 错误: {status_response.status_code}")
                        print(status_response.text)
            else:
                print("Flask API: 没有找到任务")
            return data
        else:
            print(f"Flask API 错误: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"Flask API 请求失败: {str(e)}")
        return None

def test_django_batch_status():
    """测试Django版本的batch status API"""
    try:
        # 先获取所有任务
        url = "http://127.0.0.1:8001/api/batch/all/"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("Django API - 所有任务:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 如果有任务，测试获取单个任务状态
            if data.get("tasks") and len(data["tasks"]) > 0:
                task_id = data["tasks"][0].get("task_id") or data["tasks"][0].get("id")
                if task_id:
                    status_url = f"http://127.0.0.1:8001/api/batch/status/"
                    status_response = requests.get(status_url, params={"task_id": task_id}, timeout=10)
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        print(f"\nDjango API - 任务 {task_id} 状态:")
                        print(json.dumps(status_data, indent=2, ensure_ascii=False))
                        return status_data
                    else:
                        print(f"Django status API 错误: {status_response.status_code}")
                        print(status_response.text)
            else:
                print("Django API: 没有找到任务")
            return data
        else:
            print(f"Django API 错误: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"Django API 请求失败: {str(e)}")
        return None

def compare_status_responses(flask_data, django_data):
    """比较两个API的状态响应"""
    if not flask_data or not django_data:
        print("无法比较：其中一个API响应为空")
        return
    
    print("\n=== 状态响应比较 ===")
    
    # 检查状态字段
    flask_status = None
    django_status = None
    
    if "task" in flask_data and "status" in flask_data["task"]:
        flask_status = flask_data["task"]["status"]
    elif "tasks" in flask_data and len(flask_data["tasks"]) > 0:
        flask_status = flask_data["tasks"][0].get("status")
    
    if "task" in django_data and "status" in django_data["task"]:
        django_status = django_data["task"]["status"]
    elif "tasks" in django_data and len(django_data["tasks"]) > 0:
        django_status = django_data["tasks"][0].get("status")
    
    print(f"Flask状态: {flask_status}")
    print(f"Django状态: {django_status}")
    
    if flask_status == django_status:
        print("✅ 状态值一致")
    else:
        print("❌ 状态值不一致")
    
    # 检查状态是否为中文
    expected_statuses = ["正在运行", "运行成功", "运行失败"]
    
    if flask_status in expected_statuses:
        print("✅ Flask状态为中文")
    else:
        print(f"❌ Flask状态不是预期的中文状态: {flask_status}")
    
    if django_status in expected_statuses:
        print("✅ Django状态为中文")
    else:
        print(f"❌ Django状态不是预期的中文状态: {django_status}")

if __name__ == "__main__":
    print("开始测试batch status接口...")
    
    flask_data = test_flask_batch_status()
    print("\n" + "="*50 + "\n")
    django_data = test_django_batch_status()
    
    if flask_data and django_data:
        compare_status_responses(flask_data, django_data)
    
    print("\n测试完成")
