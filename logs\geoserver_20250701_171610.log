2025-07-01 17:16:10,886 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250701_171610.log
2025-07-01 17:16:10,905 - root - INFO - 开始执行命令: batch
2025-07-01 17:16:10,906 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-01 17:16:10,906 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-01 17:16:10,951 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-01 17:16:10,951 - root - INFO - 执行批处理文件: batch.json
2025-07-01 17:16:10,952 - root - INFO - 执行操作 1/2: publish_shapefile_directory
2025-07-01 17:16:10,953 - root - INFO - 在目录 'data/20250701/testshp' 中找到 5 个Shapefile文件
2025-07-01 17:16:10,955 - root - INFO - 使用字符集: UTF-8
2025-07-01 17:16:10,956 - root - INFO - 未提供存储名称，使用目录名称作为前缀: testshp
2025-07-01 17:16:10,968 - root - INFO - 工作区 'test_myworkspace' 不存在，正在创建...
2025-07-01 17:16:11,010 - root - INFO - 成功创建工作区 'test_myworkspace'
2025-07-01 17:16:11,024 - root - INFO - 在工作区 'test_myworkspace' 中找到 0 个图层
2025-07-01 17:16:11,024 - root - INFO - 发布前工作区 'test_myworkspace' 中有 0 个图层
2025-07-01 17:16:11,025 - root - INFO - 正在发布Shapefile: 招商引资片区.shp, 存储名: testshp
2025-07-01 17:16:11,038 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区.shp
2025-07-01 17:16:11,039 - root - INFO - 添加文件到ZIP: 招商引资片区.shp
2025-07-01 17:16:11,163 - root - INFO - 添加文件到ZIP: 招商引资片区.dbf
2025-07-01 17:16:11,164 - root - INFO - 添加文件到ZIP: 招商引资片区.shx
2025-07-01 17:16:11,165 - root - INFO - 添加文件到ZIP: 招商引资片区.prj
2025-07-01 17:16:11,166 - root - INFO - 添加文件到ZIP: 招商引资片区.qix
2025-07-01 17:16:11,167 - root - INFO - 添加文件到ZIP: 招商引资片区.cpg
2025-07-01 17:16:11,168 - root - INFO - 添加文件到ZIP: 招商引资片区.qmd
2025-07-01 17:16:11,169 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_myworkspace/datastores/testshp/file.shp
2025-07-01 17:16:11,169 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 17:16:11,294 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 17:16:11,306 - root - INFO - 图层 '招商引资片区' 已存在
2025-07-01 17:16:11,322 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_myworkspace/testshp/招商引资片区
2025-07-01 17:16:11,322 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区.shp' 成功发布为图层 'test_myworkspace:招商引资片区'
2025-07-01 17:16:11,324 - root - INFO - 正在发布Shapefile: 招商引资片区交种植区.shp, 存储名: testshp
2025-07-01 17:16:11,337 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区交种植区.shp
2025-07-01 17:16:11,339 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shp
2025-07-01 17:16:12,035 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.dbf
2025-07-01 17:16:12,218 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shx
2025-07-01 17:16:12,231 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.prj
2025-07-01 17:16:12,232 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qix
2025-07-01 17:16:12,248 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.cpg
2025-07-01 17:16:12,249 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qmd
2025-07-01 17:16:12,250 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_myworkspace/datastores/testshp/file.shp
2025-07-01 17:16:12,251 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 17:16:12,463 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 17:16:12,473 - root - INFO - 图层 '招商引资片区交种植区' 已存在
2025-07-01 17:16:12,503 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_myworkspace/testshp/招商引资片区交种植区
2025-07-01 17:16:12,504 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区交种植区.shp' 成功发布为图层 'test_myworkspace:招商引资片区交种植区'
2025-07-01 17:16:12,506 - root - INFO - 正在发布Shapefile: 招商引资片区交设施农用地.shp, 存储名: testshp
2025-07-01 17:16:12,517 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区交设施农用地.shp
2025-07-01 17:16:12,518 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shp
2025-07-01 17:16:12,657 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.dbf
2025-07-01 17:16:12,669 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shx
2025-07-01 17:16:12,672 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.prj
2025-07-01 17:16:12,673 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qix
2025-07-01 17:16:12,675 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.cpg
2025-07-01 17:16:12,676 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qmd
2025-07-01 17:16:12,677 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_myworkspace/datastores/testshp/file.shp
2025-07-01 17:16:12,677 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 17:16:12,781 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 17:16:12,872 - root - INFO - 成功创建图层 '招商引资片区交设施农用地'
2025-07-01 17:16:12,885 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_myworkspace/testshp/招商引资片区交设施农用地
2025-07-01 17:16:12,886 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区交设施农用地.shp' 成功发布为图层 'test_myworkspace:招商引资片区交设施农用地'
2025-07-01 17:16:12,887 - root - INFO - 正在发布Shapefile: 种植区.shp, 存储名: testshp
2025-07-01 17:16:12,901 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\种植区.shp
2025-07-01 17:16:12,901 - root - INFO - 添加文件到ZIP: 种植区.shp
2025-07-01 17:16:14,154 - root - INFO - 添加文件到ZIP: 种植区.dbf
2025-07-01 17:16:14,362 - root - INFO - 添加文件到ZIP: 种植区.shx
2025-07-01 17:16:14,385 - root - INFO - 添加文件到ZIP: 种植区.prj
2025-07-01 17:16:14,386 - root - INFO - 添加文件到ZIP: 种植区.qix
2025-07-01 17:16:14,408 - root - INFO - 添加文件到ZIP: 种植区.cpg
2025-07-01 17:16:14,409 - root - INFO - 添加文件到ZIP: 种植区.qmd
2025-07-01 17:16:14,410 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_myworkspace/datastores/testshp/file.shp
2025-07-01 17:16:14,411 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 17:16:14,670 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 17:16:14,732 - root - INFO - 成功创建图层 '种植区'
2025-07-01 17:16:14,745 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_myworkspace/testshp/种植区
2025-07-01 17:16:14,746 - root - INFO - Shapefile 'data/20250701/testshp\种植区.shp' 成功发布为图层 'test_myworkspace:种植区'
2025-07-01 17:16:14,748 - root - INFO - 正在发布Shapefile: 设施农用地潜力.shp, 存储名: testshp
2025-07-01 17:16:14,760 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\设施农用地潜力.shp
2025-07-01 17:16:14,761 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shp
2025-07-01 17:16:15,827 - root - INFO - 添加文件到ZIP: 设施农用地潜力.dbf
2025-07-01 17:16:15,892 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shx
2025-07-01 17:16:15,901 - root - INFO - 添加文件到ZIP: 设施农用地潜力.prj
2025-07-01 17:16:15,931 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qix
2025-07-01 17:16:15,941 - root - INFO - 添加文件到ZIP: 设施农用地潜力.cpg
2025-07-01 17:16:15,942 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qmd
2025-07-01 17:16:15,946 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_myworkspace/datastores/testshp/file.shp
2025-07-01 17:16:15,947 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 17:16:16,174 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 17:16:16,260 - root - INFO - 成功创建图层 '设施农用地潜力'
2025-07-01 17:16:16,277 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_myworkspace/testshp/设施农用地潜力
2025-07-01 17:16:16,277 - root - INFO - Shapefile 'data/20250701/testshp\设施农用地潜力.shp' 成功发布为图层 'test_myworkspace:设施农用地潜力'
2025-07-01 17:16:16,292 - root - INFO - 在工作区 'test_myworkspace' 中找到 5 个图层
2025-07-01 17:16:16,293 - root - INFO - 发布后工作区 'test_myworkspace' 中有 5 个图层
2025-07-01 17:16:16,293 - root - INFO - 新增了 5 个图层: 招商引资片区交种植区, 设施农用地潜力, 招商引资片区交设施农用地, 种植区, 招商引资片区
2025-07-01 17:16:16,293 - root - INFO - 成功发布了 5 个Shapefile文件
2025-07-01 17:16:16,294 - root - INFO - 执行操作 2/2: publish_geotiff
2025-07-01 17:16:16,294 - root - INFO - 未提供存储名称，使用文件名: nanning
2025-07-01 17:16:16,295 - root - INFO - 未提供图层名称，使用存储名称: nanning
2025-07-01 17:16:16,306 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\nanning.tif
2025-07-01 17:16:16,514 - root - INFO - 发布GeoTIFF 'data/20250701/nanning.tif' 到 'test_myworkspace:nanning'
2025-07-01 17:16:16,559 - root - INFO - 图层验证成功: 直接图层路径: test_myworkspace:nanning
2025-07-01 17:16:16,560 - root - INFO - GeoTIFF 'data/20250701/nanning.tif' 成功发布为图层 'test_myworkspace:nanning'
2025-07-01 17:16:16,561 - root - INFO - 批处理文件执行完成
2025-07-01 17:16:16,561 - root - INFO - 命令执行完成: batch
