# GeoServer管理API文档

GeoServer管理API模块提供完整的GeoServer资源管理功能，包括工作区管理、图层发布、数据存储管理等核心功能。

## 模块概述

GeoServer管理模块是系统的核心管理功能，提供：
- **工作区管理**：创建、删除、查询工作区
- **图层发布**：Shapefile、GeoTIFF文件和目录的发布
- **数据存储管理**：PostGIS数据存储的创建和管理
- **图层管理**：图层列表查询、删除、样式设置
- **系统诊断**：GeoServer连接状态和系统健康检查

## API端点列表

### 工作区管理

#### 1. 获取工作区列表

**端点**: `GET /api/management/workspaces/`

**描述**: 获取GeoServer中所有工作区的列表

**参数**: 无

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/workspaces/"
```

**成功响应**:
```json
{
  "status": "success",
  "count": 3,
  "workspaces": [
    "default",
    "test_workspace",
    "production"
  ]
}
```

#### 2. 创建工作区

**端点**: `GET /api/management/workspaces/create/`

**描述**: 创建新的GeoServer工作区

**参数**:
- `name` (必选): 工作区名称

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/workspaces/create/?name=new_workspace"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "工作区 new_workspace 创建成功"
}
```

**错误响应**:
```json
{
  "status": "error",
  "message": "工作区 new_workspace 创建失败"
}
```

#### 3. 删除工作区

**端点**: `GET /api/management/workspaces/delete/`

**描述**: 删除指定的GeoServer工作区

**参数**:
- `name` (必选): 工作区名称
- `recurse` (可选): 是否递归删除，默认为true

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/workspaces/delete/?name=old_workspace&recurse=true"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "工作区 old_workspace 删除成功"
}
```

### 图层管理

#### 4. 获取图层列表

**端点**: `GET /api/management/layers/`

**描述**: 获取指定工作区或所有工作区的图层列表

**参数**:
- `workspace` (可选): 工作区名称，不指定则获取所有图层

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/layers/?workspace=test"
```

**成功响应**:
```json
{
  "status": "success",
  "workspace": "test",
  "count": 5,
  "layers": [
    "elevation",
    "temperature",
    "boundaries",
    "roads",
    "buildings"
  ]
}
```

### 数据存储管理

#### 5. 获取数据存储列表

**端点**: `GET /api/management/datastores/`

**描述**: 获取指定工作区中的所有数据存储

**参数**:
- `workspace` (必选): 工作区名称

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/datastores/?workspace=test"
```

**成功响应**:
```json
{
  "status": "success",
  "count": 2,
  "datastores": [
    "shapefile_store",
    "postgis_store"
  ]
}
```

#### 6. 创建PostGIS数据存储

**端点**: `GET /api/management/datastores/create/`

**描述**: 创建新的PostGIS数据存储连接

**参数**:
- `name` (必选): 数据存储名称
- `workspace` (必选): 工作区名称
- `host` (必选): 数据库主机地址
- `port` (必选): 数据库端口
- `database` (必选): 数据库名称
- `username` (必选): 数据库用户名
- `password` (必选): 数据库密码
- `schema` (可选): 数据库模式，默认为public

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/datastores/create/?name=my_postgis&workspace=test&host=localhost&port=5432&database=gisdb&username=gisuser&password=gispass&schema=public"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "PostGIS数据存储 test:my_postgis 创建成功"
}
```

### Shapefile发布

#### 7. 发布单个Shapefile

**端点**: `GET /api/management/shapefiles/publish/`

**描述**: 发布单个Shapefile文件到GeoServer

**参数**:
- `file` (必选): Shapefile文件路径
- `workspace` (必选): 目标工作区名称
- `store` (可选): 存储名称，默认使用文件名
- `layer` (可选): 图层名称，默认使用文件名
- `charset` (可选): 字符集，默认为UTF-8

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/shapefiles/publish/?file=D:/data/boundaries.shp&workspace=test&charset=UTF-8"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "Shapefile boundaries.shp 发布成功为 test:boundaries"
}
```

#### 8. 发布Shapefile目录

**端点**: `GET /api/management/shapefile-directories/publish/`

**描述**: 批量发布目录中的所有Shapefile文件

**参数**:
- `directory` (必选): Shapefile目录路径
- `workspace` (必选): 目标工作区名称
- `store` (可选): 存储名称前缀，默认为shapefiles
- `charset` (可选): 字符集，默认为UTF-8

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/shapefile-directories/publish/?directory=D:/shapefiles/&workspace=test"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "处理完成，成功发布 3/5 个Shapefile",
  "total": 5,
  "success": 3,
  "results": [
    {
      "file": "D:/shapefiles/roads.shp",
      "status": "success",
      "layer": "test:roads"
    },
    {
      "file": "D:/shapefiles/buildings.shp",
      "status": "success",
      "layer": "test:buildings"
    },
    {
      "file": "D:/shapefiles/water.shp",
      "status": "success",
      "layer": "test:water"
    },
    {
      "file": "D:/shapefiles/corrupt.shp",
      "status": "failed",
      "error": "文件损坏"
    },
    {
      "file": "D:/shapefiles/invalid.shp",
      "status": "failed",
      "error": "投影信息缺失"
    }
  ]
}
```

### GeoTIFF发布

#### 9. 发布单个GeoTIFF

**端点**: `GET /api/management/geotiffs/publish/`

**描述**: 发布单个GeoTIFF文件到GeoServer

**参数**:
- `file` (必选): GeoTIFF文件路径
- `workspace` (必选): 目标工作区名称
- `store` (可选): 存储名称，默认使用文件名
- `layer` (可选): 图层名称，默认使用文件名

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/geotiffs/publish/?file=D:/data/elevation.tif&workspace=test"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "GeoTIFF elevation.tif 发布成功为 test:elevation"
}
```

#### 10. 发布GeoTIFF目录

**端点**: `GET /api/management/geotiff-directories/publish/`

**描述**: 批量发布目录中的所有GeoTIFF文件

**参数**:
- `directory` (必选): GeoTIFF目录路径
- `workspace` (必选): 目标工作区名称
- `store` (可选): 存储名称前缀，默认为geotiffs

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/geotiff-directories/publish/?directory=D:/geotiffs/&workspace=test"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "处理完成，成功发布 2/3 个GeoTIFF",
  "total": 3,
  "success": 2,
  "results": [
    {
      "file": "D:/geotiffs/dem.tif",
      "status": "success",
      "layer": "test:dem"
    },
    {
      "file": "D:/geotiffs/ortho.tif",
      "status": "success",
      "layer": "test:ortho"
    },
    {
      "file": "D:/geotiffs/invalid.tif",
      "status": "failed",
      "error": "不是有效的GeoTIFF文件"
    }
  ]
}
```

### 高级管理功能

#### 11. 发布PostGIS表

**端点**: `GET /api/management/postgis/publish/`

**描述**: 发布PostGIS数据库表为GeoServer图层

**参数**:
- `table` (必选): 表名
- `workspace` (必选): 工作区名称
- `store` (必选): 数据存储名称
- `layer` (可选): 图层名称，默认使用表名
- `geometry_column` (可选): 几何列名，默认为geom
- `srid` (可选): 空间参考系统ID，默认为4326

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/postgis/publish/?table=cities&workspace=test&store=postgis_store"
```

**响应**:
```json
{
  "status": "error",
  "message": "PostGIS发布功能暂未实现"
}
```

#### 12. 删除图层

**端点**: `GET /api/management/layers/delete/`

**描述**: 删除指定的图层

**参数**:
- `workspace` (必选): 工作区名称
- `layer` (必选): 图层名称
- `recurse` (可选): 是否递归删除，默认为true

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/layers/delete/?workspace=test&layer=old_layer"
```

**响应**:
```json
{
  "status": "error",
  "message": "删除图层功能暂未实现"
}
```

#### 13. 设置图层样式

**端点**: `GET /api/management/layers/style/`

**描述**: 设置图层的显示样式

**参数**:
- `workspace` (必选): 工作区名称
- `layer` (必选): 图层名称
- `style` (必选): 样式名称

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/layers/style/?workspace=test&layer=roads&style=road_style"
```

**响应**:
```json
{
  "status": "error",
  "message": "设置图层样式功能暂未实现"
}
```

#### 14. 执行批处理文件

**端点**: `GET /api/management/batch/execute/`

**描述**: 执行指定的批处理文件

**参数**:
- `batch_file` (必选): 批处理文件路径
- `args` (可选): 传递给批处理文件的参数

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/batch/execute/?batch_file=D:/scripts/import.bat&args=--verbose"
```

**响应**:
```json
{
  "status": "error",
  "message": "执行批处理文件功能暂未实现"
}
```

#### 15. 诊断GeoServer

**端点**: `GET /api/management/diagnose/`

**描述**: 诊断GeoServer连接状态和系统健康

**参数**: 无

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/management/diagnose/"
```

**成功响应**:
```json
{
  "status": "success",
  "diagnosis": {
    "geoserver_connection": "ok",
    "geoserver_url": "http://localhost:8083/geoserver",
    "workspaces_count": 5,
    "workspaces": [
      "default",
      "test",
      "production",
      "staging",
      "demo"
    ]
  }
}
```

## 错误处理

### 常见错误类型

**参数缺失**:
```json
{
  "status": "error",
  "message": "缺少必要参数: name"
}
```

**文件不存在**:
```json
{
  "status": "error",
  "message": "文件不存在: D:/data/missing.shp"
}
```

**目录不存在**:
```json
{
  "status": "error",
  "message": "目录不存在: D:/missing_directory/"
}
```

**工作区不存在**:
```json
{
  "status": "error",
  "message": "工作区 'nonexistent' 不存在"
}
```

**GeoServer连接失败**:
```json
{
  "status": "error",
  "message": "无法连接到GeoServer: Connection refused"
}
```

**权限不足**:
```json
{
  "status": "error",
  "message": "权限不足，无法访问文件: D:/protected/data.shp"
}
```

## 使用示例

### Python客户端示例

```python
import requests
import os

class GeoServerManagementClient:
    def __init__(self, base_url):
        self.base_url = base_url
    
    def create_workspace(self, name):
        """创建工作区"""
        url = f"{self.base_url}/api/management/workspaces/create/"
        params = {'name': name}
        response = requests.get(url, params=params)
        return response.json()
    
    def publish_shapefile(self, file_path, workspace, **kwargs):
        """发布Shapefile"""
        url = f"{self.base_url}/api/management/shapefiles/publish/"
        params = {
            'file': file_path,
            'workspace': workspace,
            **kwargs
        }
        response = requests.get(url, params=params)
        return response.json()
    
    def publish_shapefile_directory(self, directory, workspace, **kwargs):
        """批量发布Shapefile目录"""
        url = f"{self.base_url}/api/management/shapefile-directories/publish/"
        params = {
            'directory': directory,
            'workspace': workspace,
            **kwargs
        }
        response = requests.get(url, params=params)
        return response.json()
    
    def get_workspaces(self):
        """获取工作区列表"""
        url = f"{self.base_url}/api/management/workspaces/"
        response = requests.get(url)
        return response.json()
    
    def diagnose_geoserver(self):
        """诊断GeoServer状态"""
        url = f"{self.base_url}/api/management/diagnose/"
        response = requests.get(url)
        return response.json()

# 使用示例
client = GeoServerManagementClient("http://127.0.0.1:5000")

# 1. 创建工作区
result = client.create_workspace("my_project")
if result['status'] == 'success':
    print("工作区创建成功")

# 2. 发布单个Shapefile
shapefile_result = client.publish_shapefile(
    "D:/data/boundaries.shp", 
    "my_project",
    charset="UTF-8"
)
print(f"Shapefile发布结果: {shapefile_result['message']}")

# 3. 批量发布Shapefile目录
directory_result = client.publish_shapefile_directory(
    "D:/shapefiles/",
    "my_project"
)
print(f"批量发布结果: {directory_result['message']}")
print(f"成功发布: {directory_result['success']}/{directory_result['total']}")

# 4. 获取工作区列表
workspaces = client.get_workspaces()
print(f"工作区数量: {workspaces['count']}")

# 5. 诊断GeoServer
diagnosis = client.diagnose_geoserver()
print(f"GeoServer状态: {diagnosis['diagnosis']['geoserver_connection']}")
```

### 批量处理示例

```python
def batch_publish_project(client, project_name, data_directory):
    """批量发布整个项目的数据"""
    
    # 1. 创建项目工作区
    print(f"创建工作区: {project_name}")
    workspace_result = client.create_workspace(project_name)
    if workspace_result['status'] != 'success':
        print(f"工作区创建失败: {workspace_result['message']}")
        return False
    
    # 2. 发布Shapefile数据
    shapefile_dir = os.path.join(data_directory, "shapefiles")
    if os.path.exists(shapefile_dir):
        print(f"发布Shapefile目录: {shapefile_dir}")
        shp_result = client.publish_shapefile_directory(shapefile_dir, project_name)
        print(f"Shapefile发布结果: {shp_result['message']}")
    
    # 3. 发布GeoTIFF数据
    geotiff_dir = os.path.join(data_directory, "geotiffs")
    if os.path.exists(geotiff_dir):
        print(f"发布GeoTIFF目录: {geotiff_dir}")
        # 这里需要调用GeoTIFF发布接口
        pass
    
    # 4. 验证发布结果
    layers = client.get_layers(project_name)
    print(f"项目 {project_name} 包含 {layers['count']} 个图层")
    
    return True

# 使用示例
success = batch_publish_project(
    client, 
    "urban_planning", 
    "D:/projects/urban_planning/data/"
)
```

### 错误处理和重试机制

```python
import time
from functools import wraps

def retry_on_failure(max_retries=3, delay=2):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    result = func(*args, **kwargs)
                    if result.get('status') == 'success':
                        return result
                    elif attempt == max_retries - 1:
                        return result
                    else:
                        print(f"尝试 {attempt + 1} 失败，{delay}秒后重试...")
                        time.sleep(delay)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    print(f"尝试 {attempt + 1} 出错: {e}，{delay}秒后重试...")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator

class RobustGeoServerClient(GeoServerManagementClient):
    @retry_on_failure(max_retries=3, delay=2)
    def create_workspace_robust(self, name):
        """带重试机制的工作区创建"""
        return self.create_workspace(name)
    
    @retry_on_failure(max_retries=3, delay=5)
    def publish_shapefile_robust(self, file_path, workspace, **kwargs):
        """带重试机制的Shapefile发布"""
        return self.publish_shapefile(file_path, workspace, **kwargs)
```

## 最佳实践

### 1. 工作区命名规范

```python
def validate_workspace_name(name):
    """验证工作区名称"""
    import re
    
    # 工作区名称规则
    if not re.match(r'^[a-zA-Z][a-zA-Z0-9_-]*$', name):
        return False, "工作区名称必须以字母开头，只能包含字母、数字、下划线和连字符"
    
    if len(name) > 50:
        return False, "工作区名称不能超过50个字符"
    
    # 保留关键字检查
    reserved_names = ['admin', 'root', 'system', 'default']
    if name.lower() in reserved_names:
        return False, f"'{name}' 是保留名称，不能使用"
    
    return True, "名称有效"

# 使用示例
is_valid, message = validate_workspace_name("my-project_2024")
if is_valid:
    client.create_workspace("my-project_2024")
else:
    print(f"名称无效: {message}")
```

### 2. 文件路径处理

```python
import os
import glob

def find_shapefiles(directory):
    """查找目录中的所有Shapefile"""
    shapefiles = []
    
    # 递归查找.shp文件
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.shp'):
                shp_path = os.path.join(root, file)
                
                # 检查必需的辅助文件
                base_name = os.path.splitext(shp_path)[0]
                required_files = ['.shx', '.dbf']
                optional_files = ['.prj', '.cpg']
                
                missing_files = []
                for ext in required_files:
                    if not os.path.exists(base_name + ext):
                        missing_files.append(ext)
                
                if not missing_files:
                    shapefiles.append({
                        'path': shp_path,
                        'name': os.path.splitext(file)[0],
                        'size': os.path.getsize(shp_path),
                        'has_projection': os.path.exists(base_name + '.prj')
                    })
                else:
                    print(f"跳过 {shp_path}，缺少文件: {missing_files}")
    
    return shapefiles

def find_geotiffs(directory):
    """查找目录中的所有GeoTIFF文件"""
    geotiffs = []
    
    # 支持的扩展名
    extensions = ['*.tif', '*.tiff', '*.TIF', '*.TIFF']
    
    for ext in extensions:
        pattern = os.path.join(directory, '**', ext)
        for tif_path in glob.glob(pattern, recursive=True):
            geotiffs.append({
                'path': tif_path,
                'name': os.path.splitext(os.path.basename(tif_path))[0],
                'size': os.path.getsize(tif_path)
            })
    
    return geotiffs
```

### 3. 发布状态监控

```python
def monitor_publication_progress(client, workspace, expected_layers):
    """监控发布进度"""
    import time
    
    start_time = time.time()
    timeout = 300  # 5分钟超时
    
    while time.time() - start_time < timeout:
        try:
            layers = client.get_layers(workspace)
            current_count = layers.get('count', 0)
            
            print(f"当前图层数量: {current_count}/{expected_layers}")
            
            if current_count >= expected_layers:
                print("所有图层发布完成!")
                return True
            
            time.sleep(10)  # 每10秒检查一次
            
        except Exception as e:
            print(f"检查进度时出错: {e}")
            time.sleep(5)
    
    print("发布超时!")
    return False
```

## 故障排除

### 常见问题诊断

```python
def diagnose_publication_issues(client, file_path, workspace):
    """诊断发布问题"""
    issues = []
    
    # 1. 检查文件存在性
    if not os.path.exists(file_path):
        issues.append(f"文件不存在: {file_path}")
        return issues
    
    # 2. 检查文件权限
    if not os.access(file_path, os.R_OK):
        issues.append(f"文件无读取权限: {file_path}")
    
    # 3. 检查工作区
    workspaces = client.get_workspaces()
    if workspace not in workspaces.get('workspaces', []):
        issues.append(f"工作区不存在: {workspace}")
    
    # 4. 检查GeoServer连接
    diagnosis = client.diagnose_geoserver()
    if diagnosis.get('diagnosis', {}).get('geoserver_connection') != 'ok':
        issues.append("GeoServer连接异常")
    
    # 5. 检查文件格式
    if file_path.lower().endswith('.shp'):
        base_name = os.path.splitext(file_path)[0]
        required_files = ['.shx', '.dbf']
        for ext in required_files:
            if not os.path.exists(base_name + ext):
                issues.append(f"缺少必需文件: {base_name + ext}")
    
    return issues

# 使用示例
issues = diagnose_publication_issues(
    client, 
    "D:/data/boundaries.shp", 
    "test_workspace"
)

if issues:
    print("发现以下问题:")
    for issue in issues:
        print(f"  - {issue}")
else:
    print("未发现问题，可以尝试发布")
```

### 日志分析

```python
def analyze_geoserver_logs(log_directory):
    """分析GeoServer日志"""
    import re
    from datetime import datetime, timedelta
    
    log_files = glob.glob(os.path.join(log_directory, "*.log"))
    
    errors = []
    warnings = []
    recent_activities = []
    
    # 分析最近24小时的日志
    cutoff_time = datetime.now() - timedelta(hours=24)
    
    for log_file in log_files:
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    # 提取时间戳
                    timestamp_match = re.search(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', line)
                    if timestamp_match:
                        try:
                            log_time = datetime.strptime(timestamp_match.group(), '%Y-%m-%d %H:%M:%S')
                            if log_time < cutoff_time:
                                continue
                        except:
                            continue
                    
                    # 分类日志条目
                    if 'ERROR' in line:
                        errors.append(line.strip())
                    elif 'WARN' in line:
                        warnings.append(line.strip())
                    elif any(keyword in line for keyword in ['published', 'created', 'deleted']):
                        recent_activities.append(line.strip())
        
        except Exception as e:
            print(f"无法读取日志文件 {log_file}: {e}")
    
    return {
        'errors': errors[-10:],  # 最近10个错误
        'warnings': warnings[-10:],  # 最近10个警告
        'activities': recent_activities[-20:]  # 最近20个活动
    }
```

## 性能优化

### 批量操作优化

```python
def optimized_batch_publish(client, files, workspace, batch_size=5):
    """优化的批量发布"""
    import concurrent.futures
    import threading
    
    results = []
    lock = threading.Lock()
    
    def publish_file(file_path):
        try:
            result = client.publish_shapefile(file_path, workspace)
            with lock:
                results.append({
                    'file': file_path,
                    'result': result
                })
            return result
        except Exception as e:
            with lock:
                results.append({
                    'file': file_path,
                    'error': str(e)
                })
            return None
    
    # 使用线程池进行并发发布
    with concurrent.futures.ThreadPoolExecutor(max_workers=batch_size) as executor:
        futures = [executor.submit(publish_file, file_path) for file_path in files]
        concurrent.futures.wait(futures)
    
    return results
```

这个管理API文档涵盖了所有17个管理接口的详细说明，包括参数、示例、错误处理、最佳实践等。接下来我将创建其他模块的文档。
