2025-07-25 17:08:23,830 - INFO - ============ TIF处理任务 4c45af59-78c5-4f87-90b6-0418138bdd75 开始执行 ============
2025-07-25 17:08:23,834 - INFO - 开始时间: 2025-07-25 17:08:23
2025-07-25 17:08:23,836 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-25 17:08:23,839 - INFO - 系统信息:
2025-07-25 17:08:23,844 - INFO -   操作系统: Windows 10.0.19045
2025-07-25 17:08:23,849 - INFO -   Python版本: 3.8.20
2025-07-25 17:08:23,862 - INFO -   GDAL版本: 3.9.2
2025-07-25 17:08:23,867 - INFO -   GPU可用: 否
2025-07-25 17:08:23,870 - INFO - 检查参数有效性...
2025-07-25 17:08:23,871 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-25 17:08:23,876 - INFO - 开始执行TIF处理流程...
2025-07-25 17:08:23,889 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-25 17:08:23,892 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-25 17:08:23,893 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-25 17:09:20,950 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (17:09:19)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-25 17:09:21,022 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-25 17:09:25,046 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:04<01:19,  4.00s/it]
2025-07-25 17:09:25,275 - ERROR - 
处理数据块:  10%|######7                                                                | 2/21 [00:04<00:34,  1.79s/it]
2025-07-25 17:09:26,168 - ERROR - 
处理数据块:  14%|##########1                                                            | 3/21 [00:05<00:24,  1.38s/it]
2025-07-25 17:09:26,347 - ERROR - 
处理数据块:  43%|##############################4                                        | 9/21 [00:05<00:03,  3.28it/s]
2025-07-25 17:09:26,415 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:05<00:00,  3.90it/s]
2025-07-25 17:09:26,431 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-25 17:09:26,432 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-25 17:09:26,582 - ERROR - 
合并结果:  14%|##########4                                                              | 3/21 [00:00<00:00, 20.18it/s]
2025-07-25 17:09:26,835 - ERROR - 
合并结果:  29%|####################8                                                    | 6/21 [00:00<00:01, 14.29it/s]
2025-07-25 17:09:26,976 - ERROR - 
合并结果:  38%|###########################8                                             | 8/21 [00:00<00:00, 14.27it/s]
2025-07-25 17:09:27,095 - ERROR - 
合并结果:  48%|##################################2                                     | 10/21 [00:00<00:00, 15.05it/s]
2025-07-25 17:09:27,199 - ERROR - 
合并结果:  57%|#########################################1                              | 12/21 [00:00<00:00, 16.19it/s]
2025-07-25 17:09:27,472 - ERROR - 
合并结果:  67%|################################################                        | 14/21 [00:01<00:00, 11.68it/s]
2025-07-25 17:09:27,617 - ERROR - 
合并结果:  76%|######################################################8                 | 16/21 [00:01<00:00, 12.31it/s]
2025-07-25 17:09:27,736 - ERROR - 
合并结果:  86%|#############################################################7          | 18/21 [00:01<00:00, 13.36it/s]
2025-07-25 17:09:27,850 - ERROR - 
合并结果:  95%|####################################################################5   | 20/21 [00:01<00:00, 14.42it/s]
2025-07-25 17:09:27,914 - ERROR - 
合并结果: 100%|########################################################################| 21/21 [00:01<00:00, 14.19it/s]
2025-07-25 17:09:30,538 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 10.04 秒 (17:09:29)预计总处理时间: 约 30.11 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (17:09:29)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-25 17:09:30,543 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-25 17:10:12,062 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-25 17:10:12,089 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-25 17:10:12,091 - ERROR - [A
2025-07-25 17:10:12,385 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:05,  3.42it/s]
2025-07-25 17:10:12,386 - ERROR - [A
2025-07-25 17:10:12,545 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  7.37it/s]
2025-07-25 17:10:12,546 - ERROR - [A
2025-07-25 17:10:12,661 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  7.78it/s]
2025-07-25 17:10:12,663 - ERROR - [A
2025-07-25 17:10:12,840 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:02,  6.88it/s]
2025-07-25 17:10:12,841 - ERROR - [A
2025-07-25 17:10:12,984 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:02,  6.90it/s]
2025-07-25 17:10:12,986 - ERROR - [A
2025-07-25 17:10:13,141 - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  6.73it/s]
2025-07-25 17:10:13,142 - ERROR - [A
2025-07-25 17:10:13,378 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:02,  5.68it/s]
2025-07-25 17:10:13,380 - ERROR - [A
2025-07-25 17:10:14,198 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:02<00:04,  2.67it/s]
2025-07-25 17:10:14,200 - ERROR - [A
2025-07-25 17:10:15,625 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:03<00:07,  1.45it/s]
2025-07-25 17:10:15,630 - ERROR - [A
2025-07-25 17:10:17,106 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:05<00:09,  1.07it/s]
2025-07-25 17:10:17,107 - ERROR - [A
2025-07-25 17:10:17,633 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:05<00:07,  1.23it/s]
2025-07-25 17:10:17,633 - ERROR - [A
2025-07-25 17:10:18,579 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:06<00:06,  1.18it/s]
2025-07-25 17:10:18,585 - ERROR - [A
2025-07-25 17:10:18,775 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:06<00:04,  1.53it/s]
2025-07-25 17:10:18,776 - ERROR - [A
2025-07-25 17:10:19,222 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:07<00:03,  1.69it/s]
2025-07-25 17:10:19,223 - ERROR - [A
2025-07-25 17:10:20,512 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:08<00:03,  1.25it/s]
2025-07-25 17:10:20,517 - ERROR - [A
2025-07-25 17:10:20,668 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:08<00:02,  1.64it/s]
2025-07-25 17:10:20,669 - ERROR - [A
2025-07-25 17:10:20,804 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:08<00:01,  2.14it/s]
2025-07-25 17:10:20,804 - ERROR - [A
2025-07-25 17:10:21,430 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:09<00:01,  1.94it/s]
2025-07-25 17:10:21,433 - ERROR - [A
2025-07-25 17:10:21,636 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:09<00:00,  2.37it/s]
2025-07-25 17:10:21,636 - ERROR - [A
2025-07-25 17:10:21,782 - ERROR - 
波段 1/3 写入进度: 100%|###############################################################| 21/21 [00:09<00:00,  2.95it/s]
2025-07-25 17:10:21,785 - ERROR - [A
2025-07-25 17:10:21,788 - ERROR - [A
2025-07-25 17:11:22,204 - ERROR - 
处理波段:  33%|########################3                                                | 1/3 [01:51<03:43, 111.66s/it]
2025-07-25 17:11:34,224 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-25 17:11:34,259 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-25 17:11:34,261 - ERROR - [A
2025-07-25 17:11:34,982 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:14,  1.39it/s]
2025-07-25 17:11:34,983 - ERROR - [A
2025-07-25 17:11:35,474 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:01<00:11,  1.71it/s]
2025-07-25 17:11:35,474 - ERROR - [A
2025-07-25 17:11:36,199 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:11,  1.54it/s]
2025-07-25 17:11:36,199 - ERROR - [A
2025-07-25 17:11:36,418 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:02<00:08,  2.08it/s]
2025-07-25 17:11:36,419 - ERROR - [A
2025-07-25 17:11:36,846 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:02<00:07,  2.17it/s]
2025-07-25 17:11:36,848 - ERROR - [A
2025-07-25 17:11:37,137 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:06,  2.48it/s]
2025-07-25 17:11:37,138 - ERROR - [A
2025-07-25 17:11:37,439 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:03<00:05,  2.70it/s]
2025-07-25 17:11:37,440 - ERROR - [A
2025-07-25 17:11:37,753 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:03<00:04,  2.84it/s]
2025-07-25 17:11:37,753 - ERROR - [A
2025-07-25 17:11:37,893 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:03<00:03,  3.50it/s]
2025-07-25 17:11:37,894 - ERROR - [A
2025-07-25 17:11:38,088 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:03<00:02,  3.88it/s]
2025-07-25 17:11:38,089 - ERROR - [A
2025-07-25 17:11:38,217 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:03<00:02,  4.58it/s]
2025-07-25 17:11:38,218 - ERROR - [A
2025-07-25 17:11:38,440 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:04<00:01,  4.55it/s]
2025-07-25 17:11:38,441 - ERROR - [A
2025-07-25 17:11:38,608 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:04<00:01,  4.91it/s]
2025-07-25 17:11:38,608 - ERROR - [A
2025-07-25 17:11:39,115 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:04<00:02,  3.38it/s]
2025-07-25 17:11:39,115 - ERROR - [A
2025-07-25 17:11:39,302 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:05<00:01,  3.80it/s]
2025-07-25 17:11:39,303 - ERROR - [A
2025-07-25 17:11:41,269 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:07<00:03,  1.29it/s]
2025-07-25 17:11:41,270 - ERROR - [A
2025-07-25 17:11:44,975 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:10<00:06,  1.66s/it]
2025-07-25 17:11:44,984 - ERROR - [A
2025-07-25 17:11:50,986 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:16<00:08,  2.97s/it]
2025-07-25 17:11:51,011 - ERROR - [A
2025-07-25 17:12:01,900 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:27<00:10,  5.35s/it]
2025-07-25 17:12:01,910 - ERROR - [A
2025-07-25 17:12:12,190 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:37<00:06,  6.83s/it]
2025-07-25 17:12:12,192 - ERROR - [A
2025-07-25 17:12:13,686 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:39<00:00,  5.23s/it]
2025-07-25 17:12:13,686 - ERROR - [A
2025-07-25 17:12:13,688 - ERROR - [A
2025-07-25 17:14:05,561 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [04:35<02:22, 142.07s/it]
2025-07-25 17:14:25,544 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-25 17:14:25,578 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-25 17:14:25,579 - ERROR - [A
2025-07-25 17:14:26,417 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:16,  1.20it/s]
2025-07-25 17:14:26,418 - ERROR - [A
2025-07-25 17:14:26,703 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:01<00:09,  1.96it/s]
2025-07-25 17:14:26,704 - ERROR - [A
2025-07-25 17:14:27,250 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:09,  1.90it/s]
2025-07-25 17:14:27,251 - ERROR - [A
2025-07-25 17:14:27,627 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:02<00:07,  2.14it/s]
2025-07-25 17:14:27,627 - ERROR - [A
2025-07-25 17:14:28,325 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:02<00:08,  1.81it/s]
2025-07-25 17:14:28,326 - ERROR - [A
2025-07-25 17:14:28,515 - ERROR - 
波段 3/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:06,  2.33it/s]
2025-07-25 17:14:28,528 - ERROR - [A
2025-07-25 17:14:28,683 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:03<00:04,  2.91it/s]
2025-07-25 17:14:28,684 - ERROR - [A
2025-07-25 17:14:28,814 - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:03<00:03,  3.63it/s]
2025-07-25 17:14:28,814 - ERROR - [A
2025-07-25 17:14:28,915 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:03<00:02,  4.53it/s]
2025-07-25 17:14:28,916 - ERROR - [A
2025-07-25 17:14:29,026 - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:03<00:01,  7.00it/s]
2025-07-25 17:14:29,027 - ERROR - [A
2025-07-25 17:14:29,132 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:03<00:00,  9.36it/s]
2025-07-25 17:14:29,133 - ERROR - [A
2025-07-25 17:14:29,258 - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 15/21 [00:03<00:00, 11.02it/s]
2025-07-25 17:14:29,261 - ERROR - [A
2025-07-25 17:14:29,473 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [00:03<00:00, 10.38it/s]
2025-07-25 17:14:29,476 - ERROR - [A
2025-07-25 17:14:29,801 - ERROR - 
波段 3/3 写入进度:  90%|#########################################################      | 19/21 [00:04<00:00,  8.42it/s]
2025-07-25 17:14:29,807 - ERROR - [A
2025-07-25 17:14:30,147 - ERROR - 
波段 3/3 写入进度: 100%|###############################################################| 21/21 [00:04<00:00,  7.36it/s]
2025-07-25 17:14:30,155 - ERROR - [A
2025-07-25 17:14:30,160 - ERROR - [A
2025-07-25 17:16:37,710 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [07:07<00:00, 146.67s/it]
2025-07-25 17:16:37,712 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [07:07<00:00, 142.39s/it]
2025-07-25 17:18:34,227 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 428.40 秒 (17:16:38)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (17:16:38)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-25 17:18:34,237 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-25 17:18:34,798 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.79it/s]
2025-07-25 17:18:34,864 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.60it/s]
2025-07-25 17:18:34,958 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-25 17:18:50,116 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-25 17:18:50,119 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-25 17:18:51,472 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:01<00:19,  1.48it/s]
2025-07-25 17:18:51,643 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:13,  2.15it/s]
2025-07-25 17:18:51,709 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 19.52it/s]
2025-07-25 17:18:56,420 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-25 17:18:56,421 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-25 17:18:57,074 - ERROR - 
写入多边形:   6%|####                                                                   | 2/35 [00:00<00:10,  3.07it/s]
2025-07-25 17:18:58,511 - ERROR - 
写入多边形:  31%|######################                                                | 11/35 [00:02<00:04,  5.49it/s]
2025-07-25 17:18:58,516 - ERROR - 
写入多边形: 100%|######################################################################| 35/35 [00:02<00:00, 16.71it/s]
2025-07-25 17:18:58,766 - INFO - 处理完成，耗时: 634.87 秒 (10.58 分钟)
2025-07-25 17:18:58,767 - INFO - 处理结果: 成功
2025-07-25 17:18:58,770 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-25 17:18:58,773 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-25 17:18:59,422 - INFO - TIF处理任务 4c45af59-78c5-4f87-90b6-0418138bdd75 执行成功
2025-07-25 17:18:59,423 - INFO - 完成时间: 2025-07-25 17:18:58
2025-07-25 17:18:59,424 - INFO - 状态: 运行成功
2025-07-25 17:18:59,424 - INFO - ============ 任务执行结束 ============
