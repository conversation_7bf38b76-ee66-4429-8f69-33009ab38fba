#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批处理执行器 (Django版本)
"""

import os
import sys
import logging
import subprocess
import threading
import time
import uuid
import json
from datetime import datetime
from django.conf import settings

# 获取Django配置的日志记录器
logger = logging.getLogger('geoserver_api')


class BatchExecutor:
    """批处理文件执行器"""

    def __init__(self):
        """初始化批处理执行器"""
        self.tasks = {}
        self.log_dir = settings.BASE_DIR / 'geoserverRest' / 'batlog'
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.status_file = self.log_dir / 'batch_status.json'
        self.load_status()

    def load_status(self):
        """从文件加载任务状态"""
        try:
            if self.status_file.exists():
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    self.tasks = json.load(f)
                logger.info(f"加载了 {len(self.tasks)} 个批处理任务状态")
        except Exception as e:
            logger.error(f"加载任务状态失败: {str(e)}")
            self.tasks = {}

    def save_status(self):
        """保存任务状态到文件"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.tasks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存任务状态失败: {str(e)}")

    def execute_batch(self, batch_path, args=None):
        """
        执行批处理文件

        Args:
            batch_path: 批处理文件路径
            args: 参数字典

        Returns:
            任务ID
        """
        task_id = str(uuid.uuid4())
        
        # 创建任务记录
        task_info = {
            'id': task_id,
            'batch_path': batch_path,
            'args': args or {},
            'status': 'running',
            'start_time': datetime.now().isoformat(),
            'end_time': None,
            'exit_code': None,
            'log_file': str(self.log_dir / f'{task_id}.log')
        }
        
        self.tasks[task_id] = task_info
        self.save_status()
        
        # 在新线程中执行批处理
        thread = threading.Thread(
            target=self._execute_batch_thread,
            args=(task_id, batch_path, args or {})
        )
        thread.daemon = True
        thread.start()
        
        logger.info(f"启动批处理任务 {task_id}: {batch_path}")
        return task_id

    def _execute_batch_thread(self, task_id, batch_path, args):
        """在线程中执行批处理文件"""
        log_file = self.log_dir / f'{task_id}.log'
        
        try:
            # 构建命令行参数
            cmd = [batch_path]
            
            # 处理特殊参数 project_path
            if 'project_path' in args:
                cmd.append(args['project_path'])
            
            # 处理其他参数
            for key, value in args.items():
                if key == 'project_path':
                    continue  # 已经处理过了
                
                if value == '' or value is None:
                    # 无值标志参数
                    cmd.append(f'--{key}')
                else:
                    # 有值参数
                    cmd.extend([f'--{key}', str(value)])
            
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            # 执行批处理文件
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"开始执行批处理: {batch_path}\n")
                f.write(f"命令行: {' '.join(cmd)}\n")
                f.write(f"开始时间: {datetime.now().isoformat()}\n")
                f.write("-" * 50 + "\n")
                f.flush()
                
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    cwd=os.path.dirname(batch_path)
                )
                
                # 实时写入输出
                for line in process.stdout:
                    f.write(line)
                    f.flush()
                
                # 等待进程完成
                exit_code = process.wait()
                
                f.write("-" * 50 + "\n")
                f.write(f"结束时间: {datetime.now().isoformat()}\n")
                f.write(f"退出代码: {exit_code}\n")
            
            # 更新任务状态
            self.tasks[task_id].update({
                'status': 'completed' if exit_code == 0 else 'failed',
                'end_time': datetime.now().isoformat(),
                'exit_code': exit_code
            })
            
            logger.info(f"批处理任务 {task_id} 完成，退出代码: {exit_code}")
            
        except Exception as e:
            # 更新任务状态为失败
            self.tasks[task_id].update({
                'status': 'failed',
                'end_time': datetime.now().isoformat(),
                'error': str(e)
            })
            
            # 写入错误日志
            try:
                with open(log_file, 'a', encoding='utf-8') as f:
                    f.write(f"\n执行出错: {str(e)}\n")
            except:
                pass
            
            logger.error(f"批处理任务 {task_id} 执行失败: {str(e)}")
        
        finally:
            self.save_status()

    def get_task_status(self, task_id):
        """获取任务状态"""
        return self.tasks.get(task_id)

    def get_all_tasks(self):
        """获取所有任务状态"""
        return list(self.tasks.values())

    def get_task_log(self, task_id):
        """获取任务日志"""
        task = self.tasks.get(task_id)
        if not task:
            return None
        
        log_file = task.get('log_file')
        if not log_file or not os.path.exists(log_file):
            return None
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取任务日志失败: {str(e)}")
            return None


# 全局批处理执行器实例
executor = BatchExecutor()
