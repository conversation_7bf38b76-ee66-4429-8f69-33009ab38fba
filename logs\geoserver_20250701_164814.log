2025-07-01 16:48:14,728 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250701_164814.log
2025-07-01 16:48:14,747 - root - INFO - 开始执行命令: publish-shapefile-directory
2025-07-01 16:48:14,747 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-01 16:48:14,748 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-01 16:48:14,764 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-01 16:48:14,765 - root - INFO - 在目录 'data/20250701/testshp' 中找到 5 个Shapefile文件
2025-07-01 16:48:14,766 - root - INFO - 使用字符集: UTF-8
2025-07-01 16:48:14,774 - root - INFO - 未提供存储名称，使用目录名称作为前缀: testshp
2025-07-01 16:48:14,786 - root - INFO - 工作区 'test_workspace' 不存在，正在创建...
2025-07-01 16:48:14,827 - root - INFO - 成功创建工作区 'test_workspace'
2025-07-01 16:48:14,839 - root - INFO - 在工作区 'test_workspace' 中找到 0 个图层
2025-07-01 16:48:14,840 - root - INFO - 发布前工作区 'test_workspace' 中有 0 个图层
2025-07-01 16:48:14,840 - root - INFO - 正在发布Shapefile: 招商引资片区.shp, 存储名: testshp
2025-07-01 16:48:14,857 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区.shp
2025-07-01 16:48:14,858 - root - INFO - 添加文件到ZIP: 招商引资片区.shp
2025-07-01 16:48:14,982 - root - INFO - 添加文件到ZIP: 招商引资片区.dbf
2025-07-01 16:48:14,985 - root - INFO - 添加文件到ZIP: 招商引资片区.shx
2025-07-01 16:48:14,987 - root - INFO - 添加文件到ZIP: 招商引资片区.prj
2025-07-01 16:48:14,989 - root - INFO - 添加文件到ZIP: 招商引资片区.qix
2025-07-01 16:48:14,990 - root - INFO - 添加文件到ZIP: 招商引资片区.cpg
2025-07-01 16:48:14,991 - root - INFO - 添加文件到ZIP: 招商引资片区.qmd
2025-07-01 16:48:14,993 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:48:14,996 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:48:15,133 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:48:15,197 - root - INFO - 成功创建图层 '招商引资片区'
2025-07-01 16:48:15,209 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/招商引资片区
2025-07-01 16:48:15,211 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区.shp' 成功发布为图层 'test_workspace:招商引资片区'
2025-07-01 16:48:15,212 - root - INFO - 正在发布Shapefile: 招商引资片区交种植区.shp, 存储名: testshp
2025-07-01 16:48:15,230 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区交种植区.shp
2025-07-01 16:48:15,231 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shp
2025-07-01 16:48:15,930 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.dbf
2025-07-01 16:48:16,101 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shx
2025-07-01 16:48:16,118 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.prj
2025-07-01 16:48:16,119 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qix
2025-07-01 16:48:16,132 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.cpg
2025-07-01 16:48:16,134 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qmd
2025-07-01 16:48:16,135 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:48:16,135 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:48:16,324 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:48:16,334 - root - INFO - 图层 '招商引资片区交种植区' 已存在
2025-07-01 16:48:16,350 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/招商引资片区交种植区
2025-07-01 16:48:16,351 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区交种植区.shp' 成功发布为图层 'test_workspace:招商引资片区交种植区'
2025-07-01 16:48:16,353 - root - INFO - 正在发布Shapefile: 招商引资片区交设施农用地.shp, 存储名: testshp
2025-07-01 16:48:16,366 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区交设施农用地.shp
2025-07-01 16:48:16,367 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shp
2025-07-01 16:48:16,500 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.dbf
2025-07-01 16:48:16,514 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shx
2025-07-01 16:48:16,516 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.prj
2025-07-01 16:48:16,517 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qix
2025-07-01 16:48:16,519 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.cpg
2025-07-01 16:48:16,521 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qmd
2025-07-01 16:48:16,523 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:48:16,523 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:48:16,685 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:48:16,771 - root - INFO - 成功创建图层 '招商引资片区交设施农用地'
2025-07-01 16:48:16,785 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/招商引资片区交设施农用地
2025-07-01 16:48:16,786 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区交设施农用地.shp' 成功发布为图层 'test_workspace:招商引资片区交设施农用地'
2025-07-01 16:48:16,787 - root - INFO - 正在发布Shapefile: 种植区.shp, 存储名: testshp
2025-07-01 16:48:16,799 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\种植区.shp
2025-07-01 16:48:16,800 - root - INFO - 添加文件到ZIP: 种植区.shp
2025-07-01 16:48:17,998 - root - INFO - 添加文件到ZIP: 种植区.dbf
2025-07-01 16:48:18,253 - root - INFO - 添加文件到ZIP: 种植区.shx
2025-07-01 16:48:18,277 - root - INFO - 添加文件到ZIP: 种植区.prj
2025-07-01 16:48:18,278 - root - INFO - 添加文件到ZIP: 种植区.qix
2025-07-01 16:48:18,302 - root - INFO - 添加文件到ZIP: 种植区.cpg
2025-07-01 16:48:18,303 - root - INFO - 添加文件到ZIP: 种植区.qmd
2025-07-01 16:48:18,304 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:48:18,304 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:48:18,551 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:48:18,611 - root - INFO - 成功创建图层 '种植区'
2025-07-01 16:48:18,625 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/种植区
2025-07-01 16:48:18,626 - root - INFO - Shapefile 'data/20250701/testshp\种植区.shp' 成功发布为图层 'test_workspace:种植区'
2025-07-01 16:48:18,628 - root - INFO - 正在发布Shapefile: 设施农用地潜力.shp, 存储名: testshp
2025-07-01 16:48:18,643 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\设施农用地潜力.shp
2025-07-01 16:48:18,644 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shp
2025-07-01 16:48:19,751 - root - INFO - 添加文件到ZIP: 设施农用地潜力.dbf
2025-07-01 16:48:19,813 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shx
2025-07-01 16:48:19,822 - root - INFO - 添加文件到ZIP: 设施农用地潜力.prj
2025-07-01 16:48:19,823 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qix
2025-07-01 16:48:19,834 - root - INFO - 添加文件到ZIP: 设施农用地潜力.cpg
2025-07-01 16:48:19,835 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qmd
2025-07-01 16:48:19,836 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:48:19,836 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:48:20,041 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:48:20,132 - root - INFO - 成功创建图层 '设施农用地潜力'
2025-07-01 16:48:20,150 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/设施农用地潜力
2025-07-01 16:48:20,151 - root - INFO - Shapefile 'data/20250701/testshp\设施农用地潜力.shp' 成功发布为图层 'test_workspace:设施农用地潜力'
2025-07-01 16:48:20,172 - root - INFO - 在工作区 'test_workspace' 中找到 5 个图层
2025-07-01 16:48:20,173 - root - INFO - 发布后工作区 'test_workspace' 中有 5 个图层
2025-07-01 16:48:20,174 - root - INFO - 新增了 5 个图层: 种植区, 招商引资片区交设施农用地, 设施农用地潜力, 招商引资片区交种植区, 招商引资片区
2025-07-01 16:48:20,175 - root - INFO - 成功发布了 5 个Shapefile文件
2025-07-01 16:48:20,175 - root - INFO - 成功发布: 5, 失败: 0
2025-07-01 16:48:20,176 - root - INFO - 命令执行完成: publish-shapefile-directory
