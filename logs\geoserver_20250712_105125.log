2025-07-12 10:51:25,814 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250712_105125.log
2025-07-12 10:51:25,816 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 10:51:25,816 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 10:51:25,860 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 10:51:25,870 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 10:51:25,870 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 10:51:25,883 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 10:51:25,889 - root - INFO - === GeoServer REST API服务 ===
2025-07-12 10:51:25,889 - root - INFO - 主机: 0.0.0.0
2025-07-12 10:51:25,889 - root - INFO - 端口: 5083
2025-07-12 10:51:25,889 - root - INFO - 调试模式: 禁用
2025-07-12 10:51:25,890 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-12 10:51:25,912 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-12 10:51:25,913 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 10:52:08,809 - root - INFO - 工作区 'tttt' 不存在，正在创建...
2025-07-12 10:52:08,853 - root - INFO - 成功创建工作区: tttt
2025-07-12 10:52:08,853 - root - INFO - 成功创建工作区 'tttt'
2025-07-12 10:52:09,570 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/nanning/file.geotiff
2025-07-12 10:52:10,264 - root - INFO - 成功通过REST API发布GeoTIFF: 201
2025-07-12 10:52:10,264 - root - INFO - 成功发布GeoTIFF: tttt:nanning:nanning
2025-07-12 10:52:10,285 - root - INFO - 图层验证成功: tttt:nanning
2025-07-12 10:52:10,290 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 10:52:10] "GET /api/management/geotiffs/publish?file=D:/Drone_Project/geoserverapi/data/20250701/nanning.tif&workspace=tttt&charset=UTF-8 HTTP/1.1" 200 -
2025-07-12 10:54:04,153 - root - INFO - 在目录 D:/Drone_Project/geoserverapi/data/20250701 中找到 2 个GeoTIFF文件
2025-07-12 10:54:04,153 - root - INFO - 正在发布GeoTIFF: D:/Drone_Project/geoserverapi/data/20250701\nanning.tif
2025-07-12 10:54:04,633 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/20250701_nanning/file.geotiff
2025-07-12 10:54:04,797 - root - INFO - 成功通过REST API发布GeoTIFF: 201
2025-07-12 10:54:04,797 - root - INFO - 成功发布GeoTIFF: tttt:20250701_nanning:nanning
2025-07-12 10:54:04,839 - root - INFO - 图层验证成功(使用存储名): tttt:20250701_nanning
2025-07-12 10:54:04,843 - root - INFO - 正在发布GeoTIFF: D:/Drone_Project/geoserverapi/data/20250701\nanning2.tif
2025-07-12 10:54:04,875 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/20250701_nanning2/file.geotiff
2025-07-12 10:54:05,052 - root - INFO - 成功通过REST API发布GeoTIFF: 201
2025-07-12 10:54:05,052 - root - INFO - 成功发布GeoTIFF: tttt:20250701_nanning2:nanning2
2025-07-12 10:54:05,096 - root - INFO - 图层验证成功(使用存储名): tttt:20250701_nanning2
2025-07-12 10:54:05,100 - root - INFO - 成功发布 2/2 个GeoTIFF文件
2025-07-12 10:54:05,101 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 10:54:05] "GET /api/management/geotiff-directories/publish?directory=D:/Drone_Project/geoserverapi/data/20250701&workspace=tttt&charset=UTF-8 HTTP/1.1" 200 -
2025-07-12 10:54:38,551 - root - INFO - 工作区 'tttt' 不存在，正在创建...
2025-07-12 10:54:38,598 - root - INFO - 成功创建工作区: tttt
2025-07-12 10:54:38,599 - root - INFO - 成功创建工作区 'tttt'
2025-07-12 10:54:38,621 - root - INFO - 在目录 D:/Drone_Project/geoserverapi/data/20250701 中找到 2 个GeoTIFF文件
2025-07-12 10:54:38,622 - root - INFO - 正在发布GeoTIFF: D:/Drone_Project/geoserverapi/data/20250701\nanning.tif
2025-07-12 10:54:38,653 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/20250701_nanning/file.geotiff
2025-07-12 10:54:38,827 - root - INFO - 成功通过REST API发布GeoTIFF: 201
2025-07-12 10:54:38,828 - root - INFO - 成功发布GeoTIFF: tttt:20250701_nanning:nanning
2025-07-12 10:54:38,865 - root - INFO - 图层验证成功(使用存储名): tttt:20250701_nanning
2025-07-12 10:54:38,870 - root - INFO - 正在发布GeoTIFF: D:/Drone_Project/geoserverapi/data/20250701\nanning2.tif
2025-07-12 10:54:38,897 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/20250701_nanning2/file.geotiff
2025-07-12 10:54:39,078 - root - INFO - 成功通过REST API发布GeoTIFF: 201
2025-07-12 10:54:39,078 - root - INFO - 成功发布GeoTIFF: tttt:20250701_nanning2:nanning2
2025-07-12 10:54:39,117 - root - INFO - 图层验证成功(使用存储名): tttt:20250701_nanning2
2025-07-12 10:54:39,121 - root - INFO - 成功发布 2/2 个GeoTIFF文件
2025-07-12 10:54:39,122 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 10:54:39] "GET /api/management/geotiff-directories/publish?directory=D:/Drone_Project/geoserverapi/data/20250701&workspace=tttt&charset=UTF-8 HTTP/1.1" 200 -
