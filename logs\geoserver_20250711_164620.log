2025-07-11 16:46:20,768 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250711_164620.log
2025-07-11 16:46:20,771 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-11 16:46:20,771 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-11 16:46:20,795 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-11 16:46:20,802 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-11 16:46:20,802 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-11 16:46:20,821 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-11 16:46:20,831 - root - INFO - === GeoServer REST API服务 ===
2025-07-11 16:46:20,831 - root - INFO - 主机: 0.0.0.0
2025-07-11 16:46:20,832 - root - INFO - 端口: 5000
2025-07-11 16:46:20,832 - root - INFO - 调试模式: 启用
2025-07-11 16:46:20,832 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5000
2025-07-11 16:46:20,877 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-11 16:46:20,878 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-11 16:46:20,884 - werkzeug - INFO -  * Restarting with stat
2025-07-11 16:49:10,797 - werkzeug - INFO -  * Restarting with stat
