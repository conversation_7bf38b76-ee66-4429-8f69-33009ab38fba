@echo off
setlocal enabledelayedexpansion

REM Initialize parameter variables
set "arg1="
set "arg2="

REM Parse named parameters
:parse_args
if "%~1"=="" goto :end_parse
if "%~1"=="--arg1" (
    set "arg1=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--arg2" (
    set "arg2=%~2"
    shift
    shift
    goto :parse_args
)
shift
goto :parse_args
:end_parse

REM Check if parameters are provided
if not defined arg1 (
    echo Error: Missing parameter --arg1
    goto :usage
)
if not defined arg2 (
    echo Error: Missing parameter --arg2
    goto :usage
)

REM Execute main functionality
echo Value of parameter 1 is: %arg1%
echo Waiting for 60 seconds...
timeout /t 60 /nobreak
echo Value of parameter 2 is: %arg2%
goto :eof

:usage
echo Usage: test_script.bat --arg1 value1 --arg2 value2
exit /b 1 