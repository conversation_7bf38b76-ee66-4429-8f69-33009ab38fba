2025-07-28 10:41:17,357 - INFO - ============ TIF处理任务 1f376117-4639-4e05-8be5-839efea9682a 开始执行 ============
2025-07-28 10:41:17,361 - INFO - 开始时间: 2025-07-28 10:41:17
2025-07-28 10:41:17,364 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 10:41:17,366 - INFO - 系统信息:
2025-07-28 10:41:17,367 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 10:41:17,369 - INFO -   Python版本: 3.8.20
2025-07-28 10:41:17,371 - INFO -   GDAL版本: 3.9.2
2025-07-28 10:41:17,372 - INFO -   GPU可用: 否
2025-07-28 10:41:17,373 - INFO - 检查参数有效性...
2025-07-28 10:41:17,384 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 10:41:17,387 - INFO - 开始执行TIF处理流程...
2025-07-28 10:41:17,388 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 10:41:17,388 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-07-28 10:41:17,389 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-07-28 10:41:17,393 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-28 10:41:20,966 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17007, 高=20364, 波段数=4开始读取影像数据，大小约: 5284.58 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (10:41:20)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 10:41:21,023 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 10:41:21,194 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:02,  7.20it/s]
2025-07-28 10:41:21,345 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 31.06it/s]
2025-07-28 10:41:21,560 - ERROR - 
处理数据块:  57%|########################################                              | 12/21 [00:00<00:00, 24.18it/s]
2025-07-28 10:41:21,749 - ERROR - 
处理数据块:  81%|########################################################6             | 17/21 [00:00<00:00, 25.19it/s]
2025-07-28 10:41:21,788 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 28.66it/s]
2025-07-28 10:41:21,789 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 10:41:21,794 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 10:41:21,842 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 528.69it/s]
2025-07-28 10:41:23,612 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 2.07 秒 (10:41:22)预计总处理时间: 约 6.21 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif (10:41:22)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif使用已处理过的影像信息影像信息: 宽=17007, 高=20364, 波段数=3掩码形状: (20364, 17007), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 尺寸: 17007x20364x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 10:41:23,614 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 10:41:27,975 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=42.58, std=63.34应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 10:41:27,979 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 10:41:27,984 - ERROR - [A
2025-07-28 10:41:28,651 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:13,  1.51it/s]
2025-07-28 10:41:28,653 - ERROR - [A
2025-07-28 10:41:28,756 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  6.49it/s]
2025-07-28 10:41:28,760 - ERROR - [A
2025-07-28 10:41:28,862 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 17.02it/s]
2025-07-28 10:41:28,862 - ERROR - [A
2025-07-28 10:41:28,978 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:00<00:00, 25.60it/s]
2025-07-28 10:41:28,979 - ERROR - [A
2025-07-28 10:41:29,073 - ERROR - [A
2025-07-28 10:42:11,468 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:47<01:35, 47.85s/it]
2025-07-28 10:42:15,569 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=45.79, std=65.41应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 10:42:15,573 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 10:42:15,576 - ERROR - [A
2025-07-28 10:42:20,598 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:05<01:40,  5.02s/it]
2025-07-28 10:42:20,598 - ERROR - [A
2025-07-28 10:42:25,349 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:09<01:32,  4.86s/it]
2025-07-28 10:42:25,350 - ERROR - [A
2025-07-28 10:42:29,173 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:13<01:18,  4.39s/it]
2025-07-28 10:46:02,288 - ERROR - [A
2025-07-28 10:46:04,391 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [03:48<24:49, 87.62s/it]
2025-07-28 10:46:04,394 - ERROR - [A
2025-07-28 10:46:14,669 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [03:59<15:55, 59.73s/it]
2025-07-28 10:46:14,670 - ERROR - [A
2025-07-28 10:46:29,498 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [04:13<11:06, 44.46s/it]
2025-07-28 10:46:29,499 - ERROR - [A
2025-07-28 10:46:41,391 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [04:25<07:53, 33.82s/it]
2025-07-28 10:46:41,392 - ERROR - [A
2025-07-28 10:46:45,560 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [04:29<05:16, 24.38s/it]
2025-07-28 10:46:45,563 - ERROR - [A
2025-07-28 10:46:49,832 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [04:34<03:37, 18.09s/it]
2025-07-28 10:46:49,833 - ERROR - [A
2025-07-28 10:46:51,866 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [04:36<02:24, 13.13s/it]
2025-07-28 10:46:51,867 - ERROR - [A
2025-07-28 10:46:57,092 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [04:41<01:47, 10.71s/it]
2025-07-28 10:46:57,093 - ERROR - [A
2025-07-28 10:47:00,244 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [04:44<01:15,  8.41s/it]
2025-07-28 10:47:00,245 - ERROR - [A
2025-07-28 10:47:01,796 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [04:46<00:50,  6.33s/it]
2025-07-28 10:47:01,796 - ERROR - [A
2025-07-28 10:47:06,133 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [04:50<00:40,  5.73s/it]
2025-07-28 10:47:06,133 - ERROR - [A
2025-07-28 10:47:06,301 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [04:50<00:15,  3.12s/it]
2025-07-28 10:47:06,302 - ERROR - [A
2025-07-28 10:47:12,279 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [04:56<00:15,  3.83s/it]
2025-07-28 10:47:12,280 - ERROR - [A
2025-07-28 10:47:37,136 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [05:21<00:28,  9.34s/it]
2025-07-28 10:47:37,137 - ERROR - [A
2025-07-28 10:48:17,882 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [06:02<00:35, 17.89s/it]
2025-07-28 10:48:17,882 - ERROR - [A
2025-07-28 10:49:29,812 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [07:14<00:33, 33.03s/it]
2025-07-28 10:49:29,814 - ERROR - [A
2025-07-28 10:49:29,821 - ERROR - [A
2025-07-28 10:55:14,444 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [13:50<08:00, 480.28s/it]
2025-07-28 10:55:20,559 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=35.76, std=51.77应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 10:55:20,560 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 10:55:20,565 - ERROR - [A
2025-07-28 10:55:21,161 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:11,  1.68it/s]
2025-07-28 10:55:21,163 - ERROR - [A
2025-07-28 10:55:22,588 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:02<00:20,  1.08s/it]
2025-07-28 10:55:22,589 - ERROR - [A
2025-07-28 10:55:24,857 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:04<00:29,  1.63s/it]
2025-07-28 10:55:24,858 - ERROR - [A
2025-07-28 10:55:27,344 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:06<00:33,  1.97s/it]
2025-07-28 10:55:27,503 - ERROR - [A
2025-07-28 10:55:27,535 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:06<00:21,  1.33s/it]
2025-07-28 10:55:27,535 - ERROR - [A
2025-07-28 10:55:27,638 - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:07<00:07,  1.85it/s]
2025-07-28 10:55:27,639 - ERROR - [A
2025-07-28 10:55:27,777 - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 12/21 [00:07<00:02,  3.71it/s]
2025-07-28 10:55:27,778 - ERROR - [A
2025-07-28 10:55:27,900 - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 15/21 [00:07<00:01,  5.37it/s]
2025-07-28 10:55:27,903 - ERROR - [A
2025-07-28 10:55:28,029 - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 18/21 [00:07<00:00,  7.29it/s]
2025-07-28 10:55:28,029 - ERROR - [A
2025-07-28 10:55:28,093 - ERROR - [A
2025-07-28 10:57:48,722 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [16:25<00:00, 331.42s/it]
2025-07-28 10:57:48,723 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [16:25<00:00, 328.37s/it]
2025-07-28 10:58:08,965 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif影像保存完成，耗时: 986.53 秒 (10:57:49)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp (10:57:49)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tifTIF文件信息: 宽=17007, 高=20364, 波段数=3开始读取影像数据，大小约: 3963.44 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif提取TIF文件信息...TIF文件信息: 宽=17007, 高=20364, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 218929698 个无效像素检查波段 2...波段 2 检测到 218929698 个无效像素检查波段 3...波段 3 检测到 218929698 个无效像素掩码统计: 总像素数 346330548, 有效像素数 127400850 (36.79%), 无效像素数 218929698 (63.21%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 1 个初始轮廓轮廓面积统计: 最小=127380512.50, 最大=127380512.50, 平均=127380512.50, 总数=1将 1 个轮廓转换为地理坐标...
2025-07-28 10:58:08,991 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-28 10:58:09,416 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.37it/s]
2025-07-28 10:58:09,417 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.36it/s]
2025-07-28 10:58:10,177 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-28 10:58:22,500 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-28 10:58:22,501 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-28 10:58:23,352 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:12,  2.36it/s]
2025-07-28 10:58:23,485 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:00<00:08,  3.30it/s]
2025-07-28 10:58:23,520 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 30.50it/s]
2025-07-28 10:58:46,206 - INFO - 处理了 82263 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp写入shapefile特征...处理MultiPolygon，包含 19 个多边形
2025-07-28 10:58:46,209 - ERROR - 
写入多边形:   0%|                                                                               | 0/19 [00:00<?, ?it/s]
2025-07-28 10:58:46,265 - ERROR - 
写入多边形: 100%|#####################################################################| 19/19 [00:00<00:00, 387.73it/s]
2025-07-28 10:58:46,409 - INFO - 处理完成，耗时: 1049.02 秒 (17.48 分钟)
2025-07-28 10:58:46,409 - INFO - 处理结果: 成功
2025-07-28 10:58:46,413 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 大小: 740.84 MB
2025-07-28 10:58:46,417 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp, 大小: 22.81 KB
2025-07-28 10:58:46,423 - INFO - TIF处理任务 1f376117-4639-4e05-8be5-839efea9682a 执行成功
2025-07-28 10:58:46,428 - INFO - 完成时间: 2025-07-28 10:58:46
2025-07-28 10:58:46,428 - INFO - 状态: 运行成功
2025-07-28 10:58:46,432 - INFO - ============ 任务执行结束 ============
