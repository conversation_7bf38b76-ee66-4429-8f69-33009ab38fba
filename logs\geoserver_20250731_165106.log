2025-07-31 16:51:06,555 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250731_165106.log
2025-07-31 16:51:06,572 - geo_publisher - INFO - 加载了 32 个任务状态
2025-07-31 16:51:06,673 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-31 16:51:06,674 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-31 16:51:06,683 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-31 16:51:06,690 - root - INFO - === GeoServer REST API服务 ===
2025-07-31 16:51:06,690 - root - INFO - 主机: 0.0.0.0
2025-07-31 16:51:06,690 - root - INFO - 端口: 5083
2025-07-31 16:51:06,692 - root - INFO - 调试模式: 禁用
2025-07-31 16:51:06,692 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-31 16:51:06,710 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-31 16:51:06,711 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 16:51:08,830 - werkzeug - INFO - ************** - - [31/Jul/2025 16:51:08] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-31 16:51:08,835 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-31 16:51:08,864 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-31 16:51:08,866 - werkzeug - INFO - ************** - - [31/Jul/2025 16:51:08] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-31 16:51:11,216 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:51:11,231 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:51:11,244 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:51:11,252 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:51:11,256 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:51:11] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:51:11,260 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:51:11] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:51:11,297 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:51:11,304 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-31 16:51:11,311 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:51:11,315 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-31 16:51:11,320 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:51:11] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:51:11,321 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:51:11] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-31 16:51:11,359 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:51:11,384 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:51:11,389 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:51:11] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:51:11,396 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-31 16:51:11,415 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-31 16:51:11,420 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:51:11] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-31 16:51:12,319 - werkzeug - INFO - ************** - - [31/Jul/2025 16:51:12] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:51:12,327 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:51:12,364 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:51:12,365 - werkzeug - INFO - ************** - - [31/Jul/2025 16:51:12] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:51:13,066 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:51:13,097 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:51:13,101 - werkzeug - INFO - ************** - - [31/Jul/2025 16:51:13] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:51:54,005 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:51:54,008 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:51:54,018 - map_api - INFO - 找到 3 个目录
2025-07-31 16:51:54,020 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:51:54,099 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:51:54,100 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:51:54,125 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:51:54,127 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:51:54,142 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:51:54,144 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:51:54,145 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:51:54,147 - werkzeug - INFO - ************** - - [31/Jul/2025 16:51:54] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-31 16:52:51,275 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:52:51,276 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:52:51,377 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-31 16:52:51,403 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:52:51,405 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:51] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:52:51,421 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:52:51,421 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-31 16:52:51,424 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:51] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:52:51,425 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:51] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-31 16:52:51,476 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:52:51,478 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:52:51,478 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-31 16:52:51,484 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:52:51,487 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:51] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:52:51,488 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:52:51,490 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:51] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:52:51,498 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-31 16:52:51,499 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:51] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-31 16:52:51,965 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:51] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:52:51,988 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:52:52,011 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:52:52,012 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:52] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:52:53,749 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:52:53,750 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:52:53,757 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:52:53,758 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:52:53,760 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:53] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:52:53,761 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:53] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:52:53,812 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:52:53,814 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-31 16:52:53,818 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:52:53,820 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:53] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:52:53,821 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-31 16:52:53,823 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:53] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-31 16:52:53,855 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:52:53,857 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-31 16:52:53,862 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:52:53,866 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:53] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:52:53,863 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-31 16:52:53,868 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:53] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-31 16:52:54,016 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:52:54,019 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:52:54,029 - map_api - INFO - 找到 3 个目录
2025-07-31 16:52:54,030 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:52:54,036 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:52:54,037 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:52:54,043 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:52:54,044 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:52:54,050 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:52:54,051 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:52:54,052 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:52:54,054 - werkzeug - INFO - ************** - - [31/Jul/2025 16:52:54] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-31 16:52:54,151 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:52:54,174 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:52:54,175 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:54] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:52:58,430 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:52:58,434 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:52:58,435 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:52:58,437 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:58] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:52:58,441 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:52:58,442 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:58] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:52:58,480 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:52:58,480 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-31 16:52:58,487 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:52:58,490 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:58] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:52:58,488 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-31 16:52:58,492 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:58] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-31 16:52:58,775 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:52:58,777 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-31 16:52:58,780 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:52:58,782 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:58] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:52:58,783 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-31 16:52:58,786 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:58] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-31 16:52:58,874 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:58] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:52:58,905 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:52:58,927 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:52:58,929 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:52:58] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:53:04,645 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:53:04,647 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:53:04,653 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:53:04,656 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:53:04] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:53:04,656 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:53:04,662 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:53:04] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:53:04,722 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:53:04,725 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-31 16:53:04,728 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:53:04,731 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-31 16:53:04,734 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:53:04,740 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:53:04] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:53:04,741 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-31 16:53:04,744 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:53:04] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-31 16:53:04,742 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:53:04,743 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-31 16:53:04,747 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:53:04] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:53:04,748 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:53:04] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-31 16:53:05,120 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:53:05] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:53:05,145 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:53:05,177 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:53:05,179 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:53:05] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:53:08,838 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:53:08,847 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:53:08,851 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:53:08] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:53:08,861 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:53:08,870 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:53:08,873 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:53:08] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:53:08,886 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:53:08,888 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-31 16:53:08,895 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:53:08,900 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:53:08] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:53:08,899 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-31 16:53:08,906 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:53:08] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-31 16:53:09,027 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:53:09,068 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-31 16:53:09,113 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:53:09,119 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:53:09] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:53:09,122 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-31 16:53:09,134 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:53:09] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-31 16:53:10,041 - werkzeug - INFO - ************** - - [31/Jul/2025 16:53:10] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:53:10,063 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:53:10,110 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:53:10,115 - werkzeug - INFO - ************** - - [31/Jul/2025 16:53:10] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:53:10,996 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:53:11,030 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:53:11,031 - werkzeug - INFO - ************** - - [31/Jul/2025 16:53:11] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:53:54,023 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:53:54,024 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:53:54,032 - map_api - INFO - 找到 3 个目录
2025-07-31 16:53:54,034 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:53:54,039 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:53:54,040 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:53:54,045 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:53:54,047 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:53:54,052 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:53:54,053 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:53:54,054 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:53:54,056 - werkzeug - INFO - ************** - - [31/Jul/2025 16:53:54] "GET /api/map/odm/tasks HTTP/1.1" 200 -
