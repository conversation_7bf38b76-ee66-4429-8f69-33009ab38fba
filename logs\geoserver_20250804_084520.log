2025-08-04 08:45:20,597 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250804_084520.log
2025-08-04 08:45:20,599 - geo_publisher - INFO - 加载了 32 个任务状态
2025-08-04 08:45:20,702 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-04 08:45:20,702 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-04 08:45:20,726 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-04 08:45:20,735 - root - INFO - === GeoServer REST API服务 ===
2025-08-04 08:45:20,735 - root - INFO - 主机: 0.0.0.0
2025-08-04 08:45:20,740 - root - INFO - 端口: 5083
2025-08-04 08:45:20,741 - root - INFO - 调试模式: 禁用
2025-08-04 08:45:20,745 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-08-04 08:45:20,772 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-08-04 08:45:20,773 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-04 10:04:51,354 - geo_publisher - INFO - 启动特定结构GeoTIFF发布任务 bd6c42de-cfac-4dc4-b697-ad21c06f525c: D:/Drone_Project/nginxData/ODM/Output, workspace=testodm
2025-08-04 10:04:51,355 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - INFO - GeoServer发布任务 bd6c42de-cfac-4dc4-b697-ad21c06f525c 开始执行
2025-08-04 10:04:51,361 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 10:04:51] "GET /api/geo/structured-geotiff/execute?root_directory=D:/Drone_Project/nginxData/ODM/Output&workspace=testodm HTTP/1.1" 200 -
2025-08-04 10:04:51,361 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - INFO - 开始时间: 2025-08-04 10:04:51
2025-08-04 10:04:51,362 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - INFO - 参数: {
  "root_directory": "D:/Drone_Project/nginxData/ODM/Output",
  "workspace": "testodm",
  "store_name": null
}
2025-08-04 10:04:51,363 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-04 10:04:51,364 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-04 10:04:51,413 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 10:04:51] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-04 10:04:51,423 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-04 10:04:51,426 - geo_publisher - ERROR - 执行任务 bd6c42de-cfac-4dc4-b697-ad21c06f525c 时出错: name 'task_logger' is not defined
2025-08-04 10:04:51,434 - geo_publisher - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 410, in _run_with_logging
    result = wrapper_func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 408, in wrapper_func
    return func(*args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 587, in publish_func
    task_logger.info(f"开始扫描根目录: {root_dir}")
NameError: name 'task_logger' is not defined

2025-08-04 10:04:51,462 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - ERROR - 执行出错: name 'task_logger' is not defined
2025-08-04 10:04:51,462 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 410, in _run_with_logging
    result = wrapper_func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 408, in wrapper_func
    return func(*args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 587, in publish_func
    task_logger.info(f"开始扫描根目录: {root_dir}")
NameError: name 'task_logger' is not defined

2025-08-04 10:04:51,465 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - ERROR - 完成时间: 2025-08-04 10:04:51
2025-08-04 10:04:51,480 - geo_task_bd6c42de-cfac-4dc4-b697-ad21c06f525c - ERROR - 状态: 发布失败
