2025-07-16 10:58:20,085 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250716_105820.log
2025-07-16 10:58:20,096 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 10:58:20,096 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 10:58:20,376 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 10:58:20,689 - batch_executor - INFO - 加载了 35 个任务状态
2025-07-16 10:58:27,097 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 10:58:27,098 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 10:58:28,736 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 10:58:28,742 - root - INFO - === GeoServer REST API服务 ===
2025-07-16 10:58:28,742 - root - INFO - 主机: 0.0.0.0
2025-07-16 10:58:28,743 - root - INFO - 端口: 5083
2025-07-16 10:58:28,744 - root - INFO - 调试模式: 禁用
2025-07-16 10:58:28,744 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-16 10:58:28,763 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-16 10:58:28,764 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-16 11:07:23,638 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 11:07:23] "[33mGET /api/tif/process/async?input_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif&output_shp=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
