<!DOCTYPE html>
<html>
<head>
    <title>GeoServer栅格数据坐标查询测试</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input, button { padding: 8px; }
        button { cursor: pointer; background-color: #4CAF50; color: white; border: none; }
        pre { background-color: #f5f5f5; padding: 15px; overflow: auto; }
        .tabs { display: flex; margin-bottom: 20px; }
        .tab { padding: 10px 20px; cursor: pointer; background-color: #f1f1f1; border: 1px solid #ccc; }
        .tab.active { background-color: #4CAF50; color: white; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
    </style>
</head>
<body>
    <h1>GeoServer栅格数据坐标查询测试</h1>
    
    <div class="tabs">
        <div class="tab active" onclick="showTab('query-tab')">坐标点查询</div>
        <div class="tab" onclick="showTab('layers-tab')">获取图层列表</div>
    </div>
    
    <div id="query-tab" class="tab-content active">
        <h2>坐标点查询</h2>
        <div class="form-group">
            <label for="workspace">工作区:</label>
            <input type="text" id="workspace" value="my_workspace">
        </div>
        
        <div class="form-group">
            <label for="lat">纬度:</label>
            <input type="number" id="lat" value="22.8167" step="0.0001">
        </div>
        
        <div class="form-group">
            <label for="lon">经度:</label>
            <input type="number" id="lon" value="108.3667" step="0.0001">
        </div>
        
        <button onclick="queryPoint()">查询</button>
        
        <h3>结果:</h3>
        <pre id="query-result">点击"查询"按钮获取结果...</pre>
        
        <h3>查询URL:</h3>
        <pre id="query-url"></pre>
    </div>
    
    <div id="layers-tab" class="tab-content">
        <h2>获取图层列表</h2>
        <div class="form-group">
            <label for="layers-workspace">工作区:</label>
            <input type="text" id="layers-workspace" value="my_workspace">
        </div>
        
        <button onclick="getLayers()">获取图层列表</button>
        
        <h3>结果:</h3>
        <pre id="layers-result">点击"获取图层列表"按钮获取结果...</pre>
        
        <h3>查询URL:</h3>
        <pre id="layers-url"></pre>
    </div>
    
    <script>
        // 显示选中的标签页
        function showTab(tabId) {
            // 隐藏所有标签页
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有标签的活动状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabId).classList.add('active');
            
            // 设置选中标签的活动状态
            Array.from(document.querySelectorAll('.tab')).find(tab => 
                tab.getAttribute('onclick').includes(tabId)
            ).classList.add('active');
        }
        
        // API基础URL
        const baseUrl = "http://localhost:5000";
        
        // 坐标点查询
        function queryPoint() {
            const workspace = document.getElementById('workspace').value;
            const lat = parseFloat(document.getElementById('lat').value);
            const lon = parseFloat(document.getElementById('lon').value);
            
            if (!workspace || isNaN(lat) || isNaN(lon)) {
                document.getElementById('query-result').textContent = '错误: 请填写所有必要的字段，并确保经纬度是有效的数值';
                return;
            }
            
            document.getElementById('query-result').textContent = '正在查询...';
            
            // 构建查询URL
            const queryUrl = `${baseUrl}/api/query?lat=${lat}&lon=${lon}&workspace=${encodeURIComponent(workspace)}`;
            
            // 显示查询URL
            document.getElementById('query-url').textContent = queryUrl;
            
            // 发送GET请求
            fetch(queryUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                document.getElementById('query-result').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('query-result').textContent = `错误: ${error.message}\n\n可能原因:\n1. API服务未运行\n2. CORS策略限制\n3. 网络连接问题`;
            });
        }
        
        // 获取图层列表
        function getLayers() {
            const workspace = document.getElementById('layers-workspace').value;
            
            if (!workspace) {
                document.getElementById('layers-result').textContent = '错误: 请填写工作区名称';
                return;
            }
            
            document.getElementById('layers-result').textContent = '正在获取图层列表...';
            
            // 构建查询URL
            const layersUrl = `${baseUrl}/api/layers?workspace=${encodeURIComponent(workspace)}`;
            
            // 显示查询URL
            document.getElementById('layers-url').textContent = layersUrl;
            
            // 发送GET请求
            fetch(layersUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                document.getElementById('layers-result').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('layers-result').textContent = `错误: ${error.message}\n\n可能原因:\n1. API服务未运行\n2. CORS策略限制\n3. 网络连接问题`;
            });
        }
    </script>
</body>
</html> 