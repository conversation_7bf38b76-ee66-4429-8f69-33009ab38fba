#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试图层查询问题的脚本
"""

import sys
import os
import json
import requests
from requests.auth import HTTPBasicAuth

# 添加项目路径
sys.path.insert(0, os.path.abspath('.'))

# 导入配置和核心模块
from config import GEOSERVER_URL, GEOSERVER_USER, GEOSERVER_PASSWORD
from geoserverRest.core.manager import GeoServerManager
from geoserverRest.core.raster_query import GeoServerRasterQuery

def test_geoserver_connection():
    """测试GeoServer连接"""
    print("=== 测试GeoServer连接 ===")
    print(f"GeoServer URL: {GEOSERVER_URL}")
    print(f"用户名: {GEOSERVER_USER}")
    
    try:
        # 测试基本连接
        auth = HTTPBasicAuth(GEOSERVER_USER, GEOSERVER_PASSWORD)
        response = requests.get(f"{GEOSERVER_URL}/rest/workspaces.json", auth=auth, timeout=10)
        
        print(f"连接状态码: {response.status_code}")
        if response.status_code == 200:
            workspaces = response.json()
            print("✅ GeoServer连接成功")
            print(f"工作空间数量: {len(workspaces.get('workspaces', {}).get('workspace', []))}")
            return True
        else:
            print(f"❌ GeoServer连接失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 连接异常: {str(e)}")
        return False

def test_workspace_exists(workspace_name):
    """测试工作空间是否存在"""
    print(f"\n=== 测试工作空间 '{workspace_name}' ===")
    
    try:
        auth = HTTPBasicAuth(GEOSERVER_USER, GEOSERVER_PASSWORD)
        response = requests.get(f"{GEOSERVER_URL}/rest/workspaces/{workspace_name}.json", auth=auth, timeout=10)
        
        print(f"工作空间查询状态码: {response.status_code}")
        if response.status_code == 200:
            workspace_info = response.json()
            print("✅ 工作空间存在")
            print(f"工作空间信息: {json.dumps(workspace_info, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 工作空间不存在或无法访问: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 查询工作空间异常: {str(e)}")
        return False

def test_layer_query(workspace_name, layer_name):
    """测试图层查询"""
    print(f"\n=== 测试图层查询 '{workspace_name}:{layer_name}' ===")
    
    try:
        # 使用API查询图层
        manager = GeoServerManager()
        query = GeoServerRasterQuery(manager)
        
        print("1. 查询栅格图层...")
        raster_layers = query.get_raster_layers(workspace_name)
        print(f"栅格图层数量: {len(raster_layers)}")
        
        raster_layer_names = [layer['name'] for layer in raster_layers]
        print(f"栅格图层名称: {raster_layer_names}")
        
        if layer_name in raster_layer_names:
            print(f"✅ 在栅格图层中找到 '{layer_name}'")
            target_layer = next(layer for layer in raster_layers if layer['name'] == layer_name)
            print(f"图层详情: {json.dumps(target_layer, indent=2, ensure_ascii=False)}")
            return target_layer
        
        print("2. 查询矢量图层...")
        vector_layers = query.get_vector_layers(workspace_name)
        print(f"矢量图层数量: {len(vector_layers)}")
        
        vector_layer_names = [layer['name'] for layer in vector_layers]
        print(f"矢量图层名称: {vector_layer_names}")
        
        if layer_name in vector_layer_names:
            print(f"✅ 在矢量图层中找到 '{layer_name}'")
            target_layer = next(layer for layer in vector_layers if layer['name'] == layer_name)
            print(f"图层详情: {json.dumps(target_layer, indent=2, ensure_ascii=False)}")
            return target_layer
        
        print(f"❌ 在工作空间 '{workspace_name}' 中未找到图层 '{layer_name}'")
        
        # 显示所有图层
        all_layers = raster_layers + vector_layers
        print(f"\n所有图层 ({len(all_layers)} 个):")
        for i, layer in enumerate(all_layers, 1):
            print(f"  {i}. {layer['name']} (类型: {'栅格' if layer in raster_layers else '矢量'})")
        
        return None
        
    except Exception as e:
        print(f"❌ 查询图层异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_direct_geoserver_api(workspace_name, layer_name):
    """直接测试GeoServer REST API"""
    print(f"\n=== 直接测试GeoServer REST API ===")
    
    auth = HTTPBasicAuth(GEOSERVER_USER, GEOSERVER_PASSWORD)
    
    # 测试栅格图层
    print("1. 查询栅格存储...")
    try:
        url = f"{GEOSERVER_URL}/rest/workspaces/{workspace_name}/coveragestores.json"
        response = requests.get(url, auth=auth, timeout=10)
        print(f"栅格存储查询状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"栅格存储响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"栅格存储查询失败: {response.text}")
    except Exception as e:
        print(f"栅格存储查询异常: {str(e)}")
    
    # 测试矢量图层
    print("\n2. 查询数据存储...")
    try:
        url = f"{GEOSERVER_URL}/rest/workspaces/{workspace_name}/datastores.json"
        response = requests.get(url, auth=auth, timeout=10)
        print(f"数据存储查询状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"数据存储响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"数据存储查询失败: {response.text}")
    except Exception as e:
        print(f"数据存储查询异常: {str(e)}")

def main():
    """主函数"""
    workspace_name = "testodm"
    layer_name = "20250705171602"
    
    print("🔍 GeoServer图层查询诊断工具")
    print("=" * 50)
    
    # 1. 测试GeoServer连接
    if not test_geoserver_connection():
        print("\n❌ GeoServer连接失败，请检查配置")
        return
    
    # 2. 测试工作空间
    if not test_workspace_exists(workspace_name):
        print(f"\n❌ 工作空间 '{workspace_name}' 不存在")
        return
    
    # 3. 测试图层查询
    layer = test_layer_query(workspace_name, layer_name)
    
    # 4. 直接测试GeoServer API
    test_direct_geoserver_api(workspace_name, layer_name)
    
    print("\n" + "=" * 50)
    if layer:
        print("✅ 诊断完成：图层查询成功")
    else:
        print("❌ 诊断完成：图层查询失败")

if __name__ == '__main__':
    main()
