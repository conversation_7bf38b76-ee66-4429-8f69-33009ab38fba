2025-07-28 09:19:16,782 - INFO - ============ TIF处理任务 39c0179c-a598-430b-b16d-1f43fad72ea4 开始执行 ============
2025-07-28 09:19:16,783 - INFO - 开始时间: 2025-07-28 09:19:16
2025-07-28 09:19:16,784 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 09:19:16,795 - INFO - 系统信息:
2025-07-28 09:19:16,795 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 09:19:16,798 - INFO -   Python版本: 3.8.20
2025-07-28 09:19:16,798 - INFO -   GDAL版本: 3.9.2
2025-07-28 09:19:16,801 - INFO -   GPU可用: 否
2025-07-28 09:19:16,802 - INFO - 检查参数有效性...
2025-07-28 09:19:16,804 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:19:16,807 - INFO - 开始执行TIF处理流程...
2025-07-28 09:19:16,808 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:19:16,809 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-07-28 09:19:16,811 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-07-28 09:19:21,191 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17007, 高=20364, 波段数=4开始读取影像数据，大小约: 5284.58 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (09:19:20)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 09:19:21,248 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 09:19:21,270 - ERROR - [A
2025-07-28 09:19:23,162 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:01<00:36,  1.84s/it]
2025-07-28 09:19:23,163 - ERROR - [A
2025-07-28 09:19:23,167 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:01<00:00, 11.39it/s]
2025-07-28 09:19:23,168 - INFO - 波段 2/3 处理完成已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 09:19:23,169 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 09:19:23,170 - ERROR - [A
2025-07-28 09:19:23,248 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 271.24it/s]
2025-07-28 09:19:25,154 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 4.20 秒 (09:19:25)预计总处理时间: 约 12.61 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif (09:19:25)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif使用已处理过的影像信息影像信息: 宽=17007, 高=20364, 波段数=3掩码形状: (20364, 17007), 数据类型: uint8获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 尺寸: 17007x20364x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 09:19:25,156 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 09:19:25,161 - ERROR - [A
2025-07-28 09:19:37,892 - INFO - 波段 3 所有像素值有效波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=42.58, std=63.34波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 09:19:37,903 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:19:37,904 - ERROR - [A[A
2025-07-28 09:19:38,213 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:06,  3.25it/s]
2025-07-28 09:19:38,214 - ERROR - [A[A
2025-07-28 09:19:38,364 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:04,  4.64it/s]
2025-07-28 09:19:38,364 - ERROR - [A[A
2025-07-28 09:19:38,535 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:03,  5.13it/s]
2025-07-28 09:19:38,535 - ERROR - [A[A
2025-07-28 09:19:38,641 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  6.26it/s]
2025-07-28 09:19:38,642 - ERROR - [A[A
2025-07-28 09:19:38,867 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:02,  7.40it/s]
2025-07-28 09:19:38,868 - ERROR - [A[A
2025-07-28 09:19:39,040 - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  6.88it/s]
2025-07-28 09:19:39,043 - ERROR - [A[A
2025-07-28 09:19:39,194 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:01,  6.77it/s]
2025-07-28 09:19:39,194 - ERROR - [A[A
2025-07-28 09:19:39,305 - INFO - 应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 09:19:39,307 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:19:39,310 - ERROR - [A[A[A
2025-07-28 09:19:39,359 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  6.53it/s]
2025-07-28 09:19:39,361 - ERROR - [A[A
2025-07-28 09:19:39,477 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  6.99it/s]
2025-07-28 09:19:39,478 - ERROR - [A[A
2025-07-28 09:19:39,606 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:01,  7.20it/s]
2025-07-28 09:19:39,607 - ERROR - [A[A
2025-07-28 09:19:39,726 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:01<00:01,  7.50it/s]
2025-07-28 09:19:39,727 - ERROR - [A[A
2025-07-28 09:19:39,869 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:01,  7.37it/s]
2025-07-28 09:19:39,870 - ERROR - [A[A
2025-07-28 09:19:40,096 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:01,  6.14it/s]
2025-07-28 09:19:40,096 - ERROR - [A[A
2025-07-28 09:19:40,231 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:02<00:00,  6.46it/s]
2025-07-28 09:19:40,232 - ERROR - [A[A
2025-07-28 09:19:40,370 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:02<00:00,  6.68it/s]
2025-07-28 09:19:40,371 - ERROR - [A[A
2025-07-28 09:19:40,507 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:01<00:23,  1.20s/it]
2025-07-28 09:19:40,508 - ERROR - [A[A[A
2025-07-28 09:19:40,523 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:02<00:00,  6.62it/s]
2025-07-28 09:19:40,524 - ERROR - [A[A
2025-07-28 09:19:40,655 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:02<00:00,  6.89it/s]
2025-07-28 09:19:40,655 - ERROR - [A[A
2025-07-28 09:19:40,802 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:02<00:00,  6.85it/s]
2025-07-28 09:19:40,804 - ERROR - [A[A
2025-07-28 09:19:40,924 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:03<00:00,  7.21it/s]
2025-07-28 09:19:40,925 - ERROR - [A[A
2025-07-28 09:19:40,961 - ERROR - [A[A
2025-07-28 09:19:42,247 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:02<00:28,  1.52s/it]
2025-07-28 09:19:42,247 - ERROR - [A[A[A
2025-07-28 09:19:44,284 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:04<00:31,  1.75s/it]
2025-07-28 09:19:44,285 - ERROR - [A[A[A
2025-07-28 09:19:45,177 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:05<00:24,  1.41s/it]
2025-07-28 09:19:45,178 - ERROR - [A[A[A
2025-07-28 09:19:48,384 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:09<00:32,  2.06s/it]
2025-07-28 09:19:48,385 - ERROR - [A[A[A
2025-07-28 09:19:53,689 - ERROR - 
波段 3/3 写入进度:  29%|##################2                                             | 6/21 [00:14<00:47,  3.16s/it]
2025-07-28 09:19:53,690 - ERROR - [A[A[A
2025-07-28 09:19:58,280 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:18<00:50,  3.63s/it]
2025-07-28 09:19:58,281 - ERROR - [A[A[A
2025-07-28 09:20:05,728 - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:26<01:02,  4.85s/it]
2025-07-28 09:20:05,729 - ERROR - [A[A[A
2025-07-28 09:20:09,625 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:30<00:54,  4.55s/it]
2025-07-28 09:20:09,625 - ERROR - [A[A[A
2025-07-28 09:20:17,330 - ERROR - 
波段 3/3 写入进度:  48%|##############################                                 | 10/21 [00:38<01:00,  5.52s/it]
2025-07-28 09:20:17,330 - ERROR - [A[A[A
2025-07-28 09:20:26,047 - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:46<01:05,  6.50s/it]
2025-07-28 09:20:26,048 - ERROR - [A[A[A
2025-07-28 09:20:33,868 - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 12/21 [00:54<01:02,  6.90s/it]
2025-07-28 09:20:33,868 - ERROR - [A[A[A
2025-07-28 09:20:40,920 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:15<02:31, 75.75s/it]
2025-07-28 09:20:40,921 - ERROR - [A
2025-07-28 09:20:41,441 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [01:02<00:56,  7.11s/it]
2025-07-28 09:20:41,441 - ERROR - [A[A[A
2025-07-28 09:20:48,973 - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [01:09<00:50,  7.23s/it]
2025-07-28 09:20:48,977 - ERROR - [A[A[A
2025-07-28 09:21:03,961 - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 15/21 [01:24<00:57,  9.56s/it]
2025-07-28 09:21:03,986 - ERROR - [A[A[A
2025-07-28 09:21:27,439 - ERROR - 
波段 3/3 写入进度:  76%|################################################               | 16/21 [01:48<01:08, 13.76s/it]
2025-07-28 09:21:27,441 - ERROR - [A[A[A
2025-07-28 09:21:27,450 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=45.79, std=65.41应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 09:21:27,451 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:21:27,452 - ERROR - [A[A
2025-07-28 09:21:32,402 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:04<01:39,  4.95s/it]
2025-07-28 09:21:32,403 - ERROR - [A[A
2025-07-28 09:21:50,082 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:22<03:56, 12.44s/it]
2025-07-28 09:21:50,082 - ERROR - [A[A
2025-07-28 09:21:55,925 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:28<02:49,  9.43s/it]
2025-07-28 09:21:55,926 - ERROR - [A[A
2025-07-28 09:22:07,209 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:39<02:52, 10.16s/it]
2025-07-28 09:22:07,210 - ERROR - [A[A
2025-07-28 09:22:12,617 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:45<02:15,  8.45s/it]
2025-07-28 09:22:12,617 - ERROR - [A[A
2025-07-28 09:23:02,102 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [01:34<05:35, 22.40s/it]
2025-07-28 09:23:02,103 - ERROR - [A[A
2025-07-28 09:23:02,164 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [03:22<02:32, 38.11s/it]
2025-07-28 09:23:02,167 - ERROR - [A[A[A
2025-07-28 09:23:34,047 - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 18/21 [03:54<01:48, 36.24s/it]
2025-07-28 09:23:34,048 - ERROR - [A[A[A
2025-07-28 09:23:34,233 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [02:06<05:58, 25.58s/it]
2025-07-28 09:23:34,236 - ERROR - [A[A
2025-07-28 09:23:46,631 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [02:19<04:37, 21.38s/it]
2025-07-28 09:23:46,633 - ERROR - [A[A
2025-07-28 09:24:07,314 - ERROR - 
波段 3/3 写入进度:  90%|#########################################################      | 19/21 [04:28<01:10, 35.34s/it]
2025-07-28 09:24:07,315 - ERROR - [A[A[A
2025-07-28 09:24:12,244 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [02:44<04:32, 22.71s/it]
2025-07-28 09:24:12,245 - ERROR - [A[A
2025-07-28 09:25:13,542 - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [05:34<00:44, 44.62s/it]
2025-07-28 09:25:13,543 - ERROR - [A[A[A
2025-07-28 09:25:14,159 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [03:46<06:22, 34.81s/it]
2025-07-28 09:25:14,159 - ERROR - [A[A
2025-07-28 09:25:22,248 - ERROR - 
波段 3/3 写入进度: 100%|###############################################################| 21/21 [05:42<00:00, 33.84s/it]
2025-07-28 09:25:22,248 - ERROR - [A[A[A
2025-07-28 09:25:22,250 - ERROR - [A[A[A
2025-07-28 09:25:26,610 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [03:59<04:39, 27.97s/it]
2025-07-28 09:25:26,611 - ERROR - [A[A
2025-07-28 09:25:35,084 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [04:07<03:18, 22.04s/it]
2025-07-28 09:25:35,084 - ERROR - [A[A
2025-07-28 09:25:38,717 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [04:11<02:11, 16.46s/it]
2025-07-28 09:25:38,718 - ERROR - [A[A
2025-07-28 09:25:46,950 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [04:19<01:37, 13.98s/it]
2025-07-28 09:25:46,951 - ERROR - [A[A
2025-07-28 09:25:51,437 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [04:23<01:06, 11.12s/it]
2025-07-28 09:25:51,438 - ERROR - [A[A
2025-07-28 09:26:25,804 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [04:58<01:30, 18.11s/it]
2025-07-28 09:26:25,804 - ERROR - [A[A
2025-07-28 09:26:36,863 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [05:09<01:03, 15.99s/it]
2025-07-28 09:26:36,863 - ERROR - [A[A
2025-07-28 09:26:49,307 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [05:21<00:44, 14.92s/it]
2025-07-28 09:26:49,318 - ERROR - [A[A
2025-07-28 09:26:55,384 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [05:27<00:24, 12.27s/it]
2025-07-28 09:26:55,385 - ERROR - [A[A
2025-07-28 09:27:00,051 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [05:32<00:09,  9.99s/it]
2025-07-28 09:27:00,068 - ERROR - [A[A
2025-07-28 09:27:00,870 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [05:33<00:00,  7.23s/it]
2025-07-28 09:27:00,871 - ERROR - [A[A
2025-07-28 09:27:00,873 - ERROR - [A[A
2025-07-28 09:28:14,851 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [08:49<04:58, 298.21s/it]
2025-07-28 09:28:14,852 - ERROR - [A
2025-07-28 09:28:29,192 - INFO - 设置波段 3 的NoData值为 -9999设置波段 2 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 724.49 秒 (09:27:39)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (09:27:39)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=35.76, std=51.77应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 09:28:29,224 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:28:30,111 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:17,  1.13it/s]
2025-07-28 09:28:32,646 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:03<00:35,  1.86s/it]
2025-07-28 09:28:34,494 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:05<00:33,  1.85s/it]
2025-07-28 09:28:36,412 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:07<00:31,  1.88s/it]
2025-07-28 09:28:37,233 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:08<00:23,  1.50s/it]
2025-07-28 09:28:38,366 - ERROR - 
波段 3/3 写入进度:  29%|##################2                                             | 6/21 [00:09<00:20,  1.37s/it]
2025-07-28 09:28:41,245 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:12<00:26,  1.87s/it]
2025-07-28 09:28:42,483 - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:13<00:21,  1.67s/it]
2025-07-28 09:28:43,474 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:14<00:17,  1.45s/it]
2025-07-28 09:28:45,422 - ERROR - 
波段 3/3 写入进度:  48%|##############################                                 | 10/21 [00:16<00:17,  1.61s/it]
2025-07-28 09:28:46,364 - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:17<00:14,  1.40s/it]
2025-07-28 09:28:50,616 - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 12/21 [00:21<00:20,  2.27s/it]
2025-07-28 09:29:00,093 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:30<00:35,  4.45s/it]
2025-07-28 09:29:17,884 - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:48<00:59,  8.46s/it]
2025-07-28 09:29:38,264 - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 15/21 [01:09<01:12, 12.07s/it]
2025-07-28 09:29:45,251 - INFO - 获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-28 09:29:45,258 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-28 09:29:45,299 - ERROR - [A[A
2025-07-28 09:29:46,089 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.27it/s]
2025-07-28 09:29:46,090 - ERROR - [A[A
2025-07-28 09:29:46,092 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.26it/s]
2025-07-28 09:29:46,542 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-28 09:29:49,158 - ERROR - 
波段 3/3 写入进度:  76%|################################################               | 16/21 [01:19<00:58, 11.72s/it]
2025-07-28 09:29:59,826 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-28 09:29:59,826 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-28 09:29:59,829 - ERROR - [A[A
2025-07-28 09:30:00,020 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:02, 10.55it/s]
2025-07-28 09:30:00,020 - ERROR - [A[A
2025-07-28 09:30:00,822 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [01:31<00:46, 11.70s/it]
2025-07-28 09:30:00,943 - ERROR - 
处理轮廓:  13%|#########4                                                               | 4/31 [00:01<00:08,  3.22it/s]
2025-07-28 09:30:00,947 - ERROR - [A[A
2025-07-28 09:30:01,005 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 26.39it/s]
2025-07-28 09:30:01,777 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-28 09:30:01,778 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-28 09:30:01,786 - ERROR - [A[A
2025-07-28 09:30:01,801 - ERROR - 
写入多边形: 100%|####################################################################| 35/35 [00:00<00:00, 2500.27it/s]
2025-07-28 09:30:02,452 - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 18/21 [01:33<00:26,  8.68s/it]
2025-07-28 09:30:03,125 - ERROR - 
波段 3/3 写入进度:  90%|#########################################################      | 19/21 [01:33<00:12,  6.27s/it]
2025-07-28 09:30:03,354 - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [01:34<00:04,  4.46s/it]
2025-07-28 09:31:26,560 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [12:01<00:00, 249.58s/it]
2025-07-28 09:31:26,563 - ERROR - [A
2025-07-28 09:31:26,564 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [12:01<00:00, 240.47s/it]
2025-07-28 09:32:02,433 - INFO - 处理完成，耗时: 765.62 秒 (12.76 分钟)
2025-07-28 09:32:02,434 - INFO - 处理结果: 成功
2025-07-28 09:32:02,437 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 大小: 740.84 MB
2025-07-28 09:32:02,438 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp, 大小: 22.81 KB
2025-07-28 09:32:02,441 - INFO - TIF处理任务 39c0179c-a598-430b-b16d-1f43fad72ea4 执行成功
2025-07-28 09:32:02,442 - INFO - 完成时间: 2025-07-28 09:32:02
2025-07-28 09:32:02,443 - INFO - 状态: 运行成功
2025-07-28 09:32:02,443 - INFO - ============ 任务执行结束 ============
