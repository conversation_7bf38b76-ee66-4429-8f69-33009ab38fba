2025-07-24 15:18:42,109 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250724_151842.log
2025-07-24 15:18:42,138 - geo_publisher - INFO - 加载了 19 个任务状态
2025-07-24 15:18:42,232 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-24 15:18:42,233 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-24 15:18:42,352 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-24 15:18:42,361 - root - INFO - === GeoServer REST API服务 ===
2025-07-24 15:18:42,362 - root - INFO - 主机: 0.0.0.0
2025-07-24 15:18:42,362 - root - INFO - 端口: 5083
2025-07-24 15:18:42,364 - root - INFO - 调试模式: 禁用
2025-07-24 15:18:42,365 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-24 15:18:42,413 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-24 15:18:42,415 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-24 15:19:08,407 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 15:19:08,413 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 15:19:08,415 - tif_api - INFO - 输入文件大小: 222.48 MB
2025-07-24 15:19:08,418 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 15:19:08,420 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 15:19:08,422 - tif_api - INFO - 黑色阈值: 0
2025-07-24 15:19:08,423 - tif_api - INFO - 白色阈值: 255
2025-07-24 15:19:08,424 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 15:19:08,563 - tif_executor - INFO - 启动TIF处理任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 15:19:08,564 - tif_api - INFO - 异步任务启动成功，任务ID: 7869ab0c-addd-47d1-b52b-5dcc70ba31fa
2025-07-24 15:19:08,568 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:19:08] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 15:19:08,594 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 15:19:08,600 - tif_api - INFO - 查询任务: 7869ab0c-addd-47d1-b52b-5dcc70ba31fa
2025-07-24 15:19:09,135 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - ============ TIF处理任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 开始执行 ============
2025-07-24 15:19:09,142 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 开始时间: 2025-07-24 15:19:08
2025-07-24 15:19:09,136 - tif_api - INFO - 任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 状态查询成功，当前状态: 正在运行
2025-07-24 15:19:09,146 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 15:19:09,147 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:19:09] "GET /api/tif/status?task_id=7869ab0c-addd-47d1-b52b-5dcc70ba31fa HTTP/1.1" 200 -
2025-07-24 15:19:09,148 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 系统信息:
2025-07-24 15:19:09,152 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO -   操作系统: Windows 10.0.19045
2025-07-24 15:19:09,331 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO -   Python版本: 3.8.20
2025-07-24 15:19:09,338 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO -   GDAL版本: 3.9.2
2025-07-24 15:19:09,341 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO -   GPU可用: 否
2025-07-24 15:19:09,342 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 检查参数有效性...
2025-07-24 15:19:09,343 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 15:19:09,343 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 开始执行TIF处理流程...
2025-07-24 15:19:09,347 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 15:19:09,354 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 15:19:09,358 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 15:19:09,364 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-24 15:20:09,295 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 15:20:09,301 - tif_api - INFO - 查询任务: 7869ab0c-addd-47d1-b52b-5dcc70ba31fa
2025-07-24 15:20:09,308 - tif_api - INFO - 任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 状态查询成功，当前状态: 正在运行
2025-07-24 15:20:09,310 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:20:09] "GET /api/tif/status?task_id=7869ab0c-addd-47d1-b52b-5dcc70ba31fa HTTP/1.1" 200 -
2025-07-24 15:20:29,063 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17007, 高=20364, 波段数=4开始读取影像数据，大小约: 5284.58 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (15:20:28)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-24 15:20:29,069 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-24 15:20:29,839 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:15,  1.33it/s]
2025-07-24 15:20:32,008 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:02<00:04,  2.87it/s]
2025-07-24 15:20:32,307 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理数据块:  43%|##############################4                                        | 9/21 [00:03<00:04,  2.90it/s]
2025-07-24 15:20:32,311 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:03<00:00,  6.49it/s]
2025-07-24 15:20:32,330 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-24 15:20:32,332 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-24 15:20:32,410 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 271.26it/s]
2025-07-24 15:20:43,792 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 4.90 秒 (15:20:33)预计总处理时间: 约 14.71 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif (15:20:33)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif使用已处理过的影像信息影像信息: 宽=17007, 高=20364, 波段数=3掩码形状: (20364, 17007), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, 尺寸: 17007x20364x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-24 15:20:43,795 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-24 15:20:58,824 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=42.58, std=63.34应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-24 15:20:58,856 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 15:20:58,863 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:20:59,053 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  5.32it/s]
2025-07-24 15:20:59,054 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:20:59,187 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:01, 13.87it/s]
2025-07-24 15:20:59,196 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:20:59,499 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:01,  9.22it/s]
2025-07-24 15:20:59,500 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:00,082 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:02,  5.60it/s]
2025-07-24 15:21:00,083 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:00,940 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:02<00:03,  3.08it/s]
2025-07-24 15:21:00,941 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:01,350 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:02<00:03,  2.90it/s]
2025-07-24 15:21:01,351 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:01,696 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:02<00:03,  2.90it/s]
2025-07-24 15:21:01,698 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:02,028 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:03<00:03,  2.93it/s]
2025-07-24 15:21:02,029 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:02,353 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:03<00:02,  2.97it/s]
2025-07-24 15:21:02,354 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:03,003 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:04<00:02,  2.35it/s]
2025-07-24 15:21:03,004 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:03,317 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:04<00:02,  2.54it/s]
2025-07-24 15:21:03,318 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:03,678 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:04<00:01,  2.61it/s]
2025-07-24 15:21:03,682 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:03,980 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:05<00:01,  2.78it/s]
2025-07-24 15:21:03,981 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:04,274 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:05<00:01,  2.94it/s]
2025-07-24 15:21:04,282 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:04,622 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:05<00:00,  2.92it/s]
2025-07-24 15:21:04,622 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:04,832 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:05<00:00,  3.30it/s]
2025-07-24 15:21:04,833 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:04,868 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:21:09,589 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 15:21:09,608 - tif_api - INFO - 查询任务: 7869ab0c-addd-47d1-b52b-5dcc70ba31fa
2025-07-24 15:21:09,626 - tif_api - INFO - 任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 状态查询成功，当前状态: 正在运行
2025-07-24 15:21:09,645 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:21:09] "GET /api/tif/status?task_id=7869ab0c-addd-47d1-b52b-5dcc70ba31fa HTTP/1.1" 200 -
2025-07-24 15:21:57,168 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:13<02:26, 73.36s/it]
2025-07-24 15:22:06,428 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=45.79, std=65.41应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-24 15:22:06,429 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 15:22:06,430 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:09,668 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 15:22:09,669 - tif_api - INFO - 查询任务: 7869ab0c-addd-47d1-b52b-5dcc70ba31fa
2025-07-24 15:22:10,186 - tif_api - INFO - 任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 状态查询成功，当前状态: 正在运行
2025-07-24 15:22:10,188 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:22:10] "GET /api/tif/status?task_id=7869ab0c-addd-47d1-b52b-5dcc70ba31fa HTTP/1.1" 200 -
2025-07-24 15:22:13,891 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:07<02:29,  7.46s/it]
2025-07-24 15:22:13,891 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:22,917 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:16<02:39,  8.38s/it]
2025-07-24 15:22:22,918 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:31,741 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:25<02:34,  8.58s/it]
2025-07-24 15:22:31,741 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:32,165 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:25<01:31,  5.36s/it]
2025-07-24 15:22:32,168 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:35,547 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:29<01:14,  4.65s/it]
2025-07-24 15:22:35,548 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:38,239 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:31<00:59,  3.98s/it]
2025-07-24 15:22:38,240 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:39,687 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:33<00:44,  3.15s/it]
2025-07-24 15:22:39,689 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:40,733 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:34<00:32,  2.48s/it]
2025-07-24 15:22:40,733 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:43,368 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:36<00:30,  2.53s/it]
2025-07-24 15:22:43,370 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:46,027 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:39<00:28,  2.57s/it]
2025-07-24 15:22:46,027 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:48,249 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:41<00:24,  2.46s/it]
2025-07-24 15:22:48,250 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:51,224 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:44<00:23,  2.62s/it]
2025-07-24 15:22:51,224 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:54,136 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:47<00:21,  2.71s/it]
2025-07-24 15:22:54,137 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:54,303 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:47<00:13,  1.94s/it]
2025-07-24 15:22:54,304 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:54,457 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:48<00:08,  1.40s/it]
2025-07-24 15:22:54,457 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:22:54,622 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:48<00:05,  1.03s/it]
2025-07-24 15:22:54,623 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:23:04,974 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:58<00:15,  3.83s/it]
2025-07-24 15:23:04,974 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:23:11,594 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 15:23:11,595 - tif_api - INFO - 查询任务: 7869ab0c-addd-47d1-b52b-5dcc70ba31fa
2025-07-24 15:23:11,856 - tif_api - INFO - 任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 状态查询成功，当前状态: 正在运行
2025-07-24 15:23:11,859 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:23:11] "GET /api/tif/status?task_id=7869ab0c-addd-47d1-b52b-5dcc70ba31fa HTTP/1.1" 200 -
2025-07-24 15:23:26,615 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [01:20<00:27,  9.18s/it]
2025-07-24 15:23:26,616 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:23:35,100 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [01:28<00:17,  8.97s/it]
2025-07-24 15:23:35,100 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:23:44,751 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [01:38<00:09,  9.18s/it]
2025-07-24 15:23:44,751 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:23:44,757 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:24:11,879 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 15:24:11,880 - tif_api - INFO - 查询任务: 7869ab0c-addd-47d1-b52b-5dcc70ba31fa
2025-07-24 15:24:12,169 - tif_api - INFO - 任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 状态查询成功，当前状态: 正在运行
2025-07-24 15:24:12,171 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:24:12] "GET /api/tif/status?task_id=7869ab0c-addd-47d1-b52b-5dcc70ba31fa HTTP/1.1" 200 -
2025-07-24 15:25:12,191 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 15:25:12,193 - tif_api - INFO - 查询任务: 7869ab0c-addd-47d1-b52b-5dcc70ba31fa
2025-07-24 15:25:12,197 - tif_api - INFO - 任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 状态查询成功，当前状态: 正在运行
2025-07-24 15:25:12,203 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:25:12] "GET /api/tif/status?task_id=7869ab0c-addd-47d1-b52b-5dcc70ba31fa HTTP/1.1" 200 -
2025-07-24 15:25:20,733 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [04:36<02:29, 149.95s/it]
2025-07-24 15:25:28,297 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=35.76, std=51.77应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-24 15:25:28,298 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 15:25:28,298 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:25:28,953 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:13,  1.53it/s]
2025-07-24 15:25:28,954 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:25:29,202 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:07,  2.40it/s]
2025-07-24 15:25:29,203 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:25:30,283 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:12,  1.39it/s]
2025-07-24 15:25:30,283 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:25:31,747 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:03<00:17,  1.01s/it]
2025-07-24 15:25:31,748 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:25:31,849 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:03<00:05,  2.51it/s]
2025-07-24 15:25:31,849 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:25:31,960 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 12/21 [00:03<00:01,  5.79it/s]
2025-07-24 15:25:31,960 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:25:32,063 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
波段 3/3 写入进度:  76%|################################################               | 16/21 [00:03<00:00,  8.86it/s]
2025-07-24 15:25:32,063 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:25:32,143 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - [A
2025-07-24 15:26:12,219 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 15:26:12,220 - tif_api - INFO - 查询任务: 7869ab0c-addd-47d1-b52b-5dcc70ba31fa
2025-07-24 15:26:12,223 - tif_api - INFO - 任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 状态查询成功，当前状态: 正在运行
2025-07-24 15:26:12,224 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:26:12] "GET /api/tif/status?task_id=7869ab0c-addd-47d1-b52b-5dcc70ba31fa HTTP/1.1" 200 -
2025-07-24 15:27:02,775 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [06:18<00:00, 128.08s/it]
2025-07-24 15:27:02,778 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [06:18<00:00, 126.32s/it]
2025-07-24 15:27:12,241 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 15:27:12,245 - tif_api - INFO - 查询任务: 7869ab0c-addd-47d1-b52b-5dcc70ba31fa
2025-07-24 15:27:12,591 - tif_api - INFO - 任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 状态查询成功，当前状态: 正在运行
2025-07-24 15:27:12,594 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:27:12] "GET /api/tif/status?task_id=7869ab0c-addd-47d1-b52b-5dcc70ba31fa HTTP/1.1" 200 -
2025-07-24 15:27:36,738 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif影像保存完成，耗时: 389.51 秒 (15:27:03)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp (15:27:03)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tifTIF文件信息: 宽=17007, 高=20364, 波段数=3开始读取影像数据，大小约: 3963.44 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif提取TIF文件信息...TIF文件信息: 宽=17007, 高=20364, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 218929698 个无效像素检查波段 2...波段 2 检测到 218929698 个无效像素检查波段 3...波段 3 检测到 218929698 个无效像素掩码统计: 总像素数 346330548, 有效像素数 127400850 (36.79%), 无效像素数 218929698 (63.21%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 1 个初始轮廓轮廓面积统计: 最小=127380512.50, 最大=127380512.50, 平均=127380512.50, 总数=1将 1 个轮廓转换为地理坐标...
2025-07-24 15:27:36,763 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-24 15:27:37,260 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.02it/s]
2025-07-24 15:27:37,261 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.01it/s]
2025-07-24 15:27:37,408 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-24 15:27:47,699 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-24 15:27:47,699 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-24 15:27:48,573 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:12,  2.29it/s]
2025-07-24 15:27:48,736 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:09,  3.10it/s]
2025-07-24 15:27:48,785 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 28.57it/s]
2025-07-24 15:27:49,508 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 处理了 82263 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp写入shapefile特征...处理MultiPolygon，包含 19 个多边形
2025-07-24 15:27:49,509 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
写入多边形:   0%|                                                                               | 0/19 [00:00<?, ?it/s]
2025-07-24 15:27:49,669 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
写入多边形:  26%|##################6                                                    | 5/19 [00:00<00:00, 31.49it/s]
2025-07-24 15:27:49,674 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - ERROR - 
写入多边形: 100%|#####################################################################| 19/19 [00:00<00:00, 115.60it/s]
2025-07-24 15:27:49,842 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 处理完成，耗时: 520.48 秒 (8.67 分钟)
2025-07-24 15:27:49,843 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 处理结果: 成功
2025-07-24 15:27:49,844 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, 大小: 740.84 MB
2025-07-24 15:27:49,844 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp, 大小: 22.81 KB
2025-07-24 15:27:49,858 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - TIF处理任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 执行成功
2025-07-24 15:27:49,859 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 完成时间: 2025-07-24 15:27:49
2025-07-24 15:27:49,859 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - 状态: 运行成功
2025-07-24 15:27:49,860 - tif_task_7869ab0c-addd-47d1-b52b-5dcc70ba31fa - INFO - ============ 任务执行结束 ============
2025-07-24 15:28:12,612 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 15:28:12,614 - tif_api - INFO - 查询任务: 7869ab0c-addd-47d1-b52b-5dcc70ba31fa
2025-07-24 15:28:12,616 - tif_api - INFO - 任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 状态查询成功，当前状态: 运行成功
2025-07-24 15:28:12,620 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:28:12] "GET /api/tif/status?task_id=7869ab0c-addd-47d1-b52b-5dcc70ba31fa HTTP/1.1" 200 -
2025-07-24 15:28:14,044 - geo_publisher - INFO - 启动GeoTIFF发布任务 d61f6b3b-018a-4ebb-9152-d64b13a733e6: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-07-24 15:28:14,045 - geo_task_d61f6b3b-018a-4ebb-9152-d64b13a733e6 - INFO - GeoServer发布任务 d61f6b3b-018a-4ebb-9152-d64b13a733e6 开始执行
2025-07-24 15:28:14,045 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:28:14] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-24 15:28:14,046 - geo_task_d61f6b3b-018a-4ebb-9152-d64b13a733e6 - INFO - 开始时间: 2025-07-24 15:28:14
2025-07-24 15:28:14,047 - geo_task_d61f6b3b-018a-4ebb-9152-d64b13a733e6 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-24 15:28:14,048 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-24 15:28:14,048 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-24 15:28:14,062 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:28:14] "GET /api/geo/status?task_id=d61f6b3b-018a-4ebb-9152-d64b13a733e6 HTTP/1.1" 200 -
2025-07-24 15:28:15,485 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-24 15:28:15,486 - root - INFO - 未提供存储名称，使用文件名: 20250705171601
2025-07-24 15:28:15,487 - root - INFO - 未提供图层名称，使用存储名称: 20250705171601
2025-07-24 15:28:15,563 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601.tif
2025-07-24 15:28:32,140 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 到 'testodm:20250705171601'
2025-07-24 15:28:34,184 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171601
2025-07-24 15:28:34,184 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 成功发布为图层 'testodm:20250705171601'
2025-07-24 15:28:34,187 - geo_task_d61f6b3b-018a-4ebb-9152-d64b13a733e6 - INFO - GeoServer发布任务 d61f6b3b-018a-4ebb-9152-d64b13a733e6 执行成功
2025-07-24 15:28:34,199 - geo_task_d61f6b3b-018a-4ebb-9152-d64b13a733e6 - INFO - 完成时间: 2025-07-24 15:28:34
2025-07-24 15:28:34,200 - geo_task_d61f6b3b-018a-4ebb-9152-d64b13a733e6 - INFO - 状态: 发布成功
2025-07-24 15:29:14,075 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:29:14] "GET /api/geo/status?task_id=d61f6b3b-018a-4ebb-9152-d64b13a733e6 HTTP/1.1" 200 -
2025-07-24 16:11:55,469 - batch_executor - INFO - 启动任务 5a392606-e2d0-4ded-afe1-003f5a6bdeb4: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
2025-07-24 16:11:55,470 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:11:55] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-24 16:11:55,488 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:11:55] "GET /api/batch/status?task_id=5a392606-e2d0-4ded-afe1-003f5a6bdeb4 HTTP/1.1" 200 -
2025-07-24 16:12:57,139 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:12:57] "GET /api/batch/status?task_id=5a392606-e2d0-4ded-afe1-003f5a6bdeb4 HTTP/1.1" 200 -
2025-07-24 16:12:58,832 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 16:12:58,833 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:12:58,834 - tif_api - INFO - 输入文件大小: 237.45 MB
2025-07-24 16:12:58,835 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif
2025-07-24 16:12:58,835 - tif_api - INFO - 创建输出TIF目录: D:/Drone_Project/nginxData/ODM/Output/20250705171602
2025-07-24 16:12:58,938 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp
2025-07-24 16:12:58,939 - tif_api - INFO - 黑色阈值: 0
2025-07-24 16:12:58,939 - tif_api - INFO - 白色阈值: 255
2025-07-24 16:12:58,940 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 16:12:58,944 - tif_executor - INFO - 启动TIF处理任务 4acbd746-f075-45ba-b856-04ec2ebb15e0: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp
2025-07-24 16:12:58,945 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - ============ TIF处理任务 4acbd746-f075-45ba-b856-04ec2ebb15e0 开始执行 ============
2025-07-24 16:12:58,946 - tif_api - INFO - 异步任务启动成功，任务ID: 4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-24 16:12:58,949 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 开始时间: 2025-07-24 16:12:58
2025-07-24 16:12:58,950 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:12:58] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 16:12:58,951 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 16:12:58,953 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 系统信息:
2025-07-24 16:12:58,954 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO -   操作系统: Windows 10.0.19045
2025-07-24 16:12:58,955 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO -   Python版本: 3.8.20
2025-07-24 16:12:58,955 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO -   GDAL版本: 3.9.2
2025-07-24 16:12:58,957 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO -   GPU可用: 否
2025-07-24 16:12:58,958 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 检查参数有效性...
2025-07-24 16:12:58,959 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:12:58,960 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 开始执行TIF处理流程...
2025-07-24 16:12:58,960 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:12:58,961 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif
2025-07-24 16:12:58,962 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp
2025-07-24 16:12:58,971 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:12:58,972 - tif_api - INFO - 查询任务: 4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-24 16:12:58,974 - tif_api - INFO - 任务 4acbd746-f075-45ba-b856-04ec2ebb15e0 状态查询成功，当前状态: 正在运行
2025-07-24 16:12:58,975 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:12:58] "GET /api/tif/status?task_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-24 16:13:04,287 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (16:13:04)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-24 16:13:04,318 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-24 16:13:04,451 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:02,  7.78it/s]
2025-07-24 16:13:04,566 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理数据块:  29%|####################2                                                  | 6/21 [00:00<00:00, 27.79it/s]
2025-07-24 16:13:04,697 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理数据块:  43%|##############################4                                        | 9/21 [00:00<00:00, 25.45it/s]
2025-07-24 16:13:06,039 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理数据块:  57%|########################################                              | 12/21 [00:01<00:01,  5.14it/s]
2025-07-24 16:13:06,397 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理数据块:  67%|##############################################6                       | 14/21 [00:02<00:01,  5.26it/s]
2025-07-24 16:13:07,311 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理数据块:  81%|########################################################6             | 17/21 [00:02<00:00,  4.31it/s]
2025-07-24 16:13:08,063 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理数据块:  86%|############################################################          | 18/21 [00:03<00:00,  3.24it/s]
2025-07-24 16:13:08,107 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:03<00:00,  5.55it/s]
2025-07-24 16:13:08,108 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-24 16:13:08,109 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-24 16:13:08,146 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 583.43it/s]
2025-07-24 16:13:13,860 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 9.85 秒 (16:13:13)预计总处理时间: 约 29.55 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif (16:13:13)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-24 16:13:13,861 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-24 16:13:48,381 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-24 16:13:48,412 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:13:48,414 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:13:48,580 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:01, 12.08it/s]
2025-07-24 16:13:48,581 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:13:48,714 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:00, 17.55it/s]
2025-07-24 16:13:48,717 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:13:48,862 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:00<00:00, 18.78it/s]
2025-07-24 16:13:48,863 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:13:48,967 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 18.87it/s]
2025-07-24 16:13:48,968 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:13:49,074 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:00<00:00, 21.83it/s]
2025-07-24 16:13:49,074 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:13:49,174 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:00<00:00, 24.17it/s]
2025-07-24 16:13:49,175 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:13:49,288 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:00<00:00, 24.92it/s]
2025-07-24 16:13:49,289 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:13:49,349 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:00,679 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:14:00,684 - tif_api - INFO - 查询任务: 4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-24 16:14:00,692 - tif_api - INFO - 任务 4acbd746-f075-45ba-b856-04ec2ebb15e0 状态查询成功，当前状态: 正在运行
2025-07-24 16:14:00,706 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:14:00] "GET /api/tif/status?task_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-24 16:14:38,486 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:24<02:49, 84.62s/it]
2025-07-24 16:14:43,242 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-24 16:14:43,245 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:14:43,245 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:43,601 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:07,  2.83it/s]
2025-07-24 16:14:43,601 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:43,705 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:03,  4.82it/s]
2025-07-24 16:14:43,708 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:43,812 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  6.19it/s]
2025-07-24 16:14:43,813 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:43,918 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  7.17it/s]
2025-07-24 16:14:43,918 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:44,024 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:02,  7.85it/s]
2025-07-24 16:14:44,026 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:44,154 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:01,  7.79it/s]
2025-07-24 16:14:44,155 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:44,357 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  6.53it/s]
2025-07-24 16:14:44,358 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:44,491 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:01,  6.81it/s]
2025-07-24 16:14:44,493 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:44,664 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  6.45it/s]
2025-07-24 16:14:44,665 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:44,803 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  6.66it/s]
2025-07-24 16:14:44,804 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:44,953 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:01,  6.66it/s]
2025-07-24 16:14:44,954 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:45,178 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:01,  7.55it/s]
2025-07-24 16:14:45,178 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:45,297 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:00,  7.74it/s]
2025-07-24 16:14:45,298 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:46,779 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:03<00:01,  2.60it/s]
2025-07-24 16:14:46,779 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:49,268 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:06<00:03,  1.15it/s]
2025-07-24 16:14:49,269 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:52,866 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:09<00:04,  1.54s/it]
2025-07-24 16:14:52,867 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:14:57,411 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:14<00:04,  2.32s/it]
2025-07-24 16:14:57,411 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:15:02,346 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:19<00:03,  3.03s/it]
2025-07-24 16:15:02,347 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:15:02,641 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:15:02,643 - tif_api - INFO - 查询任务: 4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-24 16:15:02,646 - tif_api - INFO - 任务 4acbd746-f075-45ba-b856-04ec2ebb15e0 状态查询成功，当前状态: 正在运行
2025-07-24 16:15:02,648 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:15:02] "GET /api/tif/status?task_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-24 16:15:03,555 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:20<00:00,  2.52s/it]
2025-07-24 16:15:03,555 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:15:03,556 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:16:04,629 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:16:04,630 - tif_api - INFO - 查询任务: 4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-24 16:16:05,046 - tif_api - INFO - 任务 4acbd746-f075-45ba-b856-04ec2ebb15e0 状态查询成功，当前状态: 正在运行
2025-07-24 16:16:05,048 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:16:05] "GET /api/tif/status?task_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-24 16:16:18,272 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [03:04<01:33, 93.54s/it]
2025-07-24 16:16:23,338 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-24 16:16:23,339 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:16:23,340 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:16:23,713 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:07,  2.69it/s]
2025-07-24 16:16:23,714 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:16:24,089 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:07,  2.67it/s]
2025-07-24 16:16:24,090 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:16:24,511 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:07,  2.53it/s]
2025-07-24 16:16:24,511 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:16:24,899 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:01<00:06,  2.54it/s]
2025-07-24 16:16:24,900 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:16:25,319 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:01<00:06,  2.48it/s]
2025-07-24 16:16:25,319 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:16:25,432 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:02<00:02,  5.52it/s]
2025-07-24 16:16:25,433 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:16:25,545 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:02<00:01,  8.72it/s]
2025-07-24 16:16:25,546 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:16:25,652 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:00, 12.06it/s]
2025-07-24 16:16:25,652 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:16:25,758 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 18/21 [00:02<00:00, 17.07it/s]
2025-07-24 16:16:25,760 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:16:25,816 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - [A
2025-07-24 16:17:06,616 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:17:06,617 - tif_api - INFO - 查询任务: 4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-24 16:17:06,622 - tif_api - INFO - 任务 4acbd746-f075-45ba-b856-04ec2ebb15e0 状态查询成功，当前状态: 正在运行
2025-07-24 16:17:06,624 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:17:06] "GET /api/tif/status?task_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-24 16:18:08,633 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:18:08,636 - tif_api - INFO - 查询任务: 4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-24 16:18:08,639 - tif_api - INFO - 任务 4acbd746-f075-45ba-b856-04ec2ebb15e0 状态查询成功，当前状态: 正在运行
2025-07-24 16:18:08,640 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:18:08] "GET /api/tif/status?task_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-24 16:18:13,019 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [04:59<00:00, 103.22s/it]
2025-07-24 16:18:13,020 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:59<00:00, 99.72s/it]
2025-07-24 16:18:40,777 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif影像保存完成，耗时: 299.76 秒 (16:18:13)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp (16:18:13)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp使用NoData值: -9999, 简化容差: 0.1读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-24 16:18:40,803 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-24 16:18:41,211 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.45it/s]
2025-07-24 16:18:41,213 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.45it/s]
2025-07-24 16:18:41,237 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-24 16:18:53,208 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-24 16:18:53,209 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-24 16:18:54,596 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:01<00:20,  1.44it/s]
2025-07-24 16:18:54,756 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:13,  2.13it/s]
2025-07-24 16:18:54,813 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 19.34it/s]
2025-07-24 16:18:55,460 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-24 16:18:55,461 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-24 16:18:55,475 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - ERROR - 
写入多边形: 100%|####################################################################| 35/35 [00:00<00:00, 2916.41it/s]
2025-07-24 16:18:55,607 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 处理完成，耗时: 356.64 秒 (5.94 分钟)
2025-07-24 16:18:55,607 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 处理结果: 成功
2025-07-24 16:18:55,608 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, 大小: 781.83 MB
2025-07-24 16:18:55,608 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp, 大小: 21.20 KB
2025-07-24 16:18:55,613 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - TIF处理任务 4acbd746-f075-45ba-b856-04ec2ebb15e0 执行成功
2025-07-24 16:18:55,613 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 完成时间: 2025-07-24 16:18:55
2025-07-24 16:18:55,614 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - 状态: 运行成功
2025-07-24 16:18:55,614 - tif_task_4acbd746-f075-45ba-b856-04ec2ebb15e0 - INFO - ============ 任务执行结束 ============
2025-07-24 16:19:09,879 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:19:09,881 - tif_api - INFO - 查询任务: 4acbd746-f075-45ba-b856-04ec2ebb15e0
2025-07-24 16:19:09,883 - tif_api - INFO - 任务 4acbd746-f075-45ba-b856-04ec2ebb15e0 状态查询成功，当前状态: 运行成功
2025-07-24 16:19:09,887 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:19:09] "GET /api/tif/status?task_id=4acbd746-f075-45ba-b856-04ec2ebb15e0 HTTP/1.1" 200 -
2025-07-24 16:19:12,684 - geo_task_c96109c4-298c-456f-b5bd-96940b503e94 - INFO - GeoServer发布任务 c96109c4-298c-456f-b5bd-96940b503e94 开始执行
2025-07-24 16:19:12,686 - geo_task_c96109c4-298c-456f-b5bd-96940b503e94 - INFO - 开始时间: 2025-07-24 16:19:12
2025-07-24 16:19:12,686 - geo_task_c96109c4-298c-456f-b5bd-96940b503e94 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-24 16:19:12,687 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-24 16:19:12,688 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-24 16:19:12,683 - geo_publisher - INFO - 启动GeoTIFF发布任务 c96109c4-298c-456f-b5bd-96940b503e94: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-24 16:19:12,784 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:19:12] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-24 16:19:12,810 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:19:12] "GET /api/geo/status?task_id=c96109c4-298c-456f-b5bd-96940b503e94 HTTP/1.1" 200 -
2025-07-24 16:19:18,185 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-24 16:19:18,186 - root - INFO - 未提供存储名称，使用文件名: 20250705171602
2025-07-24 16:19:18,186 - root - INFO - 未提供图层名称，使用存储名称: 20250705171602
2025-07-24 16:19:18,236 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171602\20250705171602.tif
2025-07-24 16:20:14,798 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:20:14] "GET /api/geo/status?task_id=c96109c4-298c-456f-b5bd-96940b503e94 HTTP/1.1" 200 -
2025-07-24 16:21:17,066 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:21:17] "GET /api/geo/status?task_id=c96109c4-298c-456f-b5bd-96940b503e94 HTTP/1.1" 200 -
2025-07-24 16:22:18,781 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:22:18] "GET /api/geo/status?task_id=c96109c4-298c-456f-b5bd-96940b503e94 HTTP/1.1" 200 -
2025-07-24 16:24:32,871 - root - ERROR - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 失败: [Errno 9] Bad file descriptor
2025-07-24 16:24:32,875 - geo_task_c96109c4-298c-456f-b5bd-96940b503e94 - INFO - GeoServer发布任务 c96109c4-298c-456f-b5bd-96940b503e94 执行失败
2025-07-24 16:24:32,876 - geo_task_c96109c4-298c-456f-b5bd-96940b503e94 - INFO - 完成时间: 2025-07-24 16:24:32
2025-07-24 16:24:32,876 - geo_task_c96109c4-298c-456f-b5bd-96940b503e94 - INFO - 状态: 发布失败
2025-07-24 16:26:05,537 - batch_executor - INFO - 启动任务 d0d93862-342e-461e-a4ac-f77801a11543: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
2025-07-24 16:26:05,538 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:26:05] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-24 16:26:05,565 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:26:05] "GET /api/batch/status?task_id=d0d93862-342e-461e-a4ac-f77801a11543 HTTP/1.1" 200 -
2025-07-24 16:27:07,502 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:27:07] "GET /api/batch/status?task_id=d0d93862-342e-461e-a4ac-f77801a11543 HTTP/1.1" 200 -
2025-07-24 16:27:09,291 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 16:27:09,293 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:27:09,294 - tif_api - INFO - 输入文件大小: 237.46 MB
2025-07-24 16:27:09,294 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-24 16:27:09,296 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-24 16:27:09,296 - tif_api - INFO - 黑色阈值: 0
2025-07-24 16:27:09,297 - tif_api - INFO - 白色阈值: 255
2025-07-24 16:27:09,297 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 16:27:09,301 - tif_executor - INFO - 启动TIF处理任务 5b370409-64f7-4fec-a2ad-fa2ee4a7c892: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-24 16:27:09,302 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - ============ TIF处理任务 5b370409-64f7-4fec-a2ad-fa2ee4a7c892 开始执行 ============
2025-07-24 16:27:09,302 - tif_api - INFO - 异步任务启动成功，任务ID: 5b370409-64f7-4fec-a2ad-fa2ee4a7c892
2025-07-24 16:27:09,304 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 开始时间: 2025-07-24 16:27:09
2025-07-24 16:27:09,306 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:27:09] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 16:27:09,307 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 16:27:09,309 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 系统信息:
2025-07-24 16:27:09,309 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO -   操作系统: Windows 10.0.19045
2025-07-24 16:27:09,310 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO -   Python版本: 3.8.20
2025-07-24 16:27:09,311 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO -   GDAL版本: 3.9.2
2025-07-24 16:27:09,311 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO -   GPU可用: 否
2025-07-24 16:27:09,313 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 检查参数有效性...
2025-07-24 16:27:09,314 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:27:09,315 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 开始执行TIF处理流程...
2025-07-24 16:27:09,315 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:27:09,317 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-24 16:27:09,318 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-24 16:27:09,330 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:27:09,331 - tif_api - INFO - 查询任务: 5b370409-64f7-4fec-a2ad-fa2ee4a7c892
2025-07-24 16:27:09,333 - tif_api - INFO - 任务 5b370409-64f7-4fec-a2ad-fa2ee4a7c892 状态查询成功，当前状态: 正在运行
2025-07-24 16:27:09,335 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:27:09] "GET /api/tif/status?task_id=5b370409-64f7-4fec-a2ad-fa2ee4a7c892 HTTP/1.1" 200 -
2025-07-24 16:27:14,440 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (16:27:14)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-24 16:27:14,444 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-24 16:27:14,715 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:05,  3.71it/s]
2025-07-24 16:27:14,937 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 18.74it/s]
2025-07-24 16:27:15,072 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理数据块:  52%|####################################6                                 | 11/21 [00:00<00:00, 19.81it/s]
2025-07-24 16:27:15,222 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 22.15it/s]
2025-07-24 16:27:15,329 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理数据块:  86%|############################################################          | 18/21 [00:00<00:00, 23.63it/s]
2025-07-24 16:27:15,330 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 23.74it/s]
2025-07-24 16:27:15,331 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-24 16:27:15,332 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-24 16:27:15,372 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 523.92it/s]
2025-07-24 16:27:16,494 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.12 秒 (16:27:16)预计总处理时间: 约 6.36 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (16:27:16)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-24 16:27:16,509 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-24 16:27:20,909 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-24 16:27:20,911 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:27:20,912 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:27:21,028 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:00, 25.95it/s]
2025-07-24 16:27:21,029 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:27:21,130 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:00, 44.68it/s]
2025-07-24 16:27:21,130 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:27:21,234 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:00<00:00, 54.73it/s]
2025-07-24 16:27:21,234 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:27:21,308 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:08,949 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:52<01:44, 52.44s/it]
2025-07-24 16:28:11,295 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:28:11,296 - tif_api - INFO - 查询任务: 5b370409-64f7-4fec-a2ad-fa2ee4a7c892
2025-07-24 16:28:11,300 - tif_api - INFO - 任务 5b370409-64f7-4fec-a2ad-fa2ee4a7c892 状态查询成功，当前状态: 正在运行
2025-07-24 16:28:11,301 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:28:11] "GET /api/tif/status?task_id=5b370409-64f7-4fec-a2ad-fa2ee4a7c892 HTTP/1.1" 200 -
2025-07-24 16:28:13,977 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-24 16:28:13,979 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:28:13,979 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:14,187 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:04,  4.85it/s]
2025-07-24 16:28:14,188 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:14,295 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:02,  6.77it/s]
2025-07-24 16:28:14,295 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:14,421 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  7.25it/s]
2025-07-24 16:28:14,421 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:14,606 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  6.40it/s]
2025-07-24 16:28:14,606 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:14,809 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:01,  7.85it/s]
2025-07-24 16:28:14,810 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:14,990 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:01,  8.97it/s]
2025-07-24 16:28:14,990 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:15,108 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  8.85it/s]
2025-07-24 16:28:15,108 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:15,274 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:01,  9.90it/s]
2025-07-24 16:28:15,276 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:15,459 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:00, 10.21it/s]
2025-07-24 16:28:15,461 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:15,595 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:01<00:00, 11.40it/s]
2025-07-24 16:28:15,596 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:19,147 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:05<00:02,  1.56it/s]
2025-07-24 16:28:19,148 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:22,610 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:08<00:03,  1.18s/it]
2025-07-24 16:28:22,611 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:27,152 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:13<00:03,  1.90s/it]
2025-07-24 16:28:27,153 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:32,013 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:18<00:02,  2.59s/it]
2025-07-24 16:28:32,013 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:33,113 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:19<00:00,  2.22s/it]
2025-07-24 16:28:33,114 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:28:33,115 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:29:13,258 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:29:13,259 - tif_api - INFO - 查询任务: 5b370409-64f7-4fec-a2ad-fa2ee4a7c892
2025-07-24 16:29:14,086 - tif_api - INFO - 任务 5b370409-64f7-4fec-a2ad-fa2ee4a7c892 状态查询成功，当前状态: 正在运行
2025-07-24 16:29:14,088 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:29:14] "GET /api/tif/status?task_id=5b370409-64f7-4fec-a2ad-fa2ee4a7c892 HTTP/1.1" 200 -
2025-07-24 16:29:45,039 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [02:28<01:18, 78.12s/it]
2025-07-24 16:30:00,669 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-24 16:30:00,670 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:30:00,671 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:30:00,961 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:05,  3.45it/s]
2025-07-24 16:30:00,963 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:30:01,074 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:03,  5.38it/s]
2025-07-24 16:30:01,075 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:30:01,186 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  6.57it/s]
2025-07-24 16:30:01,187 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:30:01,299 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  7.33it/s]
2025-07-24 16:30:01,300 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:30:01,422 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:02,  7.60it/s]
2025-07-24 16:30:01,423 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:30:01,553 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:00, 15.41it/s]
2025-07-24 16:30:01,554 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:30:01,678 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:00, 20.60it/s]
2025-07-24 16:30:01,679 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:30:01,789 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [00:01<00:00, 25.01it/s]
2025-07-24 16:30:01,790 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:30:01,847 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - [A
2025-07-24 16:30:16,311 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:30:16,871 - tif_api - INFO - 查询任务: 5b370409-64f7-4fec-a2ad-fa2ee4a7c892
2025-07-24 16:30:18,127 - tif_api - INFO - 任务 5b370409-64f7-4fec-a2ad-fa2ee4a7c892 状态查询成功，当前状态: 正在运行
2025-07-24 16:30:18,128 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:30:18] "GET /api/tif/status?task_id=5b370409-64f7-4fec-a2ad-fa2ee4a7c892 HTTP/1.1" 200 -
2025-07-24 16:31:20,179 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:31:20,183 - tif_api - INFO - 查询任务: 5b370409-64f7-4fec-a2ad-fa2ee4a7c892
2025-07-24 16:31:21,705 - tif_api - INFO - 任务 5b370409-64f7-4fec-a2ad-fa2ee4a7c892 状态查询成功，当前状态: 正在运行
2025-07-24 16:31:21,707 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:31:21] "GET /api/tif/status?task_id=5b370409-64f7-4fec-a2ad-fa2ee4a7c892 HTTP/1.1" 200 -
2025-07-24 16:32:08,087 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [04:51<00:00, 107.77s/it]
2025-07-24 16:32:08,089 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:51<00:00, 97.19s/it]
2025-07-24 16:32:23,875 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:32:23,879 - tif_api - INFO - 查询任务: 5b370409-64f7-4fec-a2ad-fa2ee4a7c892
2025-07-24 16:32:24,263 - tif_api - INFO - 任务 5b370409-64f7-4fec-a2ad-fa2ee4a7c892 状态查询成功，当前状态: 正在运行
2025-07-24 16:32:24,271 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:32:24] "GET /api/tif/status?task_id=5b370409-64f7-4fec-a2ad-fa2ee4a7c892 HTTP/1.1" 200 -
2025-07-24 16:33:11,129 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 292.12 秒 (16:32:08)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (16:32:08)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-24 16:33:11,151 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-24 16:33:11,508 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.81it/s]
2025-07-24 16:33:11,510 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.80it/s]
2025-07-24 16:33:11,516 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-24 16:33:23,359 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-24 16:33:23,360 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-24 16:33:24,476 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理轮廓:   3%|##3                                                                      | 1/31 [00:01<00:33,  1.12s/it]
2025-07-24 16:33:24,644 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:01<00:16,  1.79it/s]
2025-07-24 16:33:24,776 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:10,  2.75it/s]
2025-07-24 16:33:24,811 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 21.37it/s]
2025-07-24 16:33:25,728 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-24 16:33:25,729 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-24 16:33:26,209 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:33:26,215 - tif_api - INFO - 查询任务: 5b370409-64f7-4fec-a2ad-fa2ee4a7c892
2025-07-24 16:33:26,649 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
写入多边形:   6%|####                                                                   | 2/35 [00:00<00:15,  2.18it/s]
2025-07-24 16:33:27,552 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
写入多边形:   9%|######                                                                 | 3/35 [00:01<00:20,  1.55it/s]
2025-07-24 16:33:27,554 - tif_api - INFO - 任务 5b370409-64f7-4fec-a2ad-fa2ee4a7c892 状态查询成功，当前状态: 正在运行
2025-07-24 16:33:27,557 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:33:27] "GET /api/tif/status?task_id=5b370409-64f7-4fec-a2ad-fa2ee4a7c892 HTTP/1.1" 200 -
2025-07-24 16:33:29,044 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
写入多边形:  31%|######################                                                | 11/35 [00:03<00:06,  3.76it/s]
2025-07-24 16:33:29,637 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
写入多边形:  34%|########################                                              | 12/35 [00:03<00:07,  3.25it/s]
2025-07-24 16:33:29,645 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - ERROR - 
写入多边形: 100%|######################################################################| 35/35 [00:03<00:00,  8.94it/s]
2025-07-24 16:33:29,875 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 处理完成，耗时: 380.56 秒 (6.34 分钟)
2025-07-24 16:33:29,876 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 处理结果: 成功
2025-07-24 16:33:29,877 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-24 16:33:29,878 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-24 16:33:29,882 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - TIF处理任务 5b370409-64f7-4fec-a2ad-fa2ee4a7c892 执行成功
2025-07-24 16:33:29,883 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 完成时间: 2025-07-24 16:33:29
2025-07-24 16:33:29,886 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - 状态: 运行成功
2025-07-24 16:33:29,886 - tif_task_5b370409-64f7-4fec-a2ad-fa2ee4a7c892 - INFO - ============ 任务执行结束 ============
2025-07-24 16:34:29,575 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:34:29,576 - tif_api - INFO - 查询任务: 5b370409-64f7-4fec-a2ad-fa2ee4a7c892
2025-07-24 16:34:29,578 - tif_api - INFO - 任务 5b370409-64f7-4fec-a2ad-fa2ee4a7c892 状态查询成功，当前状态: 运行成功
2025-07-24 16:34:29,579 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:34:29] "GET /api/tif/status?task_id=5b370409-64f7-4fec-a2ad-fa2ee4a7c892 HTTP/1.1" 200 -
2025-07-24 16:34:31,466 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:34:31] "[33mGET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&workspace=testodm&charset=UTF-8 HTTP/1.1[0m" 404 -
2025-07-24 16:36:35,624 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:36:35] "[33mGET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&workspace=testodm&charset=UTF-8 HTTP/1.1[0m" 404 -
2025-07-24 16:39:42,145 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:39:42] "[33mGET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&workspace=testodm&charset=UTF-8 HTTP/1.1[0m" 404 -
2025-07-24 16:40:35,389 - batch_executor - INFO - 启动任务 517df9a1-74d1-442c-bc26-e9bbb77c18fe: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
2025-07-24 16:40:35,398 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:40:35] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-24 16:40:35,449 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:40:35] "GET /api/batch/status?task_id=517df9a1-74d1-442c-bc26-e9bbb77c18fe HTTP/1.1" 200 -
2025-07-24 16:41:37,661 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:41:37] "GET /api/batch/status?task_id=517df9a1-74d1-442c-bc26-e9bbb77c18fe HTTP/1.1" 200 -
2025-07-24 16:42:39,789 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:42:39] "GET /api/batch/status?task_id=517df9a1-74d1-442c-bc26-e9bbb77c18fe HTTP/1.1" 200 -
2025-07-24 16:44:08,056 - batch_executor - INFO - 启动任务 d3004cb4-4fe7-43ae-95ce-ffbef65908db: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
2025-07-24 16:44:08,058 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:44:08] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-24 16:44:08,088 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:44:08] "GET /api/batch/status?task_id=d3004cb4-4fe7-43ae-95ce-ffbef65908db HTTP/1.1" 200 -
2025-07-24 16:45:10,910 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:45:10] "GET /api/batch/status?task_id=d3004cb4-4fe7-43ae-95ce-ffbef65908db HTTP/1.1" 200 -
2025-07-24 16:50:23,398 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 16:50:23,400 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:50:23,401 - tif_api - INFO - 输入文件大小: 237.49 MB
2025-07-24 16:50:23,402 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-24 16:50:23,404 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-24 16:50:23,404 - tif_api - INFO - 黑色阈值: 0
2025-07-24 16:50:23,405 - tif_api - INFO - 白色阈值: 255
2025-07-24 16:50:23,405 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 16:50:23,409 - tif_executor - INFO - 启动TIF处理任务 881f151d-cf18-4740-a577-bf1832971e01: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-24 16:50:23,410 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - ============ TIF处理任务 881f151d-cf18-4740-a577-bf1832971e01 开始执行 ============
2025-07-24 16:50:23,410 - tif_api - INFO - 异步任务启动成功，任务ID: 881f151d-cf18-4740-a577-bf1832971e01
2025-07-24 16:50:23,412 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 开始时间: 2025-07-24 16:50:23
2025-07-24 16:50:23,413 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 16:50:23,415 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:50:23] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 16:50:23,437 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 系统信息:
2025-07-24 16:50:23,442 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO -   操作系统: Windows 10.0.19045
2025-07-24 16:50:23,447 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO -   Python版本: 3.8.20
2025-07-24 16:50:23,457 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO -   GDAL版本: 3.9.2
2025-07-24 16:50:23,462 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO -   GPU可用: 否
2025-07-24 16:50:23,464 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 检查参数有效性...
2025-07-24 16:50:23,466 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:50:23,469 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 开始执行TIF处理流程...
2025-07-24 16:50:23,470 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:50:23,472 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-24 16:50:23,473 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-24 16:50:23,523 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:50:23,527 - tif_api - INFO - 查询任务: 881f151d-cf18-4740-a577-bf1832971e01
2025-07-24 16:50:23,530 - tif_api - INFO - 任务 881f151d-cf18-4740-a577-bf1832971e01 状态查询成功，当前状态: 正在运行
2025-07-24 16:50:23,532 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:50:23] "GET /api/tif/status?task_id=881f151d-cf18-4740-a577-bf1832971e01 HTTP/1.1" 200 -
2025-07-24 16:50:27,800 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (16:50:27)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-24 16:50:27,847 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-24 16:50:28,132 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:04,  4.15it/s]
2025-07-24 16:50:28,403 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 17.53it/s]
2025-07-24 16:50:28,815 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 17.21it/s]
2025-07-24 16:50:28,828 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 22.41it/s]
2025-07-24 16:50:28,831 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-24 16:50:28,832 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-24 16:50:28,870 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 552.68it/s]
2025-07-24 16:50:30,129 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.34 秒 (16:50:30)预计总处理时间: 约 7.01 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (16:50:30)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-24 16:50:30,132 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-24 16:50:34,271 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-24 16:50:35,356 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:50:35,360 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:50:35,818 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:09,  2.18it/s]
2025-07-24 16:50:35,818 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:50:35,919 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:01, 11.13it/s]
2025-07-24 16:50:35,920 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:50:36,031 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:00<00:00, 25.25it/s]
2025-07-24 16:50:36,032 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:50:36,141 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:00<00:00, 36.02it/s]
2025-07-24 16:50:36,141 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:50:36,178 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:25,922 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:51:25,926 - tif_api - INFO - 查询任务: 881f151d-cf18-4740-a577-bf1832971e01
2025-07-24 16:51:25,933 - tif_api - INFO - 任务 881f151d-cf18-4740-a577-bf1832971e01 状态查询成功，当前状态: 正在运行
2025-07-24 16:51:25,937 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:51:25] "GET /api/tif/status?task_id=881f151d-cf18-4740-a577-bf1832971e01 HTTP/1.1" 200 -
2025-07-24 16:51:29,169 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:59<01:58, 59.03s/it]
2025-07-24 16:51:34,919 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-24 16:51:34,920 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:51:34,927 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:35,250 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:06,  3.11it/s]
2025-07-24 16:51:35,251 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:35,393 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:04,  4.61it/s]
2025-07-24 16:51:35,394 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:35,553 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:03,  5.23it/s]
2025-07-24 16:51:35,557 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:35,744 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:03,  5.23it/s]
2025-07-24 16:51:35,745 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:35,930 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:01<00:03,  5.28it/s]
2025-07-24 16:51:35,934 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:36,147 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:01<00:02,  5.04it/s]
2025-07-24 16:51:36,148 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:36,266 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  5.80it/s]
2025-07-24 16:51:36,266 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:36,457 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  7.39it/s]
2025-07-24 16:51:36,458 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:36,599 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  7.30it/s]
2025-07-24 16:51:36,599 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:36,800 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:01<00:01,  8.23it/s]
2025-07-24 16:51:36,801 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:36,927 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:00,  8.14it/s]
2025-07-24 16:51:36,928 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:37,066 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:02<00:00,  9.84it/s]
2025-07-24 16:51:37,067 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:40,999 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:06<00:03,  1.29it/s]
2025-07-24 16:51:41,000 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:44,728 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:09<00:04,  1.39s/it]
2025-07-24 16:51:44,728 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:49,472 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:14<00:04,  2.15s/it]
2025-07-24 16:51:49,472 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:54,678 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:19<00:02,  2.90s/it]
2025-07-24 16:51:54,679 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:55,897 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:20<00:00,  2.46s/it]
2025-07-24 16:51:55,898 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:51:55,902 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:52:26,102 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [01:55<00:57, 57.80s/it]
2025-07-24 16:53:01,623 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-24 16:53:01,626 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:53:01,631 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:53:01,860 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:04,  4.43it/s]
2025-07-24 16:53:01,861 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:53:01,983 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:00, 30.90it/s]
2025-07-24 16:53:01,984 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:53:02,123 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:00<00:00, 30.05it/s]
2025-07-24 16:53:02,127 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:53:02,385 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [00:00<00:00, 22.21it/s]
2025-07-24 16:53:02,386 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:53:02,519 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [00:00<00:00, 22.23it/s]
2025-07-24 16:53:02,522 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:53:02,585 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - [A
2025-07-24 16:53:03,054 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [02:32<00:00, 48.28s/it]
2025-07-24 16:53:03,055 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [02:32<00:00, 50.97s/it]
2025-07-24 16:53:13,270 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 153.37 秒 (16:53:03)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (16:53:03)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1创建输出目录: D:/Drone_Project/nginxData/ODM/Output/20250705171602错误: 输入TIF文件不存在: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-24 16:53:13,273 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-24 16:53:13,273 - batch_executor - INFO - 启动任务 cb1d3f41-49fe-4d67-ac7f-1b6ba3f87fd6: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
2025-07-24 16:53:13,303 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:53:13] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-24 16:53:13,386 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:53:13] "GET /api/batch/status?task_id=cb1d3f41-49fe-4d67-ac7f-1b6ba3f87fd6 HTTP/1.1" 200 -
2025-07-24 16:53:13,441 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:02, 12.49it/s]
2025-07-24 16:53:13,691 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理轮廓:  13%|#########4                                                               | 4/31 [00:00<00:02,  9.42it/s]
2025-07-24 16:53:13,745 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:00<00:00, 66.89it/s]
2025-07-24 16:53:14,303 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-24 16:53:14,304 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-24 16:53:14,314 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - ERROR - 
写入多边形: 100%|####################################################################| 35/35 [00:00<00:00, 4379.36it/s]
2025-07-24 16:53:14,548 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 处理完成，耗时: 171.07 秒 (2.85 分钟)
2025-07-24 16:53:14,549 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 处理结果: 成功
2025-07-24 16:53:14,550 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - WARNING - 输出TIF文件不存在或未指定: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-24 16:53:14,552 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-24 16:53:14,556 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - TIF处理任务 881f151d-cf18-4740-a577-bf1832971e01 执行成功
2025-07-24 16:53:14,557 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 完成时间: 2025-07-24 16:53:14
2025-07-24 16:53:14,557 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - 状态: 运行成功
2025-07-24 16:53:14,558 - tif_task_881f151d-cf18-4740-a577-bf1832971e01 - INFO - ============ 任务执行结束 ============
2025-07-24 16:54:15,748 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:54:15] "GET /api/batch/status?task_id=cb1d3f41-49fe-4d67-ac7f-1b6ba3f87fd6 HTTP/1.1" 200 -
2025-07-24 16:54:27,233 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 16:54:27,234 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:54:27,235 - tif_api - INFO - 输入文件大小: 237.50 MB
2025-07-24 16:54:27,235 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-24 16:54:27,236 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-24 16:54:27,238 - tif_api - INFO - 黑色阈值: 0
2025-07-24 16:54:27,240 - tif_api - INFO - 白色阈值: 255
2025-07-24 16:54:27,244 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 16:54:27,249 - tif_executor - INFO - 启动TIF处理任务 6c8aba6f-9089-4964-9803-e9f3d639868d: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-24 16:54:27,250 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - ============ TIF处理任务 6c8aba6f-9089-4964-9803-e9f3d639868d 开始执行 ============
2025-07-24 16:54:27,251 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 开始时间: 2025-07-24 16:54:27
2025-07-24 16:54:27,250 - tif_api - INFO - 异步任务启动成功，任务ID: 6c8aba6f-9089-4964-9803-e9f3d639868d
2025-07-24 16:54:27,252 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 16:54:27,253 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:54:27] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 16:54:27,254 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 系统信息:
2025-07-24 16:54:27,257 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO -   操作系统: Windows 10.0.19045
2025-07-24 16:54:27,278 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO -   Python版本: 3.8.20
2025-07-24 16:54:27,279 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO -   GDAL版本: 3.9.2
2025-07-24 16:54:27,280 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:54:27,280 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO -   GPU可用: 否
2025-07-24 16:54:27,281 - tif_api - INFO - 查询任务: 6c8aba6f-9089-4964-9803-e9f3d639868d
2025-07-24 16:54:27,282 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 检查参数有效性...
2025-07-24 16:54:27,284 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:54:27,290 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 开始执行TIF处理流程...
2025-07-24 16:54:27,307 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:54:27,284 - tif_api - INFO - 任务 6c8aba6f-9089-4964-9803-e9f3d639868d 状态查询成功，当前状态: 正在运行
2025-07-24 16:54:27,309 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-24 16:54:27,310 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-24 16:54:27,312 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:54:27] "GET /api/tif/status?task_id=6c8aba6f-9089-4964-9803-e9f3d639868d HTTP/1.1" 200 -
2025-07-24 16:54:31,237 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (16:54:31)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-24 16:54:31,264 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-24 16:54:31,586 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:06,  3.14it/s]
2025-07-24 16:54:31,847 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 15.93it/s]
2025-07-24 16:54:32,079 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 21.75it/s]
2025-07-24 16:54:32,100 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 25.26it/s]
2025-07-24 16:54:32,102 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-24 16:54:32,103 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-24 16:54:32,139 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 587.29it/s]
2025-07-24 16:54:33,234 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 1.97 秒 (16:54:33)预计总处理时间: 约 5.92 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (16:54:33)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-24 16:54:33,236 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-24 16:54:37,180 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-24 16:54:37,181 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:54:37,185 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:54:37,293 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:00, 28.26it/s]
2025-07-24 16:54:37,294 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:54:37,399 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:00<00:00, 56.12it/s]
2025-07-24 16:54:37,400 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:54:37,505 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:00<00:00, 64.96it/s]
2025-07-24 16:54:37,506 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:54:37,537 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:55:27,321 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:54<01:48, 54.08s/it]
2025-07-24 16:55:29,688 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:55:29,692 - tif_api - INFO - 查询任务: 6c8aba6f-9089-4964-9803-e9f3d639868d
2025-07-24 16:55:29,696 - tif_api - INFO - 任务 6c8aba6f-9089-4964-9803-e9f3d639868d 状态查询成功，当前状态: 正在运行
2025-07-24 16:55:29,699 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:55:29] "GET /api/tif/status?task_id=6c8aba6f-9089-4964-9803-e9f3d639868d HTTP/1.1" 200 -
2025-07-24 16:55:55,633 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-24 16:55:55,635 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:55:55,637 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:55:55,825 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  5.42it/s]
2025-07-24 16:55:55,826 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:55:55,959 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:01, 10.22it/s]
2025-07-24 16:55:55,959 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:55:56,138 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:01, 10.72it/s]
2025-07-24 16:55:56,138 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:55:56,448 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:01,  8.38it/s]
2025-07-24 16:55:56,449 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:55:56,605 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:00<00:01,  7.80it/s]
2025-07-24 16:55:56,605 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:55:56,732 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  7.82it/s]
2025-07-24 16:55:56,734 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:55:56,887 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  7.40it/s]
2025-07-24 16:55:56,929 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:55:57,077 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:01,  6.69it/s]
2025-07-24 16:55:57,085 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:55:57,265 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:01<00:01,  6.23it/s]
2025-07-24 16:55:57,266 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:55:57,421 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:01,  6.27it/s]
2025-07-24 16:55:57,425 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:55:57,566 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:01<00:01,  6.45it/s]
2025-07-24 16:55:57,568 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:55:59,314 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:03<00:02,  2.08it/s]
2025-07-24 16:55:59,315 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:56:03,263 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:07<00:05,  1.33s/it]
2025-07-24 16:56:03,263 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:56:07,483 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:11<00:06,  2.08s/it]
2025-07-24 16:56:07,484 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:56:11,870 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:16<00:05,  2.70s/it]
2025-07-24 16:56:11,870 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:56:16,506 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:20<00:03,  3.24s/it]
2025-07-24 16:56:16,507 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:56:17,671 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:22<00:00,  2.65s/it]
2025-07-24 16:56:17,672 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:56:17,675 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:56:32,081 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:56:32,084 - tif_api - INFO - 查询任务: 6c8aba6f-9089-4964-9803-e9f3d639868d
2025-07-24 16:56:32,091 - tif_api - INFO - 任务 6c8aba6f-9089-4964-9803-e9f3d639868d 状态查询成功，当前状态: 正在运行
2025-07-24 16:56:32,092 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:56:32] "GET /api/tif/status?task_id=6c8aba6f-9089-4964-9803-e9f3d639868d HTTP/1.1" 200 -
2025-07-24 16:57:19,433 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [02:46<01:28, 88.22s/it]
2025-07-24 16:57:30,446 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-24 16:57:30,449 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:57:30,453 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:57:30,710 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:05,  3.93it/s]
2025-07-24 16:57:30,710 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:57:30,918 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  7.02it/s]
2025-07-24 16:57:30,918 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:57:31,043 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  7.31it/s]
2025-07-24 16:57:31,044 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:57:31,171 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:02,  7.47it/s]
2025-07-24 16:57:31,172 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:57:31,276 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:01, 10.60it/s]
2025-07-24 16:57:31,277 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:57:31,380 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 3/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 15.65it/s]
2025-07-24 16:57:31,380 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:57:31,494 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:01<00:00, 21.59it/s]
2025-07-24 16:57:31,494 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:57:31,601 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
波段 3/3 写入进度:  90%|#########################################################      | 19/21 [00:01<00:00, 28.78it/s]
2025-07-24 16:57:31,603 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:57:31,646 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - [A
2025-07-24 16:57:34,453 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:57:34,456 - tif_api - INFO - 查询任务: 6c8aba6f-9089-4964-9803-e9f3d639868d
2025-07-24 16:57:34,461 - tif_api - INFO - 任务 6c8aba6f-9089-4964-9803-e9f3d639868d 状态查询成功，当前状态: 正在运行
2025-07-24 16:57:34,464 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:57:34] "GET /api/tif/status?task_id=6c8aba6f-9089-4964-9803-e9f3d639868d HTTP/1.1" 200 -
2025-07-24 16:58:36,812 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:58:36,816 - tif_api - INFO - 查询任务: 6c8aba6f-9089-4964-9803-e9f3d639868d
2025-07-24 16:58:36,820 - tif_api - INFO - 任务 6c8aba6f-9089-4964-9803-e9f3d639868d 状态查询成功，当前状态: 正在运行
2025-07-24 16:58:36,824 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:58:36] "GET /api/tif/status?task_id=6c8aba6f-9089-4964-9803-e9f3d639868d HTTP/1.1" 200 -
2025-07-24 16:59:12,280 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:39<00:00, 99.46s/it]
2025-07-24 16:59:12,281 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:39<00:00, 93.01s/it]
2025-07-24 16:59:39,263 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 16:59:39,273 - tif_api - INFO - 查询任务: 6c8aba6f-9089-4964-9803-e9f3d639868d
2025-07-24 16:59:39,286 - tif_api - INFO - 任务 6c8aba6f-9089-4964-9803-e9f3d639868d 状态查询成功，当前状态: 正在运行
2025-07-24 16:59:39,297 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 16:59:39] "GET /api/tif/status?task_id=6c8aba6f-9089-4964-9803-e9f3d639868d HTTP/1.1" 200 -
2025-07-24 16:59:55,966 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 279.55 秒 (16:59:12)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (16:59:12)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-24 16:59:55,991 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-24 16:59:56,317 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  3.14it/s]
2025-07-24 16:59:56,318 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  3.12it/s]
2025-07-24 16:59:56,331 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-24 17:00:09,718 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-24 17:00:09,719 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-24 17:00:09,995 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:03,  7.35it/s]
2025-07-24 17:00:11,308 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:17,  1.60it/s]
2025-07-24 17:00:11,352 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 19.04it/s]
2025-07-24 17:00:12,046 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-24 17:00:12,048 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-24 17:00:12,068 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - ERROR - 
写入多边形: 100%|####################################################################| 35/35 [00:00<00:00, 2273.12it/s]
2025-07-24 17:00:12,302 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 处理完成，耗时: 344.99 秒 (5.75 分钟)
2025-07-24 17:00:12,303 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 处理结果: 成功
2025-07-24 17:00:12,306 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-24 17:00:12,309 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-24 17:00:12,313 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - TIF处理任务 6c8aba6f-9089-4964-9803-e9f3d639868d 执行成功
2025-07-24 17:00:12,314 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 完成时间: 2025-07-24 17:00:12
2025-07-24 17:00:12,314 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - 状态: 运行成功
2025-07-24 17:00:12,315 - tif_task_6c8aba6f-9089-4964-9803-e9f3d639868d - INFO - ============ 任务执行结束 ============
2025-07-24 17:00:41,449 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 17:00:41,454 - tif_api - INFO - 查询任务: 6c8aba6f-9089-4964-9803-e9f3d639868d
2025-07-24 17:00:41,459 - tif_api - INFO - 任务 6c8aba6f-9089-4964-9803-e9f3d639868d 状态查询成功，当前状态: 运行成功
2025-07-24 17:00:41,463 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 17:00:41] "GET /api/tif/status?task_id=6c8aba6f-9089-4964-9803-e9f3d639868d HTTP/1.1" 200 -
2025-07-24 17:00:46,673 - geo_task_b0f48082-8d48-4388-bb85-be96f4d4f42a - INFO - GeoServer发布任务 b0f48082-8d48-4388-bb85-be96f4d4f42a 开始执行
2025-07-24 17:00:46,672 - geo_publisher - INFO - 启动GeoTIFF发布任务 b0f48082-8d48-4388-bb85-be96f4d4f42a: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-24 17:00:46,677 - geo_task_b0f48082-8d48-4388-bb85-be96f4d4f42a - INFO - 开始时间: 2025-07-24 17:00:46
2025-07-24 17:00:46,684 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 17:00:46] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-24 17:00:46,684 - geo_task_b0f48082-8d48-4388-bb85-be96f4d4f42a - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-24 17:00:46,691 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-24 17:00:46,710 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-24 17:00:46,711 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 17:00:46] "GET /api/geo/status?task_id=b0f48082-8d48-4388-bb85-be96f4d4f42a HTTP/1.1" 200 -
2025-07-24 17:00:48,319 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-24 17:00:48,320 - root - INFO - 未提供存储名称，使用文件名: 20250705171602
2025-07-24 17:00:48,325 - root - INFO - 未提供图层名称，使用存储名称: 20250705171602
2025-07-24 17:00:48,347 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171602\20250705171602.tif
2025-07-24 17:01:47,327 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 17:01:47] "GET /api/geo/status?task_id=b0f48082-8d48-4388-bb85-be96f4d4f42a HTTP/1.1" 200 -
2025-07-24 17:02:47,904 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 17:02:47] "GET /api/geo/status?task_id=b0f48082-8d48-4388-bb85-be96f4d4f42a HTTP/1.1" 200 -
2025-07-24 17:03:48,498 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 17:03:48] "GET /api/geo/status?task_id=b0f48082-8d48-4388-bb85-be96f4d4f42a HTTP/1.1" 200 -
2025-07-24 17:03:50,962 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 到 'testodm:20250705171602'
2025-07-24 17:03:51,020 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171602
2025-07-24 17:03:51,020 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 成功发布为图层 'testodm:20250705171602'
2025-07-24 17:03:51,029 - geo_task_b0f48082-8d48-4388-bb85-be96f4d4f42a - INFO - GeoServer发布任务 b0f48082-8d48-4388-bb85-be96f4d4f42a 执行成功
2025-07-24 17:03:51,030 - geo_task_b0f48082-8d48-4388-bb85-be96f4d4f42a - INFO - 完成时间: 2025-07-24 17:03:51
2025-07-24 17:03:51,030 - geo_task_b0f48082-8d48-4388-bb85-be96f4d4f42a - INFO - 状态: 发布成功
2025-07-24 17:04:49,157 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 17:04:49] "GET /api/geo/status?task_id=b0f48082-8d48-4388-bb85-be96f4d4f42a HTTP/1.1" 200 -
2025-07-24 17:07:06,983 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 17:07:06] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-24 17:07:06,990 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-24 17:07:07,033 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-24 17:07:07,035 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 17:07:07] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-24 17:07:15,425 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 17:07:15] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-24 17:07:15,434 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-24 17:07:15,470 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-24 17:07:15,495 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 17:07:15] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-24 17:07:21,944 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 17:07:21] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171602 HTTP/1.1" 200 -
2025-07-24 17:07:21,956 - root - INFO - 获取图层 testodm:20250705171602 的信息
2025-07-24 17:07:21,988 - root - INFO - 成功获取图层 testodm:20250705171602 的边界框信息
2025-07-24 17:07:21,990 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 17:07:21] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171602 HTTP/1.1" 200 -
2025-07-25 08:42:40,785 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 08:42:40] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 08:42:40,806 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-25 08:42:41,545 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-25 08:42:41,548 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 08:42:41] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 08:42:52,485 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 08:42:52] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 08:42:52,497 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-25 08:42:55,101 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-25 08:42:55,139 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 08:42:55] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 08:43:36,133 - root - INFO - 开始查询坐标点 (23.926153119205708, 111.07855413378904) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-25 08:43:37,166 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-25 08:43:37,167 - root - INFO - 在坐标点 (23.926153119205708, 111.07855413378904) 处找到 0 个有有效数据的栅格图层
2025-07-25 08:43:37,169 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 08:43:37] "GET /api/query_values?lat=23.926153119205708&lon=111.07855413378904&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-25 08:48:14,628 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 08:48:14] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 08:48:14,659 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-25 08:48:14,689 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-25 08:48:14,690 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 08:48:14] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 08:48:19,030 - root - INFO - 开始查询坐标点 (23.752808137417745, 110.96731756152342) 在工作区 'test_myworkspace2' 中的图层及像素值
2025-07-25 08:48:19,050 - root - ERROR - 工作区 'test_myworkspace2' 不存在
2025-07-25 08:48:19,051 - root - WARNING - 工作区 'test_myworkspace2' 中没有找到栅格图层
2025-07-25 08:48:19,053 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 08:48:19] "GET /api/query_values?lat=23.752808137417745&lon=110.96731756152342&workspace=test_myworkspace2 HTTP/1.1" 200 -
2025-07-25 08:48:23,220 - root - INFO - 开始查询坐标点 (23.851442219310073, 111.50152776660155) 在工作区 'test_myworkspace2' 中的图层及像素值
2025-07-25 08:48:23,248 - root - ERROR - 工作区 'test_myworkspace2' 不存在
2025-07-25 08:48:23,249 - root - WARNING - 工作区 'test_myworkspace2' 中没有找到栅格图层
2025-07-25 08:48:23,250 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 08:48:23] "GET /api/query_values?lat=23.851442219310073&lon=111.50152776660155&workspace=test_myworkspace2 HTTP/1.1" 200 -
2025-07-25 08:48:48,514 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 08:48:48] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171602 HTTP/1.1" 200 -
2025-07-25 08:48:48,521 - root - INFO - 获取图层 testodm:20250705171602 的信息
2025-07-25 08:48:48,559 - root - INFO - 成功获取图层 testodm:20250705171602 的边界框信息
2025-07-25 08:48:48,561 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 08:48:48] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171602 HTTP/1.1" 200 -
2025-07-25 08:49:23,660 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 08:49:23] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 08:49:23,667 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-25 08:49:23,697 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-25 08:49:23,699 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 08:49:23] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 11:17:02,888 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 11:17:02,896 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 11:17:02,969 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-25 11:17:03,917 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 11:17:03,925 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:17:03] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 11:17:03,932 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 11:17:04,246 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 11:17:04,247 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:17:04] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 11:17:04,263 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 11:17:04,265 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-25 11:17:04,954 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-25 11:17:04,963 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:17:04] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-25 11:17:04,957 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 11:17:04,960 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 11:17:05,211 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:17:05] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 11:17:05,213 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:17:05] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 11:17:05,822 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-25 11:17:05,825 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:17:05] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-25 11:17:15,111 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 11:17:15,118 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 11:17:15,120 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-25 11:17:15,126 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 11:17:15,137 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 11:17:15,160 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:17:15] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 11:17:15,161 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:17:15] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 11:17:15,165 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-25 11:17:15,171 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:17:15] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-25 11:17:15,187 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 11:17:15,197 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 11:17:15,202 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:17:15] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 11:17:15,309 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-25 11:17:15,315 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 11:17:15,327 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-25 11:17:15,344 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:17:15] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-25 11:17:15,346 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 11:17:15,348 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:17:15] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 11:22:05,814 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 11:22:05,897 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 11:22:05,972 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 11:22:05,975 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:22:05] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 11:22:06,039 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 11:22:06,057 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-25 11:22:06,125 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 11:22:06,127 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 11:22:06,141 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:22:06] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 11:22:06,143 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:22:06] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 11:22:06,604 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 11:22:06,608 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-25 11:22:07,149 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-25 11:22:07,150 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 11:22:07,156 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:22:07] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-25 11:22:07,158 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:22:07] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 11:22:07,308 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-25 11:22:07,314 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:22:07] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-25 11:22:13,643 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 11:22:13,696 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-25 11:22:13,709 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-25 11:22:13,747 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:22:13] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-25 11:22:13,768 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 11:22:13,773 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 11:22:13] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 11:23:05,456 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 11:23:05] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 11:23:05,550 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-25 11:23:05,631 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-25 11:23:05,656 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 11:23:05] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 11:23:09,624 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-25 11:23:09,673 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-25 11:23:09,690 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 11:23:09] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 11:23:20,070 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 11:23:20] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 11:23:20,125 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-25 11:23:20,285 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-25 11:23:20,330 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 11:23:20] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 11:23:22,696 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-25 11:23:22,755 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-25 11:23:22,765 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 11:23:22] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 12:00:20,620 - map_api - INFO - 请求文件: baseMap.json
2025-07-25 12:00:20,813 - map_api - INFO - 成功获取文件 baseMap.json, 内容大小: 3253 字节
2025-07-25 12:00:20,818 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 12:00:20] "GET /api/map/base-map HTTP/1.1" 200 -
2025-07-25 12:00:22,152 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 12:00:22,191 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 12:00:22,224 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-25 12:00:22,276 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 12:00:22,290 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 12:00:22] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 12:00:22,316 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 12:00:22,324 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-25 12:00:22,418 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 12:00:22,422 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 12:00:22] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 12:00:22,430 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 12:00:22,461 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-25 12:00:22,463 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 12:00:22,466 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 12:00:22] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-25 12:00:22,470 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 12:00:22] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 12:00:22,601 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-25 12:00:22,603 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 12:00:22,607 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 12:00:22] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-25 12:00:22,608 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 12:00:22] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 12:00:23,575 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 12:00:23] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 12:00:23,586 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-25 12:00:23,707 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-25 12:00:23,710 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 12:00:23] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 12:00:30,763 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 12:00:30,775 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 12:00:30,779 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-25 12:00:31,685 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 12:00:31,689 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 12:00:31,698 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 12:00:31] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 12:00:31,692 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-25 12:00:31,700 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 12:00:31] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 12:00:31,767 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 12:00:31] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-25 12:00:31,770 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 12:00:31,789 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-25 12:00:31,822 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 12:00:31,846 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 12:00:31,871 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 12:00:31] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 12:00:31,850 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-25 12:00:31,854 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 12:00:31,902 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 12:00:31] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-25 12:00:31,905 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 12:00:31] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 12:00:32,850 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 12:00:32] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 12:00:32,856 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-25 12:00:32,892 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-25 12:00:32,893 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 12:00:32] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 12:00:33,645 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-25 12:00:33,688 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-25 12:00:33,690 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 12:00:33] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 12:00:47,779 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 12:00:47] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171602 HTTP/1.1" 200 -
2025-07-25 12:00:47,784 - root - INFO - 获取图层 testodm:20250705171602 的信息
2025-07-25 12:00:47,818 - root - INFO - 成功获取图层 testodm:20250705171602 的边界框信息
2025-07-25 12:00:47,820 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 12:00:47] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171602 HTTP/1.1" 200 -
2025-07-25 16:21:55,930 - map_api - INFO - 请求文件: baseMap.json
2025-07-25 16:21:55,994 - map_api - INFO - 成功获取文件 baseMap.json, 内容大小: 3253 字节
2025-07-25 16:21:56,019 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:21:56] "GET /api/map/base-map HTTP/1.1" 200 -
2025-07-25 16:35:52,550 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 16:35:52] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171601 HTTP/1.1" 200 -
2025-07-25 16:35:52,556 - root - INFO - 获取图层 testodm:20250705171601 的信息
2025-07-25 16:35:52,848 - root - INFO - 成功获取图层 testodm:20250705171601 的边界框信息
2025-07-25 16:35:52,849 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 16:35:52] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171601 HTTP/1.1" 200 -
2025-07-25 16:36:34,620 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 16:36:34] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171602 HTTP/1.1" 200 -
2025-07-25 16:36:34,625 - root - INFO - 获取图层 testodm:20250705171602 的信息
2025-07-25 16:36:34,800 - root - INFO - 成功获取图层 testodm:20250705171602 的边界框信息
2025-07-25 16:36:34,802 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 16:36:34] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171602 HTTP/1.1" 200 -
2025-07-25 16:37:00,036 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 16:37:00] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171601 HTTP/1.1" 200 -
2025-07-25 16:37:00,059 - root - INFO - 获取图层 testodm:20250705171601 的信息
2025-07-25 16:37:00,099 - root - INFO - 成功获取图层 testodm:20250705171601 的边界框信息
2025-07-25 16:37:00,101 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 16:37:00] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171601 HTTP/1.1" 200 -
2025-07-25 16:39:15,973 - map_api - INFO - 请求文件: baseMap.json
2025-07-25 16:39:18,271 - map_api - INFO - 成功获取文件 baseMap.json, 内容大小: 3253 字节
2025-07-25 16:39:18,274 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:39:18] "GET /api/map/base-map HTTP/1.1" 200 -
2025-07-25 16:40:34,051 - map_api - INFO - 请求文件: baseMap.json
2025-07-25 16:40:34,060 - map_api - INFO - 成功获取文件 baseMap.json, 内容大小: 3253 字节
2025-07-25 16:40:34,065 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:40:34] "GET /api/map/base-map HTTP/1.1" 200 -
2025-07-25 16:44:54,441 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 16:44:54,455 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 16:44:54,560 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-25 16:44:54,655 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 16:44:54,686 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:44:54] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 16:44:54,708 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 16:44:54,737 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:44:54] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 16:44:54,718 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-25 16:44:54,740 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 16:44:54,771 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:44:54] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-25 16:44:54,757 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-25 16:44:54,772 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 16:44:54,788 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 16:44:54,868 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:44:54] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 16:44:55,044 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-25 16:44:55,045 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 16:44:55,356 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:44:55] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-25 16:44:55,467 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:44:55] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 16:44:56,408 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 16:44:56] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 16:44:56,425 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-25 16:44:58,645 - map_api - INFO - 请求文件: baseMap.json
2025-07-25 16:44:59,291 - map_api - INFO - 成功获取文件 baseMap.json, 内容大小: 3253 字节
2025-07-25 16:44:59,294 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:44:59] "GET /api/map/base-map HTTP/1.1" 200 -
2025-07-25 16:44:59,778 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-25 16:44:59,780 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 16:44:59] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 16:56:32,511 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 16:56:32,592 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 16:56:32,616 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:56:32] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 16:56:35,782 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 16:56:35,789 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 16:56:35,816 - map_api - INFO - 找到 3 个目录
2025-07-25 16:56:35,818 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 16:56:36,094 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 16:56:36,096 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 16:56:36,331 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 16:56:36,334 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 16:56:36,427 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 16:56:36,431 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 16:56:36,435 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:56:36] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:07:06,130 - batch_executor - INFO - 启动任务 3b3c5ba9-4cb7-4e16-94ac-161cbe2f9c7a: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
2025-07-25 17:07:06,410 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:07:06] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-25 17:07:06,433 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:07:06] "GET /api/batch/status?task_id=3b3c5ba9-4cb7-4e16-94ac-161cbe2f9c7a HTTP/1.1" 200 -
2025-07-25 17:08:06,488 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:08:06] "GET /api/batch/status?task_id=3b3c5ba9-4cb7-4e16-94ac-161cbe2f9c7a HTTP/1.1" 200 -
2025-07-25 17:08:23,737 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-25 17:08:23,778 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-25 17:08:23,780 - tif_api - INFO - 输入文件大小: 237.51 MB
2025-07-25 17:08:23,782 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-25 17:08:23,783 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-25 17:08:23,785 - tif_api - INFO - 黑色阈值: 0
2025-07-25 17:08:23,787 - tif_api - INFO - 白色阈值: 255
2025-07-25 17:08:23,788 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-25 17:08:23,827 - tif_executor - INFO - 启动TIF处理任务 4c45af59-78c5-4f87-90b6-0418138bdd75: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-25 17:08:23,830 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - ============ TIF处理任务 4c45af59-78c5-4f87-90b6-0418138bdd75 开始执行 ============
2025-07-25 17:08:23,834 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 开始时间: 2025-07-25 17:08:23
2025-07-25 17:08:23,833 - tif_api - INFO - 异步任务启动成功，任务ID: 4c45af59-78c5-4f87-90b6-0418138bdd75
2025-07-25 17:08:23,836 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-25 17:08:23,838 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:08:23] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-25 17:08:23,839 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 系统信息:
2025-07-25 17:08:23,844 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO -   操作系统: Windows 10.0.19045
2025-07-25 17:08:23,849 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO -   Python版本: 3.8.20
2025-07-25 17:08:23,862 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO -   GDAL版本: 3.9.2
2025-07-25 17:08:23,867 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO -   GPU可用: 否
2025-07-25 17:08:23,870 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 检查参数有效性...
2025-07-25 17:08:23,869 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:08:23,871 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-25 17:08:23,872 - tif_api - INFO - 查询任务: 4c45af59-78c5-4f87-90b6-0418138bdd75
2025-07-25 17:08:23,876 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 开始执行TIF处理流程...
2025-07-25 17:08:23,889 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-25 17:08:23,892 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-25 17:08:23,893 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-25 17:08:23,903 - tif_api - INFO - 任务 4c45af59-78c5-4f87-90b6-0418138bdd75 状态查询成功，当前状态: 正在运行
2025-07-25 17:08:23,905 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:08:23] "GET /api/tif/status?task_id=4c45af59-78c5-4f87-90b6-0418138bdd75 HTTP/1.1" 200 -
2025-07-25 17:09:20,950 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (17:09:19)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-25 17:09:21,022 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-25 17:09:24,574 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:09:24,615 - tif_api - INFO - 查询任务: 4c45af59-78c5-4f87-90b6-0418138bdd75
2025-07-25 17:09:24,655 - tif_api - INFO - 任务 4c45af59-78c5-4f87-90b6-0418138bdd75 状态查询成功，当前状态: 正在运行
2025-07-25 17:09:24,731 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:09:24] "GET /api/tif/status?task_id=4c45af59-78c5-4f87-90b6-0418138bdd75 HTTP/1.1" 200 -
2025-07-25 17:09:25,046 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:04<01:19,  4.00s/it]
2025-07-25 17:09:25,275 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理数据块:  10%|######7                                                                | 2/21 [00:04<00:34,  1.79s/it]
2025-07-25 17:09:26,168 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理数据块:  14%|##########1                                                            | 3/21 [00:05<00:24,  1.38s/it]
2025-07-25 17:09:26,347 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理数据块:  43%|##############################4                                        | 9/21 [00:05<00:03,  3.28it/s]
2025-07-25 17:09:26,415 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:05<00:00,  3.90it/s]
2025-07-25 17:09:26,431 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-25 17:09:26,432 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-25 17:09:26,582 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
合并结果:  14%|##########4                                                              | 3/21 [00:00<00:00, 20.18it/s]
2025-07-25 17:09:26,835 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
合并结果:  29%|####################8                                                    | 6/21 [00:00<00:01, 14.29it/s]
2025-07-25 17:09:26,976 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
合并结果:  38%|###########################8                                             | 8/21 [00:00<00:00, 14.27it/s]
2025-07-25 17:09:27,095 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
合并结果:  48%|##################################2                                     | 10/21 [00:00<00:00, 15.05it/s]
2025-07-25 17:09:27,199 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
合并结果:  57%|#########################################1                              | 12/21 [00:00<00:00, 16.19it/s]
2025-07-25 17:09:27,472 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
合并结果:  67%|################################################                        | 14/21 [00:01<00:00, 11.68it/s]
2025-07-25 17:09:27,617 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
合并结果:  76%|######################################################8                 | 16/21 [00:01<00:00, 12.31it/s]
2025-07-25 17:09:27,736 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
合并结果:  86%|#############################################################7          | 18/21 [00:01<00:00, 13.36it/s]
2025-07-25 17:09:27,850 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
合并结果:  95%|####################################################################5   | 20/21 [00:01<00:00, 14.42it/s]
2025-07-25 17:09:27,914 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
合并结果: 100%|########################################################################| 21/21 [00:01<00:00, 14.19it/s]
2025-07-25 17:09:30,538 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 10.04 秒 (17:09:29)预计总处理时间: 约 30.11 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (17:09:29)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-25 17:09:30,543 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-25 17:10:12,062 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-25 17:10:12,089 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-25 17:10:12,091 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:12,385 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:05,  3.42it/s]
2025-07-25 17:10:12,386 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:12,545 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  7.37it/s]
2025-07-25 17:10:12,546 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:12,661 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  7.78it/s]
2025-07-25 17:10:12,663 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:12,840 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:02,  6.88it/s]
2025-07-25 17:10:12,841 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:12,984 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:02,  6.90it/s]
2025-07-25 17:10:12,986 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:13,141 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  6.73it/s]
2025-07-25 17:10:13,142 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:13,378 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:02,  5.68it/s]
2025-07-25 17:10:13,380 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:14,198 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:02<00:04,  2.67it/s]
2025-07-25 17:10:14,200 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:15,625 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:03<00:07,  1.45it/s]
2025-07-25 17:10:15,630 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:17,106 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:05<00:09,  1.07it/s]
2025-07-25 17:10:17,107 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:17,633 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:05<00:07,  1.23it/s]
2025-07-25 17:10:17,633 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:18,579 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:06<00:06,  1.18it/s]
2025-07-25 17:10:18,585 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:18,775 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:06<00:04,  1.53it/s]
2025-07-25 17:10:18,776 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:19,222 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:07<00:03,  1.69it/s]
2025-07-25 17:10:19,223 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:20,512 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:08<00:03,  1.25it/s]
2025-07-25 17:10:20,517 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:20,668 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:08<00:02,  1.64it/s]
2025-07-25 17:10:20,669 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:20,804 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:08<00:01,  2.14it/s]
2025-07-25 17:10:20,804 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:21,430 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:09<00:01,  1.94it/s]
2025-07-25 17:10:21,433 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:21,636 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:09<00:00,  2.37it/s]
2025-07-25 17:10:21,636 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:21,782 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 1/3 写入进度: 100%|###############################################################| 21/21 [00:09<00:00,  2.95it/s]
2025-07-25 17:10:21,785 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:21,788 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:10:26,093 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:10:26,104 - tif_api - INFO - 查询任务: 4c45af59-78c5-4f87-90b6-0418138bdd75
2025-07-25 17:10:26,120 - tif_api - INFO - 任务 4c45af59-78c5-4f87-90b6-0418138bdd75 状态查询成功，当前状态: 正在运行
2025-07-25 17:10:26,148 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:10:26] "GET /api/tif/status?task_id=4c45af59-78c5-4f87-90b6-0418138bdd75 HTTP/1.1" 200 -
2025-07-25 17:11:22,204 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理波段:  33%|########################3                                                | 1/3 [01:51<03:43, 111.66s/it]
2025-07-25 17:11:26,289 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:11:26,338 - tif_api - INFO - 查询任务: 4c45af59-78c5-4f87-90b6-0418138bdd75
2025-07-25 17:11:26,408 - tif_api - INFO - 任务 4c45af59-78c5-4f87-90b6-0418138bdd75 状态查询成功，当前状态: 正在运行
2025-07-25 17:11:26,428 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:11:26] "GET /api/tif/status?task_id=4c45af59-78c5-4f87-90b6-0418138bdd75 HTTP/1.1" 200 -
2025-07-25 17:11:34,224 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-25 17:11:34,259 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-25 17:11:34,261 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:34,982 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:14,  1.39it/s]
2025-07-25 17:11:34,983 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:35,474 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:01<00:11,  1.71it/s]
2025-07-25 17:11:35,474 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:36,199 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:11,  1.54it/s]
2025-07-25 17:11:36,199 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:36,418 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:02<00:08,  2.08it/s]
2025-07-25 17:11:36,419 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:36,846 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:02<00:07,  2.17it/s]
2025-07-25 17:11:36,848 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:37,137 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:06,  2.48it/s]
2025-07-25 17:11:37,138 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:37,439 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:03<00:05,  2.70it/s]
2025-07-25 17:11:37,440 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:37,753 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:03<00:04,  2.84it/s]
2025-07-25 17:11:37,753 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:37,893 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:03<00:03,  3.50it/s]
2025-07-25 17:11:37,894 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:38,088 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:03<00:02,  3.88it/s]
2025-07-25 17:11:38,089 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:38,217 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:03<00:02,  4.58it/s]
2025-07-25 17:11:38,218 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:38,440 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:04<00:01,  4.55it/s]
2025-07-25 17:11:38,441 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:38,608 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:04<00:01,  4.91it/s]
2025-07-25 17:11:38,608 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:39,115 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:04<00:02,  3.38it/s]
2025-07-25 17:11:39,115 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:39,302 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:05<00:01,  3.80it/s]
2025-07-25 17:11:39,303 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:41,269 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:07<00:03,  1.29it/s]
2025-07-25 17:11:41,270 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:44,975 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:10<00:06,  1.66s/it]
2025-07-25 17:11:44,984 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:11:50,986 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:16<00:08,  2.97s/it]
2025-07-25 17:11:51,011 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:12:01,900 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:27<00:10,  5.35s/it]
2025-07-25 17:12:01,910 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:12:12,190 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:37<00:06,  6.83s/it]
2025-07-25 17:12:12,192 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:12:13,686 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:39<00:00,  5.23s/it]
2025-07-25 17:12:13,686 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:12:13,688 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:12:26,933 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:12:26,971 - tif_api - INFO - 查询任务: 4c45af59-78c5-4f87-90b6-0418138bdd75
2025-07-25 17:12:26,997 - tif_api - INFO - 任务 4c45af59-78c5-4f87-90b6-0418138bdd75 状态查询成功，当前状态: 正在运行
2025-07-25 17:12:27,176 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:12:27] "GET /api/tif/status?task_id=4c45af59-78c5-4f87-90b6-0418138bdd75 HTTP/1.1" 200 -
2025-07-25 17:13:27,338 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:13:27,339 - tif_api - INFO - 查询任务: 4c45af59-78c5-4f87-90b6-0418138bdd75
2025-07-25 17:13:27,342 - tif_api - INFO - 任务 4c45af59-78c5-4f87-90b6-0418138bdd75 状态查询成功，当前状态: 正在运行
2025-07-25 17:13:27,344 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:13:27] "GET /api/tif/status?task_id=4c45af59-78c5-4f87-90b6-0418138bdd75 HTTP/1.1" 200 -
2025-07-25 17:14:05,561 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [04:35<02:22, 142.07s/it]
2025-07-25 17:14:25,544 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-25 17:14:25,578 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-25 17:14:25,579 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:26,417 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:16,  1.20it/s]
2025-07-25 17:14:26,418 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:26,703 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:01<00:09,  1.96it/s]
2025-07-25 17:14:26,704 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:27,250 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:09,  1.90it/s]
2025-07-25 17:14:27,251 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:27,627 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:02<00:07,  2.14it/s]
2025-07-25 17:14:27,627 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:27,783 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:14:27,792 - tif_api - INFO - 查询任务: 4c45af59-78c5-4f87-90b6-0418138bdd75
2025-07-25 17:14:27,834 - tif_api - INFO - 任务 4c45af59-78c5-4f87-90b6-0418138bdd75 状态查询成功，当前状态: 正在运行
2025-07-25 17:14:27,895 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:14:27] "GET /api/tif/status?task_id=4c45af59-78c5-4f87-90b6-0418138bdd75 HTTP/1.1" 200 -
2025-07-25 17:14:28,325 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:02<00:08,  1.81it/s]
2025-07-25 17:14:28,326 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:28,515 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:06,  2.33it/s]
2025-07-25 17:14:28,528 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:28,683 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:03<00:04,  2.91it/s]
2025-07-25 17:14:28,684 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:28,814 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:03<00:03,  3.63it/s]
2025-07-25 17:14:28,814 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:28,915 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:03<00:02,  4.53it/s]
2025-07-25 17:14:28,916 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:29,026 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:03<00:01,  7.00it/s]
2025-07-25 17:14:29,027 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:29,132 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:03<00:00,  9.36it/s]
2025-07-25 17:14:29,133 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:29,258 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 15/21 [00:03<00:00, 11.02it/s]
2025-07-25 17:14:29,261 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:29,473 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [00:03<00:00, 10.38it/s]
2025-07-25 17:14:29,476 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:29,801 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度:  90%|#########################################################      | 19/21 [00:04<00:00,  8.42it/s]
2025-07-25 17:14:29,807 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:30,147 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
波段 3/3 写入进度: 100%|###############################################################| 21/21 [00:04<00:00,  7.36it/s]
2025-07-25 17:14:30,155 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:14:30,160 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - [A
2025-07-25 17:15:28,465 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:15:28,479 - tif_api - INFO - 查询任务: 4c45af59-78c5-4f87-90b6-0418138bdd75
2025-07-25 17:15:28,497 - tif_api - INFO - 任务 4c45af59-78c5-4f87-90b6-0418138bdd75 状态查询成功，当前状态: 正在运行
2025-07-25 17:15:28,501 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:15:28] "GET /api/tif/status?task_id=4c45af59-78c5-4f87-90b6-0418138bdd75 HTTP/1.1" 200 -
2025-07-25 17:16:28,618 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:16:28,620 - tif_api - INFO - 查询任务: 4c45af59-78c5-4f87-90b6-0418138bdd75
2025-07-25 17:16:28,624 - tif_api - INFO - 任务 4c45af59-78c5-4f87-90b6-0418138bdd75 状态查询成功，当前状态: 正在运行
2025-07-25 17:16:28,626 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:16:28] "GET /api/tif/status?task_id=4c45af59-78c5-4f87-90b6-0418138bdd75 HTTP/1.1" 200 -
2025-07-25 17:16:37,710 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [07:07<00:00, 146.67s/it]
2025-07-25 17:16:37,712 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [07:07<00:00, 142.39s/it]
2025-07-25 17:17:28,787 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:17:28,828 - tif_api - INFO - 查询任务: 4c45af59-78c5-4f87-90b6-0418138bdd75
2025-07-25 17:17:28,863 - tif_api - INFO - 任务 4c45af59-78c5-4f87-90b6-0418138bdd75 状态查询成功，当前状态: 正在运行
2025-07-25 17:17:28,921 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:17:28] "GET /api/tif/status?task_id=4c45af59-78c5-4f87-90b6-0418138bdd75 HTTP/1.1" 200 -
2025-07-25 17:18:29,289 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:18:29,314 - tif_api - INFO - 查询任务: 4c45af59-78c5-4f87-90b6-0418138bdd75
2025-07-25 17:18:29,346 - tif_api - INFO - 任务 4c45af59-78c5-4f87-90b6-0418138bdd75 状态查询成功，当前状态: 正在运行
2025-07-25 17:18:29,375 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:18:29] "GET /api/tif/status?task_id=4c45af59-78c5-4f87-90b6-0418138bdd75 HTTP/1.1" 200 -
2025-07-25 17:18:34,227 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 428.40 秒 (17:16:38)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (17:16:38)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-25 17:18:34,237 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-25 17:18:34,798 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.79it/s]
2025-07-25 17:18:34,864 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.60it/s]
2025-07-25 17:18:34,958 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-25 17:18:50,116 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-25 17:18:50,119 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-25 17:18:51,472 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:01<00:19,  1.48it/s]
2025-07-25 17:18:51,643 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:13,  2.15it/s]
2025-07-25 17:18:51,709 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 19.52it/s]
2025-07-25 17:18:56,420 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-25 17:18:56,421 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-25 17:18:57,074 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
写入多边形:   6%|####                                                                   | 2/35 [00:00<00:10,  3.07it/s]
2025-07-25 17:18:58,511 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
写入多边形:  31%|######################                                                | 11/35 [00:02<00:04,  5.49it/s]
2025-07-25 17:18:58,516 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - ERROR - 
写入多边形: 100%|######################################################################| 35/35 [00:02<00:00, 16.71it/s]
2025-07-25 17:18:58,766 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 处理完成，耗时: 634.87 秒 (10.58 分钟)
2025-07-25 17:18:58,767 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 处理结果: 成功
2025-07-25 17:18:58,770 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-25 17:18:58,773 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-25 17:18:59,422 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - TIF处理任务 4c45af59-78c5-4f87-90b6-0418138bdd75 执行成功
2025-07-25 17:18:59,423 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 完成时间: 2025-07-25 17:18:58
2025-07-25 17:18:59,424 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - 状态: 运行成功
2025-07-25 17:18:59,424 - tif_task_4c45af59-78c5-4f87-90b6-0418138bdd75 - INFO - ============ 任务执行结束 ============
2025-07-25 17:19:31,712 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:19:31,715 - tif_api - INFO - 查询任务: 4c45af59-78c5-4f87-90b6-0418138bdd75
2025-07-25 17:19:31,718 - tif_api - INFO - 任务 4c45af59-78c5-4f87-90b6-0418138bdd75 状态查询成功，当前状态: 运行成功
2025-07-25 17:19:31,720 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:19:31] "GET /api/tif/status?task_id=4c45af59-78c5-4f87-90b6-0418138bdd75 HTTP/1.1" 200 -
2025-07-25 17:19:35,243 - geo_task_6a751591-8b66-4278-8098-990307409110 - INFO - GeoServer发布任务 6a751591-8b66-4278-8098-990307409110 开始执行
2025-07-25 17:19:35,244 - geo_task_6a751591-8b66-4278-8098-990307409110 - INFO - 开始时间: 2025-07-25 17:19:34
2025-07-25 17:19:35,245 - geo_task_6a751591-8b66-4278-8098-990307409110 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-25 17:19:35,247 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-25 17:19:35,247 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-25 17:19:35,028 - geo_publisher - INFO - 启动GeoTIFF发布任务 6a751591-8b66-4278-8098-990307409110: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-25 17:19:35,491 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:19:35] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-25 17:19:35,565 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:19:35] "GET /api/geo/status?task_id=6a751591-8b66-4278-8098-990307409110 HTTP/1.1" 200 -
2025-07-25 17:19:45,194 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-25 17:19:45,196 - root - INFO - 未提供存储名称，使用文件名: 20250705171602
2025-07-25 17:19:45,196 - root - INFO - 未提供图层名称，使用存储名称: 20250705171602
2025-07-25 17:19:45,331 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171602\20250705171602.tif
2025-07-25 17:20:35,598 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:20:35] "GET /api/geo/status?task_id=6a751591-8b66-4278-8098-990307409110 HTTP/1.1" 200 -
2025-07-25 17:21:35,616 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:21:35] "GET /api/geo/status?task_id=6a751591-8b66-4278-8098-990307409110 HTTP/1.1" 200 -
2025-07-25 17:22:35,633 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:22:35] "GET /api/geo/status?task_id=6a751591-8b66-4278-8098-990307409110 HTTP/1.1" 200 -
2025-07-25 17:23:16,894 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 到 'testodm:20250705171602'
2025-07-25 17:23:17,085 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171602
2025-07-25 17:23:17,086 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 成功发布为图层 'testodm:20250705171602'
2025-07-25 17:23:17,090 - geo_task_6a751591-8b66-4278-8098-990307409110 - INFO - GeoServer发布任务 6a751591-8b66-4278-8098-990307409110 执行成功
2025-07-25 17:23:17,091 - geo_task_6a751591-8b66-4278-8098-990307409110 - INFO - 完成时间: 2025-07-25 17:23:17
2025-07-25 17:23:17,092 - geo_task_6a751591-8b66-4278-8098-990307409110 - INFO - 状态: 发布成功
2025-07-25 17:23:35,536 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:23:35] "GET /api/geo/status?task_id=6a751591-8b66-4278-8098-990307409110 HTTP/1.1" 200 -
2025-07-25 17:34:47,202 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:34:47,203 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:34:47,227 - map_api - INFO - 找到 3 个目录
2025-07-25 17:34:47,230 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:34:47,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:34:47,262 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:34:47,279 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:34:47,280 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:34:47,285 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:34:47,286 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:34:47,288 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:34:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:34:49,200 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:34:49,201 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:34:49,210 - map_api - INFO - 找到 3 个目录
2025-07-25 17:34:49,212 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:34:49,219 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:34:49,220 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:34:49,228 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:34:49,229 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:34:49,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:34:49,237 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:34:49,240 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:34:49] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:35:24,180 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:35:24,184 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:35:24,191 - map_api - INFO - 找到 3 个目录
2025-07-25 17:35:24,192 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:35:24,215 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:35:24,217 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:35:24,221 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:35:24,223 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:35:24,227 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:35:24,229 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:35:24,232 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:35:24] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:35:52,395 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:35:52,396 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:35:52,402 - map_api - INFO - 找到 3 个目录
2025-07-25 17:35:52,403 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:35:52,431 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:35:52,432 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:35:52,462 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:35:52,465 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:35:52,470 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:35:52,471 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:35:52,472 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:35:52] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:38:27,229 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:38:27,231 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:38:27,244 - map_api - INFO - 找到 3 个目录
2025-07-25 17:38:27,246 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:38:27,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:38:27,255 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:38:27,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:38:27,271 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:38:27,290 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:38:27,293 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:38:27,297 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:38:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:38:42,242 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:38:42,246 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:38:42,253 - map_api - INFO - 找到 3 个目录
2025-07-25 17:38:42,254 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:38:42,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:38:42,262 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:38:42,285 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:38:42,286 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:38:42,292 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:38:42,295 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:38:42,297 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:38:42] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:38:57,243 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:38:57,246 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:38:57,269 - map_api - INFO - 找到 3 个目录
2025-07-25 17:38:57,278 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:38:57,284 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:38:57,286 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:38:57,292 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:38:57,294 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:38:57,298 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:38:57,299 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:38:57,301 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:38:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:38:58,948 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:38:58,950 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:38:58,958 - map_api - INFO - 找到 3 个目录
2025-07-25 17:38:58,959 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:38:58,968 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:38:58,969 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:38:58,984 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:38:58,985 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:38:58,990 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:38:58,992 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:38:58,993 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:38:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:39:24,284 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:39:24,285 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:39:24,309 - map_api - INFO - 找到 3 个目录
2025-07-25 17:39:24,310 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:39:24,339 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:39:24,342 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:39:24,371 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:39:24,373 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:39:24,401 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:39:24,402 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:39:24,404 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:39:24] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:39:26,070 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:39:26,072 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:39:26,081 - map_api - INFO - 找到 3 个目录
2025-07-25 17:39:26,082 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:39:26,089 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:39:26,094 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:39:26,116 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:39:26,117 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:39:26,146 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:39:26,148 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:39:26,152 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:39:26] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:39:41,073 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:39:41,075 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:39:41,084 - map_api - INFO - 找到 3 个目录
2025-07-25 17:39:41,087 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:39:41,093 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:39:41,094 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:39:41,105 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:39:41,107 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:39:41,114 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:39:41,123 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:39:41,126 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:39:41] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:39:56,074 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:39:56,075 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:39:56,090 - map_api - INFO - 找到 3 个目录
2025-07-25 17:39:56,092 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:39:56,101 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:39:56,105 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:39:56,133 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:39:56,135 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:39:56,138 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:39:56,139 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:39:56,140 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:39:56] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:40:11,545 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:40:11,546 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:40:11,557 - map_api - INFO - 找到 3 个目录
2025-07-25 17:40:11,560 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:40:11,566 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:40:11,569 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:40:11,578 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:40:11,580 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:40:11,587 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:40:11,591 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:40:11,594 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:40:11] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:40:23,502 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:40:23,504 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:40:23,513 - map_api - INFO - 找到 3 个目录
2025-07-25 17:40:23,514 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:40:23,520 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:40:23,521 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:40:23,525 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:40:23,533 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:40:23,560 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-25 17:40:23,561 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:40:23,562 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:40:23] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:40:26,067 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:40:26,069 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:40:26,091 - map_api - INFO - 找到 3 个目录
2025-07-25 17:40:26,093 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:40:26,096 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:40:26,097 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:40:26,119 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:40:26,121 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:40:26,124 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-25 17:40:26,125 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:40:26,126 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:40:26] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:40:35,461 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:40:35,464 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:40:35,475 - map_api - INFO - 找到 3 个目录
2025-07-25 17:40:35,476 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:40:35,490 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:40:35,498 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:40:35,508 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:40:35,509 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:40:35,518 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-25 17:40:35,520 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:40:35,523 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:40:35] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:40:41,061 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:40:41,062 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:40:41,069 - map_api - INFO - 找到 3 个目录
2025-07-25 17:40:41,070 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:40:41,074 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:40:41,075 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:40:41,079 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:40:41,080 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:40:41,084 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-25 17:40:41,085 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:40:41,086 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:40:41] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:40:43,096 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:40:43,097 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:40:43,105 - map_api - INFO - 找到 3 个目录
2025-07-25 17:40:43,107 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:40:43,113 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:40:43,114 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:40:43,118 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:40:43,122 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:40:43,127 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-25 17:40:43,128 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:40:43,129 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:40:43] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:40:51,068 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:40:51,071 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:40:51,082 - map_api - INFO - 找到 3 个目录
2025-07-25 17:40:51,083 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:40:51,110 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:40:51,111 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:40:51,125 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:40:51,126 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:40:51,141 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-25 17:40:51,141 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:40:51,143 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:40:51] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:40:56,073 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:40:56,076 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:40:56,087 - map_api - INFO - 找到 3 个目录
2025-07-25 17:40:56,115 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:40:56,125 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:40:56,126 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:40:56,150 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:40:56,155 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:40:56,159 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-25 17:40:56,161 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:40:56,163 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:40:56] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:40:57,179 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:40:57,183 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:40:57,191 - map_api - INFO - 找到 3 个目录
2025-07-25 17:40:57,192 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:40:57,207 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:40:57,208 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:40:57,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:40:57,231 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:40:57,246 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-25 17:40:57,248 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:40:57,249 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:40:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:41:04,420 - batch_executor - INFO - 启动任务 f9c636f8-b5b6-4ff9-b94f-4429f88a8cd8: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
2025-07-25 17:41:04,435 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:41:04] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-25 17:41:04,457 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:41:04] "GET /api/batch/status?task_id=f9c636f8-b5b6-4ff9-b94f-4429f88a8cd8 HTTP/1.1" 200 -
2025-07-25 17:41:05,495 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:41:05,496 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:41:05,508 - map_api - INFO - 找到 3 个目录
2025-07-25 17:41:05,510 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:41:05,515 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:41:05,516 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:41:05,524 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:41:05,526 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:41:05,553 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-25 17:41:05,556 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:41:05,560 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:41:05] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:41:11,068 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:41:11,069 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:41:11,076 - map_api - INFO - 找到 3 个目录
2025-07-25 17:41:11,078 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:41:11,083 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:41:11,085 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:41:11,093 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:41:11,095 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:41:11,101 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-25 17:41:11,103 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:41:11,104 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:41:11] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:41:26,063 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:41:26,065 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:41:26,072 - map_api - INFO - 找到 3 个目录
2025-07-25 17:41:26,077 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:41:26,082 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:41:26,083 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:41:26,087 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:41:26,089 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:41:26,095 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-25 17:41:26,096 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:41:26,098 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:41:26] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:41:28,947 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:41:29,029 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:41:29,042 - map_api - INFO - 找到 3 个目录
2025-07-25 17:41:29,044 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:41:29,052 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:41:29,058 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:41:29,066 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:41:29,068 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:41:29,077 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-25 17:41:29,081 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:41:29,084 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:41:29] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:41:38,909 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:41:38,910 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:41:38,930 - map_api - INFO - 找到 3 个目录
2025-07-25 17:41:38,935 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:41:38,941 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:41:38,942 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:41:38,971 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:41:38,973 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:41:39,003 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-25 17:41:39,004 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:41:39,006 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:41:39] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:41:41,065 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:41:41,067 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:41:41,096 - map_api - INFO - 找到 3 个目录
2025-07-25 17:41:41,098 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:41:41,102 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:41:41,105 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:41:41,110 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:41:41,111 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:41:41,125 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-25 17:41:41,126 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:41:41,128 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:41:41] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:41:51,996 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:41:51,999 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:41:52,013 - map_api - INFO - 找到 3 个目录
2025-07-25 17:41:52,019 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:41:52,028 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:41:52,041 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:41:52,054 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:41:52,057 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:41:52,065 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-25 17:41:52,066 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:41:52,068 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:41:52] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:41:56,072 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:41:56,075 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:41:56,087 - map_api - INFO - 找到 3 个目录
2025-07-25 17:41:56,091 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:41:56,098 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:41:56,100 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:41:56,107 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:41:56,108 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:41:56,132 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-25 17:41:56,134 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:41:56,136 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:41:56] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:41:58,612 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:41:58,613 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:41:58,721 - map_api - INFO - 找到 3 个目录
2025-07-25 17:41:58,723 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:41:58,728 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:41:58,731 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:41:58,738 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:41:58,739 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:41:58,746 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-25 17:41:58,747 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:41:58,748 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:41:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:42:04,549 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:42:04] "GET /api/batch/status?task_id=f9c636f8-b5b6-4ff9-b94f-4429f88a8cd8 HTTP/1.1" 200 -
2025-07-25 17:42:11,547 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:42:11,549 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:42:11,562 - map_api - INFO - 找到 3 个目录
2025-07-25 17:42:11,564 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:42:11,570 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:42:11,572 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:42:11,580 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:42:11,582 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:42:11,588 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-25 17:42:11,592 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:42:11,595 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:42:11] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:42:17,527 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-25 17:42:17,530 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-25 17:42:17,532 - tif_api - INFO - 输入文件大小: 237.52 MB
2025-07-25 17:42:17,534 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-25 17:42:17,546 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-25 17:42:17,555 - tif_api - INFO - 黑色阈值: 0
2025-07-25 17:42:17,559 - tif_api - INFO - 白色阈值: 255
2025-07-25 17:42:17,560 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-25 17:42:17,565 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - ============ TIF处理任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 开始执行 ============
2025-07-25 17:42:17,566 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 开始时间: 2025-07-25 17:42:17
2025-07-25 17:42:17,567 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-25 17:42:17,569 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 系统信息:
2025-07-25 17:42:17,569 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO -   操作系统: Windows 10.0.19045
2025-07-25 17:42:17,571 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO -   Python版本: 3.8.20
2025-07-25 17:42:17,571 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO -   GDAL版本: 3.9.2
2025-07-25 17:42:17,572 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO -   GPU可用: 否
2025-07-25 17:42:17,573 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 检查参数有效性...
2025-07-25 17:42:17,573 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-25 17:42:17,576 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 开始执行TIF处理流程...
2025-07-25 17:42:17,577 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-25 17:42:17,578 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-25 17:42:17,565 - tif_executor - INFO - 启动TIF处理任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-25 17:42:17,578 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-25 17:42:17,579 - tif_api - INFO - 异步任务启动成功，任务ID: a82f6a2a-a3df-489f-84b1-ff314ddf6191
2025-07-25 17:42:17,581 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:42:17] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-25 17:42:17,609 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:42:17,610 - tif_api - INFO - 查询任务: a82f6a2a-a3df-489f-84b1-ff314ddf6191
2025-07-25 17:42:17,613 - tif_api - INFO - 任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 状态查询成功，当前状态: 正在运行
2025-07-25 17:42:17,614 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:42:17] "GET /api/tif/status?task_id=a82f6a2a-a3df-489f-84b1-ff314ddf6191 HTTP/1.1" 200 -
2025-07-25 17:42:21,833 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (17:42:21)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-25 17:42:21,875 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-25 17:42:22,264 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:06,  2.90it/s]
2025-07-25 17:42:22,372 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理数据块:  43%|##############################4                                        | 9/21 [00:00<00:00, 24.97it/s]
2025-07-25 17:42:22,517 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理数据块:  67%|##############################################6                       | 14/21 [00:00<00:00, 28.53it/s]
2025-07-25 17:42:22,729 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理数据块:  90%|###############################################################3      | 19/21 [00:00<00:00, 26.30it/s]
2025-07-25 17:42:22,954 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:01<00:00, 20.30it/s]
2025-07-25 17:42:22,955 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-25 17:42:22,956 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-25 17:42:23,031 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 279.19it/s]
2025-07-25 17:42:24,377 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.62 秒 (17:42:24)预计总处理时间: 约 7.87 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (17:42:24)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-25 17:42:24,379 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-25 17:42:26,550 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:42:26,553 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:42:26,571 - map_api - INFO - 找到 3 个目录
2025-07-25 17:42:26,572 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:42:26,598 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:42:26,600 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:42:26,639 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:42:26,641 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:42:26,648 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:42:26,656 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:42:26,659 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:42:26] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:42:33,253 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-25 17:42:33,257 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-25 17:42:33,258 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:33,843 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:11,  1.71it/s]
2025-07-25 17:42:33,843 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:34,012 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:03,  4.67it/s]
2025-07-25 17:42:34,013 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:34,117 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  5.67it/s]
2025-07-25 17:42:34,118 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:34,240 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:02,  6.30it/s]
2025-07-25 17:42:34,241 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:34,393 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:01<00:02,  6.40it/s]
2025-07-25 17:42:34,394 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:34,537 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  6.54it/s]
2025-07-25 17:42:34,537 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:34,671 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:01,  6.79it/s]
2025-07-25 17:42:34,672 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:34,810 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  6.92it/s]
2025-07-25 17:42:34,811 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:34,955 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  6.91it/s]
2025-07-25 17:42:34,956 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:35,083 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:01,  7.16it/s]
2025-07-25 17:42:35,083 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:35,217 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:01<00:01,  7.24it/s]
2025-07-25 17:42:35,218 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:35,361 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:02<00:01,  7.16it/s]
2025-07-25 17:42:35,363 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:35,506 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:00,  7.08it/s]
2025-07-25 17:42:35,507 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:35,667 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:02<00:00,  6.79it/s]
2025-07-25 17:42:35,669 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:35,803 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:02<00:00,  6.95it/s]
2025-07-25 17:42:35,804 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:35,938 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:02<00:00,  7.08it/s]
2025-07-25 17:42:35,939 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:36,072 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:02<00:00,  7.19it/s]
2025-07-25 17:42:36,073 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:36,214 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:02<00:00,  7.15it/s]
2025-07-25 17:42:36,214 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:36,342 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:03<00:00,  7.35it/s]
2025-07-25 17:42:36,342 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:36,470 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 1/3 写入进度: 100%|###############################################################| 21/21 [00:03<00:00,  7.47it/s]
2025-07-25 17:42:36,471 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:36,472 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:42:41,552 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:42:41,553 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:42:41,565 - map_api - INFO - 找到 3 个目录
2025-07-25 17:42:41,567 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:42:42,562 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:42:42,565 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:42:42,572 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:42:42,573 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:42:42,579 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:42:42,581 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:42:42,586 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:42:42] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:42:56,543 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:42:56,545 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:42:56,555 - map_api - INFO - 找到 3 个目录
2025-07-25 17:42:56,557 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:42:56,564 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:42:56,565 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:42:56,572 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:42:56,573 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:42:56,581 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:42:56,583 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:42:56,585 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:42:56] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:43:11,069 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:43:11,071 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:43:11,080 - map_api - INFO - 找到 3 个目录
2025-07-25 17:43:11,082 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:43:11,087 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:43:11,089 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:43:11,092 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:43:11,093 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:43:11,098 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:43:11,099 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:43:11,104 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:43:11] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:43:17,764 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:43:17,771 - tif_api - INFO - 查询任务: a82f6a2a-a3df-489f-84b1-ff314ddf6191
2025-07-25 17:43:18,419 - tif_api - INFO - 任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 状态查询成功，当前状态: 正在运行
2025-07-25 17:43:18,425 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:43:18] "GET /api/tif/status?task_id=a82f6a2a-a3df-489f-84b1-ff314ddf6191 HTTP/1.1" 200 -
2025-07-25 17:43:26,070 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:43:26,074 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:43:26,090 - map_api - INFO - 找到 3 个目录
2025-07-25 17:43:26,091 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:43:26,101 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:43:26,106 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:43:26,114 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:43:26,116 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:43:26,127 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:43:26,128 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:43:26,132 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:43:26] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:43:32,985 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:43:32,991 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:43:33,017 - map_api - INFO - 找到 3 个目录
2025-07-25 17:43:33,035 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:43:33,050 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:43:33,057 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:43:33,067 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:43:33,068 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:43:33,075 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:43:33,077 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:43:33,079 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:43:33] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:43:42,532 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:18<02:36, 78.15s/it]
2025-07-25 17:43:42,590 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:43:42,591 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:43:42,600 - map_api - INFO - 找到 3 个目录
2025-07-25 17:43:42,607 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:43:42,618 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:43:42,619 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:43:42,649 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:43:42,653 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:43:42,680 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:43:42,683 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:43:42,685 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:43:42] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:43:52,615 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-25 17:43:52,628 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-25 17:43:52,629 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:53,076 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:08,  2.24it/s]
2025-07-25 17:43:53,077 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:53,380 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:06,  2.76it/s]
2025-07-25 17:43:53,382 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:53,962 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:08,  2.16it/s]
2025-07-25 17:43:53,967 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:54,351 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:01<00:07,  2.31it/s]
2025-07-25 17:43:54,354 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:54,669 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:02<00:06,  2.55it/s]
2025-07-25 17:43:54,673 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:54,978 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:05,  2.75it/s]
2025-07-25 17:43:54,979 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:55,276 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:02<00:04,  2.92it/s]
2025-07-25 17:43:55,277 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:55,573 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:02<00:04,  3.05it/s]
2025-07-25 17:43:55,574 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:55,772 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:03<00:03,  3.48it/s]
2025-07-25 17:43:55,774 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:55,950 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:03<00:02,  3.94it/s]
2025-07-25 17:43:55,951 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:56,095 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:03<00:02,  4.54it/s]
2025-07-25 17:43:56,096 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:56,304 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:03<00:01,  4.61it/s]
2025-07-25 17:43:56,305 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:56,480 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:03<00:01,  4.90it/s]
2025-07-25 17:43:56,481 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:56,679 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:04<00:01,  4.94it/s]
2025-07-25 17:43:56,682 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:56,919 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:04<00:01,  4.66it/s]
2025-07-25 17:43:56,921 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:43:59,907 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:07<00:05,  1.05s/it]
2025-07-25 17:43:59,909 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:44:03,795 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:44:03,965 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:44:04,026 - map_api - INFO - 找到 3 个目录
2025-07-25 17:44:04,048 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:44:04,073 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:44:04,078 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:44:04,101 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:44:04,117 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:44:04,224 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:44:04,258 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:44:04,333 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:44:04] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:44:04,375 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:44:04,389 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:44:04,410 - map_api - INFO - 找到 3 个目录
2025-07-25 17:44:04,421 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:44:04,428 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:44:04,436 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:44:04,445 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:44:04,449 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:44:04,466 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:44:04,468 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:44:04,472 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:44:04] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:44:05,526 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:12<00:09,  2.42s/it]
2025-07-25 17:44:05,527 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:44:10,976 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:18<00:09,  3.33s/it]
2025-07-25 17:44:10,977 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:44:17,262 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:24<00:08,  4.22s/it]
2025-07-25 17:44:17,263 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:44:18,508 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:44:18,509 - tif_api - INFO - 查询任务: a82f6a2a-a3df-489f-84b1-ff314ddf6191
2025-07-25 17:44:18,514 - tif_api - INFO - 任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 状态查询成功，当前状态: 正在运行
2025-07-25 17:44:18,516 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:44:18] "GET /api/tif/status?task_id=a82f6a2a-a3df-489f-84b1-ff314ddf6191 HTTP/1.1" 200 -
2025-07-25 17:44:23,914 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:31<00:04,  4.95s/it]
2025-07-25 17:44:23,976 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:44:26,219 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:33<00:00,  4.16s/it]
2025-07-25 17:44:26,226 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:44:26,227 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:44:27,583 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:44:27,584 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:44:27,593 - map_api - INFO - 找到 3 个目录
2025-07-25 17:44:27,595 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:44:27,600 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:44:27,601 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:44:27,605 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:44:27,606 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:44:27,610 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:44:27,611 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:44:27,613 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:44:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:44:50,924 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:44:50,928 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:44:50,948 - map_api - INFO - 找到 3 个目录
2025-07-25 17:44:50,950 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:44:50,957 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:44:50,967 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:44:50,977 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:44:50,981 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:44:50,987 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:44:50,991 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:44:50,999 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:44:50] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:45:04,984 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:45:04,985 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:45:04,993 - map_api - INFO - 找到 3 个目录
2025-07-25 17:45:04,994 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:45:05,001 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:45:05,002 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:45:05,009 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:45:05,013 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:45:05,020 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:45:05,023 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:45:05,064 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:45:05] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:45:18,528 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:45:18,533 - tif_api - INFO - 查询任务: a82f6a2a-a3df-489f-84b1-ff314ddf6191
2025-07-25 17:45:18,538 - tif_api - INFO - 任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 状态查询成功，当前状态: 正在运行
2025-07-25 17:45:18,546 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:45:18] "GET /api/tif/status?task_id=a82f6a2a-a3df-489f-84b1-ff314ddf6191 HTTP/1.1" 200 -
2025-07-25 17:45:51,553 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:45:51,554 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:45:51,583 - map_api - INFO - 找到 3 个目录
2025-07-25 17:45:51,584 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:45:51,589 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:45:51,591 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:45:51,612 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:45:51,613 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:45:51,628 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:45:51,629 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:45:51,630 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:45:51] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:45:57,047 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [03:32<01:51, 111.31s/it]
2025-07-25 17:46:19,894 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:46:19,903 - tif_api - INFO - 查询任务: a82f6a2a-a3df-489f-84b1-ff314ddf6191
2025-07-25 17:46:19,912 - tif_api - INFO - 任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 状态查询成功，当前状态: 正在运行
2025-07-25 17:46:19,922 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:46:19] "GET /api/tif/status?task_id=a82f6a2a-a3df-489f-84b1-ff314ddf6191 HTTP/1.1" 200 -
2025-07-25 17:46:20,927 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-25 17:46:20,931 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-25 17:46:20,931 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:46:21,207 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:05,  3.63it/s]
2025-07-25 17:46:21,207 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:46:21,371 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:03,  4.76it/s]
2025-07-25 17:46:21,372 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:46:21,607 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:03,  4.51it/s]
2025-07-25 17:46:21,607 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:46:21,765 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:03,  5.08it/s]
2025-07-25 17:46:21,766 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:46:22,181 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:01<00:04,  3.63it/s]
2025-07-25 17:46:22,182 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:46:22,320 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  5.85it/s]
2025-07-25 17:46:22,320 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:46:22,519 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  7.09it/s]
2025-07-25 17:46:22,535 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:46:22,627 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 3/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  7.50it/s]
2025-07-25 17:46:22,628 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:46:22,729 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:00, 11.92it/s]
2025-07-25 17:46:22,730 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:46:22,866 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
波段 3/3 写入进度:  76%|################################################               | 16/21 [00:01<00:00, 14.70it/s]
2025-07-25 17:46:22,867 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:46:22,959 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - [A
2025-07-25 17:46:51,554 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:46:51,557 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:46:51,602 - map_api - INFO - 找到 3 个目录
2025-07-25 17:46:51,606 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:46:51,621 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:46:51,622 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:46:51,638 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:46:51,638 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:46:51,643 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:46:51,643 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:46:51,648 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:46:51] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:47:19,287 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:47:19,289 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:47:19,298 - map_api - INFO - 找到 3 个目录
2025-07-25 17:47:19,301 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:47:19,312 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:47:19,348 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:47:19,363 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:47:19,370 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:47:19,379 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:47:19,382 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:47:19,390 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:47:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:47:21,175 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:47:21,177 - tif_api - INFO - 查询任务: a82f6a2a-a3df-489f-84b1-ff314ddf6191
2025-07-25 17:47:21,180 - tif_api - INFO - 任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 状态查询成功，当前状态: 正在运行
2025-07-25 17:47:21,182 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:47:21] "GET /api/tif/status?task_id=a82f6a2a-a3df-489f-84b1-ff314ddf6191 HTTP/1.1" 200 -
2025-07-25 17:47:50,935 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:47:50,939 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:47:50,954 - map_api - INFO - 找到 3 个目录
2025-07-25 17:47:50,957 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:47:50,964 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:47:50,966 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:47:50,975 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:47:50,977 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:47:50,985 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:47:51,053 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:47:51,114 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:47:51] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:47:55,481 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:47:55,488 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:47:55,498 - map_api - INFO - 找到 3 个目录
2025-07-25 17:47:55,510 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:47:55,519 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:47:55,521 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:47:55,546 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:47:55,548 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:47:55,554 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:47:55,555 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:47:55,557 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:47:55] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:48:21,197 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:48:21,199 - tif_api - INFO - 查询任务: a82f6a2a-a3df-489f-84b1-ff314ddf6191
2025-07-25 17:48:23,564 - tif_api - INFO - 任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 状态查询成功，当前状态: 正在运行
2025-07-25 17:48:23,567 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:48:23] "GET /api/tif/status?task_id=a82f6a2a-a3df-489f-84b1-ff314ddf6191 HTTP/1.1" 200 -
2025-07-25 17:48:55,544 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:48:55,546 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:48:55,576 - map_api - INFO - 找到 3 个目录
2025-07-25 17:48:55,577 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:48:55,582 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:48:55,583 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:48:55,603 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:48:55,606 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:48:55,611 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-25 17:48:55,612 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:48:55,613 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:48:55] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 17:49:28,200 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:49:28,202 - tif_api - INFO - 查询任务: a82f6a2a-a3df-489f-84b1-ff314ddf6191
2025-07-25 17:49:29,133 - tif_api - INFO - 任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 状态查询成功，当前状态: 正在运行
2025-07-25 17:49:29,139 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:49:29] "GET /api/tif/status?task_id=a82f6a2a-a3df-489f-84b1-ff314ddf6191 HTTP/1.1" 200 -
2025-07-25 17:49:40,782 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 17:49:40,796 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 17:49:41,176 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 17:49:41,179 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:49:41] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 17:49:41,231 - map_api - INFO - 请求文件: baseMap3.json
2025-07-25 17:49:41,239 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-25 17:49:41,560 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 17:49:41,563 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3858 字节
2025-07-25 17:49:41,579 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:49:41] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 17:49:41,580 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:49:41] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-25 17:49:41,686 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-25 17:49:41,694 - map_api - INFO - 请求文件: baseMap2.json
2025-07-25 17:49:41,906 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-25 17:49:41,910 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:49:41] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-25 17:49:42,036 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-25 17:49:42,054 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:49:42] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-25 17:49:42,037 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6243 字节
2025-07-25 17:49:42,067 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:49:42] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-25 17:49:43,166 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:49:43] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 17:49:43,220 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-25 17:49:44,128 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-25 17:49:44,130 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:49:44] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-25 17:50:09,460 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [07:45<00:00, 175.74s/it]
2025-07-25 17:50:09,463 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [07:45<00:00, 155.03s/it]
2025-07-25 17:50:29,347 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:50:29,375 - tif_api - INFO - 查询任务: a82f6a2a-a3df-489f-84b1-ff314ddf6191
2025-07-25 17:50:29,793 - tif_api - INFO - 任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 状态查询成功，当前状态: 正在运行
2025-07-25 17:50:29,796 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:50:29] "GET /api/tif/status?task_id=a82f6a2a-a3df-489f-84b1-ff314ddf6191 HTTP/1.1" 200 -
2025-07-25 17:51:22,964 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 466.00 秒 (17:50:10)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (17:50:10)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-25 17:51:22,991 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-25 17:51:23,499 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.98it/s]
2025-07-25 17:51:23,500 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.97it/s]
2025-07-25 17:51:23,900 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-25 17:51:37,298 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:51:37,370 - tif_api - INFO - 查询任务: a82f6a2a-a3df-489f-84b1-ff314ddf6191
2025-07-25 17:51:37,395 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-25 17:51:37,396 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-25 17:51:37,427 - tif_api - INFO - 任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 状态查询成功，当前状态: 正在运行
2025-07-25 17:51:37,445 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:51:37] "GET /api/tif/status?task_id=a82f6a2a-a3df-489f-84b1-ff314ddf6191 HTTP/1.1" 200 -
2025-07-25 17:51:38,824 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:01<00:20,  1.40it/s]
2025-07-25 17:51:39,023 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:13,  2.00it/s]
2025-07-25 17:51:39,056 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 18.68it/s]
2025-07-25 17:51:39,735 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-25 17:51:39,737 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-25 17:51:39,754 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - ERROR - 
写入多边形: 100%|####################################################################| 35/35 [00:00<00:00, 2188.21it/s]
2025-07-25 17:51:39,963 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 处理完成，耗时: 562.38 秒 (9.37 分钟)
2025-07-25 17:51:39,964 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 处理结果: 成功
2025-07-25 17:51:39,969 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-25 17:51:39,970 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-25 17:51:39,976 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - TIF处理任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 执行成功
2025-07-25 17:51:39,977 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 完成时间: 2025-07-25 17:51:39
2025-07-25 17:51:39,977 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - 状态: 运行成功
2025-07-25 17:51:39,978 - tif_task_a82f6a2a-a3df-489f-84b1-ff314ddf6191 - INFO - ============ 任务执行结束 ============
2025-07-25 17:52:36,922 - tif_api - INFO - 收到任务状态查询请求
2025-07-25 17:52:36,923 - tif_api - INFO - 查询任务: a82f6a2a-a3df-489f-84b1-ff314ddf6191
2025-07-25 17:52:36,927 - tif_api - INFO - 任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 状态查询成功，当前状态: 运行成功
2025-07-25 17:52:36,928 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:52:36] "GET /api/tif/status?task_id=a82f6a2a-a3df-489f-84b1-ff314ddf6191 HTTP/1.1" 200 -
2025-07-25 17:52:39,403 - geo_task_d84cf149-f59c-428c-8cb7-3193edaa851e - INFO - GeoServer发布任务 d84cf149-f59c-428c-8cb7-3193edaa851e 开始执行
2025-07-25 17:52:39,406 - geo_task_d84cf149-f59c-428c-8cb7-3193edaa851e - INFO - 开始时间: 2025-07-25 17:52:39
2025-07-25 17:52:39,406 - geo_task_d84cf149-f59c-428c-8cb7-3193edaa851e - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-25 17:52:39,408 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-25 17:52:39,408 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-25 17:52:39,464 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-25 17:52:39,466 - root - INFO - 未提供存储名称，使用文件名: 20250705171602
2025-07-25 17:52:39,467 - root - INFO - 未提供图层名称，使用存储名称: 20250705171602
2025-07-25 17:52:39,510 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171602\20250705171602.tif
2025-07-25 17:52:39,402 - geo_publisher - INFO - 启动GeoTIFF发布任务 d84cf149-f59c-428c-8cb7-3193edaa851e: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-25 17:52:39,858 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:52:39] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-25 17:52:39,879 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:52:39] "GET /api/geo/status?task_id=d84cf149-f59c-428c-8cb7-3193edaa851e HTTP/1.1" 200 -
2025-07-25 17:53:39,936 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:53:39] "GET /api/geo/status?task_id=d84cf149-f59c-428c-8cb7-3193edaa851e HTTP/1.1" 200 -
2025-07-25 17:54:00,754 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 到 'testodm:20250705171602'
2025-07-25 17:54:00,831 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171602
2025-07-25 17:54:00,832 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 成功发布为图层 'testodm:20250705171602'
2025-07-25 17:54:00,835 - geo_task_d84cf149-f59c-428c-8cb7-3193edaa851e - INFO - GeoServer发布任务 d84cf149-f59c-428c-8cb7-3193edaa851e 执行成功
2025-07-25 17:54:00,837 - geo_task_d84cf149-f59c-428c-8cb7-3193edaa851e - INFO - 完成时间: 2025-07-25 17:54:00
2025-07-25 17:54:00,837 - geo_task_d84cf149-f59c-428c-8cb7-3193edaa851e - INFO - 状态: 发布成功
2025-07-25 17:54:39,955 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 17:54:39] "GET /api/geo/status?task_id=d84cf149-f59c-428c-8cb7-3193edaa851e HTTP/1.1" 200 -
2025-07-25 17:59:26,772 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 17:59:26,773 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 17:59:26,780 - map_api - INFO - 找到 3 个目录
2025-07-25 17:59:26,781 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 17:59:26,947 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 17:59:26,948 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 17:59:27,186 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 17:59:27,188 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 17:59:27,193 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 17:59:27,195 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 17:59:27,197 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 17:59:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:00:16,838 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:00:16,840 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:00:16,850 - map_api - INFO - 找到 3 个目录
2025-07-25 18:00:16,852 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:00:16,858 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:00:16,860 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:00:16,867 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:00:16,868 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:00:16,875 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:00:16,877 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:00:16,880 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:00:16] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:00:17,214 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:00:17,215 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:00:17,226 - map_api - INFO - 找到 3 个目录
2025-07-25 18:00:17,227 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:00:17,235 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:00:17,237 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:00:17,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:00:17,243 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:00:17,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:00:17,249 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:00:17,251 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:00:17] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:01:17,549 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:01:17,550 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:01:17,557 - map_api - INFO - 找到 3 个目录
2025-07-25 18:01:17,558 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:01:17,565 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:01:17,566 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:01:17,570 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:01:17,571 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:01:17,577 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:01:17,586 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:01:17,589 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:01:17] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:02:17,549 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:02:17,550 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:02:17,557 - map_api - INFO - 找到 3 个目录
2025-07-25 18:02:17,558 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:02:17,563 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:02:17,565 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:02:17,568 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:02:17,569 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:02:17,593 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:02:17,597 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:02:17,598 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:02:17] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:03:17,572 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:03:17,580 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:03:17,599 - map_api - INFO - 找到 3 个目录
2025-07-25 18:03:17,600 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:03:17,606 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:03:17,608 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:03:17,615 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:03:17,616 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:03:17,621 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:03:17,623 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:03:17,625 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:03:17] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:04:17,544 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:04:17,544 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:04:17,554 - map_api - INFO - 找到 3 个目录
2025-07-25 18:04:17,554 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:04:17,573 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:04:17,574 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:04:17,603 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:04:17,605 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:04:17,610 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:04:17,611 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:04:17,612 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:04:17] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:05:17,553 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:05:17,554 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:05:17,584 - map_api - INFO - 找到 3 个目录
2025-07-25 18:05:17,586 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:05:17,613 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:05:17,614 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:05:17,628 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:05:17,629 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:05:17,644 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:05:17,645 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:05:17,647 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:05:17] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:06:17,550 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:06:17,551 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:06:17,580 - map_api - INFO - 找到 3 个目录
2025-07-25 18:06:17,582 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:06:17,585 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:06:17,586 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:06:17,607 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:06:17,611 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:06:17,615 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:06:17,616 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:06:17,617 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:06:17] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:07:47,569 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:07:47,573 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:07:47,583 - map_api - INFO - 找到 3 个目录
2025-07-25 18:07:47,584 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:07:47,590 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:07:47,591 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:07:47,597 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:07:47,599 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:07:47,604 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:07:47,606 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:07:47,610 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:07:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:08:47,730 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:08:47,733 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:08:47,744 - map_api - INFO - 找到 3 个目录
2025-07-25 18:08:47,746 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:08:47,752 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:08:47,753 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:08:47,759 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:08:47,761 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:08:47,767 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:08:47,769 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:08:47,771 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:08:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:09:47,558 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:09:47,563 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:09:47,571 - map_api - INFO - 找到 3 个目录
2025-07-25 18:09:47,573 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:09:47,579 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:09:47,580 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:09:47,586 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:09:47,588 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:09:47,593 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:09:47,595 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:09:47,597 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:09:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:10:47,575 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:10:47,580 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:10:47,591 - map_api - INFO - 找到 3 个目录
2025-07-25 18:10:47,592 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:10:47,602 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:10:47,605 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:10:47,614 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:10:47,616 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:10:47,642 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:10:47,645 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:10:47,647 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:10:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:11:47,569 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:11:47,571 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:11:47,580 - map_api - INFO - 找到 3 个目录
2025-07-25 18:11:47,582 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:11:47,587 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:11:47,589 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:11:47,595 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:11:47,596 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:11:47,603 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:11:47,604 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:11:47,605 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:11:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:12:47,579 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:12:47,591 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:12:47,603 - map_api - INFO - 找到 3 个目录
2025-07-25 18:12:47,604 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:12:47,610 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:12:47,614 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:12:47,620 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:12:47,621 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:12:47,627 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:12:47,629 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:12:47,631 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:12:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:13:47,581 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:13:47,587 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:13:47,598 - map_api - INFO - 找到 3 个目录
2025-07-25 18:13:47,599 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:13:47,605 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:13:47,607 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:13:47,612 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:13:47,614 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:13:47,637 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:13:47,639 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:13:47,640 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:13:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:14:47,575 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:14:47,577 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:14:47,588 - map_api - INFO - 找到 3 个目录
2025-07-25 18:14:47,590 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:14:47,595 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:14:47,599 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:14:47,605 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:14:47,606 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:14:47,612 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:14:47,613 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:14:47,616 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:14:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:15:47,710 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:15:47,712 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:15:47,721 - map_api - INFO - 找到 3 个目录
2025-07-25 18:15:47,723 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:15:47,730 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:15:47,732 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:15:47,738 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:15:47,739 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:15:47,748 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:15:47,753 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:15:47,755 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:15:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:16:47,571 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:16:47,601 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:16:47,633 - map_api - INFO - 找到 3 个目录
2025-07-25 18:16:47,635 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:16:47,641 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:16:47,644 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:16:47,651 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:16:47,653 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:16:47,659 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:16:47,660 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:16:47,662 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:16:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:17:47,576 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:17:47,584 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:17:47,592 - map_api - INFO - 找到 3 个目录
2025-07-25 18:17:47,595 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:17:47,601 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:17:47,603 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:17:47,609 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:17:47,611 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:17:47,618 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:17:47,619 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:17:47,621 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:17:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:18:47,570 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:18:47,574 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:18:47,583 - map_api - INFO - 找到 3 个目录
2025-07-25 18:18:47,584 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:18:47,591 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:18:47,592 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:18:47,597 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:18:47,598 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:18:47,603 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:18:47,604 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:18:47,606 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:18:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:19:47,627 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:19:47,637 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:19:47,649 - map_api - INFO - 找到 3 个目录
2025-07-25 18:19:47,651 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:19:47,657 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:19:47,659 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:19:47,667 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:19:47,669 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:19:47,674 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:19:47,677 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:19:47,682 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:19:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:20:47,601 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:20:47,603 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:20:47,613 - map_api - INFO - 找到 3 个目录
2025-07-25 18:20:47,615 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:20:47,625 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:20:47,626 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:20:47,632 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:20:47,633 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:20:47,639 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:20:47,640 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:20:47,642 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:20:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:21:47,618 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:21:47,622 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:21:47,634 - map_api - INFO - 找到 3 个目录
2025-07-25 18:21:47,635 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:21:47,643 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:21:47,645 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:21:47,654 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:21:47,655 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:21:47,662 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:21:47,664 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:21:47,666 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:21:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:22:47,618 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:22:47,620 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:22:47,630 - map_api - INFO - 找到 3 个目录
2025-07-25 18:22:47,632 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:22:47,637 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:22:47,642 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:22:47,652 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:22:47,654 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:22:47,673 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:22:47,678 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:22:47,681 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:22:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:23:47,598 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:23:47,605 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:23:47,623 - map_api - INFO - 找到 3 个目录
2025-07-25 18:23:47,635 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:23:47,651 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:23:47,666 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:23:47,673 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:23:47,675 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:23:47,681 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:23:47,682 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:23:47,685 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:23:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:24:47,595 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:24:47,598 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:24:47,610 - map_api - INFO - 找到 3 个目录
2025-07-25 18:24:47,616 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:24:47,621 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:24:47,623 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:24:47,631 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:24:47,633 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:24:47,638 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:24:47,640 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:24:47,642 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:24:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:25:47,608 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:25:47,617 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:25:47,629 - map_api - INFO - 找到 3 个目录
2025-07-25 18:25:47,631 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:25:47,637 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:25:47,638 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:25:47,668 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:25:47,672 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:25:47,677 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:25:47,681 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:25:47,683 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:25:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:26:47,583 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:26:47,586 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:26:47,596 - map_api - INFO - 找到 3 个目录
2025-07-25 18:26:47,600 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:26:47,606 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:26:47,608 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:26:47,613 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:26:47,615 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:26:47,621 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:26:47,622 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:26:47,623 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:26:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:27:47,625 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:27:47,628 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:27:47,638 - map_api - INFO - 找到 3 个目录
2025-07-25 18:27:47,639 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:27:47,645 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:27:47,648 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:27:47,653 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:27:47,655 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:27:47,661 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:27:47,663 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:27:47,665 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:27:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:28:47,571 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:28:47,575 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:28:47,584 - map_api - INFO - 找到 3 个目录
2025-07-25 18:28:47,588 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:28:47,593 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:28:47,595 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:28:47,601 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:28:47,604 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:28:47,609 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:28:47,611 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:28:47,613 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:28:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:29:47,572 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:29:47,582 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:29:47,599 - map_api - INFO - 找到 3 个目录
2025-07-25 18:29:47,601 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:29:47,607 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:29:47,609 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:29:47,615 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:29:47,617 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:29:47,623 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:29:47,624 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:29:47,626 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:29:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:30:47,584 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:30:47,589 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:30:47,599 - map_api - INFO - 找到 3 个目录
2025-07-25 18:30:47,601 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:30:47,608 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:30:47,609 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:30:47,614 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:30:47,616 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:30:47,621 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:30:47,622 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:30:47,623 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:30:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:31:47,581 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:31:47,584 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:31:47,596 - map_api - INFO - 找到 3 个目录
2025-07-25 18:31:47,601 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:31:47,608 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:31:47,610 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:31:47,616 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:31:47,618 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:31:47,623 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:31:47,625 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:31:47,626 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:31:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:32:47,583 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:32:47,587 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:32:47,613 - map_api - INFO - 找到 3 个目录
2025-07-25 18:32:47,614 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:32:47,622 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:32:47,624 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:32:47,629 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:32:47,632 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:32:47,638 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:32:47,639 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:32:47,641 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:32:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:33:47,622 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:33:47,624 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:33:47,633 - map_api - INFO - 找到 3 个目录
2025-07-25 18:33:47,635 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:33:47,640 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:33:47,642 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:33:47,648 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:33:47,650 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:33:47,656 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:33:47,659 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:33:47,660 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:33:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:34:47,590 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:34:47,607 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:34:47,622 - map_api - INFO - 找到 3 个目录
2025-07-25 18:34:47,623 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:34:47,629 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:34:47,630 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:34:47,637 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:34:47,639 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:34:47,645 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:34:47,647 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:34:47,650 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:34:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:35:47,591 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:35:47,594 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:35:47,604 - map_api - INFO - 找到 3 个目录
2025-07-25 18:35:47,605 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:35:47,611 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:35:47,612 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:35:47,618 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:35:47,621 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:35:47,627 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:35:47,628 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:35:47,630 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:35:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:36:48,047 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:36:48,049 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:36:48,058 - map_api - INFO - 找到 3 个目录
2025-07-25 18:36:48,059 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:36:48,065 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:36:48,066 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:36:48,072 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:36:48,076 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:36:48,081 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:36:48,082 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:36:48,084 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:36:48] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-25 18:37:47,584 - map_api - INFO - 开始获取ODM任务列表
2025-07-25 18:37:47,586 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-25 18:37:47,597 - map_api - INFO - 找到 3 个目录
2025-07-25 18:37:47,598 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-25 18:37:47,603 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-25 18:37:47,607 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-25 18:37:47,612 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-25 18:37:47,614 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-25 18:37:47,620 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-25 18:37:47,622 - map_api - INFO - 获取到 3 个任务信息
2025-07-25 18:37:47,624 - werkzeug - INFO - 192.168.43.148 - - [25/Jul/2025 18:37:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
