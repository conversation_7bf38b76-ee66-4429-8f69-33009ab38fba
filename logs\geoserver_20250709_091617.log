2025-07-09 09:16:17,346 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250709_091617.log
2025-07-09 09:16:17,349 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-09 09:16:17,349 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-09 09:16:17,374 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-09 09:16:17,384 - root - INFO - === GeoServer REST API服务 ===
2025-07-09 09:16:17,384 - root - INFO - 主机: 127.0.0.1
2025-07-09 09:16:17,384 - root - INFO - 端口: 5084
2025-07-09 09:16:17,385 - root - INFO - 调试模式: 禁用
2025-07-09 09:16:17,387 - root - INFO - 启动GeoServer REST API服务，监听 127.0.0.1:5084
2025-07-09 09:16:17,401 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5084
2025-07-09 09:16:17,402 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 09:16:33,688 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-09 09:16:33,690 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 09:16:33] "GET /api/layers?workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-09 09:16:33,753 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 09:16:33] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-09 09:19:10,238 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 09:19:10] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-07-09 09:19:14,783 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 09:19:14] "GET /health HTTP/1.1" 200 -
2025-07-09 09:19:39,934 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-09 09:19:40,104 - root - INFO - 在工作区 'test_myworkspace' 中找到 5 个矢量图层
2025-07-09 09:19:40,106 - root - INFO - 在工作区 'test_myworkspace' 中找到总计 15 个图层
2025-07-09 09:19:40,108 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 09:19:40] "GET /api/all_layers?workspace=test_myworkspace HTTP/1.1" 200 -
