2025-07-11 17:30:24,359 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250711_173024.log
2025-07-11 17:30:24,363 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-11 17:30:24,364 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-11 17:30:24,388 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-11 17:30:24,403 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-11 17:30:24,404 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-11 17:30:24,421 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-11 17:30:24,429 - root - INFO - === GeoServer REST API服务 ===
2025-07-11 17:30:24,429 - root - INFO - 主机: 0.0.0.0
2025-07-11 17:30:24,430 - root - INFO - 端口: 5083
2025-07-11 17:30:24,430 - root - INFO - 调试模式: 禁用
2025-07-11 17:30:24,431 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-11 17:30:24,448 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-11 17:30:24,450 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-11 17:30:54,491 - root - ERROR - 发布Shapefile时出错: create_shp_datastore() got an unexpected keyword argument 'file_name'
2025-07-11 17:30:54,495 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:30:54] "[35m[1mGET /api/management/shapefiles/publish?file=D:/Drone_Project/geoserverapi/data/20250701/testshp/设施农用地潜力.shp&workspace=tttt&store=20250717&layer=设施农用地潜力&charset=UTF-8 HTTP/1.1[0m" 500 -
