<!--
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-07-09 08:56:51
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-07-12 16:03:01
 * @FilePath: \geoserverapi\geoserverRest\README.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# GeoServer REST API与命令行工具

## 项目介绍

GeoServer REST API与命令行工具是一个集成化解决方案，用于管理和访问GeoServer服务。该项目提供了两种使用方式：

1. **RESTful API服务**: 通过HTTP请求访问GeoServer的功能，特别是栅格数据坐标查询功能。
2. **命令行工具**: 通过命令行执行各种GeoServer操作，如创建工作区、发布图层等。

## 功能特性

### RESTful API服务

- 查询坐标点处的栅格图层
- 获取工作区中的所有图层
- 测试坐标点在特定图层中是否有有效数据
- 获取坐标点处的像素值
- 查询坐标点处的图层并获取像素值
- 执行批处理文件并监控执行状态
- 查询批处理文件执行状态
- TIF影像处理，设置NoData和提取有效区域shapefile
- TIF处理任务的异步执行和状态监控
- GeoServer图层的异步发布和状态监控

### GeoServer管理API服务

- 创建和删除工作区
- 发布Shapefile到GeoServer
- 发布目录中所有Shapefile到GeoServer
- 发布GeoTIFF到GeoServer
- 发布目录中所有GeoTIFF到GeoServer
- 发布PostGIS表到GeoServer
- 创建PostGIS数据存储
- 设置图层样式
- 删除图层
- 批量处理操作
- 诊断GeoServer连接和图层状态
- 异步发布图层和监控状态

### 命令行工具

- 创建和删除工作区
- 发布Shapefile到GeoServer
- 发布目录中所有Shapefile到GeoServer
- 发布GeoTIFF到GeoServer
- 发布目录中所有GeoTIFF到GeoServer
- 发布PostGIS表到GeoServer
- 创建PostGIS数据存储
- 设置图层样式
- 删除图层
- 批量处理操作
- 诊断GeoServer连接和图层状态

## 目录结构

```
geoserverRest/
  ├── api/             # RESTful API服务模块
  │   ├── __init__.py
  │   ├── server.py    # API服务器实现
  │   ├── management_api.py # GeoServer管理API实现
  │   ├── batch_api.py # 批处理执行API实现
  │   ├── tif_api.py   # TIF处理API实现
  │   └── geo_api.py   # GeoServer异步发布API实现
  ├── cli/             # 命令行工具模块
  │   ├── __init__.py
  │   └── command.py   # 命令行工具实现
  ├── core/            # 核心功能模块
  │   ├── __init__.py
  │   ├── manager.py   # GeoServer管理器
  │   ├── raster_query.py # 栅格数据查询实现
  │   ├── batch_executor.py # 批处理文件执行器
  │   ├── tif_process.py # TIF处理实现
  │   └── geo_publisher.py # GeoServer发布执行器
  ├── batlog/          # 批处理执行日志目录
  ├── tiflog/          # TIF处理日志目录
  ├── geolog/          # GeoServer发布日志目录
  ├── utils/           # 实用工具模块
  │   └── __init__.py
  ├── __init__.py
  ├── README.md        # 本文件
  ├── api-usage.md     # API使用说明
  ├── management-api.md # GeoServer管理API使用说明
  ├── batch-api.md     # 批处理执行API使用说明
  ├── tif_api_README.md # TIF处理API使用说明
  ├── geo_api_README.md # GeoServer异步发布API使用说明
  ├── cli-usage.md     # 命令行工具使用说明
  ├── geoserver_cli.py # 命令行工具入口
  └── run.py           # API服务启动脚本
```

## 安装依赖

本项目依赖以下Python库：

```bash
pip install flask flask-cors requests gdal geo-python
```

## 使用方法

### 启动API服务

```bash
# 使用默认设置（主机：0.0.0.0，端口：5000）
python -m geoserverRest.run

# 指定主机和端口
python -m geoserverRest.run --host 127.0.0.1 --port 8080

# 启用调试模式
python -m geoserverRest.run --debug
```

也可以使用提供的批处理文件：

```bash
start_geoserver_api.bat
```

## 详细文档

- [API使用说明](api-usage.md) - RESTful API服务的使用说明
- [GeoServer管理API使用说明](management-api.md) - GeoServer管理API的使用说明
- [批处理执行API使用说明](batch-api.md) - 批处理执行API的使用说明
- [TIF处理API使用说明](tif_api_README.md) - TIF处理API的使用说明
- [GeoServer异步发布API使用说明](geo_api_README.md) - GeoServer异步发布API的使用说明
- [命令行工具使用说明](cli-usage.md) - 命令行工具的使用说明