2025-07-02 09:20:49,472 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250702_092049.log
2025-07-02 09:20:51,721 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-02 09:20:51,721 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-02 09:20:51,747 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-02 09:20:51,751 - geoserver_query_api - INFO - === GeoServer栅格数据坐标查询API服务 ===
2025-07-02 09:20:51,752 - geoserver_query_api - INFO - 主机: 0.0.0.0
2025-07-02 09:20:51,752 - geoserver_query_api - INFO - 端口: 5000
2025-07-02 09:20:51,754 - geoserver_query_api - INFO - 调试模式: 启用
2025-07-02 09:20:51,767 - werkzeug - WARNING -  * Debugger is active!
2025-07-02 09:20:51,816 - werkzeug - INFO -  * Debugger PIN: 129-639-547
2025-07-02 09:21:18,421 - root - ERROR - 工作区 'my_workspace' 不存在
2025-07-02 09:21:18,422 - werkzeug - INFO - 127.0.0.1 - - [02/Jul/2025 09:21:18] "GET /api/layers?workspace=my_workspace HTTP/1.1" 200 -
2025-07-02 09:21:18,478 - werkzeug - INFO - 127.0.0.1 - - [02/Jul/2025 09:21:18] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-02 09:21:49,806 - root - INFO - 在工作区 'test_myworkspace' 中找到 1 个栅格图层
2025-07-02 09:21:49,808 - werkzeug - INFO - 127.0.0.1 - - [02/Jul/2025 09:21:49] "GET /api/layers?workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-02 09:22:22,348 - werkzeug - INFO - 127.0.0.1 - - [02/Jul/2025 09:22:22] "GET /health HTTP/1.1" 200 -
2025-07-02 09:23:57,293 - werkzeug - INFO -  * Detected change in 'D:\\Drone_Project\\geoserverapi\\coordinate_query\\geoserver_query_api.py', reloading
