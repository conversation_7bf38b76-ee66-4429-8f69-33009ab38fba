2025-07-12 10:42:31,129 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250712_104231.log
2025-07-12 10:42:31,131 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 10:42:31,132 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 10:42:31,149 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 10:42:31,157 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 10:42:31,157 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 10:42:31,168 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 10:42:31,174 - root - INFO - === GeoServer REST API服务 ===
2025-07-12 10:42:31,175 - root - INFO - 主机: 0.0.0.0
2025-07-12 10:42:31,175 - root - INFO - 端口: 5083
2025-07-12 10:42:31,175 - root - INFO - 调试模式: 禁用
2025-07-12 10:42:31,175 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-12 10:42:31,189 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-12 10:42:31,194 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 10:43:10,795 - root - INFO - 在目录 D:/Drone_Project/geoserverapi/data/20250701/testshp 中找到 5 个Shapefile文件
2025-07-12 10:43:10,796 - root - INFO - 正在发布Shapefile: D:/Drone_Project/geoserverapi/data/20250701/testshp\招商引资片区.shp
2025-07-12 10:43:10,932 - root - INFO - 添加文件到ZIP: 招商引资片区.shp
2025-07-12 10:43:10,935 - root - INFO - 添加文件到ZIP: 招商引资片区.dbf
2025-07-12 10:43:10,938 - root - INFO - 添加文件到ZIP: 招商引资片区.shx
2025-07-12 10:43:10,939 - root - INFO - 添加文件到ZIP: 招商引资片区.prj
2025-07-12 10:43:10,940 - root - INFO - 添加文件到ZIP: 招商引资片区.qix
2025-07-12 10:43:10,940 - root - INFO - 添加文件到ZIP: 招商引资片区.cpg
2025-07-12 10:43:10,941 - root - INFO - 添加文件到ZIP: 招商引资片区.qmd
2025-07-12 10:43:11,052 - root - INFO - 成功通过REST API发布Shapefile: 201
2025-07-12 10:43:11,052 - root - INFO - 成功发布Shapefile: tttt:testshp_招商引资片区:招商引资片区
2025-07-12 10:43:11,072 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: tttt/testshp_招商引资片区/招商引资片区
2025-07-12 10:43:11,073 - root - INFO - 图层验证成功: tttt:招商引资片区
2025-07-12 10:43:11,076 - root - INFO - 正在发布Shapefile: D:/Drone_Project/geoserverapi/data/20250701/testshp\招商引资片区交种植区.shp
2025-07-12 10:43:11,728 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shp
2025-07-12 10:43:11,890 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.dbf
2025-07-12 10:43:11,903 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shx
2025-07-12 10:43:11,904 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.prj
2025-07-12 10:43:11,918 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qix
2025-07-12 10:43:11,920 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.cpg
2025-07-12 10:43:11,920 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qmd
2025-07-12 10:43:12,103 - root - INFO - 成功通过REST API发布Shapefile: 201
2025-07-12 10:43:12,105 - root - INFO - 成功发布Shapefile: tttt:testshp_招商引资片区交种植区:招商引资片区交种植区
2025-07-12 10:43:12,124 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: tttt/testshp_招商引资片区交种植区/招商引资片区交种植区
2025-07-12 10:43:12,125 - root - INFO - 图层验证成功: tttt:招商引资片区交种植区
2025-07-12 10:43:12,126 - root - INFO - 正在发布Shapefile: D:/Drone_Project/geoserverapi/data/20250701/testshp\招商引资片区交设施农用地.shp
2025-07-12 10:43:12,286 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shp
2025-07-12 10:43:12,299 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.dbf
2025-07-12 10:43:12,301 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shx
2025-07-12 10:43:12,301 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.prj
2025-07-12 10:43:12,303 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qix
2025-07-12 10:43:12,304 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.cpg
2025-07-12 10:43:12,304 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qmd
2025-07-12 10:43:12,397 - root - INFO - 成功通过REST API发布Shapefile: 201
2025-07-12 10:43:12,398 - root - INFO - 成功发布Shapefile: tttt:testshp_招商引资片区交设施农用地:招商引资片区交设施农用地
2025-07-12 10:43:12,419 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: tttt/testshp_招商引资片区交设施农用地/招商引资片区交设施农用地
2025-07-12 10:43:12,420 - root - INFO - 图层验证成功: tttt:招商引资片区交设施农用地
2025-07-12 10:43:12,423 - root - INFO - 正在发布Shapefile: D:/Drone_Project/geoserverapi/data/20250701/testshp\种植区.shp
2025-07-12 10:43:13,592 - root - INFO - 添加文件到ZIP: 种植区.shp
2025-07-12 10:43:13,797 - root - INFO - 添加文件到ZIP: 种植区.dbf
2025-07-12 10:43:13,819 - root - INFO - 添加文件到ZIP: 种植区.shx
2025-07-12 10:43:13,820 - root - INFO - 添加文件到ZIP: 种植区.prj
2025-07-12 10:43:13,842 - root - INFO - 添加文件到ZIP: 种植区.qix
2025-07-12 10:43:13,843 - root - INFO - 添加文件到ZIP: 种植区.cpg
2025-07-12 10:43:13,846 - root - INFO - 添加文件到ZIP: 种植区.qmd
2025-07-12 10:43:14,102 - root - INFO - 成功通过REST API发布Shapefile: 201
2025-07-12 10:43:14,102 - root - INFO - 成功发布Shapefile: tttt:testshp_种植区:种植区
2025-07-12 10:43:14,134 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: tttt/testshp_种植区/种植区
2025-07-12 10:43:14,135 - root - INFO - 图层验证成功: tttt:种植区
2025-07-12 10:43:14,137 - root - INFO - 正在发布Shapefile: D:/Drone_Project/geoserverapi/data/20250701/testshp\设施农用地潜力.shp
2025-07-12 10:43:15,145 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shp
2025-07-12 10:43:15,204 - root - INFO - 添加文件到ZIP: 设施农用地潜力.dbf
2025-07-12 10:43:15,212 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shx
2025-07-12 10:43:15,213 - root - INFO - 添加文件到ZIP: 设施农用地潜力.prj
2025-07-12 10:43:15,224 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qix
2025-07-12 10:43:15,225 - root - INFO - 添加文件到ZIP: 设施农用地潜力.cpg
2025-07-12 10:43:15,226 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qmd
2025-07-12 10:43:15,426 - root - INFO - 成功通过REST API发布Shapefile: 201
2025-07-12 10:43:15,426 - root - INFO - 成功发布Shapefile: tttt:testshp_设施农用地潜力:设施农用地潜力
2025-07-12 10:43:15,449 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: tttt/testshp_设施农用地潜力/设施农用地潜力
2025-07-12 10:43:15,450 - root - INFO - 图层验证成功: tttt:设施农用地潜力
2025-07-12 10:43:15,452 - root - INFO - 成功发布 5/5 个Shapefile文件
2025-07-12 10:43:15,454 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 10:43:15] "GET /api/management/shapefile-directories/publish?directory=D:/Drone_Project/geoserverapi/data/20250701/testshp&workspace=tttt&charset=UTF-8 HTTP/1.1" 200 -
2025-07-12 10:46:59,571 - root - ERROR - 发布GeoTIFF时出错: create_coveragestore() got an unexpected keyword argument 'store_name'
2025-07-12 10:46:59,572 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 10:46:59] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/geoserverapi/data/20250701/nanning.tif&workspace=tttt&charset=UTF-8 HTTP/1.1[0m" 500 -
