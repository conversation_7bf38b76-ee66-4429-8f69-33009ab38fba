2025-07-24 08:28:14,250 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250724_082814.log
2025-07-24 08:28:14,252 - geo_publisher - INFO - 加载了 18 个任务状态
2025-07-24 08:28:14,273 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-24 08:28:14,274 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-24 08:28:14,298 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-24 08:28:14,305 - root - INFO - === GeoServer REST API服务 ===
2025-07-24 08:28:14,306 - root - INFO - 主机: 0.0.0.0
2025-07-24 08:28:14,307 - root - INFO - 端口: 5083
2025-07-24 08:28:14,308 - root - INFO - 调试模式: 禁用
2025-07-24 08:28:14,309 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-24 08:28:14,413 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-24 08:28:14,414 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
