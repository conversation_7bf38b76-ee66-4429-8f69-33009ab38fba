2025-07-28 16:42:47,806 - INFO - ============ TIF处理任务 9189cfe2-eb81-477d-ac3e-9c78f34b27fd 开始执行 ============
2025-07-28 16:42:47,810 - INFO - 开始时间: 2025-07-28 16:42:47
2025-07-28 16:42:47,828 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 16:42:47,831 - INFO - 系统信息:
2025-07-28 16:42:47,832 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 16:42:47,834 - INFO -   Python版本: 3.8.20
2025-07-28 16:42:47,836 - INFO -   GDAL版本: 3.9.2
2025-07-28 16:42:47,837 - INFO -   GPU可用: 否
2025-07-28 16:42:47,838 - INFO - 检查参数有效性...
2025-07-28 16:42:47,839 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 16:42:47,841 - INFO - 开始执行TIF处理流程...
2025-07-28 16:42:47,842 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 16:42:47,860 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 16:42:47,862 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 16:42:47,874 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-28 16:42:58,700 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (16:42:55)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 16:42:58,706 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 16:43:02,096 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:03<01:07,  3.36s/it]
2025-07-28 16:43:03,260 - ERROR - 
处理数据块:  10%|######7                                                                | 2/21 [00:04<00:39,  2.07s/it]
2025-07-28 16:43:05,439 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:06<00:08,  1.55it/s]
2025-07-28 16:43:05,713 - ERROR - 
处理数据块:  43%|##############################4                                        | 9/21 [00:06<00:07,  1.71it/s]
2025-07-28 16:43:06,454 - ERROR - 
处理数据块:  52%|####################################6                                 | 11/21 [00:07<00:05,  1.94it/s]
2025-07-28 16:43:06,589 - ERROR - 
处理数据块:  67%|##############################################6                       | 14/21 [00:07<00:02,  3.09it/s]
2025-07-28 16:43:07,848 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:09<00:02,  2.10it/s]
2025-07-28 16:43:08,319 - ERROR - 
处理数据块:  76%|#####################################################3                | 16/21 [00:09<00:02,  2.11it/s]
2025-07-28 16:43:08,401 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:09<00:00,  2.17it/s]
2025-07-28 16:43:08,405 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 16:43:08,407 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 16:43:08,509 - ERROR - 
合并结果:  95%|###################################################################6   | 20/21 [00:00<00:00, 196.07it/s]
2025-07-28 16:43:08,514 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 198.11it/s]
2025-07-28 16:43:13,894 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 14.05 秒 (16:43:09)预计总处理时间: 约 42.15 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (16:43:09)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 16:43:13,896 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 16:43:25,684 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 16:43:25,685 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 16:43:25,689 - ERROR - [A
2025-07-28 16:43:26,436 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:14,  1.34it/s]
2025-07-28 16:43:26,436 - ERROR - [A
2025-07-28 16:43:26,554 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:04,  4.23it/s]
2025-07-28 16:43:26,554 - ERROR - [A
2025-07-28 16:43:26,732 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:01<00:02,  6.22it/s]
2025-07-28 16:43:26,733 - ERROR - [A
2025-07-28 16:43:26,927 - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:01,  7.46it/s]
2025-07-28 16:43:26,927 - ERROR - [A
2025-07-28 16:43:27,122 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  8.32it/s]
2025-07-28 16:43:27,123 - ERROR - [A
2025-07-28 16:43:27,292 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:01,  9.27it/s]
2025-07-28 16:43:27,292 - ERROR - [A
2025-07-28 16:43:27,520 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:00,  9.10it/s]
2025-07-28 16:43:27,521 - ERROR - [A
2025-07-28 16:43:27,752 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:02<00:00,  8.95it/s]
2025-07-28 16:43:27,753 - ERROR - [A
2025-07-28 16:43:27,923 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:02<00:00,  8.12it/s]
2025-07-28 16:43:27,924 - ERROR - [A
2025-07-28 16:43:28,048 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:02<00:00,  9.80it/s]
2025-07-28 16:43:28,049 - ERROR - [A
2025-07-28 16:43:28,151 - ERROR - 
波段 1/3 写入进度: 100%|###############################################################| 21/21 [00:02<00:00, 13.70it/s]
2025-07-28 16:43:28,152 - ERROR - [A
2025-07-28 16:43:28,156 - ERROR - [A
2025-07-28 16:44:18,405 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:04<02:08, 64.50s/it]
2025-07-28 16:44:40,854 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 16:44:40,857 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 16:44:40,859 - ERROR - [A
2025-07-28 16:44:41,605 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:14,  1.34it/s]
2025-07-28 16:44:41,606 - ERROR - [A
2025-07-28 16:44:42,027 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:01<00:10,  1.80it/s]
2025-07-28 16:44:42,029 - ERROR - [A
2025-07-28 16:44:42,680 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:10,  1.67it/s]
2025-07-28 16:44:42,681 - ERROR - [A
2025-07-28 16:44:42,994 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:02<00:08,  2.05it/s]
2025-07-28 16:44:42,995 - ERROR - [A
2025-07-28 16:44:43,111 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:02<00:05,  2.83it/s]
2025-07-28 16:44:43,112 - ERROR - [A
2025-07-28 16:44:43,268 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:04,  3.49it/s]
2025-07-28 16:44:43,268 - ERROR - [A
2025-07-28 16:44:43,440 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:02<00:03,  4.02it/s]
2025-07-28 16:44:43,440 - ERROR - [A
2025-07-28 16:44:43,641 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:02<00:02,  5.63it/s]
2025-07-28 16:44:43,642 - ERROR - [A
2025-07-28 16:44:43,755 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:02<00:01,  6.20it/s]
2025-07-28 16:44:43,756 - ERROR - [A
2025-07-28 16:44:43,864 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:03<00:01,  6.79it/s]
2025-07-28 16:44:43,865 - ERROR - [A
2025-07-28 16:44:43,974 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:03<00:01,  7.32it/s]
2025-07-28 16:44:43,974 - ERROR - [A
2025-07-28 16:44:44,086 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:03<00:01,  7.69it/s]
2025-07-28 16:44:44,087 - ERROR - [A
2025-07-28 16:44:44,221 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:03<00:00,  7.60it/s]
2025-07-28 16:44:44,222 - ERROR - [A
2025-07-28 16:44:44,419 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:03<00:00,  6.63it/s]
2025-07-28 16:44:44,420 - ERROR - [A
2025-07-28 16:44:45,970 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:05<00:02,  1.78it/s]
2025-07-28 16:44:45,970 - ERROR - [A
2025-07-28 16:44:48,792 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:07<00:04,  1.23s/it]
2025-07-28 16:44:48,793 - ERROR - [A
2025-07-28 16:44:53,090 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:12<00:06,  2.14s/it]
2025-07-28 16:44:53,091 - ERROR - [A
2025-07-28 16:44:57,989 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:17<00:05,  2.96s/it]
2025-07-28 16:44:57,993 - ERROR - [A
2025-07-28 16:45:03,093 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:22<00:03,  3.60s/it]
2025-07-28 16:45:03,094 - ERROR - [A
2025-07-28 16:45:04,171 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:23<00:00,  2.85s/it]
2025-07-28 16:45:04,172 - ERROR - [A
2025-07-28 16:45:04,175 - ERROR - [A
2025-07-28 16:50:01,252 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [06:47<03:48, 228.23s/it]
2025-07-28 16:50:12,488 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 16:50:12,490 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 16:50:12,493 - ERROR - [A
2025-07-28 16:50:14,135 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:01<00:32,  1.64s/it]
2025-07-28 16:50:14,135 - ERROR - [A
2025-07-28 16:50:15,753 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:03<00:30,  1.63s/it]
2025-07-28 16:50:15,753 - ERROR - [A
2025-07-28 16:50:17,721 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:05<00:32,  1.78s/it]
2025-07-28 16:50:17,721 - ERROR - [A
2025-07-28 16:50:19,908 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:07<00:33,  1.94s/it]
2025-07-28 16:50:19,909 - ERROR - [A
2025-07-28 16:50:22,413 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:09<00:34,  2.15s/it]
2025-07-28 16:50:22,413 - ERROR - [A
2025-07-28 16:50:22,526 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:10<00:08,  1.39it/s]
2025-07-28 16:50:22,527 - ERROR - [A
2025-07-28 16:50:22,637 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:10<00:03,  2.63it/s]
2025-07-28 16:50:22,638 - ERROR - [A
2025-07-28 16:50:22,740 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [00:10<00:00,  4.26it/s]
2025-07-28 16:50:22,741 - ERROR - [A
2025-07-28 16:50:22,812 - ERROR - [A
2025-07-28 16:54:03,777 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [10:49<00:00, 234.76s/it]
2025-07-28 16:54:03,779 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [10:49<00:00, 216.62s/it]
2025-07-28 16:54:30,806 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 654.27 秒 (16:54:04)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (16:54:04)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-28 16:54:30,814 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-28 16:54:31,221 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.54it/s]
2025-07-28 16:54:31,223 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.53it/s]
2025-07-28 16:54:31,264 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-28 16:54:43,517 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-28 16:54:43,530 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-28 16:54:43,813 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:03,  7.34it/s]
2025-07-28 16:54:44,681 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:12,  2.27it/s]
2025-07-28 16:54:44,748 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 25.68it/s]
2025-07-28 16:54:45,688 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-28 16:54:45,694 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-28 16:54:45,731 - ERROR - 
写入多边形: 100%|####################################################################| 35/35 [00:00<00:00, 1476.21it/s]
2025-07-28 16:54:45,943 - INFO - 处理完成，耗时: 718.08 秒 (11.97 分钟)
2025-07-28 16:54:45,944 - INFO - 处理结果: 成功
2025-07-28 16:54:45,949 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-28 16:54:45,950 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-28 16:54:45,957 - INFO - TIF处理任务 9189cfe2-eb81-477d-ac3e-9c78f34b27fd 执行成功
2025-07-28 16:54:45,958 - INFO - 完成时间: 2025-07-28 16:54:45
2025-07-28 16:54:45,964 - INFO - 状态: 运行成功
2025-07-28 16:54:45,965 - INFO - ============ 任务执行结束 ============
