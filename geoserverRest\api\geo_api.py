#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer REST API服务 - 异步图层发布API模块
提供GeoServer资源异步发布的API端点
"""

import os
import sys
import logging
from flask import Blueprint, request, jsonify

# 导入核心模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from core.geo_publisher import geo_executor, geo_logger

# 创建API蓝图
geo_api = Blueprint('geo_api', __name__)

@geo_api.route('/shapefile/execute', methods=['GET'])
def execute_publish_shapefile():
    """
    异步发布Shapefile到GeoServer
    
    查询参数:
        path: Shapefile文件路径（必需）
        workspace: 工作区名称（必需）
        store_name: 存储名称（可选）
        layer_name: 图层名称（可选）
        charset: 字符集，默认UTF-8
        
    返回:
        任务ID及状态信息
        
    注意:
        如果GeoServer中已存在相同工作区和图层名的图层，系统会自动删除已有图层，然后发布新图层。
        这样可以确保更新数据时能够正确替换旧图层。
    """
    try:
        # 获取参数
        shapefile_path = request.args.get('path')
        workspace = request.args.get('workspace')
        
        # 验证必要参数
        if not shapefile_path or not workspace:
            missing_params = []
            if not shapefile_path:
                missing_params.append('path')
            if not workspace:
                missing_params.append('workspace')
                
            return jsonify({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }), 400
        
        # 检查文件是否存在
        if not os.path.exists(shapefile_path):
            return jsonify({
                'status': 'error',
                'message': f'Shapefile文件不存在: {shapefile_path}'
            }), 404
            
        # 可选参数
        store_name = request.args.get('store_name')
        layer_name = request.args.get('layer_name')
        charset = request.args.get('charset', 'UTF-8')
        
        # 执行异步发布任务
        task_id = geo_executor.execute_publish_shapefile(
            shapefile_path,
            workspace,
            store_name,
            layer_name,
            charset
        )
        
        if task_id:
            return jsonify({
                'status': 'success',
                'message': 'Shapefile发布任务已启动',
                'task_id': task_id
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '启动Shapefile发布任务失败'
            }), 500
            
    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        import traceback
        geo_logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@geo_api.route('/geotiff/execute', methods=['GET'])
def execute_publish_geotiff():
    """
    异步发布GeoTIFF到GeoServer
    
    查询参数:
        path: GeoTIFF文件路径（必需）
        workspace: 工作区名称（必需）
        store_name: 存储名称（可选）
        layer_name: 图层名称（可选）
        
    返回:
        任务ID及状态信息
        
    注意:
        如果GeoServer中已存在相同工作区和图层名的图层，系统会自动删除已有图层，然后发布新图层。
        这样可以确保更新数据时能够正确替换旧图层。
    """
    try:
        # 获取参数
        geotiff_path = request.args.get('path')
        workspace = request.args.get('workspace')
        
        # 验证必要参数
        if not geotiff_path or not workspace:
            missing_params = []
            if not geotiff_path:
                missing_params.append('path')
            if not workspace:
                missing_params.append('workspace')
                
            return jsonify({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }), 400
        
        # 检查文件是否存在
        if not os.path.exists(geotiff_path):
            return jsonify({
                'status': 'error',
                'message': f'GeoTIFF文件不存在: {geotiff_path}'
            }), 404
            
        # 可选参数
        store_name = request.args.get('store_name')
        layer_name = request.args.get('layer_name')
        
        # 执行异步发布任务
        task_id = geo_executor.execute_publish_geotiff(
            geotiff_path,
            workspace,
            store_name,
            layer_name
        )
        
        if task_id:
            return jsonify({
                'status': 'success',
                'message': 'GeoTIFF发布任务已启动',
                'task_id': task_id
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '启动GeoTIFF发布任务失败'
            }), 500
            
    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        import traceback
        geo_logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@geo_api.route('/shapefile-directory/execute', methods=['GET'])
def execute_publish_shapefile_directory():
    """
    异步发布shapefile目录到GeoServer
    
    查询参数:
        path: shapefile目录路径（必需）
        workspace: 工作区名称（必需）
        store_name: 存储名称（可选）
        charset: 字符集，默认UTF-8
        
    返回:
        任务ID及状态信息
        
    注意:
        如果GeoServer中已存在相同工作区和图层名的图层，系统会自动删除已有图层，然后发布新图层。
        这样可以确保更新数据时能够正确替换旧图层。
    """
    try:
        # 获取参数
        directory_path = request.args.get('path')
        workspace = request.args.get('workspace')
        
        # 验证必要参数
        if not directory_path or not workspace:
            missing_params = []
            if not directory_path:
                missing_params.append('path')
            if not workspace:
                missing_params.append('workspace')
                
            return jsonify({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }), 400
        
        # 检查目录是否存在
        if not os.path.exists(directory_path) or not os.path.isdir(directory_path):
            return jsonify({
                'status': 'error',
                'message': f'目录不存在或不是有效目录: {directory_path}'
            }), 404
            
        # 可选参数
        store_name = request.args.get('store_name')
        charset = request.args.get('charset', 'UTF-8')
        
        # 执行异步发布任务
        task_id = geo_executor.execute_publish_shapefile_directory(
            directory_path,
            workspace,
            store_name,
            charset
        )
        
        if task_id:
            return jsonify({
                'status': 'success',
                'message': 'Shapefile目录发布任务已启动',
                'task_id': task_id
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '启动Shapefile目录发布任务失败'
            }), 500
            
    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        import traceback
        geo_logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@geo_api.route('/geotiff-directory/execute', methods=['GET'])
def execute_publish_geotiff_directory():
    """
    异步发布GeoTIFF目录到GeoServer
    
    查询参数:
        path: GeoTIFF目录路径（必需）
        workspace: 工作区名称（必需）
        store_name: 存储名称前缀（可选）
        
    返回:
        任务ID及状态信息
        
    注意:
        如果GeoServer中已存在相同工作区和图层名的图层，系统会自动删除已有图层，然后发布新图层。
        这样可以确保更新数据时能够正确替换旧图层。
    """
    try:
        # 获取参数
        directory_path = request.args.get('path')
        workspace = request.args.get('workspace')
        
        # 验证必要参数
        if not directory_path or not workspace:
            missing_params = []
            if not directory_path:
                missing_params.append('path')
            if not workspace:
                missing_params.append('workspace')
                
            return jsonify({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }), 400
        
        # 检查目录是否存在
        if not os.path.exists(directory_path) or not os.path.isdir(directory_path):
            return jsonify({
                'status': 'error',
                'message': f'目录不存在或不是有效目录: {directory_path}'
            }), 404
            
        # 可选参数
        store_name = request.args.get('store_name')
        
        # 执行异步发布任务
        task_id = geo_executor.execute_publish_geotiff_directory(
            directory_path,
            workspace,
            store_name
        )
        
        if task_id:
            return jsonify({
                'status': 'success',
                'message': 'GeoTIFF目录发布任务已启动',
                'task_id': task_id
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '启动GeoTIFF目录发布任务失败'
            }), 500
            
    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        import traceback
        geo_logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@geo_api.route('/structured-geotiff/execute', methods=['GET'])
def execute_publish_structured_geotiff():
    """
    异步发布特定结构的GeoTIFF文件到GeoServer
    扫描根目录下的子目录，发布每个子目录中与子目录同名的GeoTIFF文件

    查询参数:
        root_directory: 根目录路径（必需）
        workspace: 工作区名称（必需）
        store_name: 存储名称前缀（可选）

    返回:
        任务ID及状态信息

    示例:
        输入根目录: D:/Drone_Project/nginxData/ODM/Output
        如果存在子目录: 20250705171600/20250705171600.tif
        则会发布该GeoTIFF文件到GeoServer

    注意:
        如果GeoServer中已存在相同工作区和图层名的图层，系统会自动删除已有图层，然后发布新图层。
        这样可以确保更新数据时能够正确替换旧图层。
    """
    try:
        # 获取参数
        root_directory = request.args.get('root_directory')
        workspace = request.args.get('workspace')

        # 验证必要参数
        if not root_directory or not workspace:
            missing_params = []
            if not root_directory:
                missing_params.append('root_directory')
            if not workspace:
                missing_params.append('workspace')

            return jsonify({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }), 400

        # 检查根目录是否存在
        if not os.path.exists(root_directory) or not os.path.isdir(root_directory):
            return jsonify({
                'status': 'error',
                'message': f'根目录不存在或不是有效目录: {root_directory}'
            }), 404

        # 可选参数
        store_name = request.args.get('store_name')

        # 执行异步发布任务
        task_id = geo_executor.execute_publish_structured_geotiff(
            root_directory,
            workspace,
            store_name
        )

        if task_id:
            return jsonify({
                'status': 'success',
                'message': '特定结构GeoTIFF发布任务已启动',
                'task_id': task_id
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '启动特定结构GeoTIFF发布任务失败'
            }), 500

    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        import traceback
        geo_logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@geo_api.route('/status', methods=['GET'])
def get_geo_task_status():
    """
    获取GeoServer发布任务状态
    
    查询参数:
        task_id: 任务ID（必需）
        
    返回:
        任务状态信息
    """
    try:
        # 获取任务ID
        task_id = request.args.get('task_id')
        
        # 检查任务ID是否提供
        if not task_id:
            return jsonify({
                'status': 'error',
                'message': '缺少必要参数: task_id'
            }), 400
        
        # 获取任务状态
        task_status = geo_executor.get_task_status(task_id)
        
        if not task_status:
            return jsonify({
                'status': 'error',
                'message': f'未找到任务: {task_id}'
            }), 404
        
        return jsonify({
            'status': 'success',
            'task': task_status
        })
        
    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        import traceback
        geo_logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@geo_api.route('/all', methods=['GET'])
def get_all_geo_tasks():
    """
    获取所有GeoServer发布任务状态
    
    返回:
        所有任务状态信息
    """
    try:
        # 获取所有任务状态
        tasks = geo_executor.get_all_tasks()
        
        return jsonify({
            'status': 'success',
            'count': len(tasks),
            'tasks': tasks
        })
        
    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        import traceback
        geo_logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500 