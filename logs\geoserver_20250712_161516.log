2025-07-12 16:15:16,728 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250712_161516.log
2025-07-12 16:15:16,732 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 16:15:16,732 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 16:15:16,749 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 16:15:16,753 - batch_executor - INFO - 加载了 6 个任务状态
2025-07-12 16:15:16,771 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 16:15:16,772 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 16:15:16,866 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 16:15:16,878 - root - INFO - === GeoServer REST API服务 ===
2025-07-12 16:15:16,879 - root - INFO - 主机: 0.0.0.0
2025-07-12 16:15:16,879 - root - INFO - 端口: 5083
2025-07-12 16:15:16,879 - root - INFO - 调试模式: 禁用
2025-07-12 16:15:16,880 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-12 16:15:16,898 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://************:5083
2025-07-12 16:15:16,899 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
