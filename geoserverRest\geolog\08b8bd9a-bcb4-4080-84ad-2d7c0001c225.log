2025-07-18 11:29:09,472 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - INFO - GeoServer发布任务 08b8bd9a-bcb4-4080-84ad-2d7c0001c225 开始执行
2025-07-18 11:29:09,481 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - INFO - 开始时间: 2025-07-18 11:29:09
2025-07-18 11:29:09,483 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "\"tttt\"",
  "store_name": null,
  "layer_name": null
}
2025-07-18 11:29:10,313 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - ERROR - 执行出错: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'
2025-07-18 11:29:10,579 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 352, in _run_with_logging
    result = func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 425, in publish_func
    return manager.publish_geotiff(path, ws, store, layer)
  File "D:\Drone_Project\geoserverapi\geoserver_manager.py", line 607, in publish_geotiff
    self.geo.create_workspace(workspace)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\geo\Geoserver.py", line 396, in create_workspace
    raise GeoserverException(r.status_code, r.content)
geo.Geoserver.GeoserverException: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'

2025-07-18 11:29:10,583 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - ERROR - 完成时间: 2025-07-18 11:29:10
2025-07-18 11:29:10,584 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - ERROR - 状态: 发布失败
