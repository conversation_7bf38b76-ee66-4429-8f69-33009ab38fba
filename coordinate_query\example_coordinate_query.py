#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
Description: GeoServer栅格数据坐标查询示例
'''

import os
import sys
import json

# 添加父目录到系统路径，以便导入geoserver_manager
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入查询类
from coordinate_query.geoserver_query import GeoServerRasterQuery

def main():
    """示例主函数"""
    print("GeoServer栅格数据坐标查询示例")
    
    # 创建查询实例
    query = GeoServerRasterQuery()
    
    # 示例工作区名称（根据实际情况修改）
    workspace = "my_workspace"
    
    # 示例1: 获取工作区中的所有栅格图层
    print(f"\n示例1: 获取工作区 '{workspace}' 中的栅格图层")
    layers = query.get_raster_layers(workspace)
    print(f"找到 {len(layers)} 个栅格图层:")
    for i, layer in enumerate(layers):
        print(f"  {i+1}. {layer['name']} (存储: {layer['store']})")
    
    # 如果没有找到图层，无法继续后续示例
    if not layers:
        print(f"工作区 '{workspace}' 中没有栅格图层，请先发布一些栅格数据")
        return
    
    # 示例2: 使用图层边界框中心点坐标进行查询
    print("\n示例2: 使用图层边界框中心点坐标进行查询")
    # 使用第一个图层的边界框中心点作为示例
    first_layer = layers[0]
    bbox = first_layer.get('bbox', {})
    if bbox:
        # 计算边界框中心点坐标
        minx = float(bbox.get('minx', 0))
        miny = float(bbox.get('miny', 0))
        maxx = float(bbox.get('maxx', 0))
        maxy = float(bbox.get('maxy', 0))
        
        center_lon = (minx + maxx) / 2
        center_lat = (miny + maxy) / 2
        
        print(f"使用图层 '{first_layer['name']}' 的中心点坐标: ({center_lat:.6f}, {center_lon:.6f})")
        
        # 执行查询
        result_layers = query.query_point(center_lat, center_lon, workspace)
        
        print(f"在坐标点处找到 {len(result_layers)} 个图层:")
        for i, layer in enumerate(result_layers):
            print(f"  {i+1}. {layer['name']} (存储: {layer['store']})")
    else:
        print(f"图层 '{first_layer['name']}' 没有有效的边界框信息")
    
    # 示例3: 自定义坐标查询
    print("\n示例3: 自定义坐标查询")
    # 使用南宁附近的坐标作为示例（请根据实际数据修改）
    custom_lat = 22.8167
    custom_lon = 108.3667
    print(f"使用自定义坐标: ({custom_lat:.6f}, {custom_lon:.6f})")
    
    # 执行查询
    result_layers = query.query_point(custom_lat, custom_lon, workspace)
    
    print(f"在坐标点处找到 {len(result_layers)} 个图层:")
    for i, layer in enumerate(result_layers):
        print(f"  {i+1}. {layer['name']} (存储: {layer['store']})")
        
    print("\n演示完成")

if __name__ == "__main__":
    main() 