2025-08-04 11:25:41,946 - INFO - ============ TIF处理任务 1bfb6367-c531-4a87-ab9b-bf935c260be6 开始执行 ============
2025-08-04 11:25:41,953 - INFO - 开始时间: 2025-08-04 11:25:41
2025-08-04 11:25:41,955 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171599\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-08-04 11:25:41,959 - INFO - 系统信息:
2025-08-04 11:25:41,961 - INFO -   操作系统: Windows 10.0.19045
2025-08-04 11:25:42,110 - INFO -   Python版本: 3.8.20
2025-08-04 11:25:42,115 - INFO -   GDAL版本: 3.9.2
2025-08-04 11:25:42,116 - INFO -   GPU可用: 否
2025-08-04 11:25:42,117 - INFO - 检查参数有效性...
2025-08-04 11:25:42,119 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 11:25:42,120 - INFO - 开始执行TIF处理流程...
2025-08-04 11:25:42,121 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 11:25:42,122 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-04 11:25:42,124 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp
2025-08-04 11:25:42,137 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-08-04 11:25:42,998 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171599/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171599/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=5439, 高=5151, 波段数=4开始读取影像数据，大小约: 427.49 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759518.3524715091, 0.0728352753743704, 0.0, 2488560.9880124633, 0.0, -0.07283435420913706)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171599/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 5439x5151x3估计内存使用: 总计 427.49 MB, 单个处理块 82.99 MB开始创建掩码... (11:25:42)使用全图检测无效区域模式...将处理 6 个数据块...使用多线程处理 (7 线程)...
2025-08-04 11:25:43,003 - ERROR - 
处理数据块:   0%|                                                                                | 0/6 [00:00<?, ?it/s]
2025-08-04 11:25:43,043 - ERROR - 
处理数据块: 100%|################################################################################| 6/6 [00:00<?, ?it/s]
2025-08-04 11:25:43,045 - INFO - 已完成: 6/6 块 (100.0%)合并处理结果...
2025-08-04 11:25:43,046 - ERROR - 
合并结果:   0%|                                                                                  | 0/6 [00:00<?, ?it/s]
2025-08-04 11:25:43,050 - ERROR - 
合并结果: 100%|########################################################################| 6/6 [00:00<00:00, 1485.24it/s]
2025-08-04 11:25:43,189 - INFO - 合并进度: 6/6 块 (100.0%)统计结果: 总像素 28016289, 有效像素 16204432 (57.84%), 无效像素 11811857 (42.16%)掩码创建完成，耗时: 0.36 秒 (11:25:43)预计总处理时间: 约 1.07 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif (11:25:43)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif使用已处理过的影像信息影像信息: 宽=5439, 高=5151, 波段数=3掩码形状: (5151, 5439), 数据类型: uint8获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif, 尺寸: 5439x5151x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-08-04 11:25:43,193 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-08-04 11:25:43,662 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=64.98, std=63.52应用掩码: 11811857 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 6 个数据块
2025-08-04 11:25:43,662 - ERROR - 
波段 1/3 写入进度:   0%|                                                                         | 0/6 [00:00<?, ?it/s]
2025-08-04 11:25:43,667 - ERROR - [A
2025-08-04 11:25:44,061 - ERROR - 
波段 1/3 写入进度:  17%|##########8                                                      | 1/6 [00:00<00:01,  2.54it/s]
2025-08-04 11:25:44,061 - ERROR - [A
2025-08-04 11:25:44,115 - ERROR - [A
2025-08-04 11:25:50,514 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:07<00:14,  7.31s/it]
2025-08-04 11:25:51,092 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=74.00, std=70.05应用掩码: 11811857 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 6 个数据块
2025-08-04 11:25:51,093 - ERROR - 
波段 2/3 写入进度:   0%|                                                                         | 0/6 [00:00<?, ?it/s]
2025-08-04 11:25:51,099 - ERROR - [A
2025-08-04 11:25:51,233 - ERROR - 
波段 2/3 写入进度:  33%|#####################6                                           | 2/6 [00:00<00:00, 14.97it/s]
2025-08-04 11:25:51,234 - ERROR - [A
2025-08-04 11:25:51,382 - ERROR - 
波段 2/3 写入进度:  83%|######################################################1          | 5/6 [00:00<00:00, 18.41it/s]
2025-08-04 11:25:51,391 - ERROR - [A
2025-08-04 11:25:51,402 - ERROR - [A
2025-08-04 11:26:00,797 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [00:17<00:09,  9.06s/it]
2025-08-04 11:26:01,159 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=58.21, std=56.98应用掩码: 11811857 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 6 个数据块
2025-08-04 11:26:01,161 - ERROR - 
波段 3/3 写入进度:   0%|                                                                         | 0/6 [00:00<?, ?it/s]
2025-08-04 11:26:01,169 - ERROR - [A
2025-08-04 11:26:01,237 - ERROR - [A
2025-08-04 11:26:13,336 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [00:30<00:00, 10.65s/it]
2025-08-04 11:26:13,336 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [00:30<00:00, 10.04s/it]
2025-08-04 11:26:14,673 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif影像保存完成，耗时: 30.20 秒 (11:26:13)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp (11:26:13)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp使用NoData值: -9999, 简化容差: 0.1读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tifTIF文件信息: 宽=5439, 高=5151, 波段数=3开始读取影像数据，大小约: 320.62 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759518.3524715091, 0.0728352753743704, 0.0, 2488560.9880124633, 0.0, -0.07283435420913706)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif提取TIF文件信息...TIF文件信息: 宽=5439, 高=5151, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 11811857 个无效像素检查波段 2...波段 2 检测到 11811857 个无效像素检查波段 3...波段 3 检测到 11811857 个无效像素掩码统计: 总像素数 28016289, 有效像素数 16204432 (57.84%), 无效像素数 11811857 (42.16%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 1 个初始轮廓轮廓面积统计: 最小=16198389.00, 最大=16198389.00, 平均=16198389.00, 总数=1将 1 个轮廓转换为地理坐标...
2025-08-04 11:26:14,675 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-08-04 11:26:14,752 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00, 16.86it/s]
2025-08-04 11:26:14,786 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-08-04 11:26:15,686 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 4 个轮廓，转换为地理坐标...
2025-08-04 11:26:15,687 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/4 [00:00<?, ?it/s]
2025-08-04 11:26:15,780 - ERROR - 
处理轮廓: 100%|##########################################################################| 4/4 [00:00<00:00, 46.17it/s]
2025-08-04 11:26:15,868 - INFO - 处理了 20631 个坐标点合并并简化 4 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp写入shapefile特征...处理MultiPolygon，包含 5 个多边形
2025-08-04 11:26:15,868 - ERROR - 
写入多边形:   0%|                                                                                | 0/5 [00:00<?, ?it/s]
2025-08-04 11:26:15,879 - ERROR - 
写入多边形: 100%|#######################################################################| 5/5 [00:00<00:00, 998.83it/s]
2025-08-04 11:26:15,910 - INFO - 处理完成，耗时: 33.78 秒 (0.56 分钟)
2025-08-04 11:26:15,911 - INFO - 处理结果: 成功
2025-08-04 11:26:15,915 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif, 大小: 107.15 MB
2025-08-04 11:26:15,918 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp, 大小: 0.92 KB
2025-08-04 11:26:15,923 - INFO - TIF处理任务 1bfb6367-c531-4a87-ab9b-bf935c260be6 执行成功
2025-08-04 11:26:15,924 - INFO - 完成时间: 2025-08-04 11:26:15
2025-08-04 11:26:15,925 - INFO - 状态: 运行成功
2025-08-04 11:26:15,925 - INFO - ============ 任务执行结束 ============
