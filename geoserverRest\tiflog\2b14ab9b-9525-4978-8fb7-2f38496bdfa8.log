2025-07-28 09:53:57,914 - INFO - ============ TIF处理任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 开始执行 ============
2025-07-28 09:53:57,918 - INFO - 开始时间: 2025-07-28 09:53:57
2025-07-28 09:53:57,921 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 09:53:57,924 - INFO - 系统信息:
2025-07-28 09:53:57,925 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 09:53:57,928 - INFO -   Python版本: 3.8.20
2025-07-28 09:53:57,941 - INFO -   GDAL版本: 3.9.2
2025-07-28 09:53:57,949 - INFO -   GPU可用: 否
2025-07-28 09:53:57,952 - INFO - 检查参数有效性...
2025-07-28 09:53:57,954 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:53:57,957 - INFO - 开始执行TIF处理流程...
2025-07-28 09:53:57,959 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:53:57,967 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 09:53:57,973 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:53:57,980 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-28 09:54:06,348 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (09:54:06)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 09:54:06,382 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 09:54:06,517 - ERROR - 
处理数据块:  14%|##########1                                                            | 3/21 [00:00<00:00, 22.30it/s]
2025-07-28 09:54:06,769 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 20.55it/s]
2025-07-28 09:54:06,873 - ERROR - 
处理数据块:  52%|####################################6                                 | 11/21 [00:00<00:00, 23.19it/s]
2025-07-28 09:54:07,146 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 18.60it/s]
2025-07-28 09:54:07,264 - ERROR - 
处理数据块:  95%|##################################################################6   | 20/21 [00:00<00:00, 24.40it/s]
2025-07-28 09:54:07,265 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 23.80it/s]
2025-07-28 09:54:07,271 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 09:54:07,273 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 09:54:07,328 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 393.39it/s]
2025-07-28 09:54:08,682 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.55 秒 (09:54:08)预计总处理时间: 约 7.64 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (09:54:08)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 09:54:08,684 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 09:55:05,544 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 09:55:05,546 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:55:05,553 - ERROR - [A
2025-07-28 09:55:06,040 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:09,  2.06it/s]
2025-07-28 09:55:06,041 - ERROR - [A
2025-07-28 09:55:06,549 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:09,  2.00it/s]
2025-07-28 09:55:06,550 - ERROR - [A
2025-07-28 09:55:08,044 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:02<00:17,  1.05it/s]
2025-07-28 09:55:08,045 - ERROR - [A
2025-07-28 09:55:09,330 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:03<00:18,  1.08s/it]
2025-07-28 09:55:09,330 - ERROR - [A
2025-07-28 09:55:10,529 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:04<00:18,  1.13s/it]
2025-07-28 09:55:10,530 - ERROR - [A
2025-07-28 09:55:10,790 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:05<00:12,  1.20it/s]
2025-07-28 09:55:10,792 - ERROR - [A
2025-07-28 09:55:10,903 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:05<00:02,  3.87it/s]
2025-07-28 09:55:10,904 - ERROR - [A
2025-07-28 09:55:11,012 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:05<00:00,  6.45it/s]
2025-07-28 09:55:11,014 - ERROR - [A
2025-07-28 09:55:11,123 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:05<00:00, 10.41it/s]
2025-07-28 09:55:11,125 - ERROR - [A
2025-07-28 09:55:11,167 - ERROR - [A
2025-07-28 09:56:16,988 - ERROR - 
处理波段:  33%|########################3                                                | 1/3 [02:08<04:16, 128.30s/it]
2025-07-28 09:57:35,635 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 09:57:35,639 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:57:35,640 - ERROR - [A
2025-07-28 09:57:39,441 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:03<01:16,  3.80s/it]
2025-07-28 09:57:39,442 - ERROR - [A
2025-07-28 09:57:41,696 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:06<00:54,  2.89s/it]
2025-07-28 09:57:41,698 - ERROR - [A
2025-07-28 09:57:46,447 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:10<01:07,  3.74s/it]
2025-07-28 09:57:46,447 - ERROR - [A
2025-07-28 09:57:48,505 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:12<00:52,  3.08s/it]
2025-07-28 09:57:48,505 - ERROR - [A
2025-07-28 09:57:52,490 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:16<00:54,  3.40s/it]
2025-07-28 09:57:52,495 - ERROR - [A
2025-07-28 09:57:55,410 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:19<00:48,  3.24s/it]
2025-07-28 09:57:55,411 - ERROR - [A
2025-07-28 09:57:57,580 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:21<00:40,  2.89s/it]
2025-07-28 09:57:57,580 - ERROR - [A
2025-07-28 09:57:59,390 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:23<00:33,  2.55s/it]
2025-07-28 09:57:59,390 - ERROR - [A
2025-07-28 09:58:00,849 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:25<00:26,  2.21s/it]
2025-07-28 09:58:00,849 - ERROR - [A
2025-07-28 09:58:05,757 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:30<00:33,  3.04s/it]
2025-07-28 09:58:05,780 - ERROR - [A
2025-07-28 09:58:07,949 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:32<00:27,  2.78s/it]
2025-07-28 09:58:07,950 - ERROR - [A
2025-07-28 09:58:08,674 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:33<00:19,  2.16s/it]
2025-07-28 09:58:08,675 - ERROR - [A
2025-07-28 09:58:11,277 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:35<00:18,  2.29s/it]
2025-07-28 09:58:11,280 - ERROR - [A
2025-07-28 09:58:15,742 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:40<00:20,  2.95s/it]
2025-07-28 09:58:15,744 - ERROR - [A
2025-07-28 09:58:22,114 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:46<00:23,  3.98s/it]
2025-07-28 09:58:22,115 - ERROR - [A
2025-07-28 09:59:21,587 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [01:45<01:43, 20.68s/it]
2025-07-28 09:59:21,589 - ERROR - [A
2025-07-28 10:00:30,698 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [02:55<02:20, 35.25s/it]
2025-07-28 10:00:30,698 - ERROR - [A
2025-07-28 10:01:19,665 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [03:44<01:58, 39.37s/it]
2025-07-28 10:01:19,666 - ERROR - [A
2025-07-28 10:01:49,619 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [04:13<01:13, 36.54s/it]
2025-07-28 10:01:49,619 - ERROR - [A
2025-07-28 10:02:15,851 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [04:40<00:33, 33.45s/it]
2025-07-28 10:02:15,852 - ERROR - [A
2025-07-28 10:02:17,175 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [04:41<00:00, 23.80s/it]
2025-07-28 10:02:17,176 - ERROR - [A
2025-07-28 10:02:17,182 - ERROR - [A
2025-07-28 10:08:03,170 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [13:54<07:48, 468.23s/it]
2025-07-28 10:09:00,284 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 10:09:00,308 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 10:09:00,309 - ERROR - [A
2025-07-28 10:09:02,382 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:02<00:41,  2.07s/it]
2025-07-28 10:09:02,383 - ERROR - [A
2025-07-28 10:09:03,876 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:03<00:32,  1.73s/it]
2025-07-28 10:09:03,876 - ERROR - [A
2025-07-28 10:09:05,082 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:04<00:26,  1.49s/it]
2025-07-28 10:09:05,083 - ERROR - [A
2025-07-28 10:09:05,855 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:05<00:20,  1.21s/it]
2025-07-28 10:09:05,855 - ERROR - [A
2025-07-28 10:09:06,962 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:06<00:18,  1.17s/it]
2025-07-28 10:09:06,963 - ERROR - [A
2025-07-28 10:09:07,107 - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:06<00:06,  2.04it/s]
2025-07-28 10:09:07,143 - ERROR - [A
2025-07-28 10:09:07,244 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:06<00:04,  2.44it/s]
2025-07-28 10:09:07,245 - ERROR - [A
2025-07-28 10:09:07,357 - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:07<00:02,  3.67it/s]
2025-07-28 10:09:07,358 - ERROR - [A
2025-07-28 10:09:07,479 - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:07<00:01,  6.00it/s]
2025-07-28 10:09:07,479 - ERROR - [A
2025-07-28 10:09:07,620 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [00:07<00:00,  8.36it/s]
2025-07-28 10:09:07,620 - ERROR - [A
2025-07-28 10:09:07,766 - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [00:07<00:00, 10.63it/s]
2025-07-28 10:09:07,766 - ERROR - [A
2025-07-28 10:09:07,842 - ERROR - [A
