2025-07-14 12:11:54,348 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250714_121154.log
2025-07-14 12:11:54,351 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 12:11:54,351 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 12:11:54,412 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 12:11:54,426 - batch_executor - INFO - 加载了 22 个任务状态
2025-07-14 12:11:54,438 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 12:11:54,439 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 12:11:54,456 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 12:11:54,465 - root - INFO - === GeoServer REST API服务 ===
2025-07-14 12:11:54,465 - root - INFO - 主机: 0.0.0.0
2025-07-14 12:11:54,466 - root - INFO - 端口: 5083
2025-07-14 12:11:54,466 - root - INFO - 调试模式: 禁用
2025-07-14 12:11:54,467 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-14 12:11:54,501 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-14 12:11:54,503 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-14 12:12:01,042 - batch_executor - INFO - 启动任务 72be7f27-10f8-4af2-b96b-018f79ea52d3: D:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-14 12:12:01,046 - werkzeug - INFO - ************** - - [14/Jul/2025 12:12:01] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
2025-07-14 12:12:01,116 - batch_executor - ERROR - 执行任务 72be7f27-10f8-4af2-b96b-018f79ea52d3 时出错: 'utf-8' codec can't decode byte 0xb5 in position 2: invalid start byte
2025-07-14 12:12:19,245 - batch_executor - INFO - 启动任务 51bfa8b3-beec-41c8-be96-461d36ee8da2: D:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-14 12:12:19,246 - werkzeug - INFO - ************** - - [14/Jul/2025 12:12:19] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
2025-07-14 12:12:19,311 - batch_executor - ERROR - 执行任务 51bfa8b3-beec-41c8-be96-461d36ee8da2 时出错: 'utf-8' codec can't decode byte 0xb5 in position 2: invalid start byte
