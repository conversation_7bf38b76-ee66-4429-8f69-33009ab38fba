2025-07-09 10:13:29,226 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250709_101329.log
2025-07-09 10:13:31,053 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-09 10:13:31,053 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-09 10:13:31,069 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-09 10:13:31,074 - geoserver_query_api - INFO - === GeoServer栅格数据坐标查询API服务 ===
2025-07-09 10:13:31,074 - geoserver_query_api - INFO - 主机: 0.0.0.0
2025-07-09 10:13:31,076 - geoserver_query_api - INFO - 端口: 5084
2025-07-09 10:13:31,080 - geoserver_query_api - INFO - 调试模式: 禁用
2025-07-09 10:13:31,094 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5084
 * Running on http://**************:5084
2025-07-09 10:13:31,094 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 10:13:43,683 - root - INFO - 开始查询坐标点 (22.834372754198128, 108.19687725970199) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-09 10:13:43,869 - root - INFO - 尝试从WMS GetCapabilities获取 11 个图层的边界框
2025-07-09 10:13:45,702 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-09 10:13:45,704 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-09 10:13:45,705 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-09 10:13:45,707 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-09 10:13:45,709 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-09 10:13:45,711 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-09 10:13:45,712 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-09 10:13:45,714 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-09 10:13:45,715 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-09 10:13:45,717 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-09 10:13:45,718 - root - INFO - 成功从WMS获取图层 '5-1584' 的边界框: (107.569621272, 22.496618819) - (107.574347728, 22.501006876)
2025-07-09 10:13:45,720 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-09 10:13:45,772 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-09 10:13:45,849 - root - INFO - 图层 'id2' 在该坐标点有有效数据，添加到结果中
2025-07-09 10:13:45,878 - root - INFO - 在坐标点 (22.834372754198128, 108.19687725970199) 处找到 2 个有有效数据的栅格图层
2025-07-09 10:13:45,879 - werkzeug - INFO - ************** - - [09/Jul/2025 10:13:45] "GET /api/query_values?lat=22.834372754198128&lon=108.19687725970199&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-09 10:13:45,947 - werkzeug - INFO - ************** - - [09/Jul/2025 10:13:45] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
