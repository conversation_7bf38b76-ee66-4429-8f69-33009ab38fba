#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
Author: 吴博文 <EMAIL>
Date: 2025-07-01
Description: GeoServer命令行工具，支持自动化操作GeoServer
'''

import os
import sys
import argparse
import logging
import json
import datetime
from geoserver_manager import GeoServerManager, logger as manager_logger
from config import DEFAULT_WORKSPACE

# 配置日志记录
def setup_logging(log_level="INFO", log_file=None):
    """
    设置日志配置
    
    Args:
        log_level: 日志级别，默认为INFO
        log_file: 日志文件路径，如果为None则输出到控制台
    """
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    if not log_file:
        # 如果没有指定日志文件，创建一个带时间戳的日志文件
        log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(log_dir, f'geoserver_{timestamp}.log')
    
    # 创建日志处理器
    handlers = [
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()  # 同时输出到控制台
    ]
    
    # 设置日志配置
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=handlers
    )
    
    return logging.getLogger('geoserver_cli')

# 命令行参数解析
def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='GeoServer命令行工具')
    
    # 通用选项
    parser.add_argument('--log-level', type=str, default='INFO',
                      choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                      help='日志级别')
    parser.add_argument('--log-file', type=str,
                      help='日志文件路径，默认为logs目录下带时间戳的文件')
    
    # 子命令解析器
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 创建工作区命令
    workspace_parser = subparsers.add_parser('create-workspace', help='创建GeoServer工作区')
    workspace_parser.add_argument('--name', type=str, required=True,
                               help='工作区名称')
    
    # 发布Shapefile命令
    shapefile_parser = subparsers.add_parser('publish-shapefile', help='发布Shapefile到GeoServer')
    shapefile_parser.add_argument('--file', type=str, required=True,
                               help='Shapefile文件路径')
    shapefile_parser.add_argument('--workspace', type=str, default=DEFAULT_WORKSPACE,
                               help=f'工作区名称，默认为{DEFAULT_WORKSPACE}')
    shapefile_parser.add_argument('--store', type=str,
                               help='存储名称，默认使用文件名')
    shapefile_parser.add_argument('--layer', type=str,
                               help='图层名称，默认使用存储名')
    shapefile_parser.add_argument('--charset', type=str, default='UTF-8',
                               help='DBF文件字符集，默认为UTF-8以支持中文')
    
    # 发布Shapefile目录命令
    shapefile_dir_parser = subparsers.add_parser('publish-shapefile-directory', help='发布目录中所有Shapefile到GeoServer')
    shapefile_dir_parser.add_argument('--directory', type=str, required=True,
                                  help='包含Shapefile文件的目录路径')
    shapefile_dir_parser.add_argument('--workspace', type=str, default=DEFAULT_WORKSPACE,
                                  help=f'工作区名称，默认为{DEFAULT_WORKSPACE}')
    shapefile_dir_parser.add_argument('--store', type=str,
                                  help='存储名称，如果不提供则使用目录名称')
    shapefile_dir_parser.add_argument('--charset', type=str, default='UTF-8',
                                  help='DBF文件字符集，默认为UTF-8以支持中文')
    
    # 发布GeoTIFF目录命令
    geotiff_dir_parser = subparsers.add_parser('publish-geotiff-directory', help='发布目录中所有GeoTIFF到GeoServer')
    geotiff_dir_parser.add_argument('--directory', type=str, required=True,
                                 help='包含GeoTIFF文件的目录路径')
    geotiff_dir_parser.add_argument('--workspace', type=str, default=DEFAULT_WORKSPACE,
                                 help=f'工作区名称，默认为{DEFAULT_WORKSPACE}')
    geotiff_dir_parser.add_argument('--store', type=str,
                                 help='存储名称，如果不提供则使用目录名称')
    
    # 发布GeoTIFF命令
    geotiff_parser = subparsers.add_parser('publish-geotiff', help='发布GeoTIFF到GeoServer')
    geotiff_parser.add_argument('--file', type=str, required=True,
                             help='GeoTIFF文件路径')
    geotiff_parser.add_argument('--workspace', type=str, default=DEFAULT_WORKSPACE,
                             help=f'工作区名称，默认为{DEFAULT_WORKSPACE}')
    geotiff_parser.add_argument('--store', type=str,
                             help='存储名称，默认使用文件名')
    geotiff_parser.add_argument('--layer', type=str,
                             help='图层名称，默认使用存储名')
    
    # 发布PostGIS表命令
    postgis_parser = subparsers.add_parser('publish-postgis', help='发布PostGIS表到GeoServer')
    postgis_parser.add_argument('--table', type=str, required=True,
                             help='数据表名称')
    postgis_parser.add_argument('--workspace', type=str, default=DEFAULT_WORKSPACE,
                             help=f'工作区名称，默认为{DEFAULT_WORKSPACE}')
    postgis_parser.add_argument('--store', type=str, required=True,
                             help='数据存储名称')
    postgis_parser.add_argument('--layer', type=str,
                             help='图层名称，默认使用表名')
    postgis_parser.add_argument('--geometry-column', type=str, default='geom',
                             help='几何列名称，默认为geom')
    postgis_parser.add_argument('--srid', type=int, default=4326,
                             help='空间参考系统ID，默认为4326')
    
    # 创建PostGIS数据存储命令
    datastore_parser = subparsers.add_parser('create-datastore', help='创建PostGIS数据存储')
    datastore_parser.add_argument('--name', type=str, required=True,
                               help='数据存储名称')
    datastore_parser.add_argument('--workspace', type=str, default=DEFAULT_WORKSPACE,
                               help=f'工作区名称，默认为{DEFAULT_WORKSPACE}')
    datastore_parser.add_argument('--host', type=str, default='localhost',
                               help='数据库主机，默认为localhost')
    datastore_parser.add_argument('--port', type=int, default=5432,
                               help='数据库端口，默认为5432')
    datastore_parser.add_argument('--database', type=str, required=True,
                               help='数据库名称')
    datastore_parser.add_argument('--username', type=str, required=True,
                               help='数据库用户名')
    datastore_parser.add_argument('--password', type=str, required=True,
                               help='数据库密码')
    
    # 设置图层样式命令
    style_parser = subparsers.add_parser('set-style', help='设置图层样式')
    style_parser.add_argument('--layer', type=str, required=True,
                           help='图层名称')
    style_parser.add_argument('--style', type=str, required=True,
                           help='样式名称')
    style_parser.add_argument('--workspace', type=str, default=DEFAULT_WORKSPACE,
                           help=f'工作区名称，默认为{DEFAULT_WORKSPACE}')
    
    # 删除图层命令
    delete_layer_parser = subparsers.add_parser('delete-layer', help='删除图层')
    delete_layer_parser.add_argument('--name', type=str, required=True,
                                  help='图层名称')
    delete_layer_parser.add_argument('--workspace', type=str, default=DEFAULT_WORKSPACE,
                                  help=f'工作区名称，默认为{DEFAULT_WORKSPACE}')
    
    # 删除工作区命令
    delete_workspace_parser = subparsers.add_parser('delete-workspace', help='删除工作区')
    delete_workspace_parser.add_argument('--name', type=str, required=True,
                                      help='工作区名称')
    
    # 批处理命令
    batch_parser = subparsers.add_parser('batch', help='批量处理操作')
    batch_parser.add_argument('--file', type=str, required=True,
                           help='JSON格式的批处理配置文件')
    
    # 诊断命令
    diagnose_parser = subparsers.add_parser('diagnose', help='诊断GeoServer连接和图层状态')
    diagnose_parser.add_argument('--workspace', type=str,
                              help='要检查的特定工作区，不提供则检查所有工作区')
    diagnose_parser.add_argument('--detail', action='store_true',
                              help='显示详细诊断信息，包括所有图层和数据存储')
    
    return parser.parse_args()

def execute_batch_file(batch_file, manager, logger):
    """
    执行批处理文件中的操作
    
    Args:
        batch_file: 批处理文件路径
        manager: GeoServerManager实例
        logger: 日志记录器
    """
    logger.info(f"执行批处理文件: {batch_file}")
    
    try:
        with open(batch_file, 'r', encoding='utf-8') as f:
            batch_config = json.load(f)
        
        if not isinstance(batch_config, list):
            logger.error("批处理配置必须是操作列表")
            return False
        
        for i, operation in enumerate(batch_config):
            logger.info(f"执行操作 {i+1}/{len(batch_config)}: {operation.get('type', 'unknown')}")
            
            op_type = operation.get('type')
            params = operation.get('params', {})
            
            if op_type == 'create_workspace':
                manager.create_workspace(params.get('name'))
            
            elif op_type == 'publish_shapefile':
                # 工作区现在是可选的，如果不存在会自动创建
                manager.publish_shapefile(
                    shapefile_path=params.get('file'),
                    workspace=params.get('workspace', DEFAULT_WORKSPACE),
                    store_name=params.get('store'),
                    layer_name=params.get('layer'),
                    charset=params.get('charset', 'UTF-8')
                )
                
            elif op_type == 'publish_shapefile_directory':
                # 工作区现在是可选的，如果不存在会自动创建
                manager.publish_shapefile_directory(
                    directory_path=params.get('directory'),
                    workspace=params.get('workspace', DEFAULT_WORKSPACE),
                    store_name=params.get('store'),
                    charset=params.get('charset', 'UTF-8')
                )
                
            elif op_type == 'publish_geotiff_directory':
                # 工作区现在是可选的，如果不存在会自动创建
                manager.publish_geotiff_directory(
                    directory_path=params.get('directory'),
                    workspace=params.get('workspace', DEFAULT_WORKSPACE),
                    store_name=params.get('store')
                )
                
            elif op_type == 'publish_geotiff':
                # 工作区现在是可选的，如果不存在会自动创建
                manager.publish_geotiff(
                    geotiff_path=params.get('file'),
                    workspace=params.get('workspace', DEFAULT_WORKSPACE),
                    store_name=params.get('store'),
                    layer_name=params.get('layer')
                )
                
            elif op_type == 'publish_postgis':
                manager.publish_postgis_layer(
                    table_name=params.get('table'),
                    workspace=params.get('workspace', DEFAULT_WORKSPACE),
                    store_name=params.get('store'),
                    layer_name=params.get('layer'),
                    geometry_column=params.get('geometry_column', 'geom'),
                    srid=params.get('srid', 4326)
                )
                
            elif op_type == 'create_datastore':
                conn_params = {
                    'host': params.get('host', 'localhost'),
                    'port': params.get('port', 5432),
                    'database': params.get('database'),
                    'username': params.get('username'),
                    'password': params.get('password')
                }
                manager.create_datastore(
                    name=params.get('name'),
                    workspace=params.get('workspace', DEFAULT_WORKSPACE),
                    conn_params=conn_params
                )
                
            elif op_type == 'set_layer_style':
                manager.set_layer_style(
                    layer_name=params.get('layer'),
                    style_name=params.get('style'),
                    workspace=params.get('workspace', DEFAULT_WORKSPACE)
                )
                
            elif op_type == 'delete_layer':
                manager.delete_layer(
                    layer_name=params.get('name'),
                    workspace=params.get('workspace', DEFAULT_WORKSPACE)
                )
                
            elif op_type == 'delete_workspace':
                manager.delete_workspace(params.get('name'))
                
            else:
                logger.warning(f"未知操作类型: {op_type}")
        
        logger.info("批处理文件执行完成")
        return True
        
    except Exception as e:
        logger.error(f"执行批处理文件时出错: {str(e)}")
        return False

def main():
    """主函数"""
    args = parse_arguments()
    
    if not args.command:
        print("错误：必须指定一个命令。使用 --help 查看帮助信息。")
        sys.exit(1)
    
    # 使用来自geoserver_manager的日志记录器
    logger = manager_logger
    logger.info(f"开始执行命令: {args.command}")
    
    # 初始化GeoServerManager
    try:
        manager = GeoServerManager()
    except Exception as e:
        logger.error(f"初始化GeoServerManager失败：{str(e)}")
        sys.exit(1)
    
    # 执行相应命令
    if args.command == 'create-workspace':
        manager.create_workspace(args.name)
        
    elif args.command == 'publish-shapefile':
        manager.publish_shapefile(
            shapefile_path=args.file,
            workspace=args.workspace,
            store_name=args.store,
            layer_name=args.layer,
            charset=args.charset
        )
        
    elif args.command == 'publish-shapefile-directory':
        result = manager.publish_shapefile_directory(
            directory_path=args.directory,
            workspace=args.workspace,
            store_name=args.store,
            charset=args.charset
        )
        logger.info(f"成功发布: {len(result['success'])}, 失败: {len(result['failed'])}")
        if result['failed']:
            logger.warning(f"失败的文件: {', '.join(result['failed'])}")
        
    elif args.command == 'publish-geotiff-directory':
        result = manager.publish_geotiff_directory(
            directory_path=args.directory,
            workspace=args.workspace,
            store_name=args.store
        )
        logger.info(f"成功发布: {len(result['success'])}, 失败: {len(result['failed'])}")
        if result['failed']:
            logger.warning(f"失败的文件: {', '.join(result['failed'])}")
        
    elif args.command == 'publish-geotiff':
        manager.publish_geotiff(
            geotiff_path=args.file,
            workspace=args.workspace,
            store_name=args.store,
            layer_name=args.layer
        )
        
    elif args.command == 'publish-postgis':
        manager.publish_postgis_layer(
            table_name=args.table,
            workspace=args.workspace,
            store_name=args.store,
            layer_name=args.layer,
            geometry_column=args.geometry_column,
            srid=args.srid
        )
        
    elif args.command == 'create-datastore':
        conn_params = {
            'host': args.host,
            'port': args.port,
            'database': args.database,
            'username': args.username,
            'password': args.password
        }
        manager.create_datastore(
            name=args.name,
            workspace=args.workspace,
            conn_params=conn_params
        )
        
    elif args.command == 'set-style':
        manager.set_layer_style(
            layer_name=args.layer,
            style_name=args.style,
            workspace=args.workspace
        )
        
    elif args.command == 'delete-layer':
        manager.delete_layer(
            layer_name=args.name,
            workspace=args.workspace
        )
        
    elif args.command == 'delete-workspace':
        manager.delete_workspace(args.name)
        
    elif args.command == 'batch':
        execute_batch_file(args.file, manager, logger)
    
    elif args.command == 'diagnose':
        logger.info(f"开始诊断GeoServer...")
        result = manager.diagnose(workspace=args.workspace, detail=args.detail)
        
        # 诊断结果汇总
        if result["connection"]:
            logger.info(f"GeoServer连接状态: 正常, 版本: {result['version']}")
        else:
            logger.error(f"GeoServer连接状态: 失败")
            
        logger.info(f"工作区: {len(result['workspaces'])}, 图层: {result['total_layers']}")
        
        if args.detail and result["detail"]:
            for ws, info in result["detail"].items():
                logger.info(f"工作区 '{ws}' 详情:")
                logger.info(f"  - 数据存储: {len(info['datastores'])}")
                logger.info(f"  - 图层: {len(info['layers'])}")
    
    logger.info(f"命令执行完成: {args.command}")
    

if __name__ == "__main__":
    main()  