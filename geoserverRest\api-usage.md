# GeoServer RESTful API服务使用说明

本文档介绍GeoServer RESTful API服务的使用方法，包括坐标查询、图层信息获取等基本功能。

## API端点

### 1. 健康检查

```
GET /health
```

检查API服务是否正常运行。

**示例:**
```
GET /health
```

**响应:**
```json
{
  "status": "ok",
  "message": "GeoServer REST API服务正在运行"
}
```

### 2. 查询坐标点处的栅格图层

```
GET /api/query?lat={纬度}&lon={经度}&workspace={工作区}
```

查询指定坐标点处存在有效数据的栅格图层列表。

**必选参数:**
- lat: 纬度
- lon: 经度
- workspace: 工作区名称

**示例:**
```
GET /api/query?lat=30.123&lon=114.456&workspace=test
```

**响应:**
```json
{
  "status": "success",
  "count": 2,
  "layers": [
    {
      "name": "ortho1",
      "store": "geotiffs",
      "workspace": "test",
      "pixel_value": [120, 145, 130]
    },
    {
      "name": "ortho2",
      "store": "geotiffs",
      "workspace": "test",
      "pixel_value": [140, 150, 160]
    }
  ]
}
```

### 3. 获取工作区中的所有栅格图层

```
GET /api/layers?workspace={工作区}
```

获取指定工作区中的所有栅格图层列表。

**必选参数:**
- workspace: 工作区名称

**示例:**
```
GET /api/layers?workspace=test
```

**响应:**
```json
{
  "status": "success",
  "count": 3,
  "layers": [
    {
      "name": "ortho1",
      "store": "geotiffs",
      "workspace": "test"
    },
    {
      "name": "ortho2",
      "store": "geotiffs",
      "workspace": "test"
    },
    {
      "name": "ortho3",
      "store": "geotiffs2",
      "workspace": "test"
    }
  ]
}
```

### 4. 获取工作区中的所有图层（栅格和矢量）

```
GET /api/all_layers?workspace={工作区}
```

获取指定工作区中的所有图层列表，包括栅格和矢量图层。

**必选参数:**
- workspace: 工作区名称

**示例:**
```
GET /api/all_layers?workspace=test
```

**响应:**
```json
{
  "status": "success",
  "count": 5,
  "layers": [
    {
      "name": "ortho1",
      "store": "geotiffs",
      "workspace": "test",
      "type": "raster"
    },
    {
      "name": "ortho2",
      "store": "geotiffs",
      "workspace": "test",
      "type": "raster"
    },
    {
      "name": "boundary1",
      "store": "shapefiles",
      "workspace": "test",
      "type": "vector"
    },
    {
      "name": "boundary2",
      "store": "shapefiles",
      "workspace": "test",
      "type": "vector"
    },
    {
      "name": "ortho3",
      "store": "geotiffs2",
      "workspace": "test",
      "type": "raster"
    }
  ]
}
```

### 5. 测试坐标点在特定图层中是否有有效数据

```
GET /api/test_point?lat={纬度}&lon={经度}&workspace={工作区}&layer={图层名称，可选}
```

测试指定坐标点在特定图层或所有图层中是否存在有效数据。

**必选参数:**
- lat: 纬度
- lon: 经度
- workspace: 工作区名称

**可选参数:**
- layer: 图层名称

**示例1 (指定图层):**
```
GET /api/test_point?lat=30.123&lon=114.456&workspace=test&layer=ortho1
```

**示例2 (测试所有图层):**
```
GET /api/test_point?lat=30.123&lon=114.456&workspace=test
```

**响应:**
```json
{
  "status": "success",
  "results": [
    {
      "name": "ortho1",
      "store": "geotiffs",
      "workspace": "test",
      "has_data": true,
      "point_in_bounds": true,
      "pixel_value": [120, 145, 130]
    }
  ]
}
```

### 6. 获取指定坐标点在特定图层中的像素值

```
GET /api/pixel_value?lat={纬度}&lon={经度}&workspace={工作区}&layer={图层名称}
```

获取指定坐标点在特定图层中的像素值。

**必选参数:**
- lat: 纬度
- lon: 经度
- workspace: 工作区名称
- layer: 图层名称

**示例:**
```
GET /api/pixel_value?lat=30.123&lon=114.456&workspace=test&layer=ortho1
```

**响应:**
```json
{
  "status": "success",
  "layer": "ortho1",
  "workspace": "test",
  "pixel_value": [120, 145, 130],
  "coordinates": {
    "lat": 30.123,
    "lon": 114.456
  }
}
```

### 7. 查询坐标点处的图层并获取像素值

```
GET /api/query_values?lat={纬度}&lon={经度}&workspace={工作区}
```

查询指定坐标点处存在有效数据的栅格图层，并获取每个图层的像素值。

**必选参数:**
- lat: 纬度
- lon: 经度
- workspace: 工作区名称

**示例:**
```
GET /api/query_values?lat=30.123&lon=114.456&workspace=test
```

**响应:**
```json
{
  "status": "success",
  "count": 2,
  "layers": [
    {
      "name": "ortho1",
      "store": "geotiffs",
      "workspace": "test",
      "pixel_value": [120, 145, 130],
      "has_data": true
    },
    {
      "name": "ortho2",
      "store": "geotiffs",
      "workspace": "test",
      "pixel_value": [140, 150, 160],
      "has_data": true
    }
  ]
}
```

### 8. 查询坐标点与所有图层的相交情况

```
GET /api/query_intersecting_layers?lat={纬度}&lon={经度}&workspace={工作区}
```

查询指定经纬度与工作空间下所有图层的相交情况，返回与坐标点相交的所有图层信息，包括边界框信息。

**必选参数:**
- lat: 纬度
- lon: 经度
- workspace: 工作区名称

**示例:**
```
GET /api/query_intersecting_layers?lat=30.123&lon=114.456&workspace=test
```

**响应:**
```json
{
  "status": "success",
  "count": 2,
  "coordinates": {
    "lat": 30.123,
    "lon": 114.456
  },
  "workspace": "test",
  "layers": [
    {
      "name": "ortho1",
      "store": "geotiffs",
      "workspace": "test",
      "type": "raster",
      "bbox": {
        "minx": 114.0,
        "miny": 30.0,
        "maxx": 115.0,
        "maxy": 31.0
      },
      "point_inside_bbox": true
    },
    {
      "name": "ortho2",
      "store": "geotiffs",
      "workspace": "test",
      "type": "raster",
      "bbox": {
        "minx": 114.5,
        "miny": 30.5,
        "maxx": 115.5,
        "maxy": 31.5
      },
      "point_inside_bbox": true
    }
  ]
}
```

### 9. 查询图层边界框相交情况

```
GET /api/layer_intersections?workspace={工作区}&layer={图层名称}
```

查询指定图层与同一工作空间其他图层的边界框相交情况，返回与查询图层相交的其他图层信息。

**必选参数:**
- workspace: 工作区名称
- layer: 图层名称

**示例:**
```
GET /api/layer_intersections?workspace=test&layer=ortho1
```

**响应:**
```json
{
  "status": "success",
  "target_layer": {
    "name": "ortho1",
    "store": "geotiffs",
    "workspace": "test",
    "type": "raster",
    "bbox": {
      "minx": 114.0,
      "miny": 30.0,
      "maxx": 115.0,
      "maxy": 31.0
    }
  },
  "intersecting_layers_count": 2,
  "intersecting_layers": [
    {
      "name": "ortho2",
      "store": "geotiffs",
      "workspace": "test",
      "type": "raster",
      "bbox": {
        "minx": 114.5,
        "miny": 30.5,
        "maxx": 115.5,
        "maxy": 31.5
      },
      "intersection": {
        "bbox": {
          "minx": 114.5,
          "miny": 30.5,
          "maxx": 115.0,
          "maxy": 31.0
        },
        "area": 0.25,
        "overlap_percentage_with_target": 25.0,
        "overlap_percentage_with_self": 25.0
      }
    },
    {
      "name": "boundary1",
      "store": "shapefiles",
      "workspace": "test",
      "type": "vector",
      "bbox": {
        "minx": 114.2,
        "miny": 30.2,
        "maxx": 114.8,
        "maxy": 30.8
      },
      "intersection": {
        "bbox": {
          "minx": 114.2,
          "miny": 30.2,
          "maxx": 114.8,
          "maxy": 30.8
        },
        "area": 0.36,
        "overlap_percentage_with_target": 36.0,
        "overlap_percentage_with_self": 100.0
      }
    }
  ]
}
```

**响应字段说明:**
- `target_layer`: 查询的目标图层信息
- `intersecting_layers_count`: 相交图层的数量
- `intersecting_layers`: 相交图层列表，按相交面积降序排列
  - `bbox`: 图层的边界框坐标
  - `intersection.bbox`: 相交区域的边界框坐标
  - `intersection.area`: 相交区域的面积
  - `intersection.overlap_percentage_with_target`: 相交区域占目标图层的百分比
  - `intersection.overlap_percentage_with_self`: 相交区域占当前图层的百分比

## 接口8响应字段说明

**查询坐标点与所有图层相交情况接口字段说明:**
- `count`: 相交图层的数量
- `coordinates`: 查询的坐标点信息
- `workspace`: 工作空间名称
- `layers`: 相交图层列表（只包含坐标点在边界框内的图层）
  - `name`: 图层名称
  - `store`: 数据存储名称
  - `workspace`: 工作空间名称
  - `type`: 图层类型（raster）
  - `bbox`: 图层的边界框坐标
    - `minx`, `miny`: 边界框左下角坐标
    - `maxx`, `maxy`: 边界框右上角坐标
  - `point_inside_bbox`: 坐标点是否在图层边界框内（始终为true）
  - `error`: 错误信息（如果处理过程中出现错误）