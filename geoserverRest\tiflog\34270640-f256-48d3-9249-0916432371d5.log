2025-07-28 17:18:36,702 - INFO - ============ TIF处理任务 34270640-f256-48d3-9249-0916432371d5 开始执行 ============
2025-07-28 17:18:36,708 - INFO - 开始时间: 2025-07-28 17:18:36
2025-07-28 17:18:36,711 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 17:18:36,717 - INFO - 系统信息:
2025-07-28 17:18:36,718 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 17:18:36,721 - INFO -   Python版本: 3.8.20
2025-07-28 17:18:36,733 - INFO -   GDAL版本: 3.9.2
2025-07-28 17:18:36,734 - INFO -   GPU可用: 否
2025-07-28 17:18:36,737 - INFO - 检查参数有效性...
2025-07-28 17:18:36,739 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 17:18:36,740 - INFO - 开始执行TIF处理流程...
2025-07-28 17:18:36,740 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 17:18:36,741 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-07-28 17:18:36,743 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-07-28 17:18:41,727 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17007, 高=20364, 波段数=4开始读取影像数据，大小约: 5284.58 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (17:18:41)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 17:18:41,743 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 17:18:42,190 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:08,  2.49it/s]
2025-07-28 17:18:42,844 - ERROR - 
处理数据块:  19%|#############5                                                         | 4/21 [00:01<00:04,  3.99it/s]
2025-07-28 17:18:45,032 - ERROR - 
处理数据块:  24%|################9                                                      | 5/21 [00:03<00:12,  1.26it/s]
2025-07-28 17:18:46,667 - ERROR - 
处理数据块:  48%|#################################3                                    | 10/21 [00:04<00:05,  2.07it/s]
2025-07-28 17:18:46,862 - ERROR - 
处理数据块:  81%|########################################################6             | 17/21 [00:05<00:00,  4.48it/s]
2025-07-28 17:18:46,891 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:05<00:00,  4.12it/s]
2025-07-28 17:18:46,895 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 17:18:46,897 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 17:18:46,951 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 394.41it/s]
2025-07-28 17:18:48,292 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 6.84 秒 (17:18:48)预计总处理时间: 约 20.53 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif (17:18:48)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif使用已处理过的影像信息影像信息: 宽=17007, 高=20364, 波段数=3掩码形状: (20364, 17007), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 尺寸: 17007x20364x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 17:18:48,294 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 17:19:36,234 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=42.58, std=63.34应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 17:19:36,256 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 17:19:36,257 - ERROR - [A
2025-07-28 17:19:36,519 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:05,  3.85it/s]
2025-07-28 17:19:36,519 - ERROR - [A
2025-07-28 17:19:36,633 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:01, 12.48it/s]
2025-07-28 17:19:36,634 - ERROR - [A
2025-07-28 17:19:36,735 - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:00, 18.06it/s]
2025-07-28 17:19:36,736 - ERROR - [A
2025-07-28 17:19:36,886 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 18.80it/s]
2025-07-28 17:19:36,887 - ERROR - [A
2025-07-28 17:19:37,020 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:00<00:00, 22.56it/s]
2025-07-28 17:19:37,021 - ERROR - [A
2025-07-28 17:19:37,122 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:00<00:00, 24.47it/s]
2025-07-28 17:19:37,123 - ERROR - [A
2025-07-28 17:19:37,231 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:00<00:00, 25.31it/s]
2025-07-28 17:19:37,232 - ERROR - [A
2025-07-28 17:19:37,250 - ERROR - [A
2025-07-28 17:20:35,429 - ERROR - 
处理波段:  33%|########################3                                                | 1/3 [01:47<03:34, 107.13s/it]
2025-07-28 17:20:43,343 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=45.79, std=65.41应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 17:20:43,345 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 17:20:43,348 - ERROR - [A
2025-07-28 17:20:43,646 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:05,  3.36it/s]
2025-07-28 17:20:43,646 - ERROR - [A
2025-07-28 17:20:44,173 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:08,  2.32it/s]
2025-07-28 17:20:44,173 - ERROR - [A
2025-07-28 17:20:44,359 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:05,  3.13it/s]
2025-07-28 17:20:44,359 - ERROR - [A
2025-07-28 17:20:45,216 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:01<00:09,  1.88it/s]
2025-07-28 17:20:45,216 - ERROR - [A
2025-07-28 17:20:45,665 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:02<00:08,  1.99it/s]
2025-07-28 17:20:45,666 - ERROR - [A
2025-07-28 17:20:46,084 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:07,  2.11it/s]
2025-07-28 17:20:46,141 - ERROR - [A
2025-07-28 17:20:48,087 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:04<00:13,  1.03it/s]
2025-07-28 17:20:48,088 - ERROR - [A
2025-07-28 17:20:48,448 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:05<00:10,  1.28it/s]
2025-07-28 17:20:48,448 - ERROR - [A
2025-07-28 17:20:48,843 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:05<00:07,  1.52it/s]
2025-07-28 17:20:48,843 - ERROR - [A
2025-07-28 17:20:49,112 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:05<00:05,  1.86it/s]
2025-07-28 17:20:49,113 - ERROR - [A
2025-07-28 17:20:55,248 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:11<00:22,  2.25s/it]
2025-07-28 17:20:55,249 - ERROR - [A
2025-07-28 17:20:55,452 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:12<00:14,  1.63s/it]
2025-07-28 17:20:55,669 - ERROR - [A
2025-07-28 17:21:00,459 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:17<00:21,  2.65s/it]
2025-07-28 17:21:00,461 - ERROR - [A
2025-07-28 17:21:01,078 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:17<00:14,  2.04s/it]
2025-07-28 17:21:01,079 - ERROR - [A
2025-07-28 17:21:03,340 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:19<00:12,  2.11s/it]
2025-07-28 17:21:03,340 - ERROR - [A
2025-07-28 17:21:06,096 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:22<00:11,  2.30s/it]
2025-07-28 17:21:06,096 - ERROR - [A
2025-07-28 17:21:10,825 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:27<00:12,  3.03s/it]
2025-07-28 17:21:10,825 - ERROR - [A
2025-07-28 17:21:15,775 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:32<00:10,  3.61s/it]
2025-07-28 17:21:15,781 - ERROR - [A
2025-07-28 17:21:20,985 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:37<00:08,  4.09s/it]
2025-07-28 17:21:20,986 - ERROR - [A
2025-07-28 17:21:26,359 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:43<00:04,  4.47s/it]
2025-07-28 17:21:26,359 - ERROR - [A
2025-07-28 17:21:26,366 - ERROR - [A
2025-07-28 17:22:37,058 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [03:48<01:55, 115.66s/it]
2025-07-28 17:22:42,871 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=35.76, std=51.77应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 17:22:42,873 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 17:22:42,878 - ERROR - [A
2025-07-28 17:22:43,354 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:09,  2.13it/s]
2025-07-28 17:22:43,355 - ERROR - [A
2025-07-28 17:22:43,632 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:06,  2.80it/s]
2025-07-28 17:22:43,632 - ERROR - [A
2025-07-28 17:22:43,939 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:06,  3.00it/s]
2025-07-28 17:22:43,940 - ERROR - [A
2025-07-28 17:22:44,242 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:01<00:05,  3.11it/s]
2025-07-28 17:22:44,243 - ERROR - [A
2025-07-28 17:22:44,352 - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:01,  8.48it/s]
2025-07-28 17:22:44,353 - ERROR - [A
2025-07-28 17:22:44,471 - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 12/21 [00:01<00:00, 13.51it/s]
2025-07-28 17:22:44,471 - ERROR - [A
2025-07-28 17:22:44,586 - ERROR - 
波段 3/3 写入进度:  76%|################################################               | 16/21 [00:01<00:00, 18.09it/s]
2025-07-28 17:22:44,587 - ERROR - [A
2025-07-28 17:22:44,672 - ERROR - [A
2025-07-28 17:24:28,218 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [05:39<00:00, 113.60s/it]
2025-07-28 17:24:28,219 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [05:39<00:00, 113.31s/it]
2025-07-28 17:25:06,559 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif影像保存完成，耗时: 340.40 秒 (17:24:28)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp (17:24:28)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tifTIF文件信息: 宽=17007, 高=20364, 波段数=3开始读取影像数据，大小约: 3963.44 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif提取TIF文件信息...TIF文件信息: 宽=17007, 高=20364, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 218929698 个无效像素检查波段 2...波段 2 检测到 218929698 个无效像素检查波段 3...波段 3 检测到 218929698 个无效像素掩码统计: 总像素数 346330548, 有效像素数 127400850 (36.79%), 无效像素数 218929698 (63.21%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 1 个初始轮廓轮廓面积统计: 最小=127380512.50, 最大=127380512.50, 平均=127380512.50, 总数=1将 1 个轮廓转换为地理坐标...
2025-07-28 17:25:06,572 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-28 17:25:07,014 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.30it/s]
2025-07-28 17:25:07,040 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.17it/s]
2025-07-28 17:25:07,111 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-28 17:25:20,162 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-28 17:25:20,176 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-28 17:25:21,457 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:01<00:18,  1.56it/s]
2025-07-28 17:25:21,676 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:13,  2.15it/s]
2025-07-28 17:25:21,717 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 20.12it/s]
2025-07-28 17:25:22,715 - INFO - 处理了 82263 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp写入shapefile特征...处理MultiPolygon，包含 19 个多边形
2025-07-28 17:25:22,717 - ERROR - 
写入多边形:   0%|                                                                               | 0/19 [00:00<?, ?it/s]
2025-07-28 17:25:22,732 - ERROR - 
写入多边形: 100%|####################################################################| 19/19 [00:00<00:00, 1927.34it/s]
2025-07-28 17:25:22,941 - INFO - 处理完成，耗时: 406.20 秒 (6.77 分钟)
2025-07-28 17:25:22,941 - INFO - 处理结果: 成功
2025-07-28 17:25:22,946 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 大小: 740.84 MB
2025-07-28 17:25:22,949 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp, 大小: 22.81 KB
2025-07-28 17:25:22,958 - INFO - TIF处理任务 34270640-f256-48d3-9249-0916432371d5 执行成功
2025-07-28 17:25:22,959 - INFO - 完成时间: 2025-07-28 17:25:22
2025-07-28 17:25:22,960 - INFO - 状态: 运行成功
2025-07-28 17:25:22,970 - INFO - ============ 任务执行结束 ============
