执行命令: D:\Drone_Project\ODM\ODM\run.bat D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project
开始时间: 2025-07-14 12:36:01

环境PATH: C:\Users\<USER>\anaconda3\envs\geoserverapi\Library\bin;C:\Users\<USER>\anaconda3\envs\geoserverapi\Lib\site-packages;C:\Users\<USER>\anaconda3\envs\geoserverapi\DLLs;C:\Users\<USER>\anaconda3\envs\geoserverapi;C:\Users\<USER>\anaconda3\envs\geoserverapi;C:\Users\<USER>\anaconda3\envs\geoserverapi\Library\mingw-w64\bin;C:\Users\<USER>\anaconda3\envs\geoserverapi\Library\usr\bin;C:\Users\<USER>\anaconda3\envs\geoserverapi\Library\bin;C:\Users\<USER>\anaconda3\envs\geoserverapi\Scripts;C:\Users\<USER>\anaconda3\envs\geoserverapi\bin;C:\Users\<USER>\anaconda3\condabin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.2\libnvvp;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.2\bin;C:\msys64\mingw64\bin;C:\Program Files (x86)\ShadowBot;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Eclipse Foundation\jdk-8.0.302.8-hotspot\bin;C:\Program Files\Microsoft MPI\Bin;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64_win\compiler;C:\Users\<USER>\anaconda3;C:\Program Files (x86)\Microsoft Visual Studio\Shared\Python37_64;C:\WINDOWS\system32;C:\Program Files\Common Files\Autodesk Shared;C:\Program Files (x86)\Autodesk\Backburner;C:\Program Files\Microsoft MPI\Bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\dotnet;C:\Strawberry\c\bin;C:\Strawberry\perl\site\bin;C:\Strawberry\perl\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files (x86)\Microsoft SQL Server\130\Tools\Binn;C:\Program Files\Microsoft SQL Server\130\Tools\Binn;C:\Program Files (x86)\Microsoft SQL Server\130\DTS\Binn;C:\Program Files\Microsoft SQL Server\130\DTS\Binn;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\130\Tools\Binn;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn;C:\Program Files\Java\jdk1.8.0_333\bin;C:\Program Files\nodejs;C:\Program Files\MySQL\MySQL Server 8.0\bin;D:\redis;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\XFTP;C:\Program Files\Docker\Docker\resources\bin;F:\ffmpeg-7.1.1-full_build\ffmpeg-7.1.1-full_build\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\NVIDIA Corporation\Nsight Compute 2023.2.0;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.2\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.2\libnvvp;C:\Program Files (x86)\ShadowBot;F:\python\Scripts;F:\python;C:\Python27\ArcGIS10.8;C:\Python27\ArcGIS10.8\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files (x86)\Microsoft Visual Studio\Shared\Python37_64;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.9;D:\Microsoft VS Code\bin;C:\Program Files\JetBrains\PyCharm Community Edition 2022.2\bin;.;C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64;C:\Users\<USER>\.dotnet\tools;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin

Traceback (most recent call last):
  File "D:\Drone_Project\ODM\ODM\\run.py", line 18, in <module>
    from stages.odm_app import ODMApp
  File "D:\Drone_Project\ODM\ODM\stages\odm_app.py", line 15, in <module>
    from stages.odm_orthophoto import ODMOrthoPhotoStage
  File "D:\Drone_Project\ODM\ODM\stages\odm_orthophoto.py", line 12, in <module>
    from opendm.cutline import compute_cutline
  File "D:\Drone_Project\ODM\ODM\opendm\cutline.py", line 18, in <module>
    from shapely.geometry import LineString, mapping, shape
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\shapely\geometry\__init__.py", line 4, in <module>
    from .base import CAP_STYLE, JOIN_STYLE
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\shapely\geometry\base.py", line 19, in <module>
    from shapely.coords import CoordinateSequence
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\shapely\coords.py", line 8, in <module>
    from shapely.geos import lgeos
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\shapely\geos.py", line 154, in <module>
    _lgeos = CDLL(os.path.join(sys.prefix, 'Library', 'bin', 'geos_c.dll'))
  File "ctypes\__init__.py", line 373, in __init__
FileNotFoundError: Could not find module 'D:\Drone_Project\ODM\ODM\venv\Library\bin\geos_c.dll'. Try using the full path with constructor syntax.

完成时间: 2025-07-14 12:36:06
返回代码: 0
状态: 运行成功
