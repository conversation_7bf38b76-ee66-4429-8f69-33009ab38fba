2025-07-18 09:08:31,959 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250718_090831.log
2025-07-18 09:08:31,961 - geo_publisher - INFO - 加载了 0 个任务状态
2025-07-18 09:08:31,983 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 09:08:31,984 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 09:08:32,004 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 09:08:32,011 - root - INFO - === GeoServer REST API服务 ===
2025-07-18 09:08:32,012 - root - INFO - 主机: 0.0.0.0
2025-07-18 09:08:32,012 - root - INFO - 端口: 5083
2025-07-18 09:08:32,013 - root - INFO - 调试模式: 禁用
2025-07-18 09:08:32,014 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-18 09:08:32,124 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-18 09:08:32,125 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
