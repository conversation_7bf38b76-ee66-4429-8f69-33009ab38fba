2025-07-09 16:53:18,231 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250709_165318.log
2025-07-09 16:53:18,242 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-09 16:53:18,242 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-09 16:53:18,290 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-09 16:53:18,296 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-09 16:53:18,298 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-09 16:53:18,309 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-09 16:53:18,315 - root - INFO - === GeoServer REST API服务 ===
2025-07-09 16:53:18,315 - root - INFO - 主机: 0.0.0.0
2025-07-09 16:53:18,316 - root - INFO - 端口: 5083
2025-07-09 16:53:18,316 - root - INFO - 调试模式: 禁用
2025-07-09 16:53:18,316 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-09 16:53:18,328 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-09 16:53:18,329 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 16:53:43,886 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-09 16:53:43,914 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-09 16:53:43,916 - werkzeug - INFO - ************** - - [09/Jul/2025 16:53:43] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:18:10,554 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-09 17:18:10,557 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-09 17:18:10,653 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-09 17:18:10,655 - werkzeug - INFO - ************** - - [09/Jul/2025 17:18:10] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-09 17:18:10,694 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-09 17:18:10,695 - werkzeug - INFO - ************** - - [09/Jul/2025 17:18:10] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:18:49,193 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-09 17:18:49,231 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-09 17:18:49,235 - werkzeug - INFO - ************** - - [09/Jul/2025 17:18:49] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:18:50,444 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-09 17:18:50,493 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-09 17:18:50,498 - werkzeug - INFO - ************** - - [09/Jul/2025 17:18:50] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-09 17:18:58,943 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-09 17:18:58,969 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-09 17:18:58,970 - werkzeug - INFO - ************** - - [09/Jul/2025 17:18:58] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-09 17:19:56,442 - werkzeug - INFO - ************** - - [09/Jul/2025 17:19:56] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:19:56,550 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-09 17:19:56,586 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-09 17:19:56,588 - werkzeug - INFO - ************** - - [09/Jul/2025 17:19:56] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:20:00,404 - werkzeug - INFO - ************** - - [09/Jul/2025 17:20:00] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-09 17:20:00,408 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-09 17:20:00,434 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-09 17:20:00,436 - werkzeug - INFO - ************** - - [09/Jul/2025 17:20:00] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-09 17:21:17,157 - werkzeug - INFO - ************** - - [09/Jul/2025 17:21:17] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:21:17,230 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-09 17:21:17,265 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-09 17:21:17,267 - werkzeug - INFO - ************** - - [09/Jul/2025 17:21:17] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:21:18,378 - werkzeug - INFO - ************** - - [09/Jul/2025 17:21:18] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-09 17:21:18,400 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-09 17:21:18,451 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-09 17:21:18,453 - werkzeug - INFO - ************** - - [09/Jul/2025 17:21:18] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-09 17:23:47,261 - werkzeug - INFO - ************** - - [09/Jul/2025 17:23:47] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:23:47,310 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-09 17:23:47,364 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-09 17:23:47,366 - werkzeug - INFO - ************** - - [09/Jul/2025 17:23:47] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:23:48,519 - werkzeug - INFO - ************** - - [09/Jul/2025 17:23:48] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-09 17:23:48,527 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-09 17:23:48,603 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-09 17:23:48,606 - werkzeug - INFO - ************** - - [09/Jul/2025 17:23:48] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-09 17:24:09,324 - werkzeug - INFO - ************** - - [09/Jul/2025 17:24:09] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:24:09,403 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-09 17:24:09,437 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-09 17:24:09,439 - werkzeug - INFO - ************** - - [09/Jul/2025 17:24:09] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:24:11,713 - werkzeug - INFO - ************** - - [09/Jul/2025 17:24:11] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-09 17:24:11,718 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-09 17:24:11,742 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-09 17:24:11,744 - werkzeug - INFO - ************** - - [09/Jul/2025 17:24:11] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-09 17:56:01,576 - werkzeug - INFO - ************** - - [09/Jul/2025 17:56:01] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:56:01,580 - werkzeug - INFO - ************** - - [09/Jul/2025 17:56:01] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:56:01,586 - werkzeug - INFO - ************** - - [09/Jul/2025 17:56:01] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:56:01,588 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-09 17:56:01,593 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-09 17:56:01,599 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-09 17:56:01,666 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-09 17:56:01,667 - werkzeug - INFO - ************** - - [09/Jul/2025 17:56:01] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:56:01,774 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-09 17:56:01,777 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-09 17:56:01,783 - werkzeug - INFO - ************** - - [09/Jul/2025 17:56:01] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:56:01,784 - werkzeug - INFO - ************** - - [09/Jul/2025 17:56:01] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:56:12,360 - werkzeug - INFO - ************** - - [09/Jul/2025 17:56:12] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:56:12,366 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-09 17:56:12,394 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-09 17:56:12,396 - werkzeug - INFO - ************** - - [09/Jul/2025 17:56:12] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:56:12,652 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-09 17:56:12,657 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-09 17:56:12,687 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-09 17:56:12,688 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-09 17:56:12,690 - werkzeug - INFO - ************** - - [09/Jul/2025 17:56:12] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-09 17:56:12,691 - werkzeug - INFO - ************** - - [09/Jul/2025 17:56:12] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
