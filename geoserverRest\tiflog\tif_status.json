{"6b4d8e21-d383-4a21-9453-2f7d34f84a85": {"task_id": "6b4d8e21-d383-4a21-9453-2f7d34f84a85", "type": "tif_process", "params": {"input_tif": "D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250705171600\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250705171600\\project\\odm_orthophoto\\odm_orthophoto_out.tif", "output_shp": "D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250705171600\\project\\odm_orthophoto\\odm_orthophoto_out.shp", "black_threshold": "0", "white_threshold": "255", "keep_alpha": false, "protect_interior": false, "use_gpu": false}, "status": "正在运行", "start_time": "2025-07-16 09:15:46", "end_time": null, "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\6b4d8e21-d383-4a21-9453-2f7d34f84a85.log"}, "7e439b3c-a76f-42be-a3e7-b54099b9235b": {"task_id": "7e439b3c-a76f-42be-a3e7-b54099b9235b", "type": "tif_process", "params": {"input_tif": "D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250705171600\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250705171600\\project\\odm_orthophoto\\odm_orthophoto_out.tif", "output_shp": "D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250705171600\\project\\odm_orthophoto\\odm_orthophoto_out.shp", "black_threshold": "0", "white_threshold": "255", "keep_alpha": false, "protect_interior": false, "use_gpu": false}, "status": "运行失败", "start_time": "2025-07-16 09:41:05", "end_time": "2025-07-16 09:41:29", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\7e439b3c-a76f-42be-a3e7-b54099b9235b.log", "progress": {"读取数据": 10, "处理数据": 18, "创建掩码": 25, "错误": 0, "总体": 0.0}, "current_stage": "错误", "current_message": "处理过程中发生错误: create_full_mask_parallel() got an unexpected keyword argument 'progress_callback'", "result": {"success": false}}, "58b96ef4-1f7a-464c-be1c-307266bf4b01": {"task_id": "58b96ef4-1f7a-464c-be1c-307266bf4b01", "parameters": {"input_tif": "D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif", "output_tif": "D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif", "output_shp": "D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行成功", "start_time": "2025-07-16 11:33:37", "end_time": "2025-07-16 12:17:34", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\58b96ef4-1f7a-464c-be1c-307266bf4b01.log", "result": true}, "682c5330-92f0-48ac-8d8e-9721547d408d": {"task_id": "682c5330-92f0-48ac-8d8e-9721547d408d", "parameters": {"input_tif": "D:/Drone_Project/geoserverapi/data/20250701/nanning.tif", "output_tif": "D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.tif", "output_shp": "D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行成功", "start_time": "2025-07-16 15:38:18", "end_time": "2025-07-16 15:38:20", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\682c5330-92f0-48ac-8d8e-9721547d408d.log", "result": true}, "39d55f9f-f1e0-49ed-94c1-e4758afdc070": {"task_id": "39d55f9f-f1e0-49ed-94c1-e4758afdc070", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171600\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:\\Drone_Project\\nginxData\\ODM\\Output\\20250705171600\\20250705171600.tif", "output_shp": "D:\\Drone_Project\\nginxData\\ODM\\Output\\20250705171600\\20250705171600.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行成功", "start_time": "2025-07-17 09:50:00", "end_time": "2025-07-17 10:28:19", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\39d55f9f-f1e0-49ed-94c1-e4758afdc070.log", "result": true}, "5f3f6493-e2d8-451c-a8a2-3745ce521c0b": {"task_id": "5f3f6493-e2d8-451c-a8a2-3745ce521c0b", "parameters": {"input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行成功", "start_time": "2025-07-17 18:46:09", "end_time": "2025-07-17 19:35:02", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\5f3f6493-e2d8-451c-a8a2-3745ce521c0b.log", "result": true}, "17c7bb5e-fb34-44a5-8a37-2d345848a203": {"task_id": "17c7bb5e-fb34-44a5-8a37-2d345848a203", "parameters": {"input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行成功", "start_time": "2025-07-18 10:27:06", "end_time": "2025-07-18 11:01:59", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\17c7bb5e-fb34-44a5-8a37-2d345848a203.log", "result": true}, "a413982e-ed7d-4158-b465-872ed73274df": {"task_id": "a413982e-ed7d-4158-b465-872ed73274df", "parameters": {"input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行成功", "start_time": "2025-07-18 12:51:50", "end_time": "2025-07-18 13:32:57", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\a413982e-ed7d-4158-b465-872ed73274df.log", "result": true}, "3fb3bacd-fccd-47a5-90b0-0a8231300ecb": {"task_id": "3fb3bacd-fccd-47a5-90b0-0a8231300ecb", "parameters": {"input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行成功", "start_time": "2025-07-21 11:10:16", "end_time": "2025-07-21 11:18:00", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\3fb3bacd-fccd-47a5-90b0-0a8231300ecb.log", "result": true}, "f3a51a13-d851-4c11-8cac-ab5a5c40a9d8": {"task_id": "f3a51a13-d851-4c11-8cac-ab5a5c40a9d8", "parameters": {"input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行成功", "start_time": "2025-07-21 11:38:34", "end_time": "2025-07-21 11:45:43", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\f3a51a13-d851-4c11-8cac-ab5a5c40a9d8.log", "result": true}, "455ae717-9ef6-40de-9f54-115f95e8dd3d": {"task_id": "455ae717-9ef6-40de-9f54-115f95e8dd3d", "parameters": {"input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行成功", "start_time": "2025-07-24 10:07:00", "end_time": "2025-07-24 10:29:22", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\455ae717-9ef6-40de-9f54-115f95e8dd3d.log", "result": true}, "27f22d80-d57e-4b97-a8ca-24ab14bfd094": {"task_id": "27f22d80-d57e-4b97-a8ca-24ab14bfd094", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行失败", "start_time": "2025-07-24 11:36:06", "end_time": "2025-07-24 11:36:06", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\27f22d80-d57e-4b97-a8ca-24ab14bfd094.log", "error": "No module named 'psutil'", "error_details": {"error_type": "ModuleNotFoundError", "error_message": "No module named 'psutil'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Drone_Project\\geoserverapi\\geoserverRest\\core\\tif_process.py\", line 2746, in _run_tif_process\n    import psutil\nModuleNotFoundError: No module named 'psutil'\n"}}, "e4488b58-3968-4dc8-be93-dd5146cbf6fc": {"task_id": "e4488b58-3968-4dc8-be93-dd5146cbf6fc", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行失败", "start_time": "2025-07-24 11:37:08", "end_time": "2025-07-24 11:37:09", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\e4488b58-3968-4dc8-be93-dd5146cbf6fc.log", "error": "No module named 'psutil'", "error_details": {"error_type": "ModuleNotFoundError", "error_message": "No module named 'psutil'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Drone_Project\\geoserverapi\\geoserverRest\\core\\tif_process.py\", line 2746, in _run_tif_process\n    import psutil\nModuleNotFoundError: No module named 'psutil'\n"}}, "f68cbd03-ac4e-4c8f-a67b-6388e92b3c33": {"task_id": "f68cbd03-ac4e-4c8f-a67b-6388e92b3c33", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行失败", "start_time": "2025-07-24 11:38:11", "end_time": "2025-07-24 11:38:11", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\f68cbd03-ac4e-4c8f-a67b-6388e92b3c33.log", "error": "No module named 'psutil'", "error_details": {"error_type": "ModuleNotFoundError", "error_message": "No module named 'psutil'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Drone_Project\\geoserverapi\\geoserverRest\\core\\tif_process.py\", line 2746, in _run_tif_process\n    import psutil\nModuleNotFoundError: No module named 'psutil'\n"}}, "624f5fe6-13ce-4726-80fb-15dcaad3622e": {"task_id": "624f5fe6-13ce-4726-80fb-15dcaad3622e", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行失败", "start_time": "2025-07-24 11:39:13", "end_time": "2025-07-24 11:39:13", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\624f5fe6-13ce-4726-80fb-15dcaad3622e.log", "error": "No module named 'psutil'", "error_details": {"error_type": "ModuleNotFoundError", "error_message": "No module named 'psutil'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Drone_Project\\geoserverapi\\geoserverRest\\core\\tif_process.py\", line 2746, in _run_tif_process\n    import psutil\nModuleNotFoundError: No module named 'psutil'\n"}}, "31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5": {"task_id": "31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行失败", "start_time": "2025-07-24 11:40:15", "end_time": "2025-07-24 11:40:15", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5.log", "error": "No module named 'psutil'", "error_details": {"error_type": "ModuleNotFoundError", "error_message": "No module named 'psutil'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Drone_Project\\geoserverapi\\geoserverRest\\core\\tif_process.py\", line 2746, in _run_tif_process\n    import psutil\nModuleNotFoundError: No module named 'psutil'\n"}}, "5cf35114-6186-423c-b9d4-db8bb62f70c9": {"task_id": "5cf35114-6186-423c-b9d4-db8bb62f70c9", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行失败", "start_time": "2025-07-24 11:41:17", "end_time": "2025-07-24 11:41:17", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\5cf35114-6186-423c-b9d4-db8bb62f70c9.log", "error": "No module named 'psutil'", "error_details": {"error_type": "ModuleNotFoundError", "error_message": "No module named 'psutil'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Drone_Project\\geoserverapi\\geoserverRest\\core\\tif_process.py\", line 2746, in _run_tif_process\n    import psutil\nModuleNotFoundError: No module named 'psutil'\n"}}, "92e83768-8bc1-4899-8f65-750a731d4c2c": {"task_id": "92e83768-8bc1-4899-8f65-750a731d4c2c", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行失败", "start_time": "2025-07-24 11:42:20", "end_time": "2025-07-24 11:42:20", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\92e83768-8bc1-4899-8f65-750a731d4c2c.log", "error": "No module named 'psutil'", "error_details": {"error_type": "ModuleNotFoundError", "error_message": "No module named 'psutil'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Drone_Project\\geoserverapi\\geoserverRest\\core\\tif_process.py\", line 2746, in _run_tif_process\n    import psutil\nModuleNotFoundError: No module named 'psutil'\n"}}, "f7812583-5d32-4b4b-85fe-188ffb30535f": {"task_id": "f7812583-5d32-4b4b-85fe-188ffb30535f", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行失败", "start_time": "2025-07-24 11:43:22", "end_time": "2025-07-24 11:43:22", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\f7812583-5d32-4b4b-85fe-188ffb30535f.log", "error": "No module named 'psutil'", "error_details": {"error_type": "ModuleNotFoundError", "error_message": "No module named 'psutil'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Drone_Project\\geoserverapi\\geoserverRest\\core\\tif_process.py\", line 2746, in _run_tif_process\n    import psutil\nModuleNotFoundError: No module named 'psutil'\n"}}, "55649807-4f46-484f-bd52-d04b597d1216": {"task_id": "55649807-4f46-484f-bd52-d04b597d1216", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行失败", "start_time": "2025-07-24 11:44:24", "end_time": "2025-07-24 11:44:24", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\55649807-4f46-484f-bd52-d04b597d1216.log", "error": "No module named 'psutil'", "error_details": {"error_type": "ModuleNotFoundError", "error_message": "No module named 'psutil'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Drone_Project\\geoserverapi\\geoserverRest\\core\\tif_process.py\", line 2746, in _run_tif_process\n    import psutil\nModuleNotFoundError: No module named 'psutil'\n"}}, "7bfcd643-629e-4654-beb8-8bcfa7c5af36": {"task_id": "7bfcd643-629e-4654-beb8-8bcfa7c5af36", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行失败", "start_time": "2025-07-24 11:45:26", "end_time": "2025-07-24 11:45:26", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\7bfcd643-629e-4654-beb8-8bcfa7c5af36.log", "error": "No module named 'psutil'", "error_details": {"error_type": "ModuleNotFoundError", "error_message": "No module named 'psutil'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Drone_Project\\geoserverapi\\geoserverRest\\core\\tif_process.py\", line 2746, in _run_tif_process\n    import psutil\nModuleNotFoundError: No module named 'psutil'\n"}}, "f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df": {"task_id": "f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行失败", "start_time": "2025-07-24 15:16:08", "end_time": "2025-07-24 15:16:08", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df.log", "error": "No module named 'psutil'", "error_details": {"error_type": "ModuleNotFoundError", "error_message": "No module named 'psutil'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Drone_Project\\geoserverapi\\geoserverRest\\core\\tif_process.py\", line 2746, in _run_tif_process\nModuleNotFoundError: No module named 'psutil'\n"}}, "05ae2ff3-1996-4c74-a831-04ba23126c27": {"task_id": "05ae2ff3-1996-4c74-a831-04ba23126c27", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255}, "status": "运行失败", "start_time": "2025-07-24 15:17:08", "end_time": "2025-07-24 15:17:08", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\05ae2ff3-1996-4c74-a831-04ba23126c27.log", "error": "No module named 'psutil'", "error_details": {"error_type": "ModuleNotFoundError", "error_message": "No module named 'psutil'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Drone_Project\\geoserverapi\\geoserverRest\\core\\tif_process.py\", line 2746, in _run_tif_process\nModuleNotFoundError: No module named 'psutil'\n"}}, "7869ab0c-addd-47d1-b52b-5dcc70ba31fa": {"task_id": "7869ab0c-addd-47d1-b52b-5dcc70ba31fa", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-24 15:19:08", "end_time": "2025-07-24 15:27:49", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\7869ab0c-addd-47d1-b52b-5dcc70ba31fa.log", "result": true, "processing_time": "520.48秒"}, "4acbd746-f075-45ba-b856-04ec2ebb15e0": {"task_id": "4acbd746-f075-45ba-b856-04ec2ebb15e0", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-24 16:12:58", "end_time": "2025-07-24 16:18:55", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\4acbd746-f075-45ba-b856-04ec2ebb15e0.log", "result": true, "processing_time": "356.64秒"}, "5b370409-64f7-4fec-a2ad-fa2ee4a7c892": {"task_id": "5b370409-64f7-4fec-a2ad-fa2ee4a7c892", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-24 16:27:09", "end_time": "2025-07-24 16:33:29", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\5b370409-64f7-4fec-a2ad-fa2ee4a7c892.log", "result": true, "processing_time": "380.56秒"}, "881f151d-cf18-4740-a577-bf1832971e01": {"task_id": "881f151d-cf18-4740-a577-bf1832971e01", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-24 16:50:23", "end_time": "2025-07-24 16:53:14", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\881f151d-cf18-4740-a577-bf1832971e01.log", "result": true, "processing_time": "171.07秒"}, "6c8aba6f-9089-4964-9803-e9f3d639868d": {"task_id": "6c8aba6f-9089-4964-9803-e9f3d639868d", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-24 16:54:27", "end_time": "2025-07-24 17:00:12", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\6c8aba6f-9089-4964-9803-e9f3d639868d.log", "result": true, "processing_time": "344.99秒"}, "4c45af59-78c5-4f87-90b6-0418138bdd75": {"task_id": "4c45af59-78c5-4f87-90b6-0418138bdd75", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-25 17:08:23", "end_time": "2025-07-25 17:18:58", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\4c45af59-78c5-4f87-90b6-0418138bdd75.log", "result": true, "processing_time": "634.87秒"}, "a82f6a2a-a3df-489f-84b1-ff314ddf6191": {"task_id": "a82f6a2a-a3df-489f-84b1-ff314ddf6191", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-25 17:42:17", "end_time": "2025-07-25 17:51:39", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\a82f6a2a-a3df-489f-84b1-ff314ddf6191.log", "result": true, "processing_time": "562.38秒"}, "b6e90861-407a-4faf-9b63-e854fc59d3b3": {"task_id": "b6e90861-407a-4faf-9b63-e854fc59d3b3", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp", "black_threshold": 0, "white_threshold": 255}, "status": "正在运行", "start_time": "2025-07-28 09:10:20", "end_time": null, "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\b6e90861-407a-4faf-9b63-e854fc59d3b3.log"}, "93d102ee-9b6d-4655-bb20-8f8ec2a3bc17": {"task_id": "93d102ee-9b6d-4655-bb20-8f8ec2a3bc17", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-28 09:15:28", "end_time": "2025-07-28 09:30:02", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\93d102ee-9b6d-4655-bb20-8f8ec2a3bc17.log", "result": true, "processing_time": "873.75秒"}, "39c0179c-a598-430b-b16d-1f43fad72ea4": {"task_id": "39c0179c-a598-430b-b16d-1f43fad72ea4", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-28 09:19:16", "end_time": "2025-07-28 09:32:02", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\39c0179c-a598-430b-b16d-1f43fad72ea4.log", "result": true, "processing_time": "765.62秒"}, "fa4b06bc-66c8-4de0-a0bb-703173c8023c": {"task_id": "fa4b06bc-66c8-4de0-a0bb-703173c8023c", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp", "black_threshold": 0, "white_threshold": 255}, "status": "正在运行", "start_time": "2025-07-28 09:48:50", "end_time": null, "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\fa4b06bc-66c8-4de0-a0bb-703173c8023c.log"}, "2b14ab9b-9525-4978-8fb7-2f38496bdfa8": {"task_id": "2b14ab9b-9525-4978-8fb7-2f38496bdfa8", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp", "black_threshold": 0, "white_threshold": 255}, "status": "正在运行", "start_time": "2025-07-28 09:53:57", "end_time": null, "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\2b14ab9b-9525-4978-8fb7-2f38496bdfa8.log"}, "1f376117-4639-4e05-8be5-839efea9682a": {"task_id": "1f376117-4639-4e05-8be5-839efea9682a", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-28 10:41:17", "end_time": "2025-07-28 10:58:46", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\1f376117-4639-4e05-8be5-839efea9682a.log", "result": true, "processing_time": "1049.02秒"}, "da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd": {"task_id": "da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-28 11:34:14", "end_time": "2025-07-28 11:41:15", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd.log", "result": true, "processing_time": "420.47秒"}, "e7d4cb74-1671-4847-95f2-47f09a4a87b4": {"task_id": "e7d4cb74-1671-4847-95f2-47f09a4a87b4", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-28 15:14:14", "end_time": "2025-07-28 15:27:53", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\e7d4cb74-1671-4847-95f2-47f09a4a87b4.log", "result": true, "processing_time": "818.67秒"}, "9189cfe2-eb81-477d-ac3e-9c78f34b27fd": {"task_id": "9189cfe2-eb81-477d-ac3e-9c78f34b27fd", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-28 16:42:47", "end_time": "2025-07-28 16:54:45", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\9189cfe2-eb81-477d-ac3e-9c78f34b27fd.log", "result": true, "processing_time": "718.08秒"}, "34270640-f256-48d3-9249-0916432371d5": {"task_id": "34270640-f256-48d3-9249-0916432371d5", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-28 17:18:36", "end_time": "2025-07-28 17:25:22", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\34270640-f256-48d3-9249-0916432371d5.log", "result": true, "processing_time": "406.20秒"}, "7bc5ceb7-adb2-40d0-8b25-e1cf218e7ea2": {"task_id": "7bc5ceb7-adb2-40d0-8b25-e1cf218e7ea2", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-07-30 15:40:28", "end_time": "2025-07-30 15:49:16", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\7bc5ceb7-adb2-40d0-8b25-e1cf218e7ea2.log", "result": true, "processing_time": "526.99秒"}, "1bfb6367-c531-4a87-ab9b-bf935c260be6": {"task_id": "1bfb6367-c531-4a87-ab9b-bf935c260be6", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171599\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-08-04 11:25:41", "end_time": "2025-08-04 11:26:15", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\1bfb6367-c531-4a87-ab9b-bf935c260be6.log", "result": true, "processing_time": "33.78秒"}, "add2250d-60e9-44b3-a6ac-ade953e72018": {"task_id": "add2250d-60e9-44b3-a6ac-ade953e72018", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-08-04 11:41:11", "end_time": "2025-08-04 11:47:08", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\add2250d-60e9-44b3-a6ac-ade953e72018.log", "result": true, "processing_time": "357.04秒"}, "4275fc11-fafe-4eb1-b54d-8a009b6bf365": {"task_id": "4275fc11-fafe-4eb1-b54d-8a009b6bf365", "parameters": {"input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif", "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp", "black_threshold": 0, "white_threshold": 255, "use_gpu": false}, "status": "运行成功", "start_time": "2025-08-04 14:59:12", "end_time": "2025-08-04 15:05:32", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\tiflog\\4275fc11-fafe-4eb1-b54d-8a009b6bf365.log", "result": true, "processing_time": "380.31秒"}}