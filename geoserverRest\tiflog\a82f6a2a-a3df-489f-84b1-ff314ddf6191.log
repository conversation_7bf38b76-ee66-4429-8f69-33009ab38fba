2025-07-25 17:42:17,565 - INFO - ============ TIF处理任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 开始执行 ============
2025-07-25 17:42:17,566 - INFO - 开始时间: 2025-07-25 17:42:17
2025-07-25 17:42:17,567 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-25 17:42:17,569 - INFO - 系统信息:
2025-07-25 17:42:17,569 - INFO -   操作系统: Windows 10.0.19045
2025-07-25 17:42:17,571 - INFO -   Python版本: 3.8.20
2025-07-25 17:42:17,571 - INFO -   GDAL版本: 3.9.2
2025-07-25 17:42:17,572 - INFO -   GPU可用: 否
2025-07-25 17:42:17,573 - INFO - 检查参数有效性...
2025-07-25 17:42:17,573 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-25 17:42:17,576 - INFO - 开始执行TIF处理流程...
2025-07-25 17:42:17,577 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-25 17:42:17,578 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-25 17:42:17,578 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-25 17:42:21,833 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (17:42:21)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-25 17:42:21,875 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-25 17:42:22,264 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:06,  2.90it/s]
2025-07-25 17:42:22,372 - ERROR - 
处理数据块:  43%|##############################4                                        | 9/21 [00:00<00:00, 24.97it/s]
2025-07-25 17:42:22,517 - ERROR - 
处理数据块:  67%|##############################################6                       | 14/21 [00:00<00:00, 28.53it/s]
2025-07-25 17:42:22,729 - ERROR - 
处理数据块:  90%|###############################################################3      | 19/21 [00:00<00:00, 26.30it/s]
2025-07-25 17:42:22,954 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:01<00:00, 20.30it/s]
2025-07-25 17:42:22,955 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-25 17:42:22,956 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-25 17:42:23,031 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 279.19it/s]
2025-07-25 17:42:24,377 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.62 秒 (17:42:24)预计总处理时间: 约 7.87 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (17:42:24)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-25 17:42:24,379 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-25 17:42:33,253 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-25 17:42:33,257 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-25 17:42:33,258 - ERROR - [A
2025-07-25 17:42:33,843 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:11,  1.71it/s]
2025-07-25 17:42:33,843 - ERROR - [A
2025-07-25 17:42:34,012 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:03,  4.67it/s]
2025-07-25 17:42:34,013 - ERROR - [A
2025-07-25 17:42:34,117 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  5.67it/s]
2025-07-25 17:42:34,118 - ERROR - [A
2025-07-25 17:42:34,240 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:02,  6.30it/s]
2025-07-25 17:42:34,241 - ERROR - [A
2025-07-25 17:42:34,393 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:01<00:02,  6.40it/s]
2025-07-25 17:42:34,394 - ERROR - [A
2025-07-25 17:42:34,537 - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  6.54it/s]
2025-07-25 17:42:34,537 - ERROR - [A
2025-07-25 17:42:34,671 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:01,  6.79it/s]
2025-07-25 17:42:34,672 - ERROR - [A
2025-07-25 17:42:34,810 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  6.92it/s]
2025-07-25 17:42:34,811 - ERROR - [A
2025-07-25 17:42:34,955 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  6.91it/s]
2025-07-25 17:42:34,956 - ERROR - [A
2025-07-25 17:42:35,083 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:01,  7.16it/s]
2025-07-25 17:42:35,083 - ERROR - [A
2025-07-25 17:42:35,217 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:01<00:01,  7.24it/s]
2025-07-25 17:42:35,218 - ERROR - [A
2025-07-25 17:42:35,361 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:02<00:01,  7.16it/s]
2025-07-25 17:42:35,363 - ERROR - [A
2025-07-25 17:42:35,506 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:00,  7.08it/s]
2025-07-25 17:42:35,507 - ERROR - [A
2025-07-25 17:42:35,667 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:02<00:00,  6.79it/s]
2025-07-25 17:42:35,669 - ERROR - [A
2025-07-25 17:42:35,803 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:02<00:00,  6.95it/s]
2025-07-25 17:42:35,804 - ERROR - [A
2025-07-25 17:42:35,938 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:02<00:00,  7.08it/s]
2025-07-25 17:42:35,939 - ERROR - [A
2025-07-25 17:42:36,072 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:02<00:00,  7.19it/s]
2025-07-25 17:42:36,073 - ERROR - [A
2025-07-25 17:42:36,214 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:02<00:00,  7.15it/s]
2025-07-25 17:42:36,214 - ERROR - [A
2025-07-25 17:42:36,342 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:03<00:00,  7.35it/s]
2025-07-25 17:42:36,342 - ERROR - [A
2025-07-25 17:42:36,470 - ERROR - 
波段 1/3 写入进度: 100%|###############################################################| 21/21 [00:03<00:00,  7.47it/s]
2025-07-25 17:42:36,471 - ERROR - [A
2025-07-25 17:42:36,472 - ERROR - [A
2025-07-25 17:43:42,532 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:18<02:36, 78.15s/it]
2025-07-25 17:43:52,615 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-25 17:43:52,628 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-25 17:43:52,629 - ERROR - [A
2025-07-25 17:43:53,076 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:08,  2.24it/s]
2025-07-25 17:43:53,077 - ERROR - [A
2025-07-25 17:43:53,380 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:06,  2.76it/s]
2025-07-25 17:43:53,382 - ERROR - [A
2025-07-25 17:43:53,962 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:08,  2.16it/s]
2025-07-25 17:43:53,967 - ERROR - [A
2025-07-25 17:43:54,351 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:01<00:07,  2.31it/s]
2025-07-25 17:43:54,354 - ERROR - [A
2025-07-25 17:43:54,669 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:02<00:06,  2.55it/s]
2025-07-25 17:43:54,673 - ERROR - [A
2025-07-25 17:43:54,978 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:05,  2.75it/s]
2025-07-25 17:43:54,979 - ERROR - [A
2025-07-25 17:43:55,276 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:02<00:04,  2.92it/s]
2025-07-25 17:43:55,277 - ERROR - [A
2025-07-25 17:43:55,573 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:02<00:04,  3.05it/s]
2025-07-25 17:43:55,574 - ERROR - [A
2025-07-25 17:43:55,772 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:03<00:03,  3.48it/s]
2025-07-25 17:43:55,774 - ERROR - [A
2025-07-25 17:43:55,950 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:03<00:02,  3.94it/s]
2025-07-25 17:43:55,951 - ERROR - [A
2025-07-25 17:43:56,095 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:03<00:02,  4.54it/s]
2025-07-25 17:43:56,096 - ERROR - [A
2025-07-25 17:43:56,304 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:03<00:01,  4.61it/s]
2025-07-25 17:43:56,305 - ERROR - [A
2025-07-25 17:43:56,480 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:03<00:01,  4.90it/s]
2025-07-25 17:43:56,481 - ERROR - [A
2025-07-25 17:43:56,679 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:04<00:01,  4.94it/s]
2025-07-25 17:43:56,682 - ERROR - [A
2025-07-25 17:43:56,919 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:04<00:01,  4.66it/s]
2025-07-25 17:43:56,921 - ERROR - [A
2025-07-25 17:43:59,907 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:07<00:05,  1.05s/it]
2025-07-25 17:43:59,909 - ERROR - [A
2025-07-25 17:44:05,526 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:12<00:09,  2.42s/it]
2025-07-25 17:44:05,527 - ERROR - [A
2025-07-25 17:44:10,976 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:18<00:09,  3.33s/it]
2025-07-25 17:44:10,977 - ERROR - [A
2025-07-25 17:44:17,262 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:24<00:08,  4.22s/it]
2025-07-25 17:44:17,263 - ERROR - [A
2025-07-25 17:44:23,914 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:31<00:04,  4.95s/it]
2025-07-25 17:44:23,976 - ERROR - [A
2025-07-25 17:44:26,219 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:33<00:00,  4.16s/it]
2025-07-25 17:44:26,226 - ERROR - [A
2025-07-25 17:44:26,227 - ERROR - [A
2025-07-25 17:45:57,047 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [03:32<01:51, 111.31s/it]
2025-07-25 17:46:20,927 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-25 17:46:20,931 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-25 17:46:20,931 - ERROR - [A
2025-07-25 17:46:21,207 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:05,  3.63it/s]
2025-07-25 17:46:21,207 - ERROR - [A
2025-07-25 17:46:21,371 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:03,  4.76it/s]
2025-07-25 17:46:21,372 - ERROR - [A
2025-07-25 17:46:21,607 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:03,  4.51it/s]
2025-07-25 17:46:21,607 - ERROR - [A
2025-07-25 17:46:21,765 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:03,  5.08it/s]
2025-07-25 17:46:21,766 - ERROR - [A
2025-07-25 17:46:22,181 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:01<00:04,  3.63it/s]
2025-07-25 17:46:22,182 - ERROR - [A
2025-07-25 17:46:22,320 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  5.85it/s]
2025-07-25 17:46:22,320 - ERROR - [A
2025-07-25 17:46:22,519 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  7.09it/s]
2025-07-25 17:46:22,535 - ERROR - [A
2025-07-25 17:46:22,627 - ERROR - 
波段 3/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  7.50it/s]
2025-07-25 17:46:22,628 - ERROR - [A
2025-07-25 17:46:22,729 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:00, 11.92it/s]
2025-07-25 17:46:22,730 - ERROR - [A
2025-07-25 17:46:22,866 - ERROR - 
波段 3/3 写入进度:  76%|################################################               | 16/21 [00:01<00:00, 14.70it/s]
2025-07-25 17:46:22,867 - ERROR - [A
2025-07-25 17:46:22,959 - ERROR - [A
2025-07-25 17:50:09,460 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [07:45<00:00, 175.74s/it]
2025-07-25 17:50:09,463 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [07:45<00:00, 155.03s/it]
2025-07-25 17:51:22,964 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 466.00 秒 (17:50:10)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (17:50:10)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-25 17:51:22,991 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-25 17:51:23,499 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.98it/s]
2025-07-25 17:51:23,500 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.97it/s]
2025-07-25 17:51:23,900 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-25 17:51:37,395 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-25 17:51:37,396 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-25 17:51:38,824 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:01<00:20,  1.40it/s]
2025-07-25 17:51:39,023 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:13,  2.00it/s]
2025-07-25 17:51:39,056 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 18.68it/s]
2025-07-25 17:51:39,735 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-25 17:51:39,737 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-25 17:51:39,754 - ERROR - 
写入多边形: 100%|####################################################################| 35/35 [00:00<00:00, 2188.21it/s]
2025-07-25 17:51:39,963 - INFO - 处理完成，耗时: 562.38 秒 (9.37 分钟)
2025-07-25 17:51:39,964 - INFO - 处理结果: 成功
2025-07-25 17:51:39,969 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-25 17:51:39,970 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-25 17:51:39,976 - INFO - TIF处理任务 a82f6a2a-a3df-489f-84b1-ff314ddf6191 执行成功
2025-07-25 17:51:39,977 - INFO - 完成时间: 2025-07-25 17:51:39
2025-07-25 17:51:39,977 - INFO - 状态: 运行成功
2025-07-25 17:51:39,978 - INFO - ============ 任务执行结束 ============
