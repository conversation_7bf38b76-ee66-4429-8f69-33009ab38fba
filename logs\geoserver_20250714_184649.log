2025-07-14 18:46:49,443 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250714_184649.log
2025-07-14 18:46:49,446 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 18:46:49,446 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 18:46:50,018 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 18:46:50,084 - batch_executor - INFO - 加载了 35 个任务状态
2025-07-14 18:46:50,095 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 18:46:50,095 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 18:46:50,115 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 18:46:50,123 - root - INFO - === GeoServer REST API服务 ===
2025-07-14 18:46:50,124 - root - INFO - 主机: 0.0.0.0
2025-07-14 18:46:50,124 - root - INFO - 端口: 5083
2025-07-14 18:46:50,124 - root - INFO - 调试模式: 禁用
2025-07-14 18:46:50,124 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-14 18:46:50,143 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-14 18:46:50,145 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-14 18:53:16,441 - werkzeug - INFO - ************** - - [14/Jul/2025 18:53:16] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:53:16,449 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 18:53:16,767 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 18:53:16,774 - werkzeug - INFO - ************** - - [14/Jul/2025 18:53:16] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:54:16,820 - werkzeug - INFO - ************** - - [14/Jul/2025 18:54:16] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:54:16,826 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 18:54:16,871 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 18:54:16,878 - werkzeug - INFO - ************** - - [14/Jul/2025 18:54:16] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:22:30,490 - werkzeug - INFO - ************** - - [14/Jul/2025 19:22:30] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:22:30,500 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:22:30,593 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:22:30,595 - werkzeug - INFO - ************** - - [14/Jul/2025 19:22:30] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:22:50,675 - werkzeug - INFO - ************** - - [14/Jul/2025 19:22:50] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:22:50,679 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:22:50,707 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:22:50,708 - werkzeug - INFO - ************** - - [14/Jul/2025 19:22:50] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:22:56,799 - werkzeug - INFO - ************** - - [14/Jul/2025 19:22:56] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:22:56,803 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:22:56,830 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:22:56,831 - werkzeug - INFO - ************** - - [14/Jul/2025 19:22:56] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:23:02,045 - werkzeug - INFO - ************** - - [14/Jul/2025 19:23:02] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:23:02,050 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:23:02,083 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:23:02,086 - werkzeug - INFO - ************** - - [14/Jul/2025 19:23:02] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:23:03,592 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:23:03,618 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:23:03,620 - werkzeug - INFO - ************** - - [14/Jul/2025 19:23:03] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:24:11,941 - werkzeug - INFO - ************** - - [14/Jul/2025 19:24:11] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:24:11,948 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:24:11,999 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:24:12,028 - werkzeug - INFO - ************** - - [14/Jul/2025 19:24:12] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:24:30,364 - werkzeug - INFO - ************** - - [14/Jul/2025 19:24:30] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:24:30,369 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:24:30,401 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:24:30,402 - werkzeug - INFO - ************** - - [14/Jul/2025 19:24:30] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:27:08,705 - werkzeug - INFO - ************** - - [14/Jul/2025 19:27:08] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:27:08,711 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:27:08,750 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:27:08,764 - werkzeug - INFO - ************** - - [14/Jul/2025 19:27:08] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:27:09,941 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:27:10,013 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:27:10,050 - werkzeug - INFO - ************** - - [14/Jul/2025 19:27:10] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:32:03,463 - werkzeug - INFO - ************** - - [14/Jul/2025 19:32:03] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:32:03,469 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:32:03,513 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:32:03,514 - werkzeug - INFO - ************** - - [14/Jul/2025 19:32:03] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:32:38,345 - werkzeug - INFO - ************** - - [14/Jul/2025 19:32:38] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:32:38,353 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:32:38,454 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:32:38,461 - werkzeug - INFO - ************** - - [14/Jul/2025 19:32:38] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:35:07,445 - werkzeug - INFO - ************** - - [14/Jul/2025 19:35:07] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:35:07,452 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:35:07,492 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:35:07,495 - werkzeug - INFO - ************** - - [14/Jul/2025 19:35:07] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:35:09,007 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:35:09,061 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:35:09,063 - werkzeug - INFO - ************** - - [14/Jul/2025 19:35:09] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:45:10,193 - werkzeug - INFO - ************** - - [14/Jul/2025 19:45:10] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:45:10,204 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:45:10,299 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:45:10,301 - werkzeug - INFO - ************** - - [14/Jul/2025 19:45:10] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:45:18,527 - werkzeug - INFO - ************** - - [14/Jul/2025 19:45:18] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:45:18,534 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:45:18,575 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:45:18,577 - werkzeug - INFO - ************** - - [14/Jul/2025 19:45:18] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:51:08,178 - werkzeug - INFO - ************** - - [14/Jul/2025 19:51:08] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:51:08,271 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:51:08,370 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:51:08,388 - werkzeug - INFO - ************** - - [14/Jul/2025 19:51:08] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:51:20,707 - werkzeug - INFO - ************** - - [14/Jul/2025 19:51:20] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:51:20,797 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:51:20,914 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:51:20,943 - werkzeug - INFO - ************** - - [14/Jul/2025 19:51:20] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:51:44,128 - werkzeug - INFO - ************** - - [14/Jul/2025 19:51:44] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:51:44,158 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:51:44,205 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:51:44,209 - werkzeug - INFO - ************** - - [14/Jul/2025 19:51:44] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:55:37,365 - werkzeug - INFO - ************** - - [14/Jul/2025 19:55:37] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-14 19:55:37,369 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-14 19:55:37,398 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-14 19:55:37,400 - werkzeug - INFO - ************** - - [14/Jul/2025 19:55:37] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-14 19:57:14,804 - werkzeug - INFO - ************** - - [14/Jul/2025 19:57:14] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:57:14,811 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:57:14,850 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:57:14,853 - werkzeug - INFO - ************** - - [14/Jul/2025 19:57:14] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:57:20,807 - werkzeug - INFO - ************** - - [14/Jul/2025 19:57:20] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 19:57:20,812 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 19:57:20,842 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 19:57:20,844 - werkzeug - INFO - ************** - - [14/Jul/2025 19:57:20] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:05:21,844 - werkzeug - INFO - ************** - - [14/Jul/2025 20:05:21] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:05:21,859 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:05:21,972 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:05:21,974 - werkzeug - INFO - ************** - - [14/Jul/2025 20:05:21] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:05:26,260 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:05:26,328 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:05:26,332 - werkzeug - INFO - ************** - - [14/Jul/2025 20:05:26] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:06:33,260 - werkzeug - INFO - ************** - - [14/Jul/2025 20:06:33] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:06:33,272 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:06:33,316 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:06:33,341 - werkzeug - INFO - ************** - - [14/Jul/2025 20:06:33] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:06:37,437 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:06:37,476 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:06:37,478 - werkzeug - INFO - ************** - - [14/Jul/2025 20:06:37] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:11:53,246 - werkzeug - INFO - ************** - - [14/Jul/2025 20:11:53] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:11:53,251 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:11:53,342 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:11:53,343 - werkzeug - INFO - ************** - - [14/Jul/2025 20:11:53] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:11:59,057 - werkzeug - INFO - ************** - - [14/Jul/2025 20:11:59] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:11:59,115 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:11:59,161 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:11:59,165 - werkzeug - INFO - ************** - - [14/Jul/2025 20:11:59] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:12:14,043 - werkzeug - INFO - ************** - - [14/Jul/2025 20:12:14] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:12:14,048 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:12:14,082 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:12:14,084 - werkzeug - INFO - ************** - - [14/Jul/2025 20:12:14] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:14:18,752 - werkzeug - INFO - ************** - - [14/Jul/2025 20:14:18] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-14 20:14:18,791 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-14 20:14:18,823 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-14 20:14:18,825 - werkzeug - INFO - ************** - - [14/Jul/2025 20:14:18] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-14 20:16:06,095 - werkzeug - INFO - ************** - - [14/Jul/2025 20:16:06] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:16:06,100 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:16:06,153 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:16:06,159 - werkzeug - INFO - ************** - - [14/Jul/2025 20:16:06] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:16:06,646 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:16:06,720 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:16:06,819 - werkzeug - INFO - ************** - - [14/Jul/2025 20:16:06] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:21:39,523 - werkzeug - INFO - ************** - - [14/Jul/2025 20:21:39] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:21:39,530 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:21:39,610 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:21:39,611 - werkzeug - INFO - ************** - - [14/Jul/2025 20:21:39] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:25:01,838 - werkzeug - INFO - ************** - - [14/Jul/2025 20:25:01] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:25:01,846 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:25:01,882 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:25:01,883 - werkzeug - INFO - ************** - - [14/Jul/2025 20:25:01] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:28:46,637 - werkzeug - INFO - ************** - - [14/Jul/2025 20:28:46] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:28:46,645 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:28:46,697 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:28:46,707 - werkzeug - INFO - ************** - - [14/Jul/2025 20:28:46] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:30:15,205 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:30:15,234 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:30:15,235 - werkzeug - INFO - ************** - - [14/Jul/2025 20:30:15] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:30:15,298 - werkzeug - INFO - ************** - - [14/Jul/2025 20:30:15] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-14 20:30:22,425 - root - INFO - 获取图层 test_myworkspace:id1 的信息
2025-07-14 20:30:22,462 - root - INFO - 成功获取图层 test_myworkspace:id1 的边界框信息
2025-07-14 20:30:22,464 - werkzeug - INFO - ************** - - [14/Jul/2025 20:30:22] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id1 HTTP/1.1" 200 -
2025-07-14 20:34:36,506 - werkzeug - INFO - ************** - - [14/Jul/2025 20:34:36] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:34:36,515 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:34:36,612 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:34:36,614 - werkzeug - INFO - ************** - - [14/Jul/2025 20:34:36] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:37:52,820 - werkzeug - INFO - ************** - - [14/Jul/2025 20:37:52] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:37:52,830 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 20:37:52,871 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 20:37:52,875 - werkzeug - INFO - ************** - - [14/Jul/2025 20:37:52] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 20:37:56,172 - werkzeug - INFO - ************** - - [14/Jul/2025 20:37:56] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=id1 HTTP/1.1" 200 -
2025-07-14 20:37:56,192 - root - INFO - 获取图层 test_myworkspace:id1 的信息
2025-07-14 20:37:56,248 - root - INFO - 成功获取图层 test_myworkspace:id1 的边界框信息
2025-07-14 20:37:56,294 - werkzeug - INFO - ************** - - [14/Jul/2025 20:37:56] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id1 HTTP/1.1" 200 -
2025-07-15 08:03:14,638 - werkzeug - INFO - ************** - - [15/Jul/2025 08:03:14] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:03:14,667 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 08:03:14,752 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 08:03:14,753 - werkzeug - INFO - ************** - - [15/Jul/2025 08:03:14] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:03:15,905 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 08:03:15,957 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 08:03:15,961 - werkzeug - INFO - ************** - - [15/Jul/2025 08:03:15] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:03:30,141 - werkzeug - INFO - ************** - - [15/Jul/2025 08:03:30] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=id7 HTTP/1.1" 200 -
2025-07-15 08:03:30,244 - root - INFO - 获取图层 test_myworkspace:id7 的信息
2025-07-15 08:03:30,327 - root - INFO - 成功获取图层 test_myworkspace:id7 的边界框信息
2025-07-15 08:03:30,337 - werkzeug - INFO - ************** - - [15/Jul/2025 08:03:30] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id7 HTTP/1.1" 200 -
2025-07-15 08:19:19,447 - werkzeug - INFO - ************** - - [15/Jul/2025 08:19:19] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:19:19,450 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 08:19:19,506 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 08:19:19,507 - werkzeug - INFO - ************** - - [15/Jul/2025 08:19:19] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:21:45,415 - werkzeug - INFO - ************** - - [15/Jul/2025 08:21:45] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:21:45,424 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 08:21:45,463 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 08:21:45,468 - werkzeug - INFO - ************** - - [15/Jul/2025 08:21:45] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:22:34,059 - werkzeug - INFO - ************** - - [15/Jul/2025 08:22:34] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=id1 HTTP/1.1" 200 -
2025-07-15 08:22:34,064 - root - INFO - 获取图层 test_myworkspace:id1 的信息
2025-07-15 08:22:34,092 - root - INFO - 成功获取图层 test_myworkspace:id1 的边界框信息
2025-07-15 08:22:34,094 - werkzeug - INFO - ************** - - [15/Jul/2025 08:22:34] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id1 HTTP/1.1" 200 -
2025-07-15 08:22:48,500 - werkzeug - INFO - ************** - - [15/Jul/2025 08:22:48] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=id2 HTTP/1.1" 200 -
2025-07-15 08:22:48,505 - root - INFO - 获取图层 test_myworkspace:id2 的信息
2025-07-15 08:22:48,529 - root - INFO - 成功获取图层 test_myworkspace:id2 的边界框信息
2025-07-15 08:22:48,530 - werkzeug - INFO - ************** - - [15/Jul/2025 08:22:48] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id2 HTTP/1.1" 200 -
2025-07-15 08:24:05,164 - werkzeug - INFO - ************** - - [15/Jul/2025 08:24:05] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=id1 HTTP/1.1" 200 -
2025-07-15 08:24:05,168 - root - INFO - 获取图层 test_myworkspace:id1 的信息
2025-07-15 08:24:05,193 - root - INFO - 成功获取图层 test_myworkspace:id1 的边界框信息
2025-07-15 08:24:05,194 - werkzeug - INFO - ************** - - [15/Jul/2025 08:24:05] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id1 HTTP/1.1" 200 -
2025-07-15 08:24:17,396 - werkzeug - INFO - ************** - - [15/Jul/2025 08:24:17] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=id2 HTTP/1.1" 200 -
2025-07-15 08:24:17,400 - root - INFO - 获取图层 test_myworkspace:id2 的信息
2025-07-15 08:24:17,423 - root - INFO - 成功获取图层 test_myworkspace:id2 的边界框信息
2025-07-15 08:24:17,425 - werkzeug - INFO - ************** - - [15/Jul/2025 08:24:17] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id2 HTTP/1.1" 200 -
2025-07-15 08:32:02,752 - werkzeug - INFO - ************** - - [15/Jul/2025 08:32:02] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:32:02,758 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 08:32:02,858 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 08:32:02,876 - werkzeug - INFO - ************** - - [15/Jul/2025 08:32:02] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:34:12,392 - werkzeug - INFO - ************** - - [15/Jul/2025 08:34:12] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:34:12,477 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 08:34:12,685 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 08:34:12,690 - werkzeug - INFO - ************** - - [15/Jul/2025 08:34:12] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:35:20,159 - werkzeug - INFO - ************** - - [15/Jul/2025 08:35:20] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:35:20,164 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 08:35:20,199 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 08:35:20,201 - werkzeug - INFO - ************** - - [15/Jul/2025 08:35:20] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:48:07,065 - werkzeug - INFO - ************** - - [15/Jul/2025 08:48:07] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:48:07,075 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 08:48:07,182 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 08:48:07,183 - werkzeug - INFO - ************** - - [15/Jul/2025 08:48:07] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:48:13,386 - werkzeug - INFO - ************** - - [15/Jul/2025 08:48:13] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:48:13,392 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 08:48:13,427 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 08:48:13,429 - werkzeug - INFO - ************** - - [15/Jul/2025 08:48:13] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 08:48:17,847 - root - INFO - 开始查询坐标点 (22.89790922390854, 108.23891278305464) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 08:48:18,193 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 08:48:18,337 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 08:48:18,379 - root - INFO - 图层 'id1' 在该坐标点有有效数据，添加到结果中
2025-07-15 08:48:18,379 - root - INFO - 在坐标点 (22.89790922390854, 108.23891278305464) 处找到 2 个有有效数据的栅格图层
2025-07-15 08:48:18,380 - werkzeug - INFO - ************** - - [15/Jul/2025 08:48:18] "GET /api/query_values?lat=22.89790922390854&lon=108.23891278305464&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 08:48:27,334 - root - INFO - 开始查询坐标点 (22.877855221718306, 108.31245276403436) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 08:48:27,639 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 08:48:27,663 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 08:48:27,699 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 08:48:27,700 - root - INFO - 在坐标点 (22.877855221718306, 108.31245276403436) 处找到 2 个有有效数据的栅格图层
2025-07-15 08:48:27,701 - werkzeug - INFO - ************** - - [15/Jul/2025 08:48:27] "GET /api/query_values?lat=22.877855221718306&lon=108.31245276403436&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 08:48:33,509 - root - INFO - 开始查询坐标点 (22.791824305002677, 108.42737121703989) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 08:48:33,806 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 08:48:33,833 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 08:48:33,875 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 08:48:33,876 - root - INFO - 在坐标点 (22.791824305002677, 108.42737121703989) 处找到 2 个有有效数据的栅格图层
2025-07-15 08:48:33,877 - werkzeug - INFO - ************** - - [15/Jul/2025 08:48:33] "GET /api/query_values?lat=22.791824305002677&lon=108.42737121703989&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 08:48:46,942 - root - INFO - 开始查询坐标点 (22.79399383043777, 108.34912467727746) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 08:48:47,240 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 08:48:47,262 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 08:48:47,293 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-15 08:48:47,293 - root - INFO - 在坐标点 (22.79399383043777, 108.34912467727746) 处找到 2 个有有效数据的栅格图层
2025-07-15 08:48:47,296 - werkzeug - INFO - ************** - - [15/Jul/2025 08:48:47] "GET /api/query_values?lat=22.79399383043777&lon=108.34912467727746&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:03:35,535 - werkzeug - INFO - ************** - - [15/Jul/2025 09:03:35] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:03:35,540 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:03:35,599 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:03:35,613 - werkzeug - INFO - ************** - - [15/Jul/2025 09:03:35] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:03:39,281 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:03:39,306 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:03:39,308 - werkzeug - INFO - ************** - - [15/Jul/2025 09:03:39] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:03:42,774 - root - INFO - 开始查询坐标点 (22.845690368551743, 108.32618022715059) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:03:43,068 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:03:43,094 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:03:43,121 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:03:43,121 - root - INFO - 在坐标点 (22.845690368551743, 108.32618022715059) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:03:43,122 - werkzeug - INFO - ************** - - [15/Jul/2025 09:03:43] "GET /api/query_values?lat=22.845690368551743&lon=108.32618022715059&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:03:53,895 - root - INFO - 开始查询坐标点 (22.794174621746222, 108.41933084578608) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:03:54,167 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:03:54,188 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:03:54,220 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:03:54,220 - root - INFO - 在坐标点 (22.794174621746222, 108.41933084578608) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:03:54,224 - werkzeug - INFO - ************** - - [15/Jul/2025 09:03:54] "GET /api/query_values?lat=22.794174621746222&lon=108.41933084578608&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:04:02,406 - root - INFO - 开始查询坐标点 (22.887250258337986, 108.45384561019257) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:04:02,675 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:04:02,698 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:04:02,727 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:04:02,727 - root - INFO - 在坐标点 (22.887250258337986, 108.45384561019257) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:04:02,728 - werkzeug - INFO - ************** - - [15/Jul/2025 09:04:02] "GET /api/query_values?lat=22.887250258337986&lon=108.45384561019257&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:04:25,101 - root - INFO - 开始查询坐标点 (22.794174621746222, 108.34461422511069) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:04:25,372 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:04:25,396 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:04:25,430 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:04:25,431 - root - INFO - 在坐标点 (22.794174621746222, 108.34461422511069) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:04:25,432 - werkzeug - INFO - ************** - - [15/Jul/2025 09:04:25] "GET /api/query_values?lat=22.794174621746222&lon=108.34461422511069&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:04:55,111 - root - INFO - 开始查询坐标点 (22.84731686406154, 108.44443249262719) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:04:55,371 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:04:55,394 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:04:55,414 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:04:55,415 - root - INFO - 在坐标点 (22.84731686406154, 108.44443249262719) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:04:55,416 - werkzeug - INFO - ************** - - [15/Jul/2025 09:04:55] "GET /api/query_values?lat=22.84731686406154&lon=108.44443249262719&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:05:01,510 - root - INFO - 开始查询坐标点 (22.84207586444164, 108.24146214512314) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:05:01,790 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:05:01,812 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:05:01,846 - root - INFO - 图层 'id2' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:05:01,847 - root - INFO - 在坐标点 (22.84207586444164, 108.24146214512314) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:05:01,848 - werkzeug - INFO - ************** - - [15/Jul/2025 09:05:01] "GET /api/query_values?lat=22.84207586444164&lon=108.24146214512314&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:05:08,959 - root - INFO - 开始查询坐标点 (22.88833425366893, 108.35324291621232) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:05:09,228 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:05:09,249 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:05:09,277 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:05:09,278 - root - INFO - 在坐标点 (22.88833425366893, 108.35324291621232) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:05:09,279 - werkzeug - INFO - ************** - - [15/Jul/2025 09:05:09] "GET /api/query_values?lat=22.88833425366893&lon=108.35324291621232&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:12:10,398 - werkzeug - INFO - ************** - - [15/Jul/2025 09:12:10] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:12:10,401 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:12:10,459 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:12:10,460 - werkzeug - INFO - ************** - - [15/Jul/2025 09:12:10] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:12:11,388 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:12:11,430 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:12:11,435 - werkzeug - INFO - ************** - - [15/Jul/2025 09:12:11] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:12:19,262 - root - INFO - 开始查询坐标点 (22.824001903365073, 108.32186585766097) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:12:19,522 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:12:19,544 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:12:19,574 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:12:19,575 - root - INFO - 在坐标点 (22.824001903365073, 108.32186585766097) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:12:19,576 - werkzeug - INFO - ************** - - [15/Jul/2025 09:12:19] "GET /api/query_values?lat=22.824001903365073&lon=108.32186585766097&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:12:25,574 - root - INFO - 开始查询坐标点 (22.87080851761732, 108.43246998905448) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:12:25,870 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:12:25,891 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:12:25,922 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:12:25,923 - root - INFO - 在坐标点 (22.87080851761732, 108.43246998905448) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:12:25,924 - werkzeug - INFO - ************** - - [15/Jul/2025 09:12:25] "GET /api/query_values?lat=22.87080851761732&lon=108.43246998905448&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:12:29,477 - root - INFO - 开始查询坐标点 (22.881107417844163, 108.39266034601746) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:12:29,730 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:12:29,749 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:12:29,779 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:12:29,779 - root - INFO - 在坐标点 (22.881107417844163, 108.39266034601746) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:12:29,781 - werkzeug - INFO - ************** - - [15/Jul/2025 09:12:29] "GET /api/query_values?lat=22.881107417844163&lon=108.39266034601746&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:12:41,324 - root - INFO - 开始查询坐标点 (22.8868889227992, 108.44462859924312) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:12:41,582 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:12:41,601 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:12:41,635 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:12:41,635 - root - INFO - 在坐标点 (22.8868889227992, 108.44462859924312) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:12:41,637 - werkzeug - INFO - ************** - - [15/Jul/2025 09:12:41] "GET /api/query_values?lat=22.8868889227992&lon=108.44462859924312&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:13:13,396 - root - INFO - 开始查询坐标点 (22.81008331792094, 108.48071221657716) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:13:13,642 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:13:13,663 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:13:13,695 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:13:13,696 - root - INFO - 在坐标点 (22.81008331792094, 108.48071221657716) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:13:13,697 - werkzeug - INFO - ************** - - [15/Jul/2025 09:13:13] "GET /api/query_values?lat=22.81008331792094&lon=108.48071221657716&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:13:24,126 - root - INFO - 开始查询坐标点 (22.887972926529613, 108.34147651925555) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:13:24,385 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:13:24,404 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:13:24,439 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:13:24,439 - root - INFO - 在坐标点 (22.887972926529613, 108.34147651925555) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:13:24,440 - werkzeug - INFO - ************** - - [15/Jul/2025 09:13:24] "GET /api/query_values?lat=22.887972926529613&lon=108.34147651925555&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:13:26,326 - root - INFO - 开始查询坐标点 (22.824363395077327, 108.25695456778287) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:13:26,580 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:13:26,602 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:13:26,629 - root - INFO - 图层 'id2' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:13:26,630 - root - INFO - 在坐标点 (22.824363395077327, 108.25695456778287) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:13:26,631 - werkzeug - INFO - ************** - - [15/Jul/2025 09:13:26] "GET /api/query_values?lat=22.824363395077327&lon=108.25695456778287&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:23:17,285 - werkzeug - INFO - ************** - - [15/Jul/2025 09:23:17] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:23:17,292 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:23:17,370 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:23:17,371 - werkzeug - INFO - ************** - - [15/Jul/2025 09:23:17] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:23:19,477 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:23:19,497 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:23:19,498 - werkzeug - INFO - ************** - - [15/Jul/2025 09:23:19] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:23:23,206 - root - INFO - 开始查询坐标点 (22.82291738937238, 108.38187448214042) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:23:23,478 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:23:23,497 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:23:23,532 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:23:23,533 - root - INFO - 在坐标点 (22.82291738937238, 108.38187448214042) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:23:23,534 - werkzeug - INFO - ************** - - [15/Jul/2025 09:23:23] "GET /api/query_values?lat=22.82291738937238&lon=108.38187448214042&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:23:26,151 - root - INFO - 开始查询坐标点 (22.804298552359825, 108.4432558529315) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:23:26,405 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:23:26,427 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:23:26,459 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:23:26,460 - root - INFO - 在坐标点 (22.804298552359825, 108.4432558529315) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:23:26,461 - werkzeug - INFO - ************** - - [15/Jul/2025 09:23:26] "GET /api/query_values?lat=22.804298552359825&lon=108.4432558529315&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:23:28,590 - root - INFO - 开始查询坐标点 (22.813879436838477, 108.41442818038743) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:23:28,835 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:23:28,857 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:23:28,887 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:23:28,888 - root - INFO - 在坐标点 (22.813879436838477, 108.41442818038743) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:23:28,889 - werkzeug - INFO - ************** - - [15/Jul/2025 09:23:28] "GET /api/query_values?lat=22.813879436838477&lon=108.41442818038743&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:23:30,910 - root - INFO - 开始查询坐标点 (22.882733494964086, 108.45502224988823) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:23:31,149 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:23:31,170 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:23:31,198 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:23:31,198 - root - INFO - 在坐标点 (22.882733494964086, 108.45502224988823) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:23:31,200 - werkzeug - INFO - ************** - - [15/Jul/2025 09:23:31] "GET /api/query_values?lat=22.882733494964086&lon=108.45502224988823&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:23:36,430 - root - INFO - 开始查询坐标点 (22.86972437684301, 108.36579373963285) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:23:36,672 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:23:36,692 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:23:36,726 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:23:36,727 - root - INFO - 在坐标点 (22.86972437684301, 108.36579373963285) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:23:36,728 - werkzeug - INFO - ************** - - [15/Jul/2025 09:23:36] "GET /api/query_values?lat=22.86972437684301&lon=108.36579373963285&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:26:57,140 - werkzeug - INFO - ************** - - [15/Jul/2025 09:26:57] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:26:57,146 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:26:57,173 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:26:57,174 - werkzeug - INFO - ************** - - [15/Jul/2025 09:26:57] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:26:58,861 - root - INFO - 开始查询坐标点 (23.8928838077194, 111.12867925585935) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:26:59,103 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:26:59,104 - root - INFO - 在坐标点 (23.8928838077194, 111.12867925585935) 处找到 0 个有有效数据的栅格图层
2025-07-15 09:26:59,105 - werkzeug - INFO - ************** - - [15/Jul/2025 09:26:59] "GET /api/query_values?lat=23.8928838077194&lon=111.12867925585935&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:27:01,507 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:27:01,528 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:27:01,529 - werkzeug - INFO - ************** - - [15/Jul/2025 09:27:01] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:27:03,976 - root - INFO - 开始查询坐标点 (22.805744766774495, 108.36344046024149) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:27:04,245 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:27:04,270 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:27:04,307 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:27:04,308 - root - INFO - 在坐标点 (22.805744766774495, 108.36344046024149) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:27:04,309 - werkzeug - INFO - ************** - - [15/Jul/2025 09:27:04] "GET /api/query_values?lat=22.805744766774495&lon=108.36344046024149&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:27:06,332 - werkzeug - INFO - ************** - - [15/Jul/2025 09:27:06] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:27:06,337 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:27:06,357 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:27:06,358 - werkzeug - INFO - ************** - - [15/Jul/2025 09:27:06] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:27:09,262 - root - INFO - 开始查询坐标点 (22.80393699635796, 108.43835318753285) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:27:09,511 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:27:09,529 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:27:09,557 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:27:09,558 - root - INFO - 在坐标点 (22.80393699635796, 108.43835318753285) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:27:09,559 - werkzeug - INFO - ************** - - [15/Jul/2025 09:27:09] "GET /api/query_values?lat=22.80393699635796&lon=108.43835318753285&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:27:12,153 - root - INFO - 开始查询坐标点 (22.865387727221773, 108.37030419179962) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:27:12,402 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:27:12,421 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:27:12,450 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:27:12,450 - root - INFO - 在坐标点 (22.865387727221773, 108.37030419179962) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:27:12,452 - werkzeug - INFO - ************** - - [15/Jul/2025 09:27:12] "GET /api/query_values?lat=22.865387727221773&lon=108.37030419179962&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:27:16,377 - root - INFO - 开始查询坐标点 (22.89032156054958, 108.44482470585906) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:27:16,768 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:27:16,787 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:27:16,831 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:27:16,832 - root - INFO - 在坐标点 (22.89032156054958, 108.44482470585906) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:27:16,833 - werkzeug - INFO - ************** - - [15/Jul/2025 09:27:16] "GET /api/query_values?lat=22.89032156054958&lon=108.44482470585906&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:27:22,263 - root - INFO - 开始查询坐标点 (22.890863560814793, 108.43835318753285) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:27:22,687 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:27:22,706 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:27:22,734 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:27:22,734 - root - INFO - 在坐标点 (22.890863560814793, 108.43835318753285) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:27:22,736 - werkzeug - INFO - ************** - - [15/Jul/2025 09:27:22] "GET /api/query_values?lat=22.890863560814793&lon=108.43835318753285&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:27:32,218 - root - INFO - 开始查询坐标点 (22.82815912696796, 108.21381111227477) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:27:32,504 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:27:32,525 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:27:32,554 - root - INFO - 图层 'id2' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:27:32,555 - root - INFO - 在坐标点 (22.82815912696796, 108.21381111227477) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:27:32,556 - werkzeug - INFO - ************** - - [15/Jul/2025 09:27:32] "GET /api/query_values?lat=22.82815912696796&lon=108.21381111227477&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:31:27,214 - root - INFO - 开始查询坐标点 (22.893212150319286, 108.26891707135557) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:31:27,593 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:31:27,635 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:31:27,797 - root - INFO - 图层 'id1' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:31:27,857 - root - INFO - 在坐标点 (22.893212150319286, 108.26891707135557) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:31:27,870 - werkzeug - INFO - ************** - - [15/Jul/2025 09:31:27] "GET /api/query_values?lat=22.893212150319286&lon=108.26891707135557&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:31:31,701 - root - INFO - 开始查询坐标点 (22.871531273326298, 108.24459985097828) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:31:31,961 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:31:31,980 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:31:32,010 - root - INFO - 图层 'id1' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:31:32,010 - root - INFO - 在坐标点 (22.871531273326298, 108.24459985097828) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:31:32,011 - werkzeug - INFO - ************** - - [15/Jul/2025 09:31:32] "GET /api/query_values?lat=22.871531273326298&lon=108.24459985097828&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:32:03,994 - werkzeug - INFO - ************** - - [15/Jul/2025 09:32:03] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:32:03,999 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:32:04,030 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:32:04,031 - werkzeug - INFO - ************** - - [15/Jul/2025 09:32:04] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:32:57,282 - werkzeug - INFO - ************** - - [15/Jul/2025 09:32:57] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:32:57,287 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:32:57,307 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:32:57,308 - werkzeug - INFO - ************** - - [15/Jul/2025 09:32:57] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:33:00,503 - root - INFO - 开始查询坐标点 (22.855991167172036, 108.46796528654069) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:33:00,742 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:33:00,762 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:33:00,794 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:33:00,794 - root - INFO - 在坐标点 (22.855991167172036, 108.46796528654069) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:33:00,795 - werkzeug - INFO - ************** - - [15/Jul/2025 09:33:00] "GET /api/query_values?lat=22.855991167172036&lon=108.46796528654069&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:33:03,742 - root - INFO - 开始查询坐标点 (22.766149086547344, 108.43913761399664) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:33:03,979 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:33:03,998 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:33:04,026 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:33:04,027 - root - INFO - 在坐标点 (22.766149086547344, 108.43913761399664) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:33:04,028 - werkzeug - INFO - ************** - - [15/Jul/2025 09:33:04] "GET /api/query_values?lat=22.766149086547344&lon=108.43913761399664&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:33:05,397 - root - INFO - 开始查询坐标点 (22.894657424868726, 108.45619888958393) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:33:05,629 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:33:05,648 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:33:05,677 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:33:05,677 - root - INFO - 在坐标点 (22.894657424868726, 108.45619888958393) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:33:05,679 - werkzeug - INFO - ************** - - [15/Jul/2025 09:33:05] "GET /api/query_values?lat=22.894657424868726&lon=108.45619888958393&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:33:05,933 - root - INFO - 开始查询坐标点 (22.90170289093537, 108.37501075058232) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:33:06,183 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:33:06,203 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:33:06,230 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:33:06,231 - root - INFO - 在坐标点 (22.90170289093537, 108.37501075058232) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:33:06,232 - werkzeug - INFO - ************** - - [15/Jul/2025 09:33:06] "GET /api/query_values?lat=22.90170289093537&lon=108.37501075058232&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:33:19,914 - werkzeug - INFO - ************** - - [15/Jul/2025 09:33:19] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:33:19,919 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:33:19,970 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:33:19,971 - werkzeug - INFO - ************** - - [15/Jul/2025 09:33:19] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:33:22,073 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:33:22,091 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:33:22,092 - werkzeug - INFO - ************** - - [15/Jul/2025 09:33:22] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:33:24,429 - root - INFO - 开始查询坐标点 (22.857075428553486, 108.40109293050311) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:33:24,654 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:33:24,675 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:33:24,702 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:33:24,702 - root - INFO - 在坐标点 (22.857075428553486, 108.40109293050311) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:33:24,705 - werkzeug - INFO - ************** - - [15/Jul/2025 09:33:24] "GET /api/query_values?lat=22.857075428553486&lon=108.40109293050311&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:34:01,820 - werkzeug - INFO - ************** - - [15/Jul/2025 09:34:01] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:34:01,828 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:34:01,861 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:34:01,863 - werkzeug - INFO - ************** - - [15/Jul/2025 09:34:01] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:34:06,335 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:34:06,379 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:34:06,387 - werkzeug - INFO - ************** - - [15/Jul/2025 09:34:06] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:34:08,789 - root - INFO - 开始查询坐标点 (22.818760005706366, 108.45364950357663) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:34:09,310 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:34:09,341 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:34:09,384 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:34:09,386 - root - INFO - 在坐标点 (22.818760005706366, 108.45364950357663) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:34:09,387 - werkzeug - INFO - ************** - - [15/Jul/2025 09:34:09] "GET /api/query_values?lat=22.818760005706366&lon=108.45364950357663&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:34:15,563 - werkzeug - INFO - ************** - - [15/Jul/2025 09:34:15] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:34:15,568 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:34:15,594 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:34:15,595 - werkzeug - INFO - ************** - - [15/Jul/2025 09:34:15] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:47:02,875 - werkzeug - INFO - ************** - - [15/Jul/2025 09:47:02] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:47:02,909 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:47:03,066 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:47:03,096 - werkzeug - INFO - ************** - - [15/Jul/2025 09:47:03] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:47:03,987 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:47:04,015 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:47:04,017 - werkzeug - INFO - ************** - - [15/Jul/2025 09:47:04] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:47:08,099 - werkzeug - INFO - ************** - - [15/Jul/2025 09:47:08] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:47:08,103 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:47:08,135 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:47:08,136 - werkzeug - INFO - ************** - - [15/Jul/2025 09:47:08] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:47:09,567 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:47:09,602 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:47:09,633 - werkzeug - INFO - ************** - - [15/Jul/2025 09:47:09] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:47:14,799 - root - INFO - 开始查询坐标点 (22.845871091235708, 108.39854354449582) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:47:15,054 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:47:15,074 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:47:15,096 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:47:15,097 - root - INFO - 在坐标点 (22.845871091235708, 108.39854354449582) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:47:15,098 - werkzeug - INFO - ************** - - [15/Jul/2025 09:47:15] "GET /api/query_values?lat=22.845871091235708&lon=108.39854354449582&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:47:18,392 - root - INFO - 开始查询坐标点 (22.773743687205453, 108.4185464193223) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:47:18,628 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:47:18,647 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:47:18,682 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:47:18,682 - root - INFO - 在坐标点 (22.773743687205453, 108.4185464193223) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:47:18,683 - werkzeug - INFO - ************** - - [15/Jul/2025 09:47:18] "GET /api/query_values?lat=22.773743687205453&lon=108.4185464193223&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:47:25,774 - root - INFO - 开始查询坐标点 (22.789654767118193, 108.3210814311972) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:47:26,040 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:47:26,059 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:47:26,089 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:47:26,090 - root - INFO - 在坐标点 (22.789654767118193, 108.3210814311972) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:47:26,090 - werkzeug - INFO - ************** - - [15/Jul/2025 09:47:26] "GET /api/query_values?lat=22.789654767118193&lon=108.3210814311972&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:47:29,142 - root - INFO - 开始查询坐标点 (22.879842687532573, 108.32951401568288) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:47:29,398 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:47:29,419 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:47:29,447 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:47:29,448 - root - INFO - 在坐标点 (22.879842687532573, 108.32951401568288) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:47:29,449 - werkzeug - INFO - ************** - - [15/Jul/2025 09:47:29] "GET /api/query_values?lat=22.879842687532573&lon=108.32951401568288&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:47:31,853 - root - INFO - 开始查询坐标点 (22.73214915175781, 108.45364950357663) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:47:32,111 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:47:32,135 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:47:32,181 - root - INFO - 图层 'id9' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:47:32,182 - root - INFO - 在坐标点 (22.73214915175781, 108.45364950357663) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:47:32,183 - werkzeug - INFO - ************** - - [15/Jul/2025 09:47:32] "GET /api/query_values?lat=22.73214915175781&lon=108.45364950357663&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:47:59,199 - root - INFO - 开始查询坐标点 (22.722200756801186, 108.31696319226232) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:47:59,445 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:47:59,471 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:47:59,499 - root - INFO - 图层 'id6' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:47:59,499 - root - INFO - 在坐标点 (22.722200756801186, 108.31696319226232) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:47:59,501 - werkzeug - INFO - ************** - - [15/Jul/2025 09:47:59] "GET /api/query_values?lat=22.722200756801186&lon=108.31696319226232&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:51:59,630 - werkzeug - INFO - ************** - - [15/Jul/2025 09:51:59] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:51:59,636 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:51:59,662 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:51:59,663 - werkzeug - INFO - ************** - - [15/Jul/2025 09:51:59] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:52:01,286 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:52:01,317 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:52:01,318 - werkzeug - INFO - ************** - - [15/Jul/2025 09:52:01] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:52:03,799 - root - INFO - 开始查询坐标点 (22.837376865504254, 108.40226957019881) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:52:04,045 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:52:04,064 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:52:04,093 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:52:04,093 - root - INFO - 在坐标点 (22.837376865504254, 108.40226957019881) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:52:04,094 - werkzeug - INFO - ************** - - [15/Jul/2025 09:52:04] "GET /api/query_values?lat=22.837376865504254&lon=108.40226957019881&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:52:07,345 - root - INFO - 开始查询坐标点 (22.852919088572094, 108.42972449643122) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:52:07,567 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:52:07,584 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:52:07,612 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:52:07,613 - root - INFO - 在坐标点 (22.852919088572094, 108.42972449643122) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:52:07,614 - werkzeug - INFO - ************** - - [15/Jul/2025 09:52:07] "GET /api/query_values?lat=22.852919088572094&lon=108.42972449643122&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:52:08,054 - root - INFO - 开始查询坐标点 (22.8427987729481, 108.37893288290124) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:52:08,283 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:52:08,303 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:52:08,333 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:52:08,334 - root - INFO - 在坐标点 (22.8427987729481, 108.37893288290124) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:52:08,335 - werkzeug - INFO - ************** - - [15/Jul/2025 09:52:08] "GET /api/query_values?lat=22.8427987729481&lon=108.37893288290124&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 09:52:10,290 - werkzeug - INFO - ************** - - [15/Jul/2025 09:52:10] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:52:10,294 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:52:10,316 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:52:10,317 - werkzeug - INFO - ************** - - [15/Jul/2025 09:52:10] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:52:11,728 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 09:52:11,765 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 09:52:11,766 - werkzeug - INFO - ************** - - [15/Jul/2025 09:52:11] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 09:52:14,175 - root - INFO - 开始查询坐标点 (22.821290602185414, 108.36520541978503) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 09:52:14,407 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 09:52:14,425 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:52:14,451 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-15 09:52:14,452 - root - INFO - 在坐标点 (22.821290602185414, 108.36520541978503) 处找到 2 个有有效数据的栅格图层
2025-07-15 09:52:14,453 - werkzeug - INFO - ************** - - [15/Jul/2025 09:52:14] "GET /api/query_values?lat=22.821290602185414&lon=108.36520541978503&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:01:14,127 - werkzeug - INFO - ************** - - [15/Jul/2025 10:01:14] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 10:01:14,132 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 10:01:14,194 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 10:01:14,195 - werkzeug - INFO - ************** - - [15/Jul/2025 10:01:14] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 10:02:29,618 - werkzeug - INFO - ************** - - [15/Jul/2025 10:02:29] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 10:02:29,623 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 10:02:29,649 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 10:02:29,650 - werkzeug - INFO - ************** - - [15/Jul/2025 10:02:29] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 10:02:42,654 - werkzeug - INFO - ************** - - [15/Jul/2025 10:02:42] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 10:02:42,659 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 10:02:42,693 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 10:02:42,695 - werkzeug - INFO - ************** - - [15/Jul/2025 10:02:42] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 10:02:44,204 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 10:02:44,226 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 10:02:44,227 - werkzeug - INFO - ************** - - [15/Jul/2025 10:02:44] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 10:02:47,215 - root - INFO - 开始查询坐标点 (22.829966575889813, 108.37638349689395) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:02:47,491 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:02:47,513 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:02:47,553 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:02:47,554 - root - INFO - 在坐标点 (22.829966575889813, 108.37638349689395) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:02:47,555 - werkzeug - INFO - ************** - - [15/Jul/2025 10:02:47] "GET /api/query_values?lat=22.829966575889813&lon=108.37638349689395&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:02:58,094 - root - INFO - 开始查询坐标点 (22.87532567769965, 108.4465896654026) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:02:58,384 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:02:58,408 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:02:58,439 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:02:58,440 - root - INFO - 在坐标点 (22.87532567769965, 108.4465896654026) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:02:58,441 - werkzeug - INFO - ************** - - [15/Jul/2025 10:02:58] "GET /api/query_values?lat=22.87532567769965&lon=108.4465896654026&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:03:01,744 - root - INFO - 开始查询坐标点 (22.799417465400865, 108.35638062206745) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:03:02,001 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:03:02,022 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:03:02,056 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:03:02,056 - root - INFO - 在坐标点 (22.799417465400865, 108.35638062206745) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:03:02,058 - werkzeug - INFO - ************** - - [15/Jul/2025 10:03:02] "GET /api/query_values?lat=22.799417465400865&lon=108.35638062206745&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:03:17,742 - root - INFO - 开始查询坐标点 (22.798694326540016, 108.44972737125771) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:03:18,004 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:03:18,021 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:03:18,050 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:03:18,051 - root - INFO - 在坐标点 (22.798694326540016, 108.44972737125771) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:03:18,052 - werkzeug - INFO - ************** - - [15/Jul/2025 10:03:18] "GET /api/query_values?lat=22.798694326540016&lon=108.44972737125771&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:03:23,143 - root - INFO - 开始查询坐标点 (22.88923758109209, 108.32088532458124) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:03:23,424 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:03:23,444 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:03:23,474 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:03:23,475 - root - INFO - 在坐标点 (22.88923758109209, 108.32088532458124) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:03:23,476 - werkzeug - INFO - ************** - - [15/Jul/2025 10:03:23] "GET /api/query_values?lat=22.88923758109209&lon=108.32088532458124&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:03:26,029 - root - INFO - 开始查询坐标点 (22.80357542836296, 108.23695171689519) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:03:26,305 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:03:26,327 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:03:26,359 - root - INFO - 图层 'id2' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:03:26,360 - root - INFO - 在坐标点 (22.80357542836296, 108.23695171689519) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:03:26,363 - werkzeug - INFO - ************** - - [15/Jul/2025 10:03:26] "GET /api/query_values?lat=22.80357542836296&lon=108.23695171689519&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:03:37,933 - root - INFO - 开始查询坐标点 (22.73341525920911, 108.35598840883557) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:03:38,188 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:03:38,216 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:03:38,245 - root - INFO - 图层 'id6' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:03:38,246 - root - INFO - 在坐标点 (22.73341525920911, 108.35598840883557) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:03:38,247 - werkzeug - INFO - ************** - - [15/Jul/2025 10:03:38] "GET /api/query_values?lat=22.73341525920911&lon=108.35598840883557&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:12:22,829 - werkzeug - INFO - ************** - - [15/Jul/2025 10:12:22] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 10:12:22,833 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 10:12:22,894 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 10:12:22,896 - werkzeug - INFO - ************** - - [15/Jul/2025 10:12:22] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 10:12:24,219 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 10:12:24,262 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 10:12:24,266 - werkzeug - INFO - ************** - - [15/Jul/2025 10:12:24] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 10:12:27,111 - root - INFO - 开始查询坐标点 (22.854726208526913, 108.44423638601121) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:12:27,400 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:12:27,423 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:12:27,583 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:12:27,583 - root - INFO - 在坐标点 (22.854726208526913, 108.44423638601121) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:12:27,584 - werkzeug - INFO - ************** - - [15/Jul/2025 10:12:27] "GET /api/query_values?lat=22.854726208526913&lon=108.44423638601121&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:12:48,184 - root - INFO - 开始查询坐标点 (22.821290602185414, 108.3369660910276) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:12:48,417 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:12:48,439 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:12:48,470 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:12:48,471 - root - INFO - 在坐标点 (22.821290602185414, 108.3369660910276) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:12:48,472 - werkzeug - INFO - ************** - - [15/Jul/2025 10:12:48] "GET /api/query_values?lat=22.821290602185414&lon=108.3369660910276&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:14:45,031 - root - INFO - 开始查询坐标点 (22.87529976873367, 108.35840003741532) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:14:45,284 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:14:45,307 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:14:45,339 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:14:45,340 - root - INFO - 在坐标点 (22.87529976873367, 108.35840003741532) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:14:45,341 - werkzeug - INFO - ************** - - [15/Jul/2025 10:14:45] "GET /api/query_values?lat=22.87529976873367&lon=108.35840003741532&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:18:36,279 - root - INFO - 开始查询坐标点 (22.83098710463932, 108.45851943164048) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:18:36,523 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:18:36,549 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:18:36,575 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:18:36,575 - root - INFO - 在坐标点 (22.83098710463932, 108.45851943164048) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:18:36,576 - werkzeug - INFO - ************** - - [15/Jul/2025 10:18:36] "GET /api/query_values?lat=22.83098710463932&lon=108.45851943164048&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:18:48,366 - root - INFO - 开始查询坐标点 (22.86872302069625, 108.2349883492646) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:18:48,602 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:18:48,621 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:18:48,647 - root - INFO - 图层 'id1' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:18:48,648 - root - INFO - 在坐标点 (22.86872302069625, 108.2349883492646) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:18:48,649 - werkzeug - INFO - ************** - - [15/Jul/2025 10:18:48] "GET /api/query_values?lat=22.86872302069625&lon=108.2349883492646&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:18:56,527 - root - INFO - 开始查询坐标点 (22.882222317327148, 108.3443119425431) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:18:56,770 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:18:56,792 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:18:56,829 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:18:56,829 - root - INFO - 在坐标点 (22.882222317327148, 108.3443119425431) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:18:56,830 - werkzeug - INFO - ************** - - [15/Jul/2025 10:18:56] "GET /api/query_values?lat=22.882222317327148&lon=108.3443119425431&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:19:17,078 - root - INFO - 开始查询坐标点 (22.891394139935656, 108.35332834619116) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:19:17,334 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:19:17,355 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:19:17,385 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:19:17,385 - root - INFO - 在坐标点 (22.891394139935656, 108.35332834619116) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:19:17,387 - werkzeug - INFO - ************** - - [15/Jul/2025 10:19:17] "GET /api/query_values?lat=22.891394139935656&lon=108.35332834619116&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:19:33,951 - root - INFO - 开始查询坐标点 (22.892911484248614, 108.37622549704777) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:19:34,521 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:19:34,599 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:19:34,741 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:19:34,746 - root - INFO - 在坐标点 (22.892911484248614, 108.37622549704777) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:19:34,748 - werkzeug - INFO - ************** - - [15/Jul/2025 10:19:34] "GET /api/query_values?lat=22.892911484248614&lon=108.37622549704777&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:20:12,377 - werkzeug - INFO - ************** - - [15/Jul/2025 10:20:12] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 10:20:12,383 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 10:20:12,413 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 10:20:12,414 - werkzeug - INFO - ************** - - [15/Jul/2025 10:20:12] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 10:20:15,190 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-15 10:20:15,209 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-15 10:20:15,210 - werkzeug - INFO - ************** - - [15/Jul/2025 10:20:15] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-15 10:20:26,753 - root - INFO - 开始查询坐标点 (22.894657424868726, 108.33029844214666) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:20:26,997 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:20:27,015 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:20:27,044 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:20:27,044 - root - INFO - 在坐标点 (22.894657424868726, 108.33029844214666) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:20:27,045 - werkzeug - INFO - ************** - - [15/Jul/2025 10:20:27] "GET /api/query_values?lat=22.894657424868726&lon=108.33029844214666&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:20:34,809 - root - INFO - 开始查询坐标点 (22.879119976057012, 108.43403884198204) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:20:35,062 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:20:35,083 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:20:35,116 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:20:35,117 - root - INFO - 在坐标点 (22.879119976057012, 108.43403884198204) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:20:35,118 - werkzeug - INFO - ************** - - [15/Jul/2025 10:20:35] "GET /api/query_values?lat=22.879119976057012&lon=108.43403884198204&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:20:35,305 - root - INFO - 开始查询坐标点 (22.81839848808457, 108.39991629080745) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:20:35,599 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:20:35,619 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:20:35,649 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:20:35,649 - root - INFO - 在坐标点 (22.81839848808457, 108.39991629080745) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:20:35,650 - werkzeug - INFO - ************** - - [15/Jul/2025 10:20:35] "GET /api/query_values?lat=22.81839848808457&lon=108.39991629080745&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-15 10:22:32,934 - root - INFO - 开始查询坐标点 (22.888622888086502, 108.42391006816396) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-15 10:22:33,209 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-15 10:22:33,225 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:22:33,254 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-15 10:22:33,255 - root - INFO - 在坐标点 (22.888622888086502, 108.42391006816396) 处找到 2 个有有效数据的栅格图层
2025-07-15 10:22:33,257 - werkzeug - INFO - ************** - - [15/Jul/2025 10:22:33] "GET /api/query_values?lat=22.888622888086502&lon=108.42391006816396&workspace=test_myworkspace HTTP/1.1" 200 -
