执行命令: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
开始时间: 2025-08-04 11:30:03

[INFO]    Initializing ODM 3.5.5 - Mon Aug 04 11:30:09  2025
[INFO]    ==============
[INFO]    3d_tiles: False
[INFO]    align: None
[INFO]    auto_boundary: False
[INFO]    auto_boundary_distance: 0
[INFO]    bg_removal: False
[INFO]    boundary: {}
[INFO]    build_overviews: False
[INFO]    camera_lens: auto
[INFO]    cameras: {}
[INFO]    cog: False
[INFO]    copy_to: None
[INFO]    crop: 3
[INFO]    dem_decimation: 1
[INFO]    dem_euclidean_map: False
[INFO]    dem_gapfill_steps: 3
[INFO]    dem_resolution: 5
[INFO]    dsm: False
[INFO]    dtm: False
[INFO]    end_with: odm_postprocess
[INFO]    fast_orthophoto: False
[INFO]    feature_quality: high
[INFO]    feature_type: dspsift
[INFO]    force_gps: False
[INFO]    gcp: None
[INFO]    geo: None
[INFO]    gltf: False
[INFO]    gps_accuracy: 3
[INFO]    ignore_gsd: False
[INFO]    matcher_neighbors: 0
[INFO]    matcher_order: 0
[INFO]    matcher_type: flann
[INFO]    max_concurrency: 8
[INFO]    merge: all
[INFO]    mesh_octree_depth: 11
[INFO]    mesh_size: 200000
[INFO]    min_num_features: 10000
[INFO]    name: D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
[INFO]    no_gpu: False
[INFO]    optimize_disk_space: False
[INFO]    orthophoto_compression: DEFLATE
[INFO]    orthophoto_cutline: False
[INFO]    orthophoto_kmz: False
[INFO]    orthophoto_no_tiled: False
[INFO]    orthophoto_png: False
[INFO]    orthophoto_resolution: 5
[INFO]    pc_classify: False
[INFO]    pc_copc: False
[INFO]    pc_csv: False
[INFO]    pc_ept: False
[INFO]    pc_filter: 5
[INFO]    pc_las: False
[INFO]    pc_quality: medium
[INFO]    pc_rectify: False
[INFO]    pc_sample: 0
[INFO]    pc_skip_geometric: False
[INFO]    primary_band: auto
[INFO]    project_path: D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
[INFO]    radiometric_calibration: none
[INFO]    rerun: None
[INFO]    rerun_all: False
[INFO]    rerun_from: None
[INFO]    rolling_shutter: False
[INFO]    rolling_shutter_readout: 0
[INFO]    sfm_algorithm: incremental
[INFO]    sfm_no_partial: False
[INFO]    skip_3dmodel: False
[INFO]    skip_band_alignment: False
[INFO]    skip_orthophoto: False
[INFO]    skip_report: False
[INFO]    sky_removal: False
[INFO]    sm_cluster: None
[INFO]    sm_no_align: False
[INFO]    smrf_scalar: 1.25
[INFO]    smrf_slope: 0.15
[INFO]    smrf_threshold: 0.5
[INFO]    smrf_window: 18.0
[INFO]    split: 999999
[INFO]    split_image_groups: None
[INFO]    split_overlap: 150
[INFO]    texturing_keep_unseen_faces: False
[INFO]    texturing_single_material: False
[INFO]    texturing_skip_global_seam_leveling: False
[INFO]    tiles: False
[INFO]    use_3dmesh: False
[INFO]    use_exif: False
[INFO]    use_fixed_camera_params: False
[INFO]    use_hybrid_bundle_adjustment: False
[INFO]    video_limit: 500
[INFO]    video_resolution: 4000
[INFO]    ==============
[INFO]    Running dataset stage
[INFO]    Loading dataset from: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\images
[INFO]    Loading 2 images
[INFO]    Wrote images database: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\images.json
[INFO]    Found 2 usable images
[INFO]    Finished dataset stage
[INFO]    Running split stage
[INFO]    Normal dataset, will process all at once.
[INFO]    Finished split stage
[INFO]    Running merge stage
[INFO]    Normal dataset, nothing to merge.
[INFO]    Finished merge stage
[INFO]    Running opensfm stage
[INFO]    Maximum photo dimensions: 5280px
[INFO]    Photo dimensions for feature extraction: 2640px
[INFO]    CUDA drivers detected
[INFO]    Altitude data detected, enabling it for GPS alignment
[INFO]    ['use_exif_size: no', 'flann_algorithm: KDTREE', 'feature_process_size: 2640', 'feature_min_frames: 10000', 'processes: 8', 'matching_gps_neighbors: 3', 'matching_gps_distance: 0', 'matching_graph_rounds: 0', 'optimize_camera_parameters: yes', 'reconstruction_algorithm: incremental', 'undistorted_image_format: tif', 'bundle_outlier_filtering_type: AUTO', 'sift_peak_threshold: 0.066', 'align_orientation_prior: vertical', 'triangulation_type: ROBUST', 'retriangulation_ratio: 2', 'matcher_type: FLANN', 'feature_type: DSPSIFT', 'use_altitude_tag: yes', 'align_method: auto', 'local_bundle_radius: 0']
[INFO]    Wrote reference_lla.json
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" detect_features "D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\opensfm"
2025-08-04 11:30:12,113 INFO: Planning to use 3791.527734375 MB of RAM for both processing queue and parallel processing.
2025-08-04 11:30:12,114 INFO: Scale-space expected size of a single image : 158.89835357666016 MB
2025-08-04 11:30:12,114 INFO: Expecting to queue at most 23 images while parallel processing of 8 images.
2025-08-04 11:30:12,121 INFO: Reading data for image DJI_20250705171600_0001_V.jpeg (queue-size=0)
2025-08-04 11:30:12,122 INFO: Reading data for image DJI_20250705172054_0039_V.jpeg (queue-size=0)
2025-08-04 11:30:12,616 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705172054_0039_V.jpeg
2025-08-04 11:30:12,660 INFO: Finished reading images
2025-08-04 11:30:12,661 INFO: Extracting ROOT_DSPSIFT features for image DJI_20250705171600_0001_V.jpeg
2025-08-04 11:30:24,291 DEBUG: Found 10000 points in 11.610482692718506s
2025-08-04 11:30:25,617 DEBUG: Found 10000 points in 12.98313856124878s
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" match_features "D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\opensfm"
2025-08-04 11:30:29,749 INFO: Altitude for orientation based matching 2611.9208479659696
2025-08-04 11:30:29,750 INFO: Matching 1 image pairs
2025-08-04 11:30:29,751 INFO: Computing pair matching with 8 processes
2025-08-04 11:30:30,683 DEBUG: Matching DJI_20250705171600_0001_V.jpeg and DJI_20250705172054_0039_V.jpeg.  Matcher: FLANN (symmetric) T-desc: 0.931 Matches: FAILED
2025-08-04 11:30:30,683 INFO: Matched 1 pairs (brown-brown: 1) in 0.9326251000000001 seconds (0.9326254 seconds/pair).
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" create_tracks "D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\opensfm"
2025-08-04 11:30:33,154 INFO: reading features
2025-08-04 11:30:33,264 DEBUG: Merging features onto tracks
2025-08-04 11:30:33,264 DEBUG: Good tracks: 0
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" reconstruct "D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\opensfm"
2025-08-04 11:30:35,385 INFO: Starting incremental reconstruction
2025-08-04 11:30:35,387 INFO: 0 partial reconstructions in total.
[ERROR]   The program could not process this dataset using the current settings. Check that the images have enough overlap, that there are enough recognizable features and that the images are in focus. The program will now exit.

完成时间: 2025-08-04 11:30:36
返回代码: 0
状态: 运行成功
