2025-07-09 09:13:35,321 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250709_091335.log
2025-07-09 09:13:35,432 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-09 09:13:35,432 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-09 09:13:35,949 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-09 09:13:35,956 - root - INFO - === GeoServer REST API服务 ===
2025-07-09 09:13:35,957 - root - INFO - 主机: 127.0.0.1
2025-07-09 09:13:35,957 - root - INFO - 端口: 5083
2025-07-09 09:13:35,958 - root - INFO - 调试模式: 禁用
2025-07-09 09:13:35,959 - root - INFO - 启动GeoServer REST API服务，监听 127.0.0.1:5083
2025-07-09 09:13:35,968 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5083
2025-07-09 09:13:35,969 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 09:15:36,470 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 09:15:36] "[33mGET /?/api/layers?workspace=test_myworkspace HTTP/1.1[0m" 404 -
2025-07-09 09:15:36,534 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 09:15:36] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-09 09:15:47,484 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-09 09:15:47,485 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 09:15:47] "GET /api/layers?workspace=test_myworkspace HTTP/1.1" 200 -
