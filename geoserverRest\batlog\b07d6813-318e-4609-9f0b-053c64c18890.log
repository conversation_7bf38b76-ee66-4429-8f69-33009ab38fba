执行命令: D:\Drone_Project\ODM\ODM\run.bat D:\Drone_Project\dataset\DJITESTIMAGE\20250714140500\project --fast-orthophoto
工作目录: D:\Drone_Project\ODM\ODM
开始时间: 2025-07-14 12:01:42

检测到ODM项目，使用特殊方法执行
创建执行批处理文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\exec_b07d6813-318e-4609-9f0b-053c64c18890.bat
批处理文件内容:
@echo off
cd /d D:\Drone_Project\ODM\ODM
echo 正在运行: run.bat D:\Drone_Project\dataset\DJITESTIMAGE\20250714140500\project --fast-orthophoto
call run.bat D:\Drone_Project\dataset\DJITESTIMAGE\20250714140500\project --fast-orthophoto
echo 命令执行完成，退出代码: %ERRORLEVEL%


开始执行批处理文件...
正在运行: run.bat D:\Drone_Project\dataset\DJITESTIMAGE\20250714140500\project --fast-orthophoto

Traceback (most recent call last):

  File "D:\Drone_Project\ODM\ODM\\run.py", line 18, in <module>

    from stages.odm_app import ODMApp

  File "D:\Drone_Project\ODM\ODM\stages\odm_app.py", line 15, in <module>

    from stages.odm_orthophoto import ODMOrthoPhotoStage

  File "D:\Drone_Project\ODM\ODM\stages\odm_orthophoto.py", line 12, in <module>

    from opendm.cutline import compute_cutline

  File "D:\Drone_Project\ODM\ODM\opendm\cutline.py", line 18, in <module>

    from shapely.geometry import LineString, mapping, shape

  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\shapely\geometry\__init__.py", line 4, in <module>

    from .base import CAP_STYLE, JOIN_STYLE

  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\shapely\geometry\base.py", line 19, in <module>

    from shapely.coords import CoordinateSequence

  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\shapely\coords.py", line 8, in <module>

    from shapely.geos import lgeos

  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\shapely\geos.py", line 154, in <module>

    _lgeos = CDLL(os.path.join(sys.prefix, 'Library', 'bin', 'geos_c.dll'))

  File "ctypes\__init__.py", line 373, in __init__

FileNotFoundError: Could not find module 'D:\Drone_Project\ODM\ODM\venv\Library\bin\geos_c.dll'. Try using the full path with constructor syntax.

命令执行完成，退出代码: 1

批处理执行完成，返回代码: 0
已删除临时批处理文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\exec_b07d6813-318e-4609-9f0b-053c64c18890.bat

检测到错误信息，设置状态为失败

完成时间: 2025-07-14 12:01:47
返回代码: 1
状态: 运行失败
