2025-07-18 09:27:29,256 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250718_092729.log
2025-07-18 09:27:29,259 - geo_publisher - INFO - 加载了 1 个任务状态
2025-07-18 09:27:29,279 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 09:27:29,279 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 09:27:29,297 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 09:27:29,302 - root - INFO - === GeoServer REST API服务 ===
2025-07-18 09:27:29,302 - root - INFO - 主机: 0.0.0.0
2025-07-18 09:27:29,304 - root - INFO - 端口: 5083
2025-07-18 09:27:29,305 - root - INFO - 调试模式: 禁用
2025-07-18 09:27:29,306 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-18 09:27:29,401 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-18 09:27:29,402 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 09:28:58,394 - geo_publisher - INFO - 启动GeoTIFF发布任务 d0f79e8e-f0a4-45a5-bf93-0458b334cb27: D:/Drone_Project/dataset/nanning.tif, workspace=tttt
2025-07-18 09:28:58,399 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 09:28:58] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/dataset/nanning.tif&workspace=tttt HTTP/1.1" 200 -
2025-07-18 09:28:58,653 - geo_task_d0f79e8e-f0a4-45a5-bf93-0458b334cb27 - INFO - GeoServer发布任务 d0f79e8e-f0a4-45a5-bf93-0458b334cb27 开始执行
2025-07-18 09:28:58,654 - geo_task_d0f79e8e-f0a4-45a5-bf93-0458b334cb27 - INFO - 开始时间: 2025-07-18 09:28:58
2025-07-18 09:28:58,657 - geo_task_d0f79e8e-f0a4-45a5-bf93-0458b334cb27 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/dataset/nanning.tif",
  "workspace": "tttt",
  "store_name": null,
  "layer_name": null
}
2025-07-18 09:28:58,658 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 09:28:58,659 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 09:28:58,676 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 09:28:58,677 - root - INFO - 未提供存储名称，使用文件名: nanning
2025-07-18 09:28:58,678 - root - INFO - 未提供图层名称，使用存储名称: nanning
2025-07-18 09:28:58,722 - root - INFO - 使用绝对路径: D:\Drone_Project\dataset\nanning.tif
2025-07-18 09:29:00,327 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/dataset/nanning.tif' 到 'tttt:nanning'
2025-07-18 09:29:00,433 - root - INFO - 图层验证成功: 直接图层路径: tttt:nanning
2025-07-18 09:29:00,434 - root - INFO - GeoTIFF 'D:/Drone_Project/dataset/nanning.tif' 成功发布为图层 'tttt:nanning'
2025-07-18 09:29:00,442 - geo_task_d0f79e8e-f0a4-45a5-bf93-0458b334cb27 - INFO - GeoServer发布任务 d0f79e8e-f0a4-45a5-bf93-0458b334cb27 执行成功
2025-07-18 09:29:00,443 - geo_task_d0f79e8e-f0a4-45a5-bf93-0458b334cb27 - INFO - 完成时间: 2025-07-18 09:29:00
2025-07-18 09:29:00,443 - geo_task_d0f79e8e-f0a4-45a5-bf93-0458b334cb27 - INFO - 状态: 发布成功
2025-07-18 09:29:07,813 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 09:29:07] "GET /api/geo/status?task_id=d0f79e8e-f0a4-45a5-bf93-0458b334cb27 HTTP/1.1" 200 -
2025-07-18 09:53:56,445 - batch_executor - INFO - 启动任务 8e04f4cd-e482-4134-8c5b-8448b5184b6c: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
2025-07-18 09:53:56,448 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 09:53:56] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-18 09:53:56,511 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 09:53:56] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 09:54:58,836 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 09:54:58] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 09:56:02,300 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 09:56:02] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 09:57:06,085 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 09:57:06] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 09:58:10,448 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 09:58:10] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 09:59:12,429 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 09:59:12] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:00:14,287 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:00:14] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:01:16,303 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:01:16] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:02:18,497 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:02:18] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:03:20,369 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:03:20] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:04:22,306 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:04:22] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:05:24,225 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:05:24] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:06:26,247 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:06:26] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:07:29,099 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:07:29] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:08:32,077 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:08:32] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:09:34,672 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:09:34] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:10:35,346 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:10:35] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:11:37,185 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:11:37] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:12:38,918 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:12:38] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:13:40,257 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:13:40] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:14:42,307 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:14:42] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:15:44,125 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:15:44] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:16:45,926 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:16:45] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:17:47,709 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:17:47] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:18:49,485 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:18:49] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:19:51,230 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:19:51] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:20:52,942 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:20:52] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:21:54,961 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:21:54] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:22:56,854 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:22:56] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:23:59,040 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:23:59] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:25:01,062 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:25:01] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:26:03,005 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:26:03] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:27:05,039 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:27:05] "GET /api/batch/status?task_id=8e04f4cd-e482-4134-8c5b-8448b5184b6c HTTP/1.1" 200 -
2025-07-18 10:27:06,170 - tif_executor - INFO - 启动TIF处理任务 17c7bb5e-fb34-44a5-8a37-2d345848a203: D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp
2025-07-18 10:27:06,174 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - INFO - TIF处理任务 17c7bb5e-fb34-44a5-8a37-2d345848a203 开始执行
2025-07-18 10:27:06,176 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:27:06] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-18 10:27:06,180 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - INFO - 开始时间: 2025-07-18 10:27:06
2025-07-18 10:27:06,187 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - INFO - 处理参数: {
  "input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-18 10:27:06,204 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-18 10:27:06,215 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:27:06] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:27:14,770 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 30414x36877x3估计内存使用: 总计 16.71 GB, 单个处理块 464.08 MB开始创建掩码... (10:27:14)使用全图检测无效区域模式...将处理 37 个数据块...使用多线程处理 (7 线程)...
2025-07-18 10:27:14,831 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块:   0%|                                                                               | 0/37 [00:00<?, ?it/s]
2025-07-18 10:27:15,152 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块:   3%|#9                                                                     | 1/37 [00:00<00:10,  3.49it/s]
2025-07-18 10:27:15,262 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块:  16%|###########5                                                           | 6/37 [00:00<00:01, 18.27it/s]
2025-07-18 10:27:15,679 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块:  24%|#################2                                                     | 9/37 [00:00<00:02, 11.01it/s]
2025-07-18 10:27:15,802 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块:  30%|####################8                                                 | 11/37 [00:00<00:02, 12.13it/s]
2025-07-18 10:27:16,052 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块:  35%|########################5                                             | 13/37 [00:01<00:02, 10.55it/s]
2025-07-18 10:27:16,377 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块:  43%|##############################2                                       | 16/37 [00:01<00:02, 10.00it/s]
2025-07-18 10:27:16,818 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块:  54%|#####################################8                                | 20/37 [00:01<00:01,  9.59it/s]
2025-07-18 10:27:17,077 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块:  68%|###############################################2                      | 25/37 [00:02<00:00, 12.24it/s]
2025-07-18 10:27:17,221 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块:  73%|###################################################                   | 27/37 [00:02<00:00, 12.52it/s]
2025-07-18 10:27:17,473 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块:  78%|######################################################8               | 29/37 [00:02<00:00, 11.11it/s]
2025-07-18 10:27:17,625 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块:  84%|##########################################################6           | 31/37 [00:02<00:00, 11.55it/s]
2025-07-18 10:27:17,799 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块:  89%|##############################################################4       | 33/37 [00:02<00:00, 11.53it/s]
2025-07-18 10:27:17,847 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理数据块: 100%|######################################################################| 37/37 [00:02<00:00, 12.41it/s]
2025-07-18 10:27:17,852 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - INFO - 已完成: 10/37 块 (27.0%)已完成: 20/37 块 (54.1%)已完成: 30/37 块 (81.1%)已完成: 37/37 块 (100.0%)合并处理结果...
2025-07-18 10:27:17,853 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
合并结果:   0%|                                                                                 | 0/37 [00:00<?, ?it/s]
2025-07-18 10:27:17,957 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
合并结果:  78%|#######################################################6               | 29/37 [00:00<00:00, 281.30it/s]
2025-07-18 10:27:17,990 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
合并结果: 100%|#######################################################################| 37/37 [00:00<00:00, 270.83it/s]
2025-07-18 10:27:21,080 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - INFO - 合并进度: 20/37 块 (54.1%)合并进度: 37/37 块 (100.0%)统计结果: 总像素 1121577078, 有效像素 248906699 (22.19%), 无效像素 872670379 (77.81%)掩码创建完成，耗时: 6.67 秒 (10:27:21)预计总处理时间: 约 20.01 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif (10:27:21)写入带有nodata值的影像...
2025-07-18 10:27:21,081 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-18 10:27:27,326 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/37 [00:00<?, ?it/s]
2025-07-18 10:27:27,352 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:27:27,466 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:   8%|#####1                                                          | 3/37 [00:00<00:01, 27.52it/s]
2025-07-18 10:27:27,469 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:27:27,569 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  16%|##########3                                                     | 6/37 [00:00<00:01, 28.53it/s]
2025-07-18 10:27:27,571 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:27:27,729 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  24%|###############5                                                | 9/37 [00:00<00:01, 23.09it/s]
2025-07-18 10:27:27,730 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:27:27,859 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  32%|####################4                                          | 12/37 [00:00<00:01, 23.00it/s]
2025-07-18 10:27:27,862 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:27:28,232 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  41%|#########################5                                     | 15/37 [00:00<00:01, 13.86it/s]
2025-07-18 10:27:28,233 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:27:43,049 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  46%|############################9                                  | 17/37 [00:15<00:38,  1.95s/it]
2025-07-18 10:27:43,050 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:27:49,196 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  49%|##############################6                                | 18/37 [00:21<00:49,  2.60s/it]
2025-07-18 10:27:49,197 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:27:59,018 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  54%|##################################                             | 20/37 [00:31<00:56,  3.30s/it]
2025-07-18 10:27:59,019 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:28:03,761 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  57%|###################################7                           | 21/37 [00:36<00:57,  3.56s/it]
2025-07-18 10:28:03,762 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:28:08,326 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:28:08] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:28:10,683 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  59%|#####################################4                         | 22/37 [00:43<01:03,  4.25s/it]
2025-07-18 10:28:10,683 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:28:15,543 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  62%|#######################################1                       | 23/37 [00:48<01:01,  4.39s/it]
2025-07-18 10:28:15,543 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:28:20,126 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  65%|########################################8                      | 24/37 [00:52<00:57,  4.43s/it]
2025-07-18 10:28:20,126 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:28:24,645 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  68%|##########################################5                    | 25/37 [00:57<00:53,  4.46s/it]
2025-07-18 10:28:24,645 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:28:28,071 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  70%|############################################2                  | 26/37 [01:00<00:45,  4.18s/it]
2025-07-18 10:28:28,074 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:28:32,768 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  73%|#############################################9                 | 27/37 [01:05<00:43,  4.32s/it]
2025-07-18 10:28:32,769 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:28:37,120 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  76%|###############################################6               | 28/37 [01:09<00:38,  4.33s/it]
2025-07-18 10:28:37,121 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:28:41,617 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  78%|#################################################3             | 29/37 [01:14<00:35,  4.38s/it]
2025-07-18 10:28:41,618 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:28:45,423 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 30/37 [01:18<00:29,  4.21s/it]
2025-07-18 10:28:45,424 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:28:49,835 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  84%|####################################################7          | 31/37 [01:22<00:25,  4.27s/it]
2025-07-18 10:28:49,836 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:28:54,246 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  86%|######################################################4        | 32/37 [01:26<00:21,  4.31s/it]
2025-07-18 10:28:54,247 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:28:58,064 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  89%|########################################################1      | 33/37 [01:30<00:16,  4.17s/it]
2025-07-18 10:28:58,065 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:29:01,480 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  92%|#########################################################8     | 34/37 [01:34<00:11,  3.94s/it]
2025-07-18 10:29:01,481 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:29:05,734 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  95%|###########################################################5   | 35/37 [01:38<00:08,  4.04s/it]
2025-07-18 10:29:05,735 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:29:10,252 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度:  97%|#############################################################2 | 36/37 [01:42<00:04,  4.18s/it]
2025-07-18 10:29:10,253 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:29:11,062 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:29:11] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:29:14,707 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 1/3 写入进度: 100%|###############################################################| 37/37 [01:47<00:00,  4.26s/it]
2025-07-18 10:29:14,708 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:29:14,712 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:30:04,467 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理波段:  33%|########################3                                                | 1/3 [02:43<05:26, 163.38s/it]
2025-07-18 10:30:12,712 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:30:12] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:30:15,321 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/37 [00:00<?, ?it/s]
2025-07-18 10:30:15,324 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:30:15,810 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:   3%|#7                                                              | 1/37 [00:00<00:17,  2.07it/s]
2025-07-18 10:30:15,811 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:30:15,928 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:   8%|#####1                                                          | 3/37 [00:00<00:05,  5.91it/s]
2025-07-18 10:30:15,929 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:30:16,049 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  14%|########6                                                       | 5/37 [00:00<00:03,  8.84it/s]
2025-07-18 10:30:16,050 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:30:16,163 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 7/37 [00:00<00:02, 11.19it/s]
2025-07-18 10:30:16,164 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:30:16,285 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  24%|###############5                                                | 9/37 [00:00<00:02, 12.70it/s]
2025-07-18 10:30:16,286 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:30:16,423 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  30%|##################7                                            | 11/37 [00:01<00:01, 13.27it/s]
2025-07-18 10:30:16,424 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:30:16,546 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  35%|######################1                                        | 13/37 [00:01<00:01, 14.14it/s]
2025-07-18 10:30:16,547 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:30:26,278 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  41%|#########################5                                     | 15/37 [00:10<00:35,  1.62s/it]
2025-07-18 10:30:26,279 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:30:44,873 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  41%|#########################5                                     | 15/37 [00:29<00:35,  1.62s/it]
2025-07-18 10:30:44,874 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:30:55,012 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  46%|############################9                                  | 17/37 [00:39<01:52,  5.64s/it]
2025-07-18 10:30:55,014 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:31:07,246 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  49%|##############################6                                | 18/37 [00:51<02:10,  6.85s/it]
2025-07-18 10:31:07,247 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:31:13,451 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:31:13] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:31:17,984 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  51%|################################3                              | 19/37 [01:02<02:17,  7.66s/it]
2025-07-18 10:31:17,996 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:31:33,012 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  54%|##################################                             | 20/37 [01:17<02:38,  9.35s/it]
2025-07-18 10:31:33,042 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:31:45,901 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  57%|###################################7                           | 21/37 [01:30<02:43, 10.22s/it]
2025-07-18 10:31:45,902 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:31:58,863 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  59%|#####################################4                         | 22/37 [01:43<02:44, 10.94s/it]
2025-07-18 10:31:58,863 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:32:11,139 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  62%|#######################################1                       | 23/37 [01:55<02:38, 11.30s/it]
2025-07-18 10:32:11,139 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:32:14,468 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:32:14] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:32:23,409 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  65%|########################################8                      | 24/37 [02:08<02:30, 11.57s/it]
2025-07-18 10:32:23,410 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:32:36,060 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  68%|##########################################5                    | 25/37 [02:20<02:22, 11.88s/it]
2025-07-18 10:32:36,061 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:32:47,187 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  70%|############################################2                  | 26/37 [02:31<02:08, 11.66s/it]
2025-07-18 10:32:47,188 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:32:58,768 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  73%|#############################################9                 | 27/37 [02:43<01:56, 11.64s/it]
2025-07-18 10:32:58,769 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:33:11,898 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  76%|###############################################6               | 28/37 [02:56<01:48, 12.08s/it]
2025-07-18 10:33:11,898 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:33:16,524 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:33:16] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:33:32,707 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  78%|#################################################3             | 29/37 [03:17<01:57, 14.66s/it]
2025-07-18 10:33:32,708 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:34:18,488 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:34:18] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:35:20,323 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:35:20] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:35:46,904 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 30/37 [05:31<05:51, 50.21s/it]
2025-07-18 10:35:46,904 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:36:22,237 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:36:22] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:36:52,147 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  84%|####################################################7          | 31/37 [06:36<05:28, 54.70s/it]
2025-07-18 10:36:52,148 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:37:15,520 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  86%|######################################################4        | 32/37 [07:00<03:46, 45.34s/it]
2025-07-18 10:37:15,521 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:37:24,186 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:37:24] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:37:46,497 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  89%|########################################################1      | 33/37 [07:31<02:44, 41.04s/it]
2025-07-18 10:37:46,497 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:38:18,990 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  92%|#########################################################8     | 34/37 [08:03<01:55, 38.48s/it]
2025-07-18 10:38:18,991 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:38:26,130 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:38:26] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:38:38,115 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  95%|###########################################################5   | 35/37 [08:22<01:05, 32.68s/it]
2025-07-18 10:38:38,116 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:39:01,080 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度:  97%|#############################################################2 | 36/37 [08:45<00:29, 29.77s/it]
2025-07-18 10:39:01,081 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:39:11,166 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 37/37 [08:55<00:00, 23.87s/it]
2025-07-18 10:39:11,167 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:39:11,170 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:39:28,049 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:39:28] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:40:30,106 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:40:30] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:41:32,089 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:41:32] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:42:02,823 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [14:41<08:09, 489.84s/it]
2025-07-18 10:42:13,655 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/37 [00:00<?, ?it/s]
2025-07-18 10:42:13,656 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:42:14,139 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:   3%|#7                                                              | 1/37 [00:00<00:17,  2.09it/s]
2025-07-18 10:42:14,140 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:42:14,296 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:   8%|#####1                                                          | 3/37 [00:00<00:06,  5.49it/s]
2025-07-18 10:42:14,297 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:42:14,439 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  14%|########6                                                       | 5/37 [00:00<00:04,  7.99it/s]
2025-07-18 10:42:14,440 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:42:14,593 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 7/37 [00:00<00:03,  9.53it/s]
2025-07-18 10:42:14,593 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:42:14,725 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  24%|###############5                                                | 9/37 [00:01<00:02, 11.08it/s]
2025-07-18 10:42:14,726 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:42:14,856 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  30%|##################7                                            | 11/37 [00:01<00:02, 12.25it/s]
2025-07-18 10:42:14,857 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:42:14,991 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  35%|######################1                                        | 13/37 [00:01<00:01, 13.00it/s]
2025-07-18 10:42:14,992 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:42:24,825 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  41%|#########################5                                     | 15/37 [00:11<00:36,  1.64s/it]
2025-07-18 10:42:24,826 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:42:34,108 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:42:34] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:42:37,388 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  43%|###########################2                                   | 16/37 [00:23<01:17,  3.68s/it]
2025-07-18 10:42:37,389 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:42:50,128 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  46%|############################9                                  | 17/37 [00:36<01:51,  5.60s/it]
2025-07-18 10:42:50,129 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:43:03,669 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  49%|##############################6                                | 18/37 [00:50<02:21,  7.44s/it]
2025-07-18 10:43:03,670 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:43:15,086 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  51%|################################3                              | 19/37 [01:01<02:31,  8.43s/it]
2025-07-18 10:43:15,086 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:43:36,117 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:43:36] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:44:38,085 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:44:38] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:44:45,358 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  54%|##################################                             | 20/37 [02:31<08:27, 29.88s/it]
2025-07-18 10:44:45,360 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:45:40,114 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:45:40] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:46:42,770 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:46:42] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:46:48,543 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  57%|###################################7                           | 21/37 [04:34<14:44, 55.30s/it]
2025-07-18 10:46:48,543 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:47:24,924 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  59%|#####################################4                         | 22/37 [05:11<12:29, 50.00s/it]
2025-07-18 10:47:24,924 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:47:44,726 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:47:44] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:48:04,073 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  62%|#######################################1                       | 23/37 [05:50<10:56, 46.90s/it]
2025-07-18 10:48:04,074 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:48:24,611 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  65%|########################################8                      | 24/37 [06:10<08:30, 39.26s/it]
2025-07-18 10:48:24,612 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:48:46,684 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:48:46] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:48:54,282 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  68%|##########################################5                    | 25/37 [06:40<07:17, 36.45s/it]
2025-07-18 10:48:54,282 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:49:04,838 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  70%|############################################2                  | 26/37 [06:51<05:16, 28.81s/it]
2025-07-18 10:49:04,839 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:49:16,200 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  73%|#############################################9                 | 27/37 [07:02<03:56, 23.64s/it]
2025-07-18 10:49:16,200 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:49:31,570 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  76%|###############################################6               | 28/37 [07:17<03:10, 21.18s/it]
2025-07-18 10:49:31,571 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:49:46,698 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  78%|#################################################3             | 29/37 [07:33<02:34, 19.37s/it]
2025-07-18 10:49:46,698 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:49:48,658 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:49:48] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:49:58,022 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 30/37 [07:44<01:58, 16.97s/it]
2025-07-18 10:49:58,022 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:50:10,153 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  84%|####################################################7          | 31/37 [07:56<01:33, 15.52s/it]
2025-07-18 10:50:10,153 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:50:26,861 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  86%|######################################################4        | 32/37 [08:13<01:19, 15.88s/it]
2025-07-18 10:50:26,861 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:50:41,093 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  89%|########################################################1      | 33/37 [08:27<01:01, 15.38s/it]
2025-07-18 10:50:41,093 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:50:50,635 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:50:50] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:50:53,882 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  92%|#########################################################8     | 34/37 [08:40<00:43, 14.61s/it]
2025-07-18 10:50:53,882 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:51:03,166 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  95%|###########################################################5   | 35/37 [08:49<00:26, 13.01s/it]
2025-07-18 10:51:03,167 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:51:17,351 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度:  97%|#############################################################2 | 36/37 [09:03<00:13, 13.36s/it]
2025-07-18 10:51:17,352 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:51:29,451 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
波段 3/3 写入进度: 100%|###############################################################| 37/37 [09:15<00:00, 12.98s/it]
2025-07-18 10:51:29,451 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:51:29,453 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - [A
2025-07-18 10:51:52,651 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:51:52] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:52:54,651 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:52:54] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:53:56,701 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:53:56] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:55:00,631 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:55:00] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:56:02,563 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:56:02] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:57:05,195 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:57:05] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:57:50,214 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [30:29<00:00, 698.77s/it]
2025-07-18 10:57:50,215 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [30:29<00:00, 609.71s/it]
2025-07-18 10:58:07,221 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:58:07] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 10:59:09,385 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 10:59:09] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 11:00:11,564 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:00:11] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 11:01:14,546 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:01:14] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 11:01:52,886 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - INFO - 影像保存完成，耗时: 1829.69 秒 (10:57:50)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp (10:57:50)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif创建掩码，识别有效区域...轻微平滑处理掩码，减少边缘锯齿...寻找有效区域轮廓...找到 2 个初始轮廓使用所有 2 个轮廓，不进行面积过滤转换轮廓为地理坐标...
2025-07-18 11:01:52,991 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/2 [00:00<?, ?it/s]
2025-07-18 11:01:53,834 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理轮廓: 100%|##########################################################################| 2/2 [00:00<00:00,  2.38it/s]
2025-07-18 11:01:53,835 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
处理轮廓: 100%|##########################################################################| 2/2 [00:00<00:00,  2.38it/s]
2025-07-18 11:01:53,990 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - INFO - 成功创建 2 个多边形合并多边形...创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp写入shapefile特征...处理MultiPolygon，包含 2 个多边形
2025-07-18 11:01:53,991 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
写入多边形:   0%|                                                                                | 0/2 [00:00<?, ?it/s]
2025-07-18 11:01:54,214 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
写入多边形:  50%|####################################                                    | 1/2 [00:00<00:00,  4.58it/s]
2025-07-18 11:01:54,217 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - ERROR - 
写入多边形: 100%|########################################################################| 2/2 [00:00<00:00,  9.03it/s]
2025-07-18 11:01:59,776 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - INFO - TIF处理任务 17c7bb5e-fb34-44a5-8a37-2d345848a203 执行成功
2025-07-18 11:01:59,776 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - INFO - 完成时间: 2025-07-18 11:01:59
2025-07-18 11:01:59,781 - tif_task_17c7bb5e-fb34-44a5-8a37-2d345848a203 - INFO - 状态: 运行成功
2025-07-18 11:02:17,687 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:02:17] "GET /api/tif/status?task_id=17c7bb5e-fb34-44a5-8a37-2d345848a203 HTTP/1.1" 200 -
2025-07-18 11:02:19,373 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - INFO - GeoServer发布任务 d72ea4b9-27ce-4b4d-89fc-216e69c0093b 开始执行
2025-07-18 11:02:19,375 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - INFO - 开始时间: 2025-07-18 11:02:19
2025-07-18 11:02:19,377 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "\"tttt\"",
  "store_name": null,
  "layer_name": null
}
2025-07-18 11:02:19,380 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 11:02:19,382 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 11:02:19,371 - geo_publisher - INFO - 启动GeoTIFF发布任务 d72ea4b9-27ce-4b4d-89fc-216e69c0093b: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace="tttt"
2025-07-18 11:02:19,420 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:02:19] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1" 200 -
2025-07-18 11:02:19,448 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:02:19] "GET /api/geo/status?task_id=d72ea4b9-27ce-4b4d-89fc-216e69c0093b HTTP/1.1" 200 -
2025-07-18 11:02:21,330 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 11:02:21,331 - root - INFO - 未提供存储名称，使用文件名: 20250705171600
2025-07-18 11:02:21,333 - root - INFO - 未提供图层名称，使用存储名称: 20250705171600
2025-07-18 11:02:21,408 - root - INFO - 工作区 '"tttt"' 不存在，正在创建...
2025-07-18 11:02:21,881 - geo_publisher - ERROR - 执行任务 d72ea4b9-27ce-4b4d-89fc-216e69c0093b 时出错: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'
2025-07-18 11:02:22,043 - geo_publisher - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 352, in _run_with_logging
    result = func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 425, in publish_func
    return manager.publish_geotiff(path, ws, store, layer)
  File "D:\Drone_Project\geoserverapi\geoserver_manager.py", line 607, in publish_geotiff
    self.geo.create_workspace(workspace)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\geo\Geoserver.py", line 396, in create_workspace
    raise GeoserverException(r.status_code, r.content)
geo.Geoserver.GeoserverException: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'

2025-07-18 11:02:22,056 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - ERROR - 执行出错: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'
2025-07-18 11:02:22,058 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 352, in _run_with_logging
    result = func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 425, in publish_func
    return manager.publish_geotiff(path, ws, store, layer)
  File "D:\Drone_Project\geoserverapi\geoserver_manager.py", line 607, in publish_geotiff
    self.geo.create_workspace(workspace)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\geo\Geoserver.py", line 396, in create_workspace
    raise GeoserverException(r.status_code, r.content)
geo.Geoserver.GeoserverException: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'

2025-07-18 11:02:22,060 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - ERROR - 完成时间: 2025-07-18 11:02:22
2025-07-18 11:02:22,061 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - ERROR - 状态: 发布失败
2025-07-18 11:03:21,380 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:03:21] "GET /api/geo/status?task_id=d72ea4b9-27ce-4b4d-89fc-216e69c0093b HTTP/1.1" 200 -
2025-07-18 11:05:25,231 - geo_publisher - INFO - 启动GeoTIFF发布任务 22d40ee0-c742-4857-b3c0-ceac20fb79e9: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace="tttt"
2025-07-18 11:05:25,234 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:05:25] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1" 200 -
2025-07-18 11:05:25,232 - geo_task_22d40ee0-c742-4857-b3c0-ceac20fb79e9 - INFO - GeoServer发布任务 22d40ee0-c742-4857-b3c0-ceac20fb79e9 开始执行
2025-07-18 11:05:25,245 - geo_task_22d40ee0-c742-4857-b3c0-ceac20fb79e9 - INFO - 开始时间: 2025-07-18 11:05:25
2025-07-18 11:05:25,247 - geo_task_22d40ee0-c742-4857-b3c0-ceac20fb79e9 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "\"tttt\"",
  "store_name": null,
  "layer_name": null
}
2025-07-18 11:05:25,249 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 11:05:25,250 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 11:05:25,264 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:05:25] "GET /api/geo/status?task_id=22d40ee0-c742-4857-b3c0-ceac20fb79e9 HTTP/1.1" 200 -
2025-07-18 11:05:25,291 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 11:05:25,293 - root - INFO - 未提供存储名称，使用文件名: 20250705171600
2025-07-18 11:05:25,297 - root - INFO - 未提供图层名称，使用存储名称: 20250705171600
2025-07-18 11:05:25,339 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171600\20250705171600.tif
2025-07-18 11:05:27,690 - root - ERROR - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif' 失败: ('Connection aborted.', ConnectionAbortedError(10053, '你的主机中的软件中止了一个已建立的连接。', None, 10053, None))
2025-07-18 11:05:27,693 - geo_task_22d40ee0-c742-4857-b3c0-ceac20fb79e9 - INFO - GeoServer发布任务 22d40ee0-c742-4857-b3c0-ceac20fb79e9 执行失败
2025-07-18 11:05:27,901 - geo_task_22d40ee0-c742-4857-b3c0-ceac20fb79e9 - INFO - 完成时间: 2025-07-18 11:05:27
2025-07-18 11:05:27,903 - geo_task_22d40ee0-c742-4857-b3c0-ceac20fb79e9 - INFO - 状态: 发布失败
2025-07-18 11:06:27,242 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:06:27] "GET /api/geo/status?task_id=22d40ee0-c742-4857-b3c0-ceac20fb79e9 HTTP/1.1" 200 -
2025-07-18 11:09:32,955 - geo_publisher - INFO - 启动GeoTIFF发布任务 9c5a0509-2017-44c3-9483-ec555fd7591d: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace="tttt"
2025-07-18 11:09:32,957 - geo_task_9c5a0509-2017-44c3-9483-ec555fd7591d - INFO - GeoServer发布任务 9c5a0509-2017-44c3-9483-ec555fd7591d 开始执行
2025-07-18 11:09:32,961 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:09:32] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1" 200 -
2025-07-18 11:09:32,962 - geo_task_9c5a0509-2017-44c3-9483-ec555fd7591d - INFO - 开始时间: 2025-07-18 11:09:32
2025-07-18 11:09:32,964 - geo_task_9c5a0509-2017-44c3-9483-ec555fd7591d - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "\"tttt\"",
  "store_name": null,
  "layer_name": null
}
2025-07-18 11:09:32,966 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 11:09:32,966 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 11:09:32,993 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:09:32] "GET /api/geo/status?task_id=9c5a0509-2017-44c3-9483-ec555fd7591d HTTP/1.1" 200 -
2025-07-18 11:09:32,999 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 11:09:33,002 - root - INFO - 未提供存储名称，使用文件名: 20250705171600
2025-07-18 11:09:33,003 - root - INFO - 未提供图层名称，使用存储名称: 20250705171600
2025-07-18 11:09:33,045 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171600\20250705171600.tif
2025-07-18 11:09:33,542 - root - ERROR - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif' 失败: ('Connection aborted.', ConnectionAbortedError(10053, '你的主机中的软件中止了一个已建立的连接。', None, 10053, None))
2025-07-18 11:09:33,545 - geo_task_9c5a0509-2017-44c3-9483-ec555fd7591d - INFO - GeoServer发布任务 9c5a0509-2017-44c3-9483-ec555fd7591d 执行失败
2025-07-18 11:09:33,549 - geo_task_9c5a0509-2017-44c3-9483-ec555fd7591d - INFO - 完成时间: 2025-07-18 11:09:33
2025-07-18 11:09:33,549 - geo_task_9c5a0509-2017-44c3-9483-ec555fd7591d - INFO - 状态: 发布失败
2025-07-18 11:10:34,907 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:10:34] "GET /api/geo/status?task_id=9c5a0509-2017-44c3-9483-ec555fd7591d HTTP/1.1" 200 -
