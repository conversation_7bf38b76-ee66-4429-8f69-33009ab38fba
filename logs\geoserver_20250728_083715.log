2025-07-28 08:37:15,048 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250728_083715.log
2025-07-28 08:37:15,070 - geo_publisher - INFO - 加载了 24 个任务状态
2025-07-28 08:37:15,130 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-28 08:37:15,130 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-28 08:37:15,235 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-28 08:37:15,244 - root - INFO - === GeoServer REST API服务 ===
2025-07-28 08:37:15,245 - root - INFO - 主机: 0.0.0.0
2025-07-28 08:37:15,245 - root - INFO - 端口: 5083
2025-07-28 08:37:15,246 - root - INFO - 调试模式: 禁用
2025-07-28 08:37:15,247 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-28 08:37:15,270 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-28 08:37:15,271 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-28 08:43:20,686 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:43:20,693 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:43:20,884 - map_api - INFO - 找到 3 个目录
2025-07-28 08:43:20,889 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:43:20,912 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:43:20,919 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:43:20,952 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:43:20,955 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:43:20,974 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:43:20,979 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:43:20,983 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:43:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:43:30,094 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:43:30,099 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:43:30,111 - map_api - INFO - 找到 3 个目录
2025-07-28 08:43:30,112 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:43:30,125 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:43:30,127 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:43:30,135 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:43:30,138 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:43:30,146 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:43:30,151 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:43:30,154 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:43:30] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:44:12,698 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:44:12,775 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:44:12,809 - map_api - INFO - 找到 3 个目录
2025-07-28 08:44:12,823 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:44:12,842 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:44:12,853 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:44:12,861 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:44:12,865 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:44:12,874 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:44:12,880 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:44:12,882 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:44:12] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:44:17,389 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:44:17,393 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:44:17,405 - map_api - INFO - 找到 3 个目录
2025-07-28 08:44:17,407 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:44:17,414 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:44:17,423 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:44:17,427 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:44:17,429 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:44:17,435 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:44:17,436 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:44:17,438 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:44:17] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:45:13,221 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:45:13,232 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:45:13,275 - map_api - INFO - 找到 3 个目录
2025-07-28 08:45:13,276 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:45:13,287 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:45:13,293 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:45:13,305 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:45:13,308 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:45:13,326 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:45:13,328 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:45:13,331 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:45:13] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:46:12,746 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:46:12,755 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:46:12,764 - map_api - INFO - 找到 3 个目录
2025-07-28 08:46:12,766 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:46:12,772 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:46:12,776 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:46:12,783 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:46:12,784 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:46:12,798 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:46:12,799 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:46:12,808 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:46:12] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:47:12,674 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:47:12,678 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:47:12,706 - map_api - INFO - 找到 3 个目录
2025-07-28 08:47:12,711 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:47:12,729 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:47:12,737 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:47:12,748 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:47:12,756 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:47:12,763 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:47:12,768 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:47:12,778 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:47:12] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:48:12,672 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:48:12,678 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:48:12,693 - map_api - INFO - 找到 3 个目录
2025-07-28 08:48:12,695 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:48:12,703 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:48:12,707 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:48:12,730 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:48:12,735 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:48:12,749 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:48:12,754 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:48:12,762 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:48:12] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:49:12,675 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:49:12,679 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:49:12,708 - map_api - INFO - 找到 3 个目录
2025-07-28 08:49:12,709 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:49:12,728 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:49:12,733 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:49:12,745 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:49:12,754 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:49:12,775 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:49:12,776 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:49:12,792 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:49:12] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:50:12,677 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:50:12,684 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:50:12,705 - map_api - INFO - 找到 3 个目录
2025-07-28 08:50:12,707 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:50:12,718 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:50:12,720 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:50:12,738 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:50:12,753 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:50:12,762 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:50:12,767 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:50:12,775 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:50:12] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:51:13,200 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:51:13,206 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:51:13,213 - map_api - INFO - 找到 3 个目录
2025-07-28 08:51:13,217 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:51:13,221 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:51:13,223 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:51:13,227 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:51:13,233 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:51:13,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:51:13,238 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:51:13,240 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:51:13] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:52:24,609 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:52:24,619 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:52:24,650 - map_api - INFO - 找到 3 个目录
2025-07-28 08:52:24,654 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:52:24,660 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:52:24,663 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:52:24,671 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:52:24,673 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:52:24,682 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:52:24,684 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:52:24,686 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:52:24] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:53:12,671 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:53:12,672 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:53:12,692 - map_api - INFO - 找到 3 个目录
2025-07-28 08:53:12,694 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:53:12,701 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:53:12,704 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:53:12,716 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:53:12,727 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:53:12,734 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:53:12,737 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:53:12,765 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:53:12] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:54:12,667 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:54:12,671 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:54:12,685 - map_api - INFO - 找到 3 个目录
2025-07-28 08:54:12,687 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:54:12,724 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:54:12,761 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:54:12,791 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:54:12,795 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:54:12,805 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:54:12,811 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:54:12,813 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:54:12] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:55:13,195 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:55:13,199 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:55:13,225 - map_api - INFO - 找到 3 个目录
2025-07-28 08:55:13,230 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:55:13,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:55:13,260 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:55:13,285 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:55:13,295 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:55:13,317 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:55:13,323 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:55:13,326 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:55:13] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:56:27,204 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:56:27,208 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:56:27,220 - map_api - INFO - 找到 3 个目录
2025-07-28 08:56:27,222 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:56:27,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:56:27,258 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:56:27,263 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:56:27,265 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:56:27,271 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:56:27,274 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:56:27,278 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:56:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:57:27,208 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:57:27,212 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:57:27,222 - map_api - INFO - 找到 3 个目录
2025-07-28 08:57:27,223 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:57:27,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:57:27,236 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:57:27,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:57:27,251 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:57:27,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:57:27,265 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:57:27,267 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:57:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:58:27,218 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:58:27,222 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:58:27,235 - map_api - INFO - 找到 3 个目录
2025-07-28 08:58:27,237 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:58:27,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:58:27,269 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:58:27,279 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:58:27,284 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:58:27,293 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:58:27,299 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:58:27,304 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:58:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 08:59:27,216 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 08:59:27,228 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 08:59:27,236 - map_api - INFO - 找到 3 个目录
2025-07-28 08:59:27,241 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 08:59:27,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 08:59:27,250 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 08:59:27,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 08:59:27,261 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 08:59:27,274 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 08:59:27,276 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 08:59:27,282 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 08:59:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:00:27,209 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:00:27,213 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:00:27,231 - map_api - INFO - 找到 3 个目录
2025-07-28 09:00:27,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:00:27,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:00:27,246 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:00:27,257 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:00:27,264 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:00:27,273 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:00:27,281 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:00:27,284 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:00:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:01:27,208 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:01:27,213 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:01:27,243 - map_api - INFO - 找到 3 个目录
2025-07-28 09:01:27,247 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:01:27,270 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:01:27,274 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:01:27,301 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:01:27,305 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:01:27,311 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:01:27,312 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:01:27,314 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:01:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:02:27,222 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:02:27,227 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:02:27,238 - map_api - INFO - 找到 3 个目录
2025-07-28 09:02:27,239 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:02:27,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:02:27,250 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:02:27,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:02:27,261 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:02:27,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:02:27,268 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:02:27,270 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:02:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:03:27,226 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:03:27,231 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:03:27,244 - map_api - INFO - 找到 3 个目录
2025-07-28 09:03:27,246 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:03:27,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:03:27,259 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:03:27,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:03:27,274 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:03:27,280 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:03:27,286 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:03:27,294 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:03:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:04:27,208 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:04:27,215 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:04:27,249 - map_api - INFO - 找到 3 个目录
2025-07-28 09:04:27,253 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:04:27,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:04:27,262 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:04:27,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:04:27,269 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:04:27,274 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:04:27,281 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:04:27,286 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:04:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:05:27,285 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:05:27,294 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:05:27,307 - map_api - INFO - 找到 3 个目录
2025-07-28 09:05:27,311 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:05:27,321 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:05:27,327 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:05:27,355 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:05:27,356 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:05:27,383 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:05:27,387 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:05:27,394 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:05:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:06:27,206 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:06:27,209 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:06:27,243 - map_api - INFO - 找到 3 个目录
2025-07-28 09:06:27,250 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:06:27,271 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:06:27,275 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:06:27,302 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:06:27,307 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:06:27,315 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:06:27,317 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:06:27,322 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:06:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:07:27,211 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:07:27,215 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:07:27,250 - map_api - INFO - 找到 3 个目录
2025-07-28 09:07:27,254 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:07:27,277 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:07:27,280 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:07:27,308 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:07:27,312 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:07:27,338 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:07:27,348 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:07:27,350 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:07:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:08:27,208 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:08:27,214 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:08:27,223 - map_api - INFO - 找到 3 个目录
2025-07-28 09:08:27,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:08:27,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:08:27,252 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:08:27,278 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:08:27,281 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:08:27,286 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:08:27,289 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:08:27,291 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:08:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:09:02,575 - batch_executor - INFO - 启动任务 550687ac-105b-4e27-97bf-9258c34647cc: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
2025-07-28 09:09:02,577 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:09:02] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-28 09:09:02,630 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:09:02] "GET /api/batch/status?task_id=550687ac-105b-4e27-97bf-9258c34647cc HTTP/1.1" 200 -
2025-07-28 09:09:13,206 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:09:13,208 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:09:13,232 - map_api - INFO - 找到 3 个目录
2025-07-28 09:09:13,233 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:09:13,260 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:09:13,262 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:09:13,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:09:13,268 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:09:13,273 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 09:09:13,278 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:09:13,280 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:09:13] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:10:03,801 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:10:03] "GET /api/batch/status?task_id=550687ac-105b-4e27-97bf-9258c34647cc HTTP/1.1" 200 -
2025-07-28 09:10:20,963 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-28 09:10:20,965 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:10:20,966 - tif_api - INFO - 输入文件大小: 237.53 MB
2025-07-28 09:10:20,968 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 09:10:20,969 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:10:20,972 - tif_api - INFO - 黑色阈值: 0
2025-07-28 09:10:20,973 - tif_api - INFO - 白色阈值: 255
2025-07-28 09:10:20,975 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-28 09:10:20,979 - tif_executor - INFO - 启动TIF处理任务 b6e90861-407a-4faf-9b63-e854fc59d3b3: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:10:20,980 - tif_api - INFO - 异步任务启动成功，任务ID: b6e90861-407a-4faf-9b63-e854fc59d3b3
2025-07-28 09:10:20,982 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:10:20] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-28 09:10:20,989 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - ============ TIF处理任务 b6e90861-407a-4faf-9b63-e854fc59d3b3 开始执行 ============
2025-07-28 09:10:20,999 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 开始时间: 2025-07-28 09:10:20
2025-07-28 09:10:21,001 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 09:10:21,003 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 系统信息:
2025-07-28 09:10:21,005 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 09:10:21,007 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO -   Python版本: 3.8.20
2025-07-28 09:10:21,009 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO -   GDAL版本: 3.9.2
2025-07-28 09:10:21,010 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO -   GPU可用: 否
2025-07-28 09:10:21,011 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 检查参数有效性...
2025-07-28 09:10:21,011 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:10:21,013 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 开始执行TIF处理流程...
2025-07-28 09:10:21,030 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:10:21,033 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 09:10:21,031 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:10:21,033 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:10:21,034 - tif_api - INFO - 查询任务: b6e90861-407a-4faf-9b63-e854fc59d3b3
2025-07-28 09:10:21,040 - tif_api - INFO - 任务 b6e90861-407a-4faf-9b63-e854fc59d3b3 状态查询成功，当前状态: 正在运行
2025-07-28 09:10:21,041 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:10:21] "GET /api/tif/status?task_id=b6e90861-407a-4faf-9b63-e854fc59d3b3 HTTP/1.1" 200 -
2025-07-28 09:10:21,043 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-28 09:10:27,217 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:10:27,226 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:10:27,243 - map_api - INFO - 找到 3 个目录
2025-07-28 09:10:27,248 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:10:27,348 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:10:27,351 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:10:27,421 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:10:27,427 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:10:27,457 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:10:27,463 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:10:27,467 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:10:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:10:40,576 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (09:10:39)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 09:10:40,667 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 09:10:40,913 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:04,  4.24it/s]
2025-07-28 09:10:41,530 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:01,  9.83it/s]
2025-07-28 09:10:41,714 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
处理数据块:  43%|##############################4                                        | 9/21 [00:01<00:01,  8.73it/s]
2025-07-28 09:10:41,834 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
处理数据块:  48%|#################################3                                    | 10/21 [00:01<00:01,  8.65it/s]
2025-07-28 09:10:41,924 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:01<00:00, 16.82it/s]
2025-07-28 09:10:41,931 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 09:10:41,932 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 09:10:41,974 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 512.19it/s]
2025-07-28 09:10:47,972 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 3.50 秒 (09:10:43)预计总处理时间: 约 10.51 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (09:10:43)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 09:10:47,975 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 09:11:11,486 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 09:11:11,510 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:11:11,511 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:11,647 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:01, 14.95it/s]
2025-07-28 09:11:11,648 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:11,814 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:01, 12.96it/s]
2025-07-28 09:11:11,815 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:12,059 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:01, 10.25it/s]
2025-07-28 09:11:12,059 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:12,322 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:00<00:01,  9.00it/s]
2025-07-28 09:11:12,322 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:12,440 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:01,  8.89it/s]
2025-07-28 09:11:12,440 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:12,551 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  8.91it/s]
2025-07-28 09:11:12,552 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:12,742 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:01<00:00,  9.50it/s]
2025-07-28 09:11:12,742 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:12,842 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:00,  9.59it/s]
2025-07-28 09:11:12,843 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:12,954 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:01<00:00,  9.41it/s]
2025-07-28 09:11:12,955 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:13,071 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:01<00:00,  9.17it/s]
2025-07-28 09:11:13,072 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:13,240 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:01<00:00,  8.00it/s]
2025-07-28 09:11:13,241 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:13,465 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:01<00:00,  6.55it/s]
2025-07-28 09:11:13,465 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:13,686 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:02<00:00,  5.81it/s]
2025-07-28 09:11:13,686 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:13,966 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:02<00:00,  4.91it/s]
2025-07-28 09:11:13,967 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:14,034 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:11:21,906 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:11:21,915 - tif_api - INFO - 查询任务: b6e90861-407a-4faf-9b63-e854fc59d3b3
2025-07-28 09:11:21,931 - tif_api - INFO - 任务 b6e90861-407a-4faf-9b63-e854fc59d3b3 状态查询成功，当前状态: 正在运行
2025-07-28 09:11:21,947 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:11:21] "GET /api/tif/status?task_id=b6e90861-407a-4faf-9b63-e854fc59d3b3 HTTP/1.1" 200 -
2025-07-28 09:11:27,239 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:11:27,247 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:11:27,341 - map_api - INFO - 找到 3 个目录
2025-07-28 09:11:27,348 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:11:27,800 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:11:27,804 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:11:27,996 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:11:28,001 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:11:28,013 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:11:28,022 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:11:28,042 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:11:28] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:12:20,165 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:32<03:04, 92.18s/it]
2025-07-28 09:12:24,063 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:12:24,072 - tif_api - INFO - 查询任务: b6e90861-407a-4faf-9b63-e854fc59d3b3
2025-07-28 09:12:24,086 - tif_api - INFO - 任务 b6e90861-407a-4faf-9b63-e854fc59d3b3 状态查询成功，当前状态: 正在运行
2025-07-28 09:12:24,091 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:12:24] "GET /api/tif/status?task_id=b6e90861-407a-4faf-9b63-e854fc59d3b3 HTTP/1.1" 200 -
2025-07-28 09:12:27,238 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:12:27,244 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:12:27,268 - map_api - INFO - 找到 3 个目录
2025-07-28 09:12:27,271 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:12:28,583 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:12:28,588 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:12:28,613 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:12:28,615 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:12:28,651 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:12:28,655 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:12:28,661 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:12:28] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:12:37,722 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 09:12:37,738 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:12:37,739 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:38,213 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:09,  2.11it/s]
2025-07-28 09:12:38,214 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:38,374 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:05,  3.45it/s]
2025-07-28 09:12:38,374 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:38,591 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:04,  3.90it/s]
2025-07-28 09:12:38,591 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:38,771 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:01<00:03,  4.42it/s]
2025-07-28 09:12:38,772 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:38,921 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:01<00:03,  5.02it/s]
2025-07-28 09:12:38,923 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:39,059 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:01<00:02,  5.61it/s]
2025-07-28 09:12:39,060 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:39,300 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  5.03it/s]
2025-07-28 09:12:39,301 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:39,476 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:02,  5.22it/s]
2025-07-28 09:12:39,476 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:39,621 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:02,  5.65it/s]
2025-07-28 09:12:39,622 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:39,771 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:02<00:01,  5.93it/s]
2025-07-28 09:12:39,773 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:40,006 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:02<00:01,  5.29it/s]
2025-07-28 09:12:40,007 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:40,184 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:02<00:01,  5.39it/s]
2025-07-28 09:12:40,184 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:40,410 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:02<00:01,  5.06it/s]
2025-07-28 09:12:40,412 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:40,572 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:01,  5.35it/s]
2025-07-28 09:12:40,573 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:40,937 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:03<00:01,  4.15it/s]
2025-07-28 09:12:40,938 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:42,733 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:04<00:03,  1.41it/s]
2025-07-28 09:12:42,734 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:46,663 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:08<00:06,  1.68s/it]
2025-07-28 09:12:46,664 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:51,879 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:14<00:08,  2.74s/it]
2025-07-28 09:12:51,879 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:12:58,428 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:20<00:07,  3.88s/it]
2025-07-28 09:12:58,429 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:13:03,980 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:26<00:04,  4.38s/it]
2025-07-28 09:13:03,981 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:13:05,173 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:27<00:00,  3.43s/it]
2025-07-28 09:13:05,174 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:13:05,177 - tif_task_b6e90861-407a-4faf-9b63-e854fc59d3b3 - ERROR - [A
2025-07-28 09:13:25,451 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:13:25,458 - tif_api - INFO - 查询任务: b6e90861-407a-4faf-9b63-e854fc59d3b3
2025-07-28 09:13:25,472 - tif_api - INFO - 任务 b6e90861-407a-4faf-9b63-e854fc59d3b3 状态查询成功，当前状态: 正在运行
2025-07-28 09:13:25,480 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:13:25] "GET /api/tif/status?task_id=b6e90861-407a-4faf-9b63-e854fc59d3b3 HTTP/1.1" 200 -
2025-07-28 09:13:27,233 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:13:27,237 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:13:27,300 - map_api - INFO - 找到 3 个目录
2025-07-28 09:13:27,306 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:13:27,365 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:13:27,370 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:13:27,405 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:13:27,408 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:13:27,414 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:13:27,416 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:13:27,425 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:13:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
