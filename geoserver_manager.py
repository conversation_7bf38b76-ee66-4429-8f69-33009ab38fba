import os
import logging
import glob
import datetime
from geo.Geoserver import Geoserver
from config import (
    GEOSERVER_URL,
    GEOSERVER_USER,
    GEOSERVER_PASSWORD,
    DEFAULT_WORKSPACE,
    DEFAULT_DATASTORE,
    LOG_LEVEL,
    LOG_FILE,
)


# 配置日志
def setup_logging():
    """配置并返回日志系统"""
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # 创建logs目录（如果不存在）
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    os.makedirs(log_dir, exist_ok=True)

    # 如果没有指定日志文件，使用带时间戳的文件名
    if not LOG_FILE or LOG_FILE == "geoserver_api.log":
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file_path = os.path.join(log_dir, f"geoserver_{timestamp}.log")
    else:
        log_file_path = os.path.join(log_dir, LOG_FILE)

    # 创建日志处理器
    file_handler = logging.FileHandler(log_file_path, encoding="utf-8")
    console_handler = logging.StreamHandler()

    # 设置格式
    formatter = logging.Formatter(log_format)
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, LOG_LEVEL))

    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 添加处理器
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    # 记录初始化日志
    root_logger.info(f"日志系统初始化完成，日志文件：{log_file_path}")
    return root_logger


# 初始化日志系统
logger = setup_logging()


class GeoServerManager:
    """用于管理GeoServer操作的类，如发布图层、创建工作区等。"""

    def __init__(
        self, url=GEOSERVER_URL, username=GEOSERVER_USER, password=GEOSERVER_PASSWORD
    ):
        """初始化GeoServer连接。"""
        self.geo = Geoserver(url, username=username, password=password)
        logger.info(f"已连接到GeoServer：{url}")
        self.check_connection()

    def check_connection(self):
        """检查GeoServer连接是否正常，并获取GeoServer信息。"""
        try:
            import requests
            from requests.auth import HTTPBasicAuth

            # 构建REST API URL
            base_url = self.geo.service_url
            if not base_url.endswith("/rest"):
                base_url = f"{base_url}/rest"

            # 尝试访问about/version.json获取GeoServer版本信息
            url = f"{base_url}/about/version.json"
            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            headers = {"Accept": "application/json"}

            logger.info(f"正在检查GeoServer连接: {url}")

            response = requests.get(url, auth=auth, headers=headers)

            if response.status_code == 200:
                try:
                    info = response.json()
                    version = (
                        info.get("about", {})
                        .get("resource", [{}])[0]
                        .get("Version", "Unknown")
                    )
                    logger.info(f"成功连接到GeoServer，版本: {version}")
                except Exception as e:
                    logger.warning(f"解析GeoServer版本信息失败: {str(e)}")
            else:
                logger.warning(f"连接GeoServer返回错误状态码: {response.status_code}")
                logger.warning(f"尝试列出工作区以进一步验证连接...")

                # 如果获取版本信息失败，尝试列出工作区
                try:
                    workspaces = self.geo.get_workspaces()
                    if workspaces and "workspaces" in workspaces:
                        ws_count = len(
                            workspaces.get("workspaces", {}).get("workspace", [])
                        )
                        logger.info(f"成功获取工作区列表，共有 {ws_count} 个工作区")
                    else:
                        logger.warning("工作区列表为空或格式不正确")
                except Exception as e:
                    logger.error(
                        f"获取工作区列表失败，可能无法正常连接GeoServer: {str(e)}"
                    )
                    raise ConnectionError(f"无法连接到GeoServer: {str(e)}")

        except Exception as e:
            logger.error(f"检查GeoServer连接失败: {str(e)}")
            raise ConnectionError(f"无法连接到GeoServer: {str(e)}")

    def check_workspace_exists(self, workspace):
        """
        检查工作区是否存在

        Args:
            workspace: 工作区名称

        Returns:
            工作区是否存在
        """
        try:
            all_workspaces = self.geo.get_workspaces()
            if all_workspaces and "workspaces" in all_workspaces:
                for ws in all_workspaces.get("workspaces", {}).get("workspace", []):
                    if ws.get("name") == workspace:
                        return True
            return False
        except Exception as e:
            logger.error(f"检查工作区 '{workspace}' 是否存在时出错: {str(e)}")
            return False

    def verify_layer(self, workspace, store_name=None, layer_name=None):
        """
        验证图层是否存在

        Args:
            workspace: 工作区名称
            store_name: 存储名称，如果不提供则使用layer_name
            layer_name: 图层名称

        Returns:
            图层是否存在
        """
        if not layer_name:
            logger.warning("验证图层需要提供图层名称")
            return False

        try:
            import requests
            from requests.auth import HTTPBasicAuth

            base_url = self.geo.service_url
            if not base_url.endswith("/rest"):
                base_url = f"{base_url}/rest"

            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            headers = {"Accept": "application/json"}

            # 尝试多种可能的URL格式
            urls_to_try = []

            # 1. 使用完整路径: /workspaces/{ws}/datastores/{store}/featuretypes/{layer}
            if store_name:
                urls_to_try.append(
                    {
                        "url": f"{base_url}/workspaces/{workspace}/datastores/{store_name}/featuretypes/{layer_name}",
                        "description": f"完整路径 - 工作区/存储/图层: {workspace}/{store_name}/{layer_name}",
                    }
                )

            # 2. 使用图层名称可能与存储名称相同: /workspaces/{ws}/datastores/{store}/featuretypes/{store}
            if store_name:
                urls_to_try.append(
                    {
                        "url": f"{base_url}/workspaces/{workspace}/datastores/{store_name}/featuretypes/{store_name}",
                        "description": f"存储名称作为图层: {workspace}/{store_name}/{store_name}",
                    }
                )

            # 3. 直接使用图层路径: /layers/{ws}:{layer}
            urls_to_try.append(
                {
                    "url": f"{base_url}/layers/{workspace}:{layer_name}",
                    "description": f"直接图层路径: {workspace}:{layer_name}",
                }
            )

            # 4. 使用原始文件名
            urls_to_try.append(
                {
                    "url": f"{base_url}/workspaces/{workspace}/featuretypes/{layer_name}",
                    "description": f"工作区/图层直接路径: {workspace}/{layer_name}",
                }
            )

            for url_info in urls_to_try:
                logger.debug(f"尝试验证图层: {url_info['description']}")
                response = requests.get(url_info["url"], auth=auth, headers=headers)
                if response.status_code == 200:
                    logger.info(f"图层验证成功: {url_info['description']}")
                    return True
                else:
                    logger.debug(
                        f"图层验证失败 ({response.status_code}): {url_info['description']}"
                    )

            # 如果所有URL都验证失败，则尝试列出所有图层并搜索
            try:
                all_layers_url = f"{base_url}/layers"
                response = requests.get(all_layers_url, auth=auth, headers=headers)
                if response.status_code == 200:
                    layers_data = response.json()
                    if "layers" in layers_data and "layer" in layers_data["layers"]:
                        for layer in layers_data["layers"]["layer"]:
                            if (
                                layer.get("name") == f"{workspace}:{layer_name}"
                                or layer.get("name") == layer_name
                            ):
                                logger.info(
                                    f"在所有图层列表中找到图层: {layer.get('name')}"
                                )
                                return True
                logger.warning(f"在所有图层列表中未找到图层: {workspace}:{layer_name}")
            except Exception as e:
                logger.warning(f"获取所有图层列表时出错: {str(e)}")

            logger.warning(f"图层 '{workspace}:{layer_name}' 不存在或无法访问")
            return False

        except Exception as e:
            logger.error(
                f"验证图层 '{workspace}:{layer_name}' 是否存在时出错: {str(e)}"
            )
            return False

    def create_workspace(self, workspace=DEFAULT_WORKSPACE):
        """如果工作区不存在，则创建一个新的工作区。"""
        try:
            self.geo.create_workspace(workspace)
            logger.info(f"工作区 '{workspace}' 创建成功")
            return True
        except Exception as e:
            logger.error(f"创建工作区 '{workspace}' 失败：{str(e)}")
            return False

    def delete_workspace(self, workspace):
        """删除工作区。"""
        try:
            self.geo.delete_workspace(workspace)
            logger.info(f"工作区 '{workspace}' 删除成功")
            return True
        except Exception as e:
            logger.error(f"删除工作区 '{workspace}' 失败：{str(e)}")
            return False

    def create_datastore(self, name, workspace=DEFAULT_WORKSPACE, conn_params=None):
        """创建PostGIS数据存储。"""
        if conn_params is None:
            logger.error("需要提供数据库连接参数")
            return False

        try:
            self.geo.create_datastore(
                name=name,
                workspace=workspace,
                host=conn_params.get("host", "localhost"),
                port=conn_params.get("port", 5432),
                database=conn_params.get("database"),
                username=conn_params.get("username"),
                password=conn_params.get("password"),
            )
            logger.info(f"数据存储 '{name}' 在工作区 '{workspace}' 中创建成功")
            return True
        except Exception as e:
            logger.error(f"创建数据存储 '{name}' 失败：{str(e)}")
            return False

    def publish_shapefile(
        self,
        shapefile_path,
        workspace=DEFAULT_WORKSPACE,
        store_name=None,
        layer_name=None,
        charset="UTF-8",
    ):
        """
        发布Shapefile到GeoServer。

        Args:
            shapefile_path: Shapefile文件路径
            workspace: 工作区名称，默认使用DEFAULT_WORKSPACE
            store_name: 存储名称，默认使用文件名
            layer_name: 图层名称，默认使用原始文件名
            charset: DBF文件字符集，默认为UTF-8以支持中文

        Returns:
            发布是否成功
        """
        if not os.path.exists(shapefile_path):
            logger.error(f"未找到Shapefile：{shapefile_path}")
            return False

        # 从路径中提取文件名和基本名称，避免中文路径问题
        filename = os.path.basename(shapefile_path)
        base_name = os.path.splitext(filename)[0]

        # 使用文件名作为图层名
        layer_name = layer_name or base_name

        # 检查工作区是否存在，如果不存在则创建
        if not self.check_workspace_exists(workspace):
            logger.info(f"工作区 '{workspace}' 不存在，正在创建...")
            self.geo.create_workspace(workspace)
            if not self.check_workspace_exists(workspace):
                logger.error(f"工作区 '{workspace}' 创建失败")
                return False
            logger.info(f"成功创建工作区 '{workspace}'")

        # 使用REST API直接发布，这样可以设置更多参数
        try:
            # 准备绝对路径
            abs_path = os.path.abspath(shapefile_path)
            logger.info(f"使用绝对路径: {abs_path}")

            # 创建一个包含所有相关文件的ZIP文件
            import io
            import zipfile
            import requests
            from requests.auth import HTTPBasicAuth
            import json

            zip_buffer = io.BytesIO()
            with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
                base_dir = os.path.dirname(abs_path)
                base_name_file = os.path.splitext(os.path.basename(abs_path))[0]

                # 添加所有相关文件到zip
                related_files_found = 0
                for ext in [".shp", ".dbf", ".shx", ".prj", ".qix", ".cpg", ".qmd"]:
                    file_path = os.path.join(base_dir, base_name_file + ext)
                    if os.path.exists(file_path):
                        logger.info(f"添加文件到ZIP: {os.path.basename(file_path)}")
                        with open(file_path, "rb") as f:
                            zip_file.writestr(os.path.basename(file_path), f.read())
                        related_files_found += 1

                if related_files_found < 3:
                    logger.warning(
                        f"只找到 {related_files_found} 个相关文件，可能不足以正确发布"
                    )

            zip_buffer.seek(0)

            # 发送ZIP文件到GeoServer
            base_url = self.geo.service_url
            if not base_url.endswith("/rest"):
                base_url = f"{base_url}/rest"

            # 构造PUT URL
            url = f"{base_url}/workspaces/{workspace}/datastores/{store_name}/file.shp"
            logger.info(f"使用URL: {url}")

            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            headers = {"Content-type": "application/zip"}

            # 添加查询参数，设置字符集和其他选项
            params = {
                "charset": charset,
                "create spatial index": "true",  # 启用空间索引
                "memory mapped buffer": "true",  # 启用内存缓存
                "cache and reuse memory maps": "true",  # 缓存和重用内存映射
            }

            # 发送请求
            logger.info("发送ZIP数据到GeoServer...")
            response = requests.put(
                url,
                auth=auth,
                headers=headers,
                data=zip_buffer.getvalue(),
                params=params,
            )

            if response.status_code >= 200 and response.status_code < 300:
                logger.info(f"成功创建数据存储 '{store_name}'")

                # 在数据存储创建成功后，配置图层
                try:
                    # 首先查询可用的特征类型
                    featuretypes_url = f"{base_url}/workspaces/{workspace}/datastores/{store_name}/featuretypes"
                    ft_response = requests.get(
                        featuretypes_url,
                        auth=auth,
                        headers={"Accept": "application/json"},
                    )

                    if ft_response.status_code == 200:
                        # 检查是否已有图层
                        ft_data = ft_response.json()
                        existing_layer = False

                        if (
                            "featureTypes" in ft_data
                            and "featureType" in ft_data["featureTypes"]
                        ):
                            for ft in ft_data["featureTypes"]["featureType"]:
                                if ft["name"] == layer_name:
                                    existing_layer = True
                                    logger.info(f"图层 '{layer_name}' 已存在")
                                    break

                        if not existing_layer:
                            # 创建图层，配置名称和标题
                            create_layer_url = f"{base_url}/workspaces/{workspace}/datastores/{store_name}/featuretypes"
                            create_headers = {
                                "Content-type": "application/json",
                                "Accept": "application/json",
                            }
                            create_data = {
                                "featureType": {
                                    "name": layer_name,
                                    "nativeName": base_name_file,
                                    "title": layer_name,
                                    "enabled": True,
                                }
                            }

                            create_response = requests.post(
                                create_layer_url,
                                auth=auth,
                                headers=create_headers,
                                data=json.dumps(create_data),
                            )

                            if (
                                create_response.status_code >= 200
                                and create_response.status_code < 300
                            ):
                                logger.info(f"成功创建图层 '{layer_name}'")
                            else:
                                logger.error(
                                    f"手动创建图层失败: {create_response.status_code}, {create_response.text}"
                                )
                                return False
                    else:
                        logger.warning(
                            f"无法获取特征类型列表: {ft_response.status_code}"
                        )

                    # 验证图层是否存在
                    if self.verify_layer(workspace, store_name, layer_name):
                        logger.info(
                            f"Shapefile '{shapefile_path}' 成功发布为图层 '{workspace}:{layer_name}'"
                        )
                        return True
                    else:
                        logger.error(f"无法确认图层是否成功创建")
                        return False

                except Exception as layer_e:
                    logger.error(f"配置图层时出错: {str(layer_e)}")
                    return False
            else:
                logger.error(
                    f"创建数据存储失败: {response.status_code}, {response.text}"
                )
                return False

        except Exception as e:
            logger.error(f"发布Shapefile '{shapefile_path}' 失败: {str(e)}")
            return False

    def list_layers(self, workspace=None):
        """
        列出GeoServer中的所有图层

        Args:
            workspace: 可选，限制为特定工作区的图层

        Returns:
            图层列表
        """
        try:
            import requests
            from requests.auth import HTTPBasicAuth

            base_url = self.geo.service_url
            if not base_url.endswith("/rest"):
                base_url = f"{base_url}/rest"

            # 构造URL
            if workspace:
                url = f"{base_url}/workspaces/{workspace}/layers"
            else:
                url = f"{base_url}/layers"

            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            headers = {"Accept": "application/json"}

            response = requests.get(url, auth=auth, headers=headers)

            if response.status_code == 200:
                try:
                    layers_data = response.json()
                    layers = []

                    if "layers" in layers_data and "layer" in layers_data["layers"]:
                        for layer in layers_data["layers"]["layer"]:
                            layers.append(layer.get("name"))

                    if workspace:
                        logger.info(
                            f"在工作区 '{workspace}' 中找到 {len(layers)} 个图层"
                        )
                    else:
                        logger.info(f"在GeoServer中找到 {len(layers)} 个图层")

                    return layers
                except Exception as e:
                    logger.error(f"解析图层列表时出错: {str(e)}")
                    return []
            else:
                logger.error(f"获取图层列表失败，状态码: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"列出图层时出错: {str(e)}")
            return []

    def publish_shapefile_directory(
        self,
        directory_path,
        workspace=DEFAULT_WORKSPACE,
        store_name=None,
        charset="UTF-8",
    ):
        """
        发布目录中的所有Shapefile文件到GeoServer

        Args:
            directory_path: Shapefile文件所在的目录路径
            workspace: 工作区名称，默认使用DEFAULT_WORKSPACE
            store_name: 存储名称前缀，如果提供则使用此名称，默认使用目录名称
            charset: DBF文件字符集，默认为UTF-8以支持中文

        Returns:
            成功和失败的Shapefile文件列表的字典
        """
        if not os.path.exists(directory_path) or not os.path.isdir(directory_path):
            logger.error(f"目录不存在或不是有效目录：{directory_path}")
            return {"success": [], "failed": []}

        # 获取目录中所有的.shp文件
        shapefile_paths = glob.glob(os.path.join(directory_path, "*.shp"))

        if not shapefile_paths:
            logger.warning(f"目录中没有找到Shapefile文件：{directory_path}")
            return {"success": [], "failed": []}

        logger.info(
            f"在目录 '{directory_path}' 中找到 {len(shapefile_paths)} 个Shapefile文件"
        )
        logger.info(f"使用字符集: {charset}")

        # 使用目录名称作为存储库名称前缀
        if store_name is None:
            # 提取目录路径的最后一个部分作为存储前缀
            store_prefix = os.path.basename(os.path.normpath(directory_path))
            logger.info(f"未提供存储名称，使用目录名称作为前缀: {store_prefix}")
        else:
            store_prefix = store_name
            logger.info(f"使用提供的存储名称作为前缀: {store_prefix}")

        # 检查工作区是否存在，如果不存在则创建
        if not self.check_workspace_exists(workspace):
            logger.info(f"工作区 '{workspace}' 不存在，正在创建...")
            self.geo.create_workspace(workspace)
            if not self.check_workspace_exists(workspace):
                logger.error(f"工作区 '{workspace}' 创建失败")
                return {"success": [], "failed": shapefile_paths}
            logger.info(f"成功创建工作区 '{workspace}'")

        # 获取发布前的图层列表，用于后续验证
        before_layers = set(self.list_layers(workspace))
        logger.info(f"发布前工作区 '{workspace}' 中有 {len(before_layers)} 个图层")

        success_files = []
        failed_files = []

        for shapefile_path in shapefile_paths:
            filename = os.path.basename(shapefile_path)
            base_name = os.path.splitext(filename)[0]

            # 使用文件名作为存储名称和图层名称，确保唯一性
            current_store_name = store_prefix  # 使用目录名作为存储名称

            logger.info(f"正在发布Shapefile: {filename}, 存储名: {current_store_name}")

            # 使用单文件发布方法发布
            result = self.publish_shapefile(
                shapefile_path=shapefile_path,
                workspace=workspace,
                store_name=current_store_name,
                layer_name=base_name,
                charset=charset,
            )

            if result:
                success_files.append(filename)
            else:
                failed_files.append(filename)

        # 验证新发布的图层
        after_layers = set(self.list_layers(workspace))
        new_layers = after_layers - before_layers
        logger.info(f"发布后工作区 '{workspace}' 中有 {len(after_layers)} 个图层")
        logger.info(f"新增了 {len(new_layers)} 个图层: {', '.join(new_layers)}")

        # 输出摘要信息
        if success_files:
            logger.info(f"成功发布了 {len(success_files)} 个Shapefile文件")
        if failed_files:
            logger.warning(f"有 {len(failed_files)} 个Shapefile文件发布失败")

        return {"success": success_files, "failed": failed_files}

    def publish_geotiff(
        self,
        geotiff_path,
        workspace=DEFAULT_WORKSPACE,
        store_name=None,
        layer_name=None,
    ):
        """
        发布GeoTIFF到GeoServer

        Args:
            geotiff_path: GeoTIFF文件路径
            workspace: 工作区名称
            store_name: 存储名称，如果不提供则使用文件名
            layer_name: 图层名称，如果不提供则使用存储名称

        Returns:
            是否发布成功
        """
        if not os.path.exists(geotiff_path):
            logger.error(f"未找到GeoTIFF文件：{geotiff_path}")
            return False

        # 从路径中提取文件名和基本名称
        filename = os.path.basename(geotiff_path)
        base_name = os.path.splitext(filename)[0]

        # 如果未提供store_name，则使用文件名
        if store_name is None:
            store_name = base_name
            logger.info(f"未提供存储名称，使用文件名: {store_name}")

        # 如果未提供layer_name，则使用store_name
        if layer_name is None:
            layer_name = store_name
            logger.info(f"未提供图层名称，使用存储名称: {layer_name}")

        # 检查工作区是否存在，如果不存在则创建
        if not self.check_workspace_exists(workspace):
            logger.info(f"工作区 '{workspace}' 不存在，正在创建...")
            self.geo.create_workspace(workspace)
            if not self.check_workspace_exists(workspace):
                logger.error(f"工作区 '{workspace}' 创建失败")
                return False
            logger.info(f"成功创建工作区 '{workspace}'")

        try:
            # 使用绝对路径以避免相对路径问题
            abs_path = os.path.abspath(geotiff_path)
            logger.info(f"使用绝对路径: {abs_path}")

            # 发布GeoTIFF
            # geoserver-rest库要求我们提供layer_name参数，但实际上它会使用此值作为存储名称
            # 对应于GeoServer UI中的"覆盖存储"(Coverage Store)名称
            self.geo.create_coveragestore(
                path=abs_path, workspace=workspace, layer_name=store_name
            )
            logger.info(f"发布GeoTIFF '{geotiff_path}' 到 '{workspace}:{store_name}'")

            # 检查是否需要创建一个不同名称的图层
            if layer_name != store_name:
                logger.info(f"图层名称与存储名称不同，尝试重命名图层...")
                # 尝试通过REST API重命名图层
                try:
                    import requests
                    from requests.auth import HTTPBasicAuth
                    import json

                    base_url = self.geo.service_url
                    if not base_url.endswith("/rest"):
                        base_url = f"{base_url}/rest"

                    # 获取当前图层信息
                    coverage_url = f"{base_url}/workspaces/{workspace}/coveragestores/{store_name}/coverages/{store_name}"

                    auth = HTTPBasicAuth(self.geo.username, self.geo.password)
                    headers = {
                        "Content-type": "application/json",
                        "Accept": "application/json",
                    }

                    # 更新图层配置
                    update_data = {
                        "coverage": {
                            "name": layer_name,
                            "title": layer_name,
                            "nativeName": store_name,
                        }
                    }

                    update_response = requests.put(
                        coverage_url,
                        auth=auth,
                        headers=headers,
                        data=json.dumps(update_data),
                    )

                    if (
                        update_response.status_code >= 200
                        and update_response.status_code < 300
                    ):
                        logger.info(
                            f"成功将图层名称从 '{store_name}' 更改为 '{layer_name}'"
                        )
                    else:
                        logger.warning(
                            f"更新图层名称失败: {update_response.status_code}, {update_response.text}"
                        )
                except Exception as layer_e:
                    logger.warning(f"重命名图层失败: {str(layer_e)}")

            # 验证图层是否成功创建
            if self.verify_layer(workspace, store_name, layer_name):
                logger.info(
                    f"GeoTIFF '{geotiff_path}' 成功发布为图层 '{workspace}:{layer_name}'"
                )
                return True
            else:
                # 图层可能以store_name命名
                if layer_name != store_name and self.verify_layer(
                    workspace, store_name, store_name
                ):
                    logger.info(
                        f"GeoTIFF '{geotiff_path}' 成功发布为图层 '{workspace}:{store_name}'"
                    )
                    return True
                else:
                    logger.error(f"无法验证图层是否成功创建")
                    return False
        except Exception as e:
            logger.error(f"发布GeoTIFF '{geotiff_path}' 失败: {str(e)}")
            return False

    def publish_postgis_layer(
        self,
        table_name,
        workspace=DEFAULT_WORKSPACE,
        store_name=DEFAULT_DATASTORE,
        layer_name=None,
        geometry_column="geom",
        srid=4326,
    ):
        """将PostGIS表发布为图层。"""
        layer_name = layer_name or table_name

        try:
            self.geo.publish_featurestore(
                store_name=store_name,
                workspace=workspace,
                pg_table=table_name,
                layer_name=layer_name,
                geometry_column=geometry_column,
                srid=srid,
            )
            logger.info(f"PostGIS表 '{table_name}' 成功发布为图层 '{layer_name}'")
            return True
        except Exception as e:
            logger.error(f"发布PostGIS表 '{table_name}' 失败：{str(e)}")
            return False

    def set_layer_style(self, layer_name, style_name, workspace=DEFAULT_WORKSPACE):
        """设置图层样式。"""
        try:
            self.geo.publish_style(
                layer_name=layer_name, style_name=style_name, workspace=workspace
            )
            logger.info(f"样式 '{style_name}' 已应用到图层 '{layer_name}'")
            return True
        except Exception as e:
            logger.error(
                f"将样式 '{style_name}' 应用到图层 '{layer_name}' 失败：{str(e)}"
            )
            return False

    def delete_layer(self, layer_name, workspace=DEFAULT_WORKSPACE):
        """从GeoServer删除图层。"""
        try:
            self.geo.delete_layer(layer_name=layer_name, workspace=workspace)
            logger.info(f"图层 '{layer_name}' 从工作区 '{workspace}' 中成功删除")
            return True
        except Exception as e:
            logger.error(f"删除图层 '{layer_name}' 失败：{str(e)}")
            return False

    def list_datastores(self, workspace):
        """
        列出工作区中的所有数据存储

        Args:
            workspace: 工作区名称

        Returns:
            数据存储列表
        """
        try:
            import requests
            from requests.auth import HTTPBasicAuth

            base_url = self.geo.service_url
            if not base_url.endswith("/rest"):
                base_url = f"{base_url}/rest"

            url = f"{base_url}/workspaces/{workspace}/datastores"

            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            headers = {"Accept": "application/json"}

            response = requests.get(url, auth=auth, headers=headers)

            if response.status_code == 200:
                try:
                    data = response.json()
                    datastores = []

                    if "dataStores" in data and "dataStore" in data["dataStores"]:
                        for store in data["dataStores"]["dataStore"]:
                            datastores.append(
                                {"name": store.get("name"), "href": store.get("href")}
                            )

                    logger.info(
                        f"在工作区 '{workspace}' 中找到 {len(datastores)} 个数据存储"
                    )
                    return datastores
                except Exception as e:
                    logger.error(f"解析数据存储列表时出错: {str(e)}")
                    return []
            else:
                logger.error(f"获取数据存储列表失败，状态码: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"列出数据存储时出错: {str(e)}")
            return []

    def diagnose(self, workspace=None, detail=False):
        """
        执行GeoServer连接和状态诊断

        Args:
            workspace: 要诊断的特定工作区，如不提供则诊断所有工作区
            detail: 是否显示详细诊断信息

        Returns:
            诊断结果字典
        """
        result = {
            "connection": False,
            "version": "Unknown",
            "workspaces": [],
            "total_layers": 0,
            "detail": {},
        }

        try:
            # 检查连接
            import requests
            from requests.auth import HTTPBasicAuth

            logger.info("开始GeoServer诊断...")

            base_url = self.geo.service_url
            if not base_url.endswith("/rest"):
                base_url = f"{base_url}/rest"

            # 尝试获取版本信息
            url = f"{base_url}/about/version.json"
            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            headers = {"Accept": "application/json"}

            logger.info(f"检查GeoServer连接: {base_url}")

            response = requests.get(url, auth=auth, headers=headers)

            if response.status_code == 200:
                result["connection"] = True
                try:
                    info = response.json()
                    result["version"] = (
                        info.get("about", {})
                        .get("resource", [{}])[0]
                        .get("Version", "Unknown")
                    )
                    logger.info(f"成功连接到GeoServer，版本: {result['version']}")
                except Exception:
                    logger.warning("无法解析GeoServer版本信息")
            else:
                logger.warning(f"连接GeoServer返回错误状态码: {response.status_code}")

            # 获取工作区
            try:
                workspaces_data = self.geo.get_workspaces()
                workspaces = []

                if workspaces_data and "workspaces" in workspaces_data:
                    for ws in workspaces_data.get("workspaces", {}).get(
                        "workspace", []
                    ):
                        workspaces.append(ws.get("name"))

                result["workspaces"] = workspaces
                logger.info(f"找到 {len(workspaces)} 个工作区: {', '.join(workspaces)}")

                # 如果指定了工作区，检查它是否存在
                if workspace and workspace not in workspaces:
                    logger.warning(f"指定的工作区 '{workspace}' 不存在")
                    return result

                # 对每个工作区进行诊断
                workspaces_to_check = [workspace] if workspace else workspaces

                all_layers = []

                for ws in workspaces_to_check:
                    ws_layers = self.list_layers(ws)
                    all_layers.extend(ws_layers)

                    if detail:
                        # 获取数据存储
                        datastores = self.list_datastores(ws)

                        result["detail"][ws] = {
                            "layers": ws_layers,
                            "datastores": [ds["name"] for ds in datastores],
                        }

                        logger.info(
                            f"工作区 '{ws}' 中有 {len(ws_layers)} 个图层，{len(datastores)} 个数据存储"
                        )
                        if detail and ws_layers:
                            logger.info(f"图层: {', '.join(ws_layers)}")
                        if detail and datastores:
                            logger.info(
                                f"数据存储: {', '.join([ds['name'] for ds in datastores])}"
                            )

                result["total_layers"] = len(all_layers)
                logger.info(f"GeoServer中共有 {result['total_layers']} 个图层")

            except Exception as e:
                logger.error(f"获取工作区列表失败: {str(e)}")

            return result

        except Exception as e:
            logger.error(f"诊断过程出错: {str(e)}")
            return result

    def publish_geotiff_directory(
        self, directory_path, workspace=DEFAULT_WORKSPACE, store_name=None
    ):
        """
        发布目录中的所有GeoTIFF文件到GeoServer

        Args:
            directory_path: GeoTIFF文件所在的目录路径
            workspace: 工作区名称，默认使用DEFAULT_WORKSPACE
            store_name: 存储名称前缀，如果提供则使用此名称，默认使用目录名称

        Returns:
            成功和失败的GeoTIFF文件列表的字典
        """
        if not os.path.exists(directory_path) or not os.path.isdir(directory_path):
            logger.error(f"目录不存在或不是有效目录：{directory_path}")
            return {"success": [], "failed": []}

        # 获取目录中所有的GeoTIFF文件(.tif, .tiff)
        geotiff_paths = []
        for ext in [".tif", ".tiff"]:
            geotiff_paths.extend(glob.glob(os.path.join(directory_path, f"*{ext}")))

        if not geotiff_paths:
            logger.warning(f"目录中没有找到GeoTIFF文件：{directory_path}")
            return {"success": [], "failed": []}

        logger.info(
            f"在目录 '{directory_path}' 中找到 {len(geotiff_paths)} 个GeoTIFF文件"
        )

        # 使用目录名称作为存储库名称前缀
        if store_name is None:
            # 提取目录路径的最后一个部分作为存储前缀
            store_prefix = os.path.basename(os.path.normpath(directory_path))
            logger.info(f"未提供存储名称，使用目录名称作为前缀: {store_prefix}")
        else:
            store_prefix = store_name
            logger.info(f"使用提供的存储名称作为前缀: {store_prefix}")

        # 检查工作区是否存在，如果不存在则创建
        if not self.check_workspace_exists(workspace):
            logger.info(f"工作区 '{workspace}' 不存在，正在创建...")
            self.geo.create_workspace(workspace)
            if not self.check_workspace_exists(workspace):
                logger.error(f"工作区 '{workspace}' 创建失败")
                return {"success": [], "failed": geotiff_paths}
            logger.info(f"成功创建工作区 '{workspace}'")

        # 获取发布前的图层列表，用于后续验证
        before_layers = set(self.list_layers(workspace))
        logger.info(f"发布前工作区 '{workspace}' 中有 {len(before_layers)} 个图层")

        success_files = []
        failed_files = []

        for geotiff_path in geotiff_paths:
            filename = os.path.basename(geotiff_path)
            base_name = os.path.splitext(filename)[0]

            # 使用文件名作为存储名称和图层名称
            current_store_name = f"{store_prefix}_{base_name}"

            logger.info(f"正在发布GeoTIFF: {filename}, 存储名: {current_store_name}")

            # 使用单文件发布方法发布
            result = self.publish_geotiff(
                geotiff_path=geotiff_path,
                workspace=workspace,
                store_name=current_store_name,
                layer_name=base_name,
            )

            if result:
                success_files.append(filename)
            else:
                failed_files.append(filename)

        # 验证新发布的图层
        after_layers = set(self.list_layers(workspace))
        new_layers = after_layers - before_layers
        logger.info(f"发布后工作区 '{workspace}' 中有 {len(after_layers)} 个图层")
        logger.info(f"新增了 {len(new_layers)} 个图层: {', '.join(new_layers)}")

        # 输出摘要信息
        if success_files:
            logger.info(f"成功发布了 {len(success_files)} 个GeoTIFF文件")
        if failed_files:
            logger.warning(f"有 {len(failed_files)} 个GeoTIFF文件发布失败")

        return {"success": success_files, "failed": failed_files}
