# GeoServer异步发布API使用说明

## 概述

GeoServer异步发布API提供了对GeoServer图层的异步发布功能，包括：

1. 发布Shapefile文件到GeoServer
2. 发布GeoTIFF文件到GeoServer
3. 发布Shapefile目录到GeoServer
4. 发布GeoTIFF目录到GeoServer
5. 发布特定结构的GeoTIFF文件到GeoServer（适用于无人机数据处理）

API采用异步处理模式：
- API会立即返回任务ID，然后在后台执行发布操作
- 通过任务ID可以查询发布任务的状态和结果

## API端点

### 1. 异步发布Shapefile文件

```
GET /api/geo/shapefile/execute?path=Shapefile文件路径&workspace=工作区名称&其他参数...
```

异步发布单个Shapefile文件到GeoServer。

**必选参数:**
- `path`: Shapefile文件路径
- `workspace`: 工作区名称

**可选参数:**
- `store_name`: 存储名称
- `layer_name`: 图层名称
- `charset`: 字符集，默认UTF-8

**示例:**
```
GET /api/geo/shapefile/execute?path=D:/data/boundary.shp&workspace=test&store_name=shapefiles&layer_name=boundary
```

**响应:**
```json
{
  "status": "success",
  "message": "Shapefile发布任务已启动",
  "task_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### 2. 异步发布GeoTIFF文件

```
GET /api/geo/geotiff/execute?path=GeoTIFF文件路径&workspace=工作区名称&其他参数...
```

异步发布单个GeoTIFF文件到GeoServer。

**必选参数:**
- `path`: GeoTIFF文件路径
- `workspace`: 工作区名称

**可选参数:**
- `store_name`: 存储名称
- `layer_name`: 图层名称

**示例:**
```
GET /api/geo/geotiff/execute?path=D:/data/orthophoto.tif&workspace=test&store_name=geotiffs&layer_name=ortho

127.0.0.1:5083/api/geo/geotiff/execute?path=D:/Drone_Project/dataset/nanning.tif&workspace=tttt

http://127.0.0.1:5083/api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=tttt
```

**响应:**
```json
{
  "status": "success",
  "message": "GeoTIFF发布任务已启动",
  "task_id": "660e8400-e29b-41d4-a716-446655440000"
}
```

### 3. 异步发布Shapefile目录

```
GET /api/geo/shapefile-directory/execute?path=Shapefile目录路径&workspace=工作区名称&其他参数...
```

异步发布整个目录中的Shapefile文件到GeoServer。

**必选参数:**
- `path`: 包含Shapefile文件的目录路径
- `workspace`: 工作区名称

**可选参数:**
- `store_name`: 存储名称
- `charset`: 字符集，默认UTF-8

**示例:**
```
GET /api/geo/shapefile-directory/execute?path=D:/data/shapefiles&workspace=test&store_name=shp_collection
```

**响应:**
```json
{
  "status": "success",
  "message": "Shapefile目录发布任务已启动",
  "task_id": "770e8400-e29b-41d4-a716-446655440000"
}
```

### 4. 异步发布GeoTIFF目录

```
GET /api/geo/geotiff-directory/execute?path=GeoTIFF目录路径&workspace=工作区名称&其他参数...
```

异步发布整个目录中的GeoTIFF文件到GeoServer。

**必选参数:**
- `path`: 包含GeoTIFF文件的目录路径
- `workspace`: 工作区名称

**可选参数:**
- `store_name`: 存储名称

**示例:**
```
GET /api/geo/geotiff-directory/execute?path=D:/data/geotiffs&workspace=test&store_name=tif_collection
```

**响应:**
```json
{
  "status": "success",
  "message": "GeoTIFF目录发布任务已启动",
  "task_id": "880e8400-e29b-41d4-a716-446655440000"
}
```

### 5. 异步发布特定结构的GeoTIFF文件

```
GET /api/geo/structured-geotiff/execute?root_directory=根目录路径&workspace=工作区名称&其他参数...
```

异步发布特定结构的GeoTIFF文件到GeoServer。该接口会扫描根目录下的子目录，发布每个子目录中与子目录同名的GeoTIFF文件。

**文件结构要求:**
- 根目录下包含多个子目录
- 每个子目录中包含与子目录同名的GeoTIFF文件
- 例如：`D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif`

**必选参数:**
- `root_directory`: 根目录路径
- `workspace`: 工作区名称

**可选参数:**
- `store_name`: 存储名称前缀

**示例:**
```
GET /api/geo/structured-geotiff/execute?root_directory=D:/Drone_Project/nginxData/ODM/Output&workspace=testodm&store_name=odm_data
```

**响应:**
```json
{
  "status": "success",
  "message": "特定结构GeoTIFF发布任务已启动",
  "task_id": "990e8400-e29b-41d4-a716-446655440000"
}
```

**工作原理:**
1. 扫描根目录 `D:/Drone_Project/nginxData/ODM/Output`
2. 找到子目录如 `20250705171600`, `20250705171601` 等
3. 在每个子目录中查找同名的GeoTIFF文件（如 `20250705171600.tif`）
4. 将找到的文件发布到GeoServer，图层名称为子目录名称
5. 如果指定了store_name，存储名称为 `{store_name}_{子目录名}`

### 6. 查询发布任务状态

```
GET /api/geo/status?task_id=任务ID

127.0.0.1:5083/api/geo/status?task_id=b84f98a5-2fa7-4f28-8c75-a9bea5862fb3
```

查询特定任务ID对应的发布任务状态。可用于跟踪异步任务的进度。

**必选参数:**
- `task_id`: 执行发布任务时返回的任务ID

**示例:**
```
GET /api/geo/status?task_id=550e8400-e29b-41d4-a716-446655440000
```

**响应:**
```json
{
  "status": "success",
  "task": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "type": "publish_shapefile",
    "parameters": {
      "shapefile_path": "D:/data/boundary.shp",
      "workspace": "test",
      "store_name": "shapefiles",
      "layer_name": "boundary",
      "charset": "UTF-8"
    },
    "status": "发布成功",
    "start_time": "2025-07-18 15:30:00",
    "end_time": "2025-07-18 15:30:05",
    "log_file": "D:/Drone_Project/geoserverapi/geoserverRest/geolog/550e8400-e29b-41d4-a716-446655440000.log",
    "result": true
  }
}
```

### 7. 查询所有发布任务状态

```
GET /api/geo/all
```

查询所有发布任务的状态。可用于监控所有任务的执行情况。

**示例:**
```
GET /api/geo/all
```

**响应:**
```json
{
  "status": "success",
  "count": 2,
  "tasks": [
    {
      "task_id": "550e8400-e29b-41d4-a716-446655440000",
      "type": "publish_shapefile",
      "parameters": {
        "shapefile_path": "D:/data/boundary.shp",
        "workspace": "test",
        "store_name": "shapefiles",
        "layer_name": "boundary",
        "charset": "UTF-8"
      },
      "status": "发布成功",
      "start_time": "2025-07-18 15:30:00",
      "end_time": "2025-07-18 15:30:05",
      "log_file": "D:/Drone_Project/geoserverapi/geoserverRest/geolog/550e8400-e29b-41d4-a716-446655440000.log",
      "result": true
    },
    {
      "task_id": "660e8400-e29b-41d4-a716-446655440000",
      "type": "publish_geotiff",
      "parameters": {
        "geotiff_path": "D:/data/orthophoto.tif",
        "workspace": "test",
        "store_name": "geotiffs",
        "layer_name": "ortho"
      },
      "status": "正在运行",
      "start_time": "2025-07-18 15:35:00",
      "end_time": null,
      "log_file": "D:/Drone_Project/geoserverapi/geoserverRest/geolog/660e8400-e29b-41d4-a716-446655440000.log"
    }
  ]
}
```

## 异步发布工作流程示例

1. **提交发布任务**：
   ```
   GET /api/geo/geotiff/execute?path=D:/data/orthophoto.tif&workspace=test&store_name=geotiffs&layer_name=ortho
   ```
   返回任务ID：`660e8400-e29b-41d4-a716-446655440000`

2. **轮询任务状态**：
   ```
   GET /api/geo/status?task_id=660e8400-e29b-41d4-a716-446655440000
   ```
   检查任务是否完成

3. **验证发布结果**：
   当任务状态变为"发布成功"时，可以通过GeoServer管理API或GeoServer管理界面验证图层是否发布成功
   
4. **查看任务日志**：
   任务日志保存在响应中提供的`log_file`路径，可用于调试或了解发布详情

## 常见问题

1. **任务超时**：对于大型文件或大量文件的目录发布，任务可能需要较长时间执行，请耐心等待并通过状态API查询进度

2. **发布失败**：如果发布失败，查看任务日志了解具体错误原因，常见原因包括：
   - 文件格式不正确
   - 工作区不存在
   - 数据存储问题
   - GeoServer权限问题

3. **找不到任务**：确保使用正确的task_id查询任务状态

4. **文件路径问题**：确保提供的文件路径正确且GeoServer服务器有权限访问 