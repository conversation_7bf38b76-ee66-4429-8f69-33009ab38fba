'''
Author: 吴博文 <EMAIL>
Date: 2025-07-01 10:15:27
LastEditors: 吴博文 <EMAIL>
LastEditTime: 2025-07-01 10:18:05
FilePath: \geoserverapi\config.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import os

# 尝试导入dotenv，如果不存在则跳过
try:
    from dotenv import load_dotenv
    # 从.env文件加载环境变量（如果存在）
    load_dotenv()
    print("成功加载.env文件")
except ImportError:
    print("警告：未找到python-dotenv模块，将使用默认配置")

# 定义一个函数来替代os.getenv，以便在没有环境变量时提供默认值
def get_env(key, default=None):
    # 尝试从.env文件中读取（如果文件存在且未使用dotenv模块）
    env_value = os.getenv(key)
    if env_value is None:
        env_path = os.path.join(os.path.dirname(__file__), '.env')
        if os.path.exists(env_path):
            try:
                with open(env_path, 'r') as env_file:
                    for line in env_file:
                        line = line.strip()
                        # 跳过注释行和空行
                        if line.startswith('#') or not line:
                            continue
                        if '=' in line:
                            env_key, env_value = line.split('=', 1)
                            if env_key == key:
                                return env_value
            except Exception:
                pass
        return default
    return env_value

# GeoServer连接设置
GEOSERVER_URL = get_env("GEOSERVER_URL", "http://localhost:8083/geoserver")
GEOSERVER_USER = get_env("GEOSERVER_USER", "admin")
GEOSERVER_PASSWORD = get_env("GEOSERVER_PASSWORD", "geoserver")

# 默认工作区和数据存储设置
DEFAULT_WORKSPACE = get_env("DEFAULT_WORKSPACE", "default_workspace")
DEFAULT_DATASTORE = get_env("DEFAULT_DATASTORE", "default_datastore")

# 日志设置
LOG_LEVEL = get_env("LOG_LEVEL", "INFO")
LOG_FILE = get_env("LOG_FILE", "geoserver_api.log") 