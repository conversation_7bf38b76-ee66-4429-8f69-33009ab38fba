#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批处理视图 - 批处理执行相关功能
"""

import os
import logging
from django.http import JsonResponse

# 导入核心模块
from ..core.batch_executor import executor as batch_executor

# 获取日志记录器
logger = logging.getLogger('geoserver_api')


def execute_batch(request):
    """
    执行批处理文件
    
    查询参数:
        batch_path: 批处理文件路径 (可选，默认: D:\Drone_Project\ODM\ODM\run.bat)
        project_path: 项目路径 (可选，默认: c://user/20250714140500/project)
        其他参数: 传递给批处理文件的参数
    """
    try:
        batch_path = request.GET.get('batch_path', 'D:\\Drone_Project\\ODM\\ODM\\run.bat')
        
        if not os.path.exists(batch_path):
            return JsonResponse({
                'status': 'error',
                'message': f'批处理文件不存在: {batch_path}'
            }, status=404)
        
        # 收集所有其他参数
        args = {}
        for key, value in request.GET.items():
            if key != 'batch_path' and value:
                args[key] = value
                
        # 如果没有提供任何参数，使用默认参数
        if not args:
            args = {
                'project_path': 'c://user/20250714140500/project',
                'fast-orthophoto': ''
            }
        
        # 执行批处理文件
        task_id = batch_executor.execute_batch(batch_path, args)
        
        return JsonResponse({
            "status": "success",
            "message": "批处理文件执行已启动",
            "task_id": task_id
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_batch_status(request):
    """
    获取批处理任务状态

    查询参数:
        task_id: 任务ID
    """
    try:
        task_id = request.GET.get('task_id')
        if not task_id:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: task_id'
            }, status=400)

        task_status = batch_executor.get_task_status(task_id)
        if not task_status:
            return JsonResponse({
                "status": "error",
                "message": f"未找到任务: {task_id}"
            }, status=404)

        return JsonResponse({
            "status": "success",
            "task": task_status
        })
        
    except Exception as e:
        batch_executor.logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_batch_log(request):
    """
    获取批处理任务日志

    查询参数:
        task_id: 任务ID
    """
    try:
        task_id = request.GET.get('task_id')
        if not task_id:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: task_id'
            }, status=400)

        log_content = batch_executor.get_task_log(task_id)
        if log_content is None:
            return JsonResponse({
                'status': 'error',
                'message': f'任务 {task_id} 的日志不存在'
            }, status=404)
        
        return JsonResponse({
            'status': 'success',
            'task_id': task_id,
            'log': log_content
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_all_batch_status(request):
    """
    获取所有批处理任务状态
    """
    try:
        tasks = batch_executor.get_all_tasks()
        return JsonResponse({
            "status": "success",
            "count": len(tasks),
            "tasks": tasks
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)
