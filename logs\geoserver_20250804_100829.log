2025-08-04 10:08:29,509 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250804_100829.log
2025-08-04 10:08:29,511 - geo_publisher - INFO - 加载了 33 个任务状态
2025-08-04 10:08:29,594 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-04 10:08:29,594 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-04 10:08:29,605 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-04 10:08:29,610 - root - INFO - === GeoServer REST API服务 ===
2025-08-04 10:08:29,610 - root - INFO - 主机: 0.0.0.0
2025-08-04 10:08:29,610 - root - INFO - 端口: 5083
2025-08-04 10:08:29,637 - root - INFO - 调试模式: 禁用
2025-08-04 10:08:29,639 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-08-04 10:08:29,655 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-08-04 10:08:29,656 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-04 10:09:11,725 - geo_publisher - INFO - 启动特定结构GeoTIFF发布任务 064b5c26-c1bf-4d01-a62b-5fedae28c716: D:/Drone_Project/nginxData/ODM/Output, workspace=testodm
2025-08-04 10:09:11,731 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 10:09:11] "GET /api/geo/structured-geotiff/execute?root_directory=D:/Drone_Project/nginxData/ODM/Output&workspace=testodm HTTP/1.1" 200 -
2025-08-04 10:09:11,733 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - GeoServer发布任务 064b5c26-c1bf-4d01-a62b-5fedae28c716 开始执行
2025-08-04 10:09:11,734 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 开始时间: 2025-08-04 10:09:11
2025-08-04 10:09:11,737 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 参数: {
  "root_directory": "D:/Drone_Project/nginxData/ODM/Output",
  "workspace": "testodm",
  "store_name": null
}
2025-08-04 10:09:11,745 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-04 10:09:11,749 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-04 10:09:11,768 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-04 10:09:11,770 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 开始扫描根目录: D:/Drone_Project/nginxData/ODM/Output
2025-08-04 10:09:11,773 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 找到 3 个子目录: ['20250705171600', '20250705171601', '20250705171602']
2025-08-04 10:09:11,776 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 找到GeoTIFF文件: D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif
2025-08-04 10:09:11,997 - root - WARNING - 在所有图层列表中未找到图层: testodm:20250705171600
2025-08-04 10:09:11,999 - root - WARNING - 图层 'testodm:20250705171600' 不存在或无法访问
2025-08-04 10:09:12,006 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 开始发布 D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif 到工作区 testodm
2025-08-04 10:09:12,032 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171600\20250705171600.tif
2025-08-04 10:13:35,128 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif' 到 'testodm:20250705171600'
2025-08-04 10:13:35,215 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171600
2025-08-04 10:13:35,216 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif' 成功发布为图层 'testodm:20250705171600'
2025-08-04 10:13:35,221 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 成功发布 D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif 为图层 testodm:20250705171600
2025-08-04 10:13:35,252 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 找到GeoTIFF文件: D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif
2025-08-04 10:13:35,367 - root - WARNING - 在所有图层列表中未找到图层: testodm:20250705171601
2025-08-04 10:13:35,367 - root - WARNING - 图层 'testodm:20250705171601' 不存在或无法访问
2025-08-04 10:13:35,371 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 开始发布 D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif 到工作区 testodm
2025-08-04 10:13:35,391 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601.tif
2025-08-04 10:13:37,859 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif' 到 'testodm:20250705171601'
2025-08-04 10:13:37,918 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171601
2025-08-04 10:13:37,919 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif' 成功发布为图层 'testodm:20250705171601'
2025-08-04 10:13:37,927 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 成功发布 D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif 为图层 testodm:20250705171601
2025-08-04 10:13:37,946 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 找到GeoTIFF文件: D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif
2025-08-04 10:13:38,076 - root - WARNING - 在所有图层列表中未找到图层: testodm:20250705171602
2025-08-04 10:13:38,076 - root - WARNING - 图层 'testodm:20250705171602' 不存在或无法访问
2025-08-04 10:13:38,080 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 开始发布 D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif 到工作区 testodm
2025-08-04 10:13:38,096 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171602\20250705171602.tif
2025-08-04 10:13:40,542 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif' 到 'testodm:20250705171602'
2025-08-04 10:13:40,602 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171602
2025-08-04 10:13:40,603 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif' 成功发布为图层 'testodm:20250705171602'
2025-08-04 10:13:40,616 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 成功发布 D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif 为图层 testodm:20250705171602
2025-08-04 10:13:40,619 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 发布完成 - 成功: 3, 失败: 0, 跳过: 0
2025-08-04 10:13:40,634 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - GeoServer发布任务 064b5c26-c1bf-4d01-a62b-5fedae28c716 执行成功
2025-08-04 10:13:40,635 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 完成时间: 2025-08-04 10:13:40
2025-08-04 10:13:40,635 - geo_task_064b5c26-c1bf-4d01-a62b-5fedae28c716 - INFO - 状态: 发布成功
2025-08-04 10:15:01,958 - geo_publisher - INFO - 启动特定结构GeoTIFF发布任务 5468daf0-2e0e-4686-9923-e412a135b1aa: D:/Drone_Project/nginxData/ODM/Output, workspace=testodm
2025-08-04 10:15:01,959 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - GeoServer发布任务 5468daf0-2e0e-4686-9923-e412a135b1aa 开始执行
2025-08-04 10:15:01,963 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 10:15:01] "GET /api/geo/structured-geotiff/execute?root_directory=D:/Drone_Project/nginxData/ODM/Output&workspace=testodm HTTP/1.1" 200 -
2025-08-04 10:15:01,964 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 开始时间: 2025-08-04 10:15:01
2025-08-04 10:15:01,966 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 参数: {
  "root_directory": "D:/Drone_Project/nginxData/ODM/Output",
  "workspace": "testodm",
  "store_name": null
}
2025-08-04 10:15:01,967 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-04 10:15:01,968 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-04 10:15:02,021 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-04 10:15:02,024 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 开始扫描根目录: D:/Drone_Project/nginxData/ODM/Output
2025-08-04 10:15:02,027 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 找到 3 个子目录: ['20250705171600', '20250705171601', '20250705171602']
2025-08-04 10:15:02,029 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 找到GeoTIFF文件: D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif
2025-08-04 10:15:02,088 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171600
2025-08-04 10:15:02,089 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 图层 'testodm:20250705171600' 已存在，正在删除...
2025-08-04 10:16:02,500 - root - INFO - 图层 '20250705171600' 从工作区 'testodm' 中成功删除
2025-08-04 10:16:02,501 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 已删除已存在的图层 'testodm:20250705171600'
2025-08-04 10:16:02,504 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 开始发布 D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif 到工作区 testodm
2025-08-04 10:16:02,536 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171600\20250705171600.tif
2025-08-04 10:17:55,823 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif' 到 'testodm:20250705171600'
2025-08-04 10:17:55,870 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171600
2025-08-04 10:17:55,870 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif' 成功发布为图层 'testodm:20250705171600'
2025-08-04 10:17:55,876 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 成功发布 D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif 为图层 testodm:20250705171600
2025-08-04 10:17:55,886 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 找到GeoTIFF文件: D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif
2025-08-04 10:17:55,937 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171601
2025-08-04 10:17:55,937 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 图层 'testodm:20250705171601' 已存在，正在删除...
2025-08-04 10:18:55,985 - root - INFO - 图层 '20250705171601' 从工作区 'testodm' 中成功删除
2025-08-04 10:18:55,985 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 已删除已存在的图层 'testodm:20250705171601'
2025-08-04 10:18:55,988 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 开始发布 D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif 到工作区 testodm
2025-08-04 10:18:56,004 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601.tif
2025-08-04 10:18:58,452 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif' 到 'testodm:20250705171601'
2025-08-04 10:18:58,497 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171601
2025-08-04 10:18:58,497 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif' 成功发布为图层 'testodm:20250705171601'
2025-08-04 10:18:58,500 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 成功发布 D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif 为图层 testodm:20250705171601
2025-08-04 10:18:58,512 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 找到GeoTIFF文件: D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif
2025-08-04 10:18:58,556 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171602
2025-08-04 10:18:58,557 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 图层 'testodm:20250705171602' 已存在，正在删除...
2025-08-04 10:19:58,609 - root - INFO - 图层 '20250705171602' 从工作区 'testodm' 中成功删除
2025-08-04 10:19:58,609 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 已删除已存在的图层 'testodm:20250705171602'
2025-08-04 10:19:58,612 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 开始发布 D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif 到工作区 testodm
2025-08-04 10:19:58,628 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171602\20250705171602.tif
2025-08-04 10:20:01,321 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif' 到 'testodm:20250705171602'
2025-08-04 10:20:01,369 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171602
2025-08-04 10:20:01,370 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif' 成功发布为图层 'testodm:20250705171602'
2025-08-04 10:20:01,373 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 成功发布 D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif 为图层 testodm:20250705171602
2025-08-04 10:20:01,383 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 发布完成 - 成功: 3, 失败: 0, 跳过: 0
2025-08-04 10:20:01,386 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - GeoServer发布任务 5468daf0-2e0e-4686-9923-e412a135b1aa 执行成功
2025-08-04 10:20:01,387 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 完成时间: 2025-08-04 10:20:01
2025-08-04 10:20:01,387 - geo_task_5468daf0-2e0e-4686-9923-e412a135b1aa - INFO - 状态: 发布成功
2025-08-04 10:31:25,210 - geo_publisher - INFO - 启动特定结构GeoTIFF发布任务 18ab6896-04b6-4c2f-b9d9-3d515b582c9e: D:/Drone_Project/nginxData/ODM/Output, workspace=testodm
2025-08-04 10:31:25,218 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 10:31:25] "GET /api/geo/structured-geotiff/execute?root_directory=D:/Drone_Project/nginxData/ODM/Output&workspace=testodm HTTP/1.1" 200 -
2025-08-04 10:31:25,536 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - GeoServer发布任务 18ab6896-04b6-4c2f-b9d9-3d515b582c9e 开始执行
2025-08-04 10:31:25,537 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 开始时间: 2025-08-04 10:31:25
2025-08-04 10:31:25,540 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 参数: {
  "root_directory": "D:/Drone_Project/nginxData/ODM/Output",
  "workspace": "testodm",
  "store_name": null
}
2025-08-04 10:31:25,541 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-04 10:31:25,542 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-04 10:31:25,583 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-04 10:31:25,584 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 开始扫描根目录: D:/Drone_Project/nginxData/ODM/Output
2025-08-04 10:31:25,587 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 找到 3 个子目录: ['20250705171600', '20250705171601', '20250705171602']
2025-08-04 10:31:25,589 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 找到GeoTIFF文件: D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif
2025-08-04 10:31:25,676 - root - WARNING - 在所有图层列表中未找到图层: testodm:20250705171600
2025-08-04 10:31:25,677 - root - WARNING - 图层 'testodm:20250705171600' 不存在或无法访问
2025-08-04 10:31:25,681 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 开始发布 D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif 到工作区 testodm
2025-08-04 10:31:25,698 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171600\20250705171600.tif
2025-08-04 10:32:16,377 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 10:32:16] "GET /api/geo/status?task_id=18ab6896-04b6-4c2f-b9d9-3d515b582c9e HTTP/1.1" 200 -
2025-08-04 10:32:34,872 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 10:32:34] "GET /api/geo/status?task_id=18ab6896-04b6-4c2f-b9d9-3d515b582c9e HTTP/1.1" 200 -
2025-08-04 10:33:16,375 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif' 到 'testodm:20250705171600'
2025-08-04 10:33:16,425 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171600
2025-08-04 10:33:16,426 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif' 成功发布为图层 'testodm:20250705171600'
2025-08-04 10:33:16,430 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 成功发布 D:/Drone_Project/nginxData/ODM/Output\20250705171600\20250705171600.tif 为图层 testodm:20250705171600
2025-08-04 10:33:16,433 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 找到GeoTIFF文件: D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif
2025-08-04 10:33:16,516 - root - WARNING - 在所有图层列表中未找到图层: testodm:20250705171601
2025-08-04 10:33:16,517 - root - WARNING - 图层 'testodm:20250705171601' 不存在或无法访问
2025-08-04 10:33:16,520 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 开始发布 D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif 到工作区 testodm
2025-08-04 10:33:16,542 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601.tif
2025-08-04 10:33:19,001 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif' 到 'testodm:20250705171601'
2025-08-04 10:33:19,054 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171601
2025-08-04 10:33:19,055 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif' 成功发布为图层 'testodm:20250705171601'
2025-08-04 10:33:19,061 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 成功发布 D:/Drone_Project/nginxData/ODM/Output\20250705171601\20250705171601.tif 为图层 testodm:20250705171601
2025-08-04 10:33:19,071 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 找到GeoTIFF文件: D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif
2025-08-04 10:33:19,175 - root - WARNING - 在所有图层列表中未找到图层: testodm:20250705171602
2025-08-04 10:33:19,176 - root - WARNING - 图层 'testodm:20250705171602' 不存在或无法访问
2025-08-04 10:33:19,182 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 开始发布 D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif 到工作区 testodm
2025-08-04 10:33:19,201 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171602\20250705171602.tif
2025-08-04 10:33:23,547 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif' 到 'testodm:20250705171602'
2025-08-04 10:33:23,596 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171602
2025-08-04 10:33:23,596 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif' 成功发布为图层 'testodm:20250705171602'
2025-08-04 10:33:23,598 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 成功发布 D:/Drone_Project/nginxData/ODM/Output\20250705171602\20250705171602.tif 为图层 testodm:20250705171602
2025-08-04 10:33:23,601 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 发布完成 - 成功: 3, 失败: 0, 跳过: 0
2025-08-04 10:33:23,604 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - GeoServer发布任务 18ab6896-04b6-4c2f-b9d9-3d515b582c9e 执行成功
2025-08-04 10:33:23,605 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 完成时间: 2025-08-04 10:33:23
2025-08-04 10:33:23,605 - geo_task_18ab6896-04b6-4c2f-b9d9-3d515b582c9e - INFO - 状态: 发布成功
2025-08-04 10:37:33,716 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 10:37:33] "GET /api/geo/status?task_id=18ab6896-04b6-4c2f-b9d9-3d515b582c9e HTTP/1.1" 200 -
