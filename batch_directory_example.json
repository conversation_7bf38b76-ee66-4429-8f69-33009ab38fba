[{"type": "create_workspace", "params": {"name": "batch_workspace"}}, {"type": "publish_shapefile_directory", "params": {"directory": "data/20250701/testshp", "workspace": "batch_workspace", "store": "testshp_store", "charset": "UTF-8"}}, {"type": "publish_shapefile_directory", "params": {"directory": "data/boundaries", "workspace": "test_workspace", "charset": "UTF-8"}}, {"type": "publish_geotiff", "params": {"file": "data/example_raster.tif", "workspace": "test_workspace", "store": "raster_store", "layer": "raster_layer"}}]