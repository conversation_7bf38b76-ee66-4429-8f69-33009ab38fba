2025-07-01 16:37:29,046 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250701_163729.log
2025-07-01 16:37:29,063 - root - INFO - 开始执行命令: publish-shapefile-directory
2025-07-01 16:37:29,064 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-01 16:37:29,064 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-01 16:37:29,114 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-01 16:37:29,116 - root - INFO - 在目录 'data/20250701/testshp' 中找到 5 个Shapefile文件
2025-07-01 16:37:29,117 - root - INFO - 使用字符集: UTF-8
2025-07-01 16:37:29,118 - root - INFO - 未提供存储名称，使用目录名称作为前缀: testshp
2025-07-01 16:37:29,132 - root - INFO - 工作区 'test_workspace' 不存在，正在创建...
2025-07-01 16:37:29,179 - root - INFO - 成功创建工作区 'test_workspace'
2025-07-01 16:37:29,192 - root - INFO - 在工作区 'test_workspace' 中找到 0 个图层
2025-07-01 16:37:29,192 - root - INFO - 发布前工作区 'test_workspace' 中有 0 个图层
2025-07-01 16:37:29,193 - root - INFO - 正在发布Shapefile: 招商引资片区.shp, 存储名: testshp
2025-07-01 16:37:29,206 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区.shp
2025-07-01 16:37:29,208 - root - INFO - 添加文件到ZIP: 招商引资片区.shp
2025-07-01 16:37:29,335 - root - INFO - 添加文件到ZIP: 招商引资片区.dbf
2025-07-01 16:37:29,337 - root - INFO - 添加文件到ZIP: 招商引资片区.shx
2025-07-01 16:37:29,338 - root - INFO - 添加文件到ZIP: 招商引资片区.prj
2025-07-01 16:37:29,339 - root - INFO - 添加文件到ZIP: 招商引资片区.qix
2025-07-01 16:37:29,340 - root - INFO - 添加文件到ZIP: 招商引资片区.cpg
2025-07-01 16:37:29,341 - root - INFO - 添加文件到ZIP: 招商引资片区.qmd
2025-07-01 16:37:29,341 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:37:29,342 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:37:29,476 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:37:29,566 - root - INFO - 成功创建图层 '招商引资片区'
2025-07-01 16:37:29,580 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/招商引资片区
2025-07-01 16:37:29,581 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区.shp' 成功发布为图层 'test_workspace:招商引资片区'
2025-07-01 16:37:29,582 - root - INFO - 正在发布Shapefile: 招商引资片区交种植区.shp, 存储名: testshp
2025-07-01 16:37:29,594 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区交种植区.shp
2025-07-01 16:37:29,595 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shp
2025-07-01 16:37:30,292 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.dbf
2025-07-01 16:37:30,470 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shx
2025-07-01 16:37:30,482 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.prj
2025-07-01 16:37:30,484 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qix
2025-07-01 16:37:30,497 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.cpg
2025-07-01 16:37:30,498 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qmd
2025-07-01 16:37:30,500 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:37:30,500 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:37:30,694 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:37:30,704 - root - INFO - 图层 '招商引资片区交种植区' 已存在
2025-07-01 16:37:30,720 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/招商引资片区交种植区
2025-07-01 16:37:30,721 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区交种植区.shp' 成功发布为图层 'test_workspace:招商引资片区交种植区'
2025-07-01 16:37:30,723 - root - INFO - 正在发布Shapefile: 招商引资片区交设施农用地.shp, 存储名: testshp
2025-07-01 16:37:30,735 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区交设施农用地.shp
2025-07-01 16:37:30,736 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shp
2025-07-01 16:37:30,883 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.dbf
2025-07-01 16:37:30,895 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shx
2025-07-01 16:37:30,897 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.prj
2025-07-01 16:37:30,898 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qix
2025-07-01 16:37:30,901 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.cpg
2025-07-01 16:37:30,902 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qmd
2025-07-01 16:37:30,903 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:37:30,904 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:37:31,026 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:37:31,110 - root - INFO - 成功创建图层 '招商引资片区交设施农用地'
2025-07-01 16:37:31,127 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/招商引资片区交设施农用地
2025-07-01 16:37:31,128 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区交设施农用地.shp' 成功发布为图层 'test_workspace:招商引资片区交设施农用地'
2025-07-01 16:37:31,129 - root - INFO - 正在发布Shapefile: 种植区.shp, 存储名: testshp
2025-07-01 16:37:31,147 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\种植区.shp
2025-07-01 16:37:31,148 - root - INFO - 添加文件到ZIP: 种植区.shp
2025-07-01 16:37:32,583 - root - INFO - 添加文件到ZIP: 种植区.dbf
2025-07-01 16:37:32,829 - root - INFO - 添加文件到ZIP: 种植区.shx
2025-07-01 16:37:32,853 - root - INFO - 添加文件到ZIP: 种植区.prj
2025-07-01 16:37:32,854 - root - INFO - 添加文件到ZIP: 种植区.qix
2025-07-01 16:37:32,879 - root - INFO - 添加文件到ZIP: 种植区.cpg
2025-07-01 16:37:32,880 - root - INFO - 添加文件到ZIP: 种植区.qmd
2025-07-01 16:37:32,881 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:37:32,881 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:37:33,208 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:37:33,286 - root - INFO - 成功创建图层 '种植区'
2025-07-01 16:37:33,305 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/种植区
2025-07-01 16:37:33,306 - root - INFO - Shapefile 'data/20250701/testshp\种植区.shp' 成功发布为图层 'test_workspace:种植区'
2025-07-01 16:37:33,308 - root - INFO - 正在发布Shapefile: 设施农用地潜力.shp, 存储名: testshp
2025-07-01 16:37:33,323 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\设施农用地潜力.shp
2025-07-01 16:37:33,324 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shp
2025-07-01 16:37:34,443 - root - INFO - 添加文件到ZIP: 设施农用地潜力.dbf
2025-07-01 16:37:34,505 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shx
2025-07-01 16:37:34,516 - root - INFO - 添加文件到ZIP: 设施农用地潜力.prj
2025-07-01 16:37:34,518 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qix
2025-07-01 16:37:34,528 - root - INFO - 添加文件到ZIP: 设施农用地潜力.cpg
2025-07-01 16:37:34,529 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qmd
2025-07-01 16:37:34,530 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:37:34,531 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:37:34,758 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:37:34,824 - root - INFO - 成功创建图层 '设施农用地潜力'
2025-07-01 16:37:34,840 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/设施农用地潜力
2025-07-01 16:37:34,841 - root - INFO - Shapefile 'data/20250701/testshp\设施农用地潜力.shp' 成功发布为图层 'test_workspace:设施农用地潜力'
2025-07-01 16:37:34,859 - root - INFO - 在工作区 'test_workspace' 中找到 5 个图层
2025-07-01 16:37:34,859 - root - INFO - 发布后工作区 'test_workspace' 中有 5 个图层
2025-07-01 16:37:34,860 - root - INFO - 新增了 5 个图层: 招商引资片区, 招商引资片区交设施农用地, 种植区, 招商引资片区交种植区, 设施农用地潜力
2025-07-01 16:37:34,860 - root - INFO - 成功发布了 5 个Shapefile文件
2025-07-01 16:37:34,860 - root - INFO - 成功发布: 5, 失败: 0
2025-07-01 16:37:34,861 - root - INFO - 命令执行完成: publish-shapefile-directory
