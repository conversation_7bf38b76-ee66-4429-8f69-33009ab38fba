2025-07-08 08:37:48,151 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250708_083748.log
2025-07-08 08:37:50,564 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-08 08:37:50,565 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-08 08:37:51,397 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-08 08:37:51,403 - geoserver_query_api - INFO - === GeoServer栅格数据坐标查询API服务 ===
2025-07-08 08:37:51,403 - geoserver_query_api - INFO - 主机: 0.0.0.0
2025-07-08 08:37:51,403 - geoserver_query_api - INFO - 端口: 5000
2025-07-08 08:37:51,404 - geoserver_query_api - INFO - 调试模式: 禁用
2025-07-08 08:37:51,417 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-08 08:37:51,417 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 08:41:05,184 - root - INFO - 开始查询坐标点 (22.80387101632344, 108.29668608733327) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-08 08:41:05,581 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 08:41:12,457 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 08:41:12,458 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 08:41:12,459 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 08:41:12,460 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 08:41:12,461 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 08:41:12,462 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 08:41:12,463 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 08:41:12,464 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 08:41:12,465 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 08:41:12,467 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 08:41:12,469 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 08:41:14,144 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-08 08:41:14,275 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-08 08:41:14,276 - root - INFO - 在坐标点 (22.80387101632344, 108.29668608733327) 处找到 2 个有有效数据的栅格图层
2025-07-08 08:41:14,277 - werkzeug - INFO - ************** - - [08/Jul/2025 08:41:14] "GET /api/query_values?lat=22.80387101632344&lon=108.29668608733327&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 08:41:26,312 - root - INFO - 开始查询坐标点 (22.809498663736846, 108.26382337023453) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-08 08:41:26,559 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 08:41:27,146 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 08:41:27,147 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 08:41:27,148 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 08:41:27,149 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 08:41:27,150 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 08:41:27,151 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 08:41:27,153 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 08:41:27,154 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 08:41:27,156 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 08:41:27,157 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 08:41:27,158 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 08:41:27,191 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-08 08:41:27,299 - root - INFO - 图层 'id2' 在该坐标点有有效数据，添加到结果中
2025-07-08 08:41:27,299 - root - INFO - 在坐标点 (22.809498663736846, 108.26382337023453) 处找到 2 个有有效数据的栅格图层
2025-07-08 08:41:27,301 - werkzeug - INFO - ************** - - [08/Jul/2025 08:41:27] "GET /api/query_values?lat=22.809498663736846&lon=108.26382337023453&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 08:41:31,654 - root - INFO - 开始查询坐标点 (22.799541898623843, 108.34175489918925) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-08 08:41:31,870 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 08:41:32,182 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 08:41:32,183 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 08:41:32,184 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 08:41:32,185 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 08:41:32,186 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 08:41:32,187 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 08:41:32,188 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 08:41:32,189 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 08:41:32,190 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 08:41:32,191 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 08:41:32,192 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 08:41:32,221 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-08 08:41:32,286 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-08 08:41:32,286 - root - INFO - 在坐标点 (22.799541898623843, 108.34175489918925) 处找到 2 个有有效数据的栅格图层
2025-07-08 08:41:32,288 - werkzeug - INFO - ************** - - [08/Jul/2025 08:41:32] "GET /api/query_values?lat=22.799541898623843&lon=108.34175489918925&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 08:41:34,565 - root - INFO - 开始查询坐标点 (22.699934229855614, 108.35630838819014) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-08 08:41:34,947 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 08:41:35,458 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 08:41:35,460 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 08:41:35,461 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 08:41:35,462 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 08:41:35,463 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 08:41:35,464 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 08:41:35,465 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 08:41:35,466 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 08:41:35,467 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 08:41:35,468 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 08:41:35,471 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 08:41:35,503 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-08 08:41:35,608 - root - INFO - 图层 'id6' 在该坐标点有有效数据，添加到结果中
2025-07-08 08:41:35,609 - root - INFO - 在坐标点 (22.699934229855614, 108.35630838819014) 处找到 2 个有有效数据的栅格图层
2025-07-08 08:41:35,611 - werkzeug - INFO - ************** - - [08/Jul/2025 08:41:35] "GET /api/query_values?lat=22.699934229855614&lon=108.35630838819014&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 08:41:38,049 - root - INFO - 开始查询坐标点 (22.701667209203947, 108.43658731138854) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-08 08:41:38,316 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 08:41:38,601 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 08:41:38,603 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 08:41:38,604 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 08:41:38,605 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 08:41:38,606 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 08:41:38,608 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 08:41:38,609 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 08:41:38,610 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 08:41:38,611 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 08:41:38,612 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 08:41:38,614 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 08:41:38,642 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-08 08:41:38,754 - root - INFO - 图层 'id9' 在该坐标点有有效数据，添加到结果中
2025-07-08 08:41:38,755 - root - INFO - 在坐标点 (22.701667209203947, 108.43658731138854) 处找到 2 个有有效数据的栅格图层
2025-07-08 08:41:38,756 - werkzeug - INFO - ************** - - [08/Jul/2025 08:41:38] "GET /api/query_values?lat=22.701667209203947&lon=108.43658731138854&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 08:41:41,726 - root - INFO - 开始查询坐标点 (22.882204255564886, 108.31452579073598) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-08 08:41:41,931 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 08:41:42,259 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 08:41:42,260 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 08:41:42,261 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 08:41:42,262 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 08:41:42,264 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 08:41:42,264 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 08:41:42,265 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 08:41:42,267 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 08:41:42,268 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 08:41:42,269 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 08:41:42,270 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 08:41:42,297 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-08 08:41:42,393 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-08 08:41:42,395 - root - INFO - 在坐标点 (22.882204255564886, 108.31452579073598) 处找到 2 个有有效数据的栅格图层
2025-07-08 08:41:42,396 - werkzeug - INFO - ************** - - [08/Jul/2025 08:41:42] "GET /api/query_values?lat=22.882204255564886&lon=108.31452579073598&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 08:54:46,957 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 08:54:46] "[33mGET /api/all_layers?workspace=test_myworkspace HTTP/1.1[0m" 404 -
2025-07-08 08:54:47,005 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 08:54:47] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
