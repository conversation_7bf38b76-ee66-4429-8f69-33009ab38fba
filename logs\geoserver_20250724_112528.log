2025-07-24 11:25:28,826 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250724_112528.log
2025-07-24 11:25:28,829 - geo_publisher - INFO - 加载了 19 个任务状态
2025-07-24 11:25:29,349 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-24 11:25:29,350 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-24 11:25:29,378 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-24 11:25:29,398 - root - INFO - === GeoServer REST API服务 ===
2025-07-24 11:25:29,399 - root - INFO - 主机: 0.0.0.0
2025-07-24 11:25:29,400 - root - INFO - 端口: 5083
2025-07-24 11:25:29,401 - root - INFO - 调试模式: 禁用
2025-07-24 11:25:29,402 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-24 11:25:29,445 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-24 11:25:29,450 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-24 11:27:03,102 - map_api - INFO - 开始获取ODM任务列表
2025-07-24 11:27:03,107 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-24 11:27:03,123 - map_api - INFO - 找到 3 个目录
2025-07-24 11:27:03,127 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-24 11:27:03,335 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-24 11:27:03,340 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-24 11:27:04,522 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2184 字节
2025-07-24 11:27:04,527 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-24 11:27:04,566 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2511 字节
2025-07-24 11:27:04,570 - map_api - INFO - 获取到 3 个任务信息
2025-07-24 11:27:04,572 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:27:04] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-24 11:27:04,635 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:27:04] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-24 11:28:57,045 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-24 11:28:57,116 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-24 11:28:57,119 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:28:57] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-24 11:29:04,853 - map_api - INFO - 请求文件: baseMap3.json
2025-07-24 11:29:04,871 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 3857 字节
2025-07-24 11:29:04,874 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:29:04] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-24 11:35:03,396 - batch_executor - INFO - 启动任务 5ba2fdc2-f850-470c-ba95-b3197e561b48: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171601/project
2025-07-24 11:35:03,397 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:35:03] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-24 11:35:03,423 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:35:03] "GET /api/batch/status?task_id=5ba2fdc2-f850-470c-ba95-b3197e561b48 HTTP/1.1" 200 -
2025-07-24 11:36:05,509 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:36:05] "GET /api/batch/status?task_id=5ba2fdc2-f850-470c-ba95-b3197e561b48 HTTP/1.1" 200 -
2025-07-24 11:36:06,776 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 11:36:06,778 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 11:36:06,779 - tif_api - INFO - 输入文件大小: 222.47 MB
2025-07-24 11:36:06,779 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 11:36:06,781 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:36:06,781 - tif_api - INFO - 黑色阈值: 0
2025-07-24 11:36:06,782 - tif_api - INFO - 白色阈值: 255
2025-07-24 11:36:06,783 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 11:36:06,786 - tif_executor - INFO - 启动TIF处理任务 27f22d80-d57e-4b97-a8ca-24ab14bfd094: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:36:06,787 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - INFO - ============ TIF处理任务 27f22d80-d57e-4b97-a8ca-24ab14bfd094 开始执行 ============
2025-07-24 11:36:06,787 - tif_api - INFO - 异步任务启动成功，任务ID: 27f22d80-d57e-4b97-a8ca-24ab14bfd094
2025-07-24 11:36:06,787 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - INFO - 开始时间: 2025-07-24 11:36:06
2025-07-24 11:36:06,788 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:36:06] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 11:36:06,788 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 11:36:06,793 - tif_executor - ERROR - 执行任务 27f22d80-d57e-4b97-a8ca-24ab14bfd094 时出错: No module named 'psutil'
2025-07-24 11:36:06,810 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 11:36:06,811 - tif_executor - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
    import psutil
ModuleNotFoundError: No module named 'psutil'

2025-07-24 11:36:06,817 - tif_api - INFO - 查询任务: 27f22d80-d57e-4b97-a8ca-24ab14bfd094
2025-07-24 11:36:06,821 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR - ============ 处理失败 ============
2025-07-24 11:36:06,821 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR - 错误类型: ModuleNotFoundError
2025-07-24 11:36:06,822 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR - 错误信息: No module named 'psutil'
2025-07-24 11:36:06,822 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR - 详细堆栈跟踪:
2025-07-24 11:36:06,831 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR - Traceback (most recent call last):
2025-07-24 11:36:06,837 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR -   File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
2025-07-24 11:36:06,838 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR -     import psutil
2025-07-24 11:36:06,839 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR - ModuleNotFoundError: No module named 'psutil'
2025-07-24 11:36:06,843 - tif_api - INFO - 任务 27f22d80-d57e-4b97-a8ca-24ab14bfd094 状态查询成功，当前状态: 运行失败
2025-07-24 11:36:06,848 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR - 系统诊断信息:
2025-07-24 11:36:06,853 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:36:06] "GET /api/tif/status?task_id=27f22d80-d57e-4b97-a8ca-24ab14bfd094 HTTP/1.1" 200 -
2025-07-24 11:36:06,854 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR - 输出目录磁盘空间: 总计 1050.91 GB, 可用 47.19 GB, 使用率 95.5%
2025-07-24 11:36:06,867 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR - 输入文件大小: 0.22 GB
2025-07-24 11:36:06,870 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR - 检查内存使用时出错: No module named 'psutil'
2025-07-24 11:36:06,879 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR - 完成时间: 2025-07-24 11:36:06
2025-07-24 11:36:06,881 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR - 状态: 运行失败
2025-07-24 11:36:06,886 - tif_task_27f22d80-d57e-4b97-a8ca-24ab14bfd094 - ERROR - ============ 任务异常结束 ============
2025-07-24 11:37:08,961 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 11:37:08,965 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 11:37:08,970 - tif_api - INFO - 输入文件大小: 222.47 MB
2025-07-24 11:37:08,973 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 11:37:08,975 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:37:08,976 - tif_api - INFO - 黑色阈值: 0
2025-07-24 11:37:08,977 - tif_api - INFO - 白色阈值: 255
2025-07-24 11:37:08,979 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 11:37:08,982 - tif_executor - INFO - 启动TIF处理任务 e4488b58-3968-4dc8-be93-dd5146cbf6fc: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:37:08,982 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - INFO - ============ TIF处理任务 e4488b58-3968-4dc8-be93-dd5146cbf6fc 开始执行 ============
2025-07-24 11:37:08,982 - tif_api - INFO - 异步任务启动成功，任务ID: e4488b58-3968-4dc8-be93-dd5146cbf6fc
2025-07-24 11:37:08,988 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - INFO - 开始时间: 2025-07-24 11:37:08
2025-07-24 11:37:09,002 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:37:09] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 11:37:09,011 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 11:37:09,019 - tif_executor - ERROR - 执行任务 e4488b58-3968-4dc8-be93-dd5146cbf6fc 时出错: No module named 'psutil'
2025-07-24 11:37:09,023 - tif_executor - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
    import psutil
ModuleNotFoundError: No module named 'psutil'

2025-07-24 11:37:09,026 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR - ============ 处理失败 ============
2025-07-24 11:37:09,031 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR - 错误类型: ModuleNotFoundError
2025-07-24 11:37:09,031 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR - 错误信息: No module named 'psutil'
2025-07-24 11:37:09,032 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR - 详细堆栈跟踪:
2025-07-24 11:37:09,033 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR - Traceback (most recent call last):
2025-07-24 11:37:09,039 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR -   File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
2025-07-24 11:37:09,034 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 11:37:09,040 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR -     import psutil
2025-07-24 11:37:09,040 - tif_api - INFO - 查询任务: e4488b58-3968-4dc8-be93-dd5146cbf6fc
2025-07-24 11:37:09,043 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR - ModuleNotFoundError: No module named 'psutil'
2025-07-24 11:37:09,044 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR - 系统诊断信息:
2025-07-24 11:37:09,044 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR - 输出目录磁盘空间: 总计 1050.91 GB, 可用 47.19 GB, 使用率 95.5%
2025-07-24 11:37:09,044 - tif_api - INFO - 任务 e4488b58-3968-4dc8-be93-dd5146cbf6fc 状态查询成功，当前状态: 运行失败
2025-07-24 11:37:09,056 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR - 输入文件大小: 0.22 GB
2025-07-24 11:37:09,064 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:37:09] "GET /api/tif/status?task_id=e4488b58-3968-4dc8-be93-dd5146cbf6fc HTTP/1.1" 200 -
2025-07-24 11:37:09,068 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR - 检查内存使用时出错: No module named 'psutil'
2025-07-24 11:37:09,069 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR - 完成时间: 2025-07-24 11:37:09
2025-07-24 11:37:09,071 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR - 状态: 运行失败
2025-07-24 11:37:09,072 - tif_task_e4488b58-3968-4dc8-be93-dd5146cbf6fc - ERROR - ============ 任务异常结束 ============
2025-07-24 11:38:11,190 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 11:38:11,194 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 11:38:11,198 - tif_api - INFO - 输入文件大小: 222.47 MB
2025-07-24 11:38:11,199 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 11:38:11,201 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:38:11,203 - tif_api - INFO - 黑色阈值: 0
2025-07-24 11:38:11,204 - tif_api - INFO - 白色阈值: 255
2025-07-24 11:38:11,205 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 11:38:11,210 - tif_executor - INFO - 启动TIF处理任务 f68cbd03-ac4e-4c8f-a67b-6388e92b3c33: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:38:11,211 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - INFO - ============ TIF处理任务 f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 开始执行 ============
2025-07-24 11:38:11,211 - tif_api - INFO - 异步任务启动成功，任务ID: f68cbd03-ac4e-4c8f-a67b-6388e92b3c33
2025-07-24 11:38:11,213 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - INFO - 开始时间: 2025-07-24 11:38:11
2025-07-24 11:38:11,214 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:38:11] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 11:38:11,224 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 11:38:11,238 - tif_executor - ERROR - 执行任务 f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 时出错: No module named 'psutil'
2025-07-24 11:38:11,240 - tif_executor - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
    import psutil
ModuleNotFoundError: No module named 'psutil'

2025-07-24 11:38:11,243 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR - ============ 处理失败 ============
2025-07-24 11:38:11,255 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR - 错误类型: ModuleNotFoundError
2025-07-24 11:38:11,256 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR - 错误信息: No module named 'psutil'
2025-07-24 11:38:11,255 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 11:38:11,263 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR - 详细堆栈跟踪:
2025-07-24 11:38:11,264 - tif_api - INFO - 查询任务: f68cbd03-ac4e-4c8f-a67b-6388e92b3c33
2025-07-24 11:38:11,266 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR - Traceback (most recent call last):
2025-07-24 11:38:11,271 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR -   File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
2025-07-24 11:38:11,268 - tif_api - INFO - 任务 f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 状态查询成功，当前状态: 运行失败
2025-07-24 11:38:11,272 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR -     import psutil
2025-07-24 11:38:11,273 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:38:11] "GET /api/tif/status?task_id=f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 HTTP/1.1" 200 -
2025-07-24 11:38:11,273 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR - ModuleNotFoundError: No module named 'psutil'
2025-07-24 11:38:11,275 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR - 系统诊断信息:
2025-07-24 11:38:11,275 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR - 输出目录磁盘空间: 总计 1050.91 GB, 可用 47.19 GB, 使用率 95.5%
2025-07-24 11:38:11,287 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR - 输入文件大小: 0.22 GB
2025-07-24 11:38:11,291 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR - 检查内存使用时出错: No module named 'psutil'
2025-07-24 11:38:11,295 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR - 完成时间: 2025-07-24 11:38:11
2025-07-24 11:38:11,296 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR - 状态: 运行失败
2025-07-24 11:38:11,297 - tif_task_f68cbd03-ac4e-4c8f-a67b-6388e92b3c33 - ERROR - ============ 任务异常结束 ============
2025-07-24 11:39:13,418 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 11:39:13,422 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 11:39:13,427 - tif_api - INFO - 输入文件大小: 222.47 MB
2025-07-24 11:39:13,428 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 11:39:13,430 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:39:13,431 - tif_api - INFO - 黑色阈值: 0
2025-07-24 11:39:13,446 - tif_api - INFO - 白色阈值: 255
2025-07-24 11:39:13,447 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 11:39:13,454 - tif_executor - INFO - 启动TIF处理任务 624f5fe6-13ce-4726-80fb-15dcaad3622e: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:39:13,455 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - INFO - ============ TIF处理任务 624f5fe6-13ce-4726-80fb-15dcaad3622e 开始执行 ============
2025-07-24 11:39:13,457 - tif_api - INFO - 异步任务启动成功，任务ID: 624f5fe6-13ce-4726-80fb-15dcaad3622e
2025-07-24 11:39:13,459 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - INFO - 开始时间: 2025-07-24 11:39:13
2025-07-24 11:39:13,461 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:39:13] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 11:39:13,461 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 11:39:13,467 - tif_executor - ERROR - 执行任务 624f5fe6-13ce-4726-80fb-15dcaad3622e 时出错: No module named 'psutil'
2025-07-24 11:39:13,475 - tif_executor - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
    import psutil
ModuleNotFoundError: No module named 'psutil'

2025-07-24 11:39:13,478 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR - ============ 处理失败 ============
2025-07-24 11:39:13,482 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR - 错误类型: ModuleNotFoundError
2025-07-24 11:39:13,483 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR - 错误信息: No module named 'psutil'
2025-07-24 11:39:13,485 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR - 详细堆栈跟踪:
2025-07-24 11:39:13,483 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 11:39:13,485 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR - Traceback (most recent call last):
2025-07-24 11:39:13,486 - tif_api - INFO - 查询任务: 624f5fe6-13ce-4726-80fb-15dcaad3622e
2025-07-24 11:39:13,490 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR -   File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
2025-07-24 11:39:13,492 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR -     import psutil
2025-07-24 11:39:13,494 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR - ModuleNotFoundError: No module named 'psutil'
2025-07-24 11:39:13,492 - tif_api - INFO - 任务 624f5fe6-13ce-4726-80fb-15dcaad3622e 状态查询成功，当前状态: 运行失败
2025-07-24 11:39:13,502 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR - 系统诊断信息:
2025-07-24 11:39:13,510 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:39:13] "GET /api/tif/status?task_id=624f5fe6-13ce-4726-80fb-15dcaad3622e HTTP/1.1" 200 -
2025-07-24 11:39:13,513 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR - 输出目录磁盘空间: 总计 1050.91 GB, 可用 47.19 GB, 使用率 95.5%
2025-07-24 11:39:13,518 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR - 输入文件大小: 0.22 GB
2025-07-24 11:39:13,523 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR - 检查内存使用时出错: No module named 'psutil'
2025-07-24 11:39:13,524 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR - 完成时间: 2025-07-24 11:39:13
2025-07-24 11:39:13,531 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR - 状态: 运行失败
2025-07-24 11:39:13,532 - tif_task_624f5fe6-13ce-4726-80fb-15dcaad3622e - ERROR - ============ 任务异常结束 ============
2025-07-24 11:40:15,624 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 11:40:15,628 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 11:40:15,634 - tif_api - INFO - 输入文件大小: 222.47 MB
2025-07-24 11:40:15,635 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 11:40:15,637 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:40:15,645 - tif_api - INFO - 黑色阈值: 0
2025-07-24 11:40:15,649 - tif_api - INFO - 白色阈值: 255
2025-07-24 11:40:15,651 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 11:40:15,662 - tif_executor - INFO - 启动TIF处理任务 31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:40:15,663 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - INFO - ============ TIF处理任务 31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 开始执行 ============
2025-07-24 11:40:15,671 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - INFO - 开始时间: 2025-07-24 11:40:15
2025-07-24 11:40:15,664 - tif_api - INFO - 异步任务启动成功，任务ID: 31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5
2025-07-24 11:40:15,671 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 11:40:15,673 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:40:15] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 11:40:15,679 - tif_executor - ERROR - 执行任务 31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 时出错: No module named 'psutil'
2025-07-24 11:40:15,681 - tif_executor - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
    import psutil
ModuleNotFoundError: No module named 'psutil'

2025-07-24 11:40:15,685 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR - ============ 处理失败 ============
2025-07-24 11:40:15,693 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR - 错误类型: ModuleNotFoundError
2025-07-24 11:40:15,694 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR - 错误信息: No module named 'psutil'
2025-07-24 11:40:15,702 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR - 详细堆栈跟踪:
2025-07-24 11:40:15,695 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 11:40:15,702 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR - Traceback (most recent call last):
2025-07-24 11:40:15,703 - tif_api - INFO - 查询任务: 31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5
2025-07-24 11:40:15,705 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR -   File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
2025-07-24 11:40:15,706 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR -     import psutil
2025-07-24 11:40:15,706 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR - ModuleNotFoundError: No module named 'psutil'
2025-07-24 11:40:15,706 - tif_api - INFO - 任务 31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 状态查询成功，当前状态: 运行失败
2025-07-24 11:40:15,707 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR - 系统诊断信息:
2025-07-24 11:40:15,708 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:40:15] "GET /api/tif/status?task_id=31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 HTTP/1.1" 200 -
2025-07-24 11:40:15,709 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR - 输出目录磁盘空间: 总计 1050.91 GB, 可用 47.19 GB, 使用率 95.5%
2025-07-24 11:40:15,710 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR - 输入文件大小: 0.22 GB
2025-07-24 11:40:15,712 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR - 检查内存使用时出错: No module named 'psutil'
2025-07-24 11:40:15,714 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR - 完成时间: 2025-07-24 11:40:15
2025-07-24 11:40:15,724 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR - 状态: 运行失败
2025-07-24 11:40:15,725 - tif_task_31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 - ERROR - ============ 任务异常结束 ============
2025-07-24 11:41:17,799 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 11:41:17,804 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 11:41:17,808 - tif_api - INFO - 输入文件大小: 222.47 MB
2025-07-24 11:41:17,809 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 11:41:17,811 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:41:17,813 - tif_api - INFO - 黑色阈值: 0
2025-07-24 11:41:17,814 - tif_api - INFO - 白色阈值: 255
2025-07-24 11:41:17,815 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 11:41:17,820 - tif_executor - INFO - 启动TIF处理任务 5cf35114-6186-423c-b9d4-db8bb62f70c9: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:41:17,821 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - INFO - ============ TIF处理任务 5cf35114-6186-423c-b9d4-db8bb62f70c9 开始执行 ============
2025-07-24 11:41:17,821 - tif_api - INFO - 异步任务启动成功，任务ID: 5cf35114-6186-423c-b9d4-db8bb62f70c9
2025-07-24 11:41:17,824 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - INFO - 开始时间: 2025-07-24 11:41:17
2025-07-24 11:41:17,827 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:41:17] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 11:41:17,837 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 11:41:17,854 - tif_executor - ERROR - 执行任务 5cf35114-6186-423c-b9d4-db8bb62f70c9 时出错: No module named 'psutil'
2025-07-24 11:41:17,857 - tif_executor - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
    import psutil
ModuleNotFoundError: No module named 'psutil'

2025-07-24 11:41:17,861 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR - ============ 处理失败 ============
2025-07-24 11:41:17,880 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR - 错误类型: ModuleNotFoundError
2025-07-24 11:41:17,881 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR - 错误信息: No module named 'psutil'
2025-07-24 11:41:17,881 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 11:41:17,882 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR - 详细堆栈跟踪:
2025-07-24 11:41:17,883 - tif_api - INFO - 查询任务: 5cf35114-6186-423c-b9d4-db8bb62f70c9
2025-07-24 11:41:17,884 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR - Traceback (most recent call last):
2025-07-24 11:41:17,886 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR -   File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
2025-07-24 11:41:17,887 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR -     import psutil
2025-07-24 11:41:17,886 - tif_api - INFO - 任务 5cf35114-6186-423c-b9d4-db8bb62f70c9 状态查询成功，当前状态: 运行失败
2025-07-24 11:41:17,888 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR - ModuleNotFoundError: No module named 'psutil'
2025-07-24 11:41:17,890 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:41:17] "GET /api/tif/status?task_id=5cf35114-6186-423c-b9d4-db8bb62f70c9 HTTP/1.1" 200 -
2025-07-24 11:41:17,898 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR - 系统诊断信息:
2025-07-24 11:41:17,902 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR - 输出目录磁盘空间: 总计 1050.91 GB, 可用 47.19 GB, 使用率 95.5%
2025-07-24 11:41:17,903 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR - 输入文件大小: 0.22 GB
2025-07-24 11:41:17,909 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR - 检查内存使用时出错: No module named 'psutil'
2025-07-24 11:41:17,911 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR - 完成时间: 2025-07-24 11:41:17
2025-07-24 11:41:17,912 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR - 状态: 运行失败
2025-07-24 11:41:17,915 - tif_task_5cf35114-6186-423c-b9d4-db8bb62f70c9 - ERROR - ============ 任务异常结束 ============
2025-07-24 11:42:20,041 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 11:42:20,045 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 11:42:20,051 - tif_api - INFO - 输入文件大小: 222.47 MB
2025-07-24 11:42:20,054 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 11:42:20,055 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:42:20,056 - tif_api - INFO - 黑色阈值: 0
2025-07-24 11:42:20,057 - tif_api - INFO - 白色阈值: 255
2025-07-24 11:42:20,058 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 11:42:20,061 - tif_executor - INFO - 启动TIF处理任务 92e83768-8bc1-4899-8f65-750a731d4c2c: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:42:20,061 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - INFO - ============ TIF处理任务 92e83768-8bc1-4899-8f65-750a731d4c2c 开始执行 ============
2025-07-24 11:42:20,061 - tif_api - INFO - 异步任务启动成功，任务ID: 92e83768-8bc1-4899-8f65-750a731d4c2c
2025-07-24 11:42:20,062 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - INFO - 开始时间: 2025-07-24 11:42:20
2025-07-24 11:42:20,063 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:42:20] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 11:42:20,064 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 11:42:20,087 - tif_executor - ERROR - 执行任务 92e83768-8bc1-4899-8f65-750a731d4c2c 时出错: No module named 'psutil'
2025-07-24 11:42:20,091 - tif_executor - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
    import psutil
ModuleNotFoundError: No module named 'psutil'

2025-07-24 11:42:20,091 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 11:42:20,095 - tif_api - INFO - 查询任务: 92e83768-8bc1-4899-8f65-750a731d4c2c
2025-07-24 11:42:20,095 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR - ============ 处理失败 ============
2025-07-24 11:42:20,168 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR - 错误类型: ModuleNotFoundError
2025-07-24 11:42:20,175 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR - 错误信息: No module named 'psutil'
2025-07-24 11:42:20,169 - tif_api - INFO - 任务 92e83768-8bc1-4899-8f65-750a731d4c2c 状态查询成功，当前状态: 运行失败
2025-07-24 11:42:20,176 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR - 详细堆栈跟踪:
2025-07-24 11:42:20,177 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:42:20] "GET /api/tif/status?task_id=92e83768-8bc1-4899-8f65-750a731d4c2c HTTP/1.1" 200 -
2025-07-24 11:42:20,178 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR - Traceback (most recent call last):
2025-07-24 11:42:20,180 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR -   File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
2025-07-24 11:42:20,181 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR -     import psutil
2025-07-24 11:42:20,182 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR - ModuleNotFoundError: No module named 'psutil'
2025-07-24 11:42:20,184 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR - 系统诊断信息:
2025-07-24 11:42:20,186 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR - 输出目录磁盘空间: 总计 1050.91 GB, 可用 47.19 GB, 使用率 95.5%
2025-07-24 11:42:20,187 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR - 输入文件大小: 0.22 GB
2025-07-24 11:42:20,191 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR - 检查内存使用时出错: No module named 'psutil'
2025-07-24 11:42:20,199 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR - 完成时间: 2025-07-24 11:42:20
2025-07-24 11:42:20,210 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR - 状态: 运行失败
2025-07-24 11:42:20,211 - tif_task_92e83768-8bc1-4899-8f65-750a731d4c2c - ERROR - ============ 任务异常结束 ============
2025-07-24 11:43:22,324 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 11:43:22,328 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 11:43:22,333 - tif_api - INFO - 输入文件大小: 222.47 MB
2025-07-24 11:43:22,334 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 11:43:22,335 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:43:22,337 - tif_api - INFO - 黑色阈值: 0
2025-07-24 11:43:22,338 - tif_api - INFO - 白色阈值: 255
2025-07-24 11:43:22,349 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 11:43:22,360 - tif_executor - INFO - 启动TIF处理任务 f7812583-5d32-4b4b-85fe-188ffb30535f: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:43:22,361 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - INFO - ============ TIF处理任务 f7812583-5d32-4b4b-85fe-188ffb30535f 开始执行 ============
2025-07-24 11:43:22,362 - tif_api - INFO - 异步任务启动成功，任务ID: f7812583-5d32-4b4b-85fe-188ffb30535f
2025-07-24 11:43:22,365 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - INFO - 开始时间: 2025-07-24 11:43:22
2025-07-24 11:43:22,366 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:43:22] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 11:43:22,367 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 11:43:22,376 - tif_executor - ERROR - 执行任务 f7812583-5d32-4b4b-85fe-188ffb30535f 时出错: No module named 'psutil'
2025-07-24 11:43:22,384 - tif_executor - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
    import psutil
ModuleNotFoundError: No module named 'psutil'

2025-07-24 11:43:22,388 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR - ============ 处理失败 ============
2025-07-24 11:43:22,393 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR - 错误类型: ModuleNotFoundError
2025-07-24 11:43:22,394 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR - 错误信息: No module named 'psutil'
2025-07-24 11:43:22,394 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 11:43:22,395 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR - 详细堆栈跟踪:
2025-07-24 11:43:22,398 - tif_api - INFO - 查询任务: f7812583-5d32-4b4b-85fe-188ffb30535f
2025-07-24 11:43:22,400 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR - Traceback (most recent call last):
2025-07-24 11:43:22,417 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR -   File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
2025-07-24 11:43:22,402 - tif_api - INFO - 任务 f7812583-5d32-4b4b-85fe-188ffb30535f 状态查询成功，当前状态: 运行失败
2025-07-24 11:43:22,418 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR -     import psutil
2025-07-24 11:43:22,419 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:43:22] "GET /api/tif/status?task_id=f7812583-5d32-4b4b-85fe-188ffb30535f HTTP/1.1" 200 -
2025-07-24 11:43:22,420 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR - ModuleNotFoundError: No module named 'psutil'
2025-07-24 11:43:22,421 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR - 系统诊断信息:
2025-07-24 11:43:22,422 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR - 输出目录磁盘空间: 总计 1050.91 GB, 可用 47.19 GB, 使用率 95.5%
2025-07-24 11:43:22,426 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR - 输入文件大小: 0.22 GB
2025-07-24 11:43:22,429 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR - 检查内存使用时出错: No module named 'psutil'
2025-07-24 11:43:22,430 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR - 完成时间: 2025-07-24 11:43:22
2025-07-24 11:43:22,430 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR - 状态: 运行失败
2025-07-24 11:43:22,440 - tif_task_f7812583-5d32-4b4b-85fe-188ffb30535f - ERROR - ============ 任务异常结束 ============
2025-07-24 11:44:24,527 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 11:44:24,535 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 11:44:24,540 - tif_api - INFO - 输入文件大小: 222.47 MB
2025-07-24 11:44:24,542 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 11:44:24,545 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:44:24,548 - tif_api - INFO - 黑色阈值: 0
2025-07-24 11:44:24,551 - tif_api - INFO - 白色阈值: 255
2025-07-24 11:44:24,553 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 11:44:24,571 - tif_executor - INFO - 启动TIF处理任务 55649807-4f46-484f-bd52-d04b597d1216: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:44:24,572 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - INFO - ============ TIF处理任务 55649807-4f46-484f-bd52-d04b597d1216 开始执行 ============
2025-07-24 11:44:24,573 - tif_api - INFO - 异步任务启动成功，任务ID: 55649807-4f46-484f-bd52-d04b597d1216
2025-07-24 11:44:24,575 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - INFO - 开始时间: 2025-07-24 11:44:24
2025-07-24 11:44:24,577 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:44:24] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 11:44:24,577 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 11:44:24,585 - tif_executor - ERROR - 执行任务 55649807-4f46-484f-bd52-d04b597d1216 时出错: No module named 'psutil'
2025-07-24 11:44:24,598 - tif_executor - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
    import psutil
ModuleNotFoundError: No module named 'psutil'

2025-07-24 11:44:24,605 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 11:44:24,607 - tif_api - INFO - 查询任务: 55649807-4f46-484f-bd52-d04b597d1216
2025-07-24 11:44:24,609 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR - ============ 处理失败 ============
2025-07-24 11:44:24,610 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR - 错误类型: ModuleNotFoundError
2025-07-24 11:44:24,611 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR - 错误信息: No module named 'psutil'
2025-07-24 11:44:24,611 - tif_api - INFO - 任务 55649807-4f46-484f-bd52-d04b597d1216 状态查询成功，当前状态: 运行失败
2025-07-24 11:44:24,613 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR - 详细堆栈跟踪:
2025-07-24 11:44:24,614 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:44:24] "GET /api/tif/status?task_id=55649807-4f46-484f-bd52-d04b597d1216 HTTP/1.1" 200 -
2025-07-24 11:44:24,615 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR - Traceback (most recent call last):
2025-07-24 11:44:24,631 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR -   File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
2025-07-24 11:44:24,632 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR -     import psutil
2025-07-24 11:44:24,633 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR - ModuleNotFoundError: No module named 'psutil'
2025-07-24 11:44:24,634 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR - 系统诊断信息:
2025-07-24 11:44:24,635 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR - 输出目录磁盘空间: 总计 1050.91 GB, 可用 47.19 GB, 使用率 95.5%
2025-07-24 11:44:24,637 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR - 输入文件大小: 0.22 GB
2025-07-24 11:44:24,641 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR - 检查内存使用时出错: No module named 'psutil'
2025-07-24 11:44:24,644 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR - 完成时间: 2025-07-24 11:44:24
2025-07-24 11:44:24,647 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR - 状态: 运行失败
2025-07-24 11:44:24,656 - tif_task_55649807-4f46-484f-bd52-d04b597d1216 - ERROR - ============ 任务异常结束 ============
2025-07-24 11:45:26,735 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 11:45:26,738 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 11:45:26,743 - tif_api - INFO - 输入文件大小: 222.47 MB
2025-07-24 11:45:26,743 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 11:45:26,744 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:45:26,745 - tif_api - INFO - 黑色阈值: 0
2025-07-24 11:45:26,746 - tif_api - INFO - 白色阈值: 255
2025-07-24 11:45:26,747 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 11:45:26,750 - tif_executor - INFO - 启动TIF处理任务 7bfcd643-629e-4654-beb8-8bcfa7c5af36: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 11:45:26,751 - tif_api - INFO - 异步任务启动成功，任务ID: 7bfcd643-629e-4654-beb8-8bcfa7c5af36
2025-07-24 11:45:26,753 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:45:26] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 11:45:26,774 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - INFO - ============ TIF处理任务 7bfcd643-629e-4654-beb8-8bcfa7c5af36 开始执行 ============
2025-07-24 11:45:26,774 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - INFO - 开始时间: 2025-07-24 11:45:26
2025-07-24 11:45:26,780 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 11:45:26,781 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 11:45:26,783 - tif_executor - ERROR - 执行任务 7bfcd643-629e-4654-beb8-8bcfa7c5af36 时出错: No module named 'psutil'
2025-07-24 11:45:26,784 - tif_api - INFO - 查询任务: 7bfcd643-629e-4654-beb8-8bcfa7c5af36
2025-07-24 11:45:26,786 - tif_executor - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
    import psutil
ModuleNotFoundError: No module named 'psutil'

2025-07-24 11:45:26,787 - tif_api - INFO - 任务 7bfcd643-629e-4654-beb8-8bcfa7c5af36 状态查询成功，当前状态: 正在运行
2025-07-24 11:45:26,800 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 11:45:26] "GET /api/tif/status?task_id=7bfcd643-629e-4654-beb8-8bcfa7c5af36 HTTP/1.1" 200 -
2025-07-24 11:45:26,802 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR - ============ 处理失败 ============
2025-07-24 11:45:26,812 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR - 错误类型: ModuleNotFoundError
2025-07-24 11:45:26,812 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR - 错误信息: No module named 'psutil'
2025-07-24 11:45:26,813 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR - 详细堆栈跟踪:
2025-07-24 11:45:26,814 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR - Traceback (most recent call last):
2025-07-24 11:45:26,815 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR -   File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
2025-07-24 11:45:26,816 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR -     import psutil
2025-07-24 11:45:26,819 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR - ModuleNotFoundError: No module named 'psutil'
2025-07-24 11:45:26,827 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR - 系统诊断信息:
2025-07-24 11:45:26,828 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR - 输出目录磁盘空间: 总计 1050.91 GB, 可用 47.19 GB, 使用率 95.5%
2025-07-24 11:45:26,834 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR - 输入文件大小: 0.22 GB
2025-07-24 11:45:26,838 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR - 检查内存使用时出错: No module named 'psutil'
2025-07-24 11:45:26,839 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR - 完成时间: 2025-07-24 11:45:26
2025-07-24 11:45:26,840 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR - 状态: 运行失败
2025-07-24 11:45:26,841 - tif_task_7bfcd643-629e-4654-beb8-8bcfa7c5af36 - ERROR - ============ 任务异常结束 ============
2025-07-24 14:59:00,094 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 14:59:00] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-24 14:59:00,105 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-24 14:59:00,239 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-24 14:59:00,241 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 14:59:00] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-24 14:59:05,623 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 14:59:05] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171600 HTTP/1.1" 200 -
2025-07-24 14:59:05,628 - root - INFO - 获取图层 testodm:20250705171600 的信息
2025-07-24 14:59:05,663 - root - INFO - 成功获取图层 testodm:20250705171600 的边界框信息
2025-07-24 14:59:05,673 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 14:59:05] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171600 HTTP/1.1" 200 -
2025-07-24 15:04:20,596 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 15:04:20] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-24 15:04:20,607 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-24 15:04:20,686 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-24 15:04:20,688 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 15:04:20] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-24 15:04:25,734 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 15:04:25] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171600 HTTP/1.1" 200 -
2025-07-24 15:04:25,740 - root - INFO - 获取图层 testodm:20250705171600 的信息
2025-07-24 15:04:25,774 - root - INFO - 成功获取图层 testodm:20250705171600 的边界框信息
2025-07-24 15:04:25,775 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 15:04:25] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171600 HTTP/1.1" 200 -
2025-07-24 15:12:06,595 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 15:12:06] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-24 15:12:06,621 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-24 15:12:06,708 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-24 15:12:06,711 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 15:12:06] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-24 15:12:11,838 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 15:12:11] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171600 HTTP/1.1" 200 -
2025-07-24 15:12:11,843 - root - INFO - 获取图层 testodm:20250705171600 的信息
2025-07-24 15:12:11,874 - root - INFO - 成功获取图层 testodm:20250705171600 的边界框信息
2025-07-24 15:12:11,875 - werkzeug - INFO - 192.168.43.148 - - [24/Jul/2025 15:12:11] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171600 HTTP/1.1" 200 -
2025-07-24 15:15:04,159 - batch_executor - INFO - 启动任务 39113527-7924-4221-8238-889c356d5c51: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171601/project
2025-07-24 15:15:04,160 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:15:04] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-24 15:15:04,193 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:15:04] "GET /api/batch/status?task_id=39113527-7924-4221-8238-889c356d5c51 HTTP/1.1" 200 -
2025-07-24 15:16:04,246 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:16:04] "GET /api/batch/status?task_id=39113527-7924-4221-8238-889c356d5c51 HTTP/1.1" 200 -
2025-07-24 15:16:08,088 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 15:16:08,090 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 15:16:08,092 - tif_api - INFO - 输入文件大小: 222.48 MB
2025-07-24 15:16:08,093 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 15:16:08,094 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 15:16:08,099 - tif_api - INFO - 黑色阈值: 0
2025-07-24 15:16:08,101 - tif_api - INFO - 白色阈值: 255
2025-07-24 15:16:08,102 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 15:16:08,107 - tif_executor - INFO - 启动TIF处理任务 f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 15:16:08,108 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - INFO - ============ TIF处理任务 f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df 开始执行 ============
2025-07-24 15:16:08,109 - tif_api - INFO - 异步任务启动成功，任务ID: f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df
2025-07-24 15:16:08,109 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - INFO - 开始时间: 2025-07-24 15:16:08
2025-07-24 15:16:08,117 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:16:08] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 15:16:08,129 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 15:16:08,146 - tif_executor - ERROR - 执行任务 f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df 时出错: No module named 'psutil'
2025-07-24 15:16:08,159 - tif_executor - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
ModuleNotFoundError: No module named 'psutil'

2025-07-24 15:16:08,171 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 15:16:08,170 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR - ============ 处理失败 ============
2025-07-24 15:16:08,176 - tif_api - INFO - 查询任务: f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df
2025-07-24 15:16:08,186 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR - 错误类型: ModuleNotFoundError
2025-07-24 15:16:08,193 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR - 错误信息: No module named 'psutil'
2025-07-24 15:16:08,195 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR - 详细堆栈跟踪:
2025-07-24 15:16:08,196 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR - Traceback (most recent call last):
2025-07-24 15:16:08,198 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR -   File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
2025-07-24 15:16:08,200 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR - ModuleNotFoundError: No module named 'psutil'
2025-07-24 15:16:08,202 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR - 系统诊断信息:
2025-07-24 15:16:08,209 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR - 输出目录磁盘空间: 总计 1050.91 GB, 可用 47.09 GB, 使用率 95.5%
2025-07-24 15:16:08,209 - tif_api - INFO - 任务 f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df 状态查询成功，当前状态: 运行失败
2025-07-24 15:16:08,218 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR - 输入文件大小: 0.22 GB
2025-07-24 15:16:08,223 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:16:08] "GET /api/tif/status?task_id=f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df HTTP/1.1" 200 -
2025-07-24 15:16:08,230 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR - 检查内存使用时出错: No module named 'psutil'
2025-07-24 15:16:08,231 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR - 完成时间: 2025-07-24 15:16:08
2025-07-24 15:16:08,232 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR - 状态: 运行失败
2025-07-24 15:16:08,233 - tif_task_f21e0f2b-e2d0-4e6d-ba86-0ecfbf7563df - ERROR - ============ 任务异常结束 ============
2025-07-24 15:17:08,290 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-24 15:17:08,294 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 15:17:08,296 - tif_api - INFO - 输入文件大小: 222.48 MB
2025-07-24 15:17:08,297 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 15:17:08,299 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 15:17:08,302 - tif_api - INFO - 黑色阈值: 0
2025-07-24 15:17:08,303 - tif_api - INFO - 白色阈值: 255
2025-07-24 15:17:08,306 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-24 15:17:08,312 - tif_executor - INFO - 启动TIF处理任务 05ae2ff3-1996-4c74-a831-04ba23126c27: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 15:17:08,313 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - INFO - ============ TIF处理任务 05ae2ff3-1996-4c74-a831-04ba23126c27 开始执行 ============
2025-07-24 15:17:08,320 - tif_api - INFO - 异步任务启动成功，任务ID: 05ae2ff3-1996-4c74-a831-04ba23126c27
2025-07-24 15:17:08,323 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - INFO - 开始时间: 2025-07-24 15:17:08
2025-07-24 15:17:08,324 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:17:08] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 15:17:08,324 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 15:17:08,332 - tif_executor - ERROR - 执行任务 05ae2ff3-1996-4c74-a831-04ba23126c27 时出错: No module named 'psutil'
2025-07-24 15:17:08,336 - tif_executor - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
ModuleNotFoundError: No module named 'psutil'

2025-07-24 15:17:08,342 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR - ============ 处理失败 ============
2025-07-24 15:17:08,354 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR - 错误类型: ModuleNotFoundError
2025-07-24 15:17:08,355 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR - 错误信息: No module named 'psutil'
2025-07-24 15:17:08,359 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR - 详细堆栈跟踪:
2025-07-24 15:17:08,356 - tif_api - INFO - 收到任务状态查询请求
2025-07-24 15:17:08,360 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR - Traceback (most recent call last):
2025-07-24 15:17:08,361 - tif_api - INFO - 查询任务: 05ae2ff3-1996-4c74-a831-04ba23126c27
2025-07-24 15:17:08,363 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR -   File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
2025-07-24 15:17:08,365 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR - ModuleNotFoundError: No module named 'psutil'
2025-07-24 15:17:08,369 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR - 系统诊断信息:
2025-07-24 15:17:08,365 - tif_api - INFO - 任务 05ae2ff3-1996-4c74-a831-04ba23126c27 状态查询成功，当前状态: 运行失败
2025-07-24 15:17:08,370 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR - 输出目录磁盘空间: 总计 1050.91 GB, 可用 47.09 GB, 使用率 95.5%
2025-07-24 15:17:08,371 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 15:17:08] "GET /api/tif/status?task_id=05ae2ff3-1996-4c74-a831-04ba23126c27 HTTP/1.1" 200 -
2025-07-24 15:17:08,372 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR - 输入文件大小: 0.22 GB
2025-07-24 15:17:08,389 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR - 检查内存使用时出错: No module named 'psutil'
2025-07-24 15:17:08,390 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR - 完成时间: 2025-07-24 15:17:08
2025-07-24 15:17:08,391 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR - 状态: 运行失败
2025-07-24 15:17:08,392 - tif_task_05ae2ff3-1996-4c74-a831-04ba23126c27 - ERROR - ============ 任务异常结束 ============
