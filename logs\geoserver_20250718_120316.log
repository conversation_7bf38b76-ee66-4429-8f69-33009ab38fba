2025-07-18 12:03:16,809 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250718_120316.log
2025-07-18 12:03:16,810 - geo_publisher - INFO - 加载了 12 个任务状态
2025-07-18 12:03:16,830 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 12:03:16,832 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 12:03:16,845 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 12:03:16,852 - root - INFO - === GeoServer REST API服务 ===
2025-07-18 12:03:16,852 - root - INFO - 主机: 0.0.0.0
2025-07-18 12:03:16,853 - root - INFO - 端口: 5083
2025-07-18 12:03:16,855 - root - INFO - 调试模式: 禁用
2025-07-18 12:03:16,856 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-18 12:03:16,949 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-18 12:03:16,951 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 12:03:17,082 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:03:17] "GET /api/geo/status?task_id=71d4f572-1811-4358-8356-1684f928fcaf HTTP/1.1" 200 -
2025-07-18 12:03:17,459 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:03:17] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-18 12:04:24,727 - geo_publisher - INFO - 启动GeoTIFF发布任务 e8c063d9-1859-4e86-88aa-d9857bf38de6: D:/Drone_Project/dataset/nanning.tif, workspace=tttt
2025-07-18 12:04:24,728 - geo_task_e8c063d9-1859-4e86-88aa-d9857bf38de6 - INFO - GeoServer发布任务 e8c063d9-1859-4e86-88aa-d9857bf38de6 开始执行
2025-07-18 12:04:24,732 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:04:24] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/dataset/nanning.tif&workspace=tttt HTTP/1.1" 200 -
2025-07-18 12:04:24,734 - geo_task_e8c063d9-1859-4e86-88aa-d9857bf38de6 - INFO - 开始时间: 2025-07-18 12:04:24
2025-07-18 12:04:24,736 - geo_task_e8c063d9-1859-4e86-88aa-d9857bf38de6 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/dataset/nanning.tif",
  "workspace": "tttt",
  "store_name": null,
  "layer_name": null
}
2025-07-18 12:04:24,738 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 12:04:24,738 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 12:04:24,759 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 12:04:24,760 - root - INFO - 未提供存储名称，使用文件名: nanning
2025-07-18 12:04:24,760 - root - INFO - 未提供图层名称，使用存储名称: nanning
2025-07-18 12:04:24,790 - root - INFO - 使用绝对路径: D:\Drone_Project\dataset\nanning.tif
2025-07-18 12:04:25,603 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/dataset/nanning.tif' 到 'tttt:nanning'
2025-07-18 12:04:25,726 - root - INFO - 图层验证成功: 直接图层路径: tttt:nanning
2025-07-18 12:04:25,751 - root - INFO - GeoTIFF 'D:/Drone_Project/dataset/nanning.tif' 成功发布为图层 'tttt:nanning'
2025-07-18 12:04:25,789 - geo_task_e8c063d9-1859-4e86-88aa-d9857bf38de6 - INFO - GeoServer发布任务 e8c063d9-1859-4e86-88aa-d9857bf38de6 执行成功
2025-07-18 12:04:25,790 - geo_task_e8c063d9-1859-4e86-88aa-d9857bf38de6 - INFO - 完成时间: 2025-07-18 12:04:25
2025-07-18 12:04:25,794 - geo_task_e8c063d9-1859-4e86-88aa-d9857bf38de6 - INFO - 状态: 发布成功
2025-07-18 12:05:13,588 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:05:13] "GET /api/geo/status?task_id=e8c063d9-1859-4e86-88aa-d9857bf38de6 HTTP/1.1" 200 -
