2025-07-14 12:16:54,112 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250714_121654.log
2025-07-14 12:16:54,114 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 12:16:54,114 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 12:16:54,137 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 12:16:54,148 - batch_executor - INFO - 加载了 24 个任务状态
2025-07-14 12:16:54,163 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 12:16:54,164 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 12:16:54,187 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 12:16:54,193 - root - INFO - === GeoServer REST API服务 ===
2025-07-14 12:16:54,193 - root - INFO - 主机: 0.0.0.0
2025-07-14 12:16:54,193 - root - INFO - 端口: 5083
2025-07-14 12:16:54,193 - root - INFO - 调试模式: 禁用
2025-07-14 12:16:54,194 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-14 12:16:54,210 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-14 12:16:54,211 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-14 12:17:19,012 - batch_executor - INFO - 启动任务 b6d58241-c6e8-4b22-bc16-9dba8b6d4cc1: D:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-14 12:17:19,013 - werkzeug - INFO - ************** - - [14/Jul/2025 12:17:19] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
