# 批处理执行API文档

批处理执行API模块提供异步批处理文件执行功能，支持ODM（OpenDroneMap）等长时间运行任务的管理和监控。

## 模块概述

批处理执行模块是系统的异步任务处理核心，提供：
- **异步执行**：批处理文件的后台异步执行
- **任务管理**：任务状态的实时查询和管理
- **日志监控**：执行过程的详细日志记录和查看
- **进度跟踪**：长时间任务的进度监控
- **错误处理**：执行失败的错误信息和恢复机制

## API端点列表

### 1. 执行批处理文件

**端点**: `GET /api/batch/execute/`

**描述**: 异步执行指定的批处理文件，支持ODM等长时间运行的任务

**参数**:
- `batch_path` (可选): 批处理文件路径，默认为 `D:\Drone_Project\ODM\ODM\run.bat`
- `project_path` (可选): 项目路径，默认为 `c://user/20250714140500/project`
- 其他参数: 传递给批处理文件的自定义参数

**请求示例**:
```bash
# 使用默认参数执行ODM批处理
curl "http://127.0.0.1:5000/api/batch/execute/"

# 指定自定义参数执行
curl "http://127.0.0.1:5000/api/batch/execute/?batch_path=D:/scripts/process.bat&project_path=D:/projects/drone_001&fast-orthophoto=true&verbose=true"

# 执行带多个参数的批处理
curl "http://127.0.0.1:5000/api/batch/execute/?project_path=D:/projects/survey_2024&dsm=true&dtm=true&orthophoto-resolution=2"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "批处理文件执行已启动",
  "task_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

**参数说明**:
- 所有参数都通过查询字符串传递
- 无值参数（如 `fast-orthophoto`）传递空字符串或任意值
- 有值参数（如 `project_path`）传递具体的值
- 系统会自动处理参数格式转换

### 2. 查询任务状态

**端点**: `GET /api/batch/status/`

**描述**: 查询指定批处理任务的执行状态

**参数**:
- `task_id` (必选): 任务ID

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/batch/status/?task_id=550e8400-e29b-41d4-a716-446655440000"
```

**成功响应**:
```json
{
  "status": "success",
  "task": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat",
    "args": {
      "project_path": "D:/projects/drone_001",
      "fast-orthophoto": ""
    },
    "status": "running",
    "start_time": "2024-01-15T10:30:00.123456",
    "end_time": null,
    "exit_code": null,
    "log_file": "D:\\Drone_Project\\geoserverAPIDJ\\geoserverRest\\batlog\\550e8400-e29b-41d4-a716-446655440000.log"
  }
}
```

**任务状态说明**:
- `running`: 任务正在执行中
- `completed`: 任务执行完成（exit_code = 0）
- `failed`: 任务执行失败（exit_code != 0 或出现异常）

### 3. 获取任务日志

**端点**: `GET /api/batch/log/`

**描述**: 获取指定批处理任务的执行日志

**参数**:
- `task_id` (必选): 任务ID

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/batch/log/?task_id=550e8400-e29b-41d4-a716-446655440000"
```

**成功响应**:
```json
{
  "status": "success",
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "log": "开始执行批处理: D:\\Drone_Project\\ODM\\ODM\\run.bat\n命令行: D:\\Drone_Project\\ODM\\ODM\\run.bat D:/projects/drone_001 --fast-orthophoto\n开始时间: 2024-01-15T10:30:00.123456\n--------------------------------------------------\nOpenDroneMap v3.0.0\n正在处理项目: D:/projects/drone_001\n发现 45 张图片\n正在生成点云...\n进度: 25%\n正在生成正射影像...\n进度: 75%\n处理完成!\n--------------------------------------------------\n结束时间: 2024-01-15T11:45:30.789012\n退出代码: 0"
}
```

### 4. 获取所有任务状态

**端点**: `GET /api/batch/all/`

**描述**: 获取所有批处理任务的状态列表

**参数**: 无

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/batch/all/"
```

**成功响应**:
```json
{
  "status": "success",
  "count": 3,
  "tasks": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat",
      "status": "completed",
      "start_time": "2024-01-15T10:30:00.123456",
      "end_time": "2024-01-15T11:45:30.789012",
      "exit_code": 0
    },
    {
      "id": "660f9511-f3ac-52e5-b827-557766551111",
      "batch_path": "D:\\scripts\\backup.bat",
      "status": "running",
      "start_time": "2024-01-15T12:00:00.000000",
      "end_time": null,
      "exit_code": null
    },
    {
      "id": "770fa622-04bd-63f6-c938-668877662222",
      "batch_path": "D:\\scripts\\cleanup.bat",
      "status": "failed",
      "start_time": "2024-01-15T09:15:00.000000",
      "end_time": "2024-01-15T09:16:30.000000",
      "exit_code": 1
    }
  ]
}
```

## 错误处理

### 常见错误响应

**任务ID不存在**:
```json
{
  "status": "error",
  "message": "任务 550e8400-e29b-41d4-a716-446655440000 不存在"
}
```

**批处理文件不存在**:
```json
{
  "status": "error",
  "message": "批处理文件不存在: D:/nonexistent/script.bat"
}
```

**参数缺失**:
```json
{
  "status": "error",
  "message": "缺少必要参数: task_id"
}
```

**日志文件不存在**:
```json
{
  "status": "error",
  "message": "任务 550e8400-e29b-41d4-a716-446655440000 的日志不存在"
}
```

**服务器内部错误**:
```json
{
  "status": "error",
  "message": "服务器内部错误: 无法启动批处理进程"
}
```

## 使用示例

### Python客户端示例

```python
import requests
import time
import json

class BatchExecutorClient:
    def __init__(self, base_url):
        self.base_url = base_url
    
    def execute_batch(self, batch_path=None, **kwargs):
        """执行批处理文件"""
        url = f"{self.base_url}/api/batch/execute/"
        params = {}
        
        if batch_path:
            params['batch_path'] = batch_path
        
        # 添加其他参数
        params.update(kwargs)
        
        response = requests.get(url, params=params)
        return response.json()
    
    def get_task_status(self, task_id):
        """获取任务状态"""
        url = f"{self.base_url}/api/batch/status/"
        params = {'task_id': task_id}
        response = requests.get(url, params=params)
        return response.json()
    
    def get_task_log(self, task_id):
        """获取任务日志"""
        url = f"{self.base_url}/api/batch/log/"
        params = {'task_id': task_id}
        response = requests.get(url, params=params)
        return response.json()
    
    def get_all_tasks(self):
        """获取所有任务"""
        url = f"{self.base_url}/api/batch/all/"
        response = requests.get(url)
        return response.json()
    
    def wait_for_completion(self, task_id, timeout=3600, check_interval=30):
        """等待任务完成"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status_result = self.get_task_status(task_id)
            
            if status_result['status'] != 'success':
                return False, f"无法获取任务状态: {status_result['message']}"
            
            task_status = status_result['task']['status']
            
            if task_status == 'completed':
                return True, "任务执行成功"
            elif task_status == 'failed':
                return False, "任务执行失败"
            
            print(f"任务 {task_id} 仍在运行中...")
            time.sleep(check_interval)
        
        return False, "任务执行超时"

# 使用示例
client = BatchExecutorClient("http://127.0.0.1:5000")

# 1. 执行ODM批处理（使用默认参数）
print("启动ODM处理...")
result = client.execute_batch()

if result['status'] == 'success':
    task_id = result['task_id']
    print(f"任务已启动，ID: {task_id}")
    
    # 2. 监控任务进度
    success, message = client.wait_for_completion(task_id, timeout=7200, check_interval=60)
    
    if success:
        print("ODM处理完成!")
        
        # 3. 获取最终日志
        log_result = client.get_task_log(task_id)
        if log_result['status'] == 'success':
            print("执行日志:")
            print(log_result['log'][-500:])  # 显示最后500个字符
    else:
        print(f"ODM处理失败: {message}")
        
        # 获取错误日志
        log_result = client.get_task_log(task_id)
        if log_result['status'] == 'success':
            print("错误日志:")
            print(log_result['log'][-1000:])  # 显示最后1000个字符
else:
    print(f"启动失败: {result['message']}")
```

### 自定义ODM处理示例

```python
def process_drone_images(client, project_path, options=None):
    """处理无人机图像"""
    
    # 默认ODM选项
    default_options = {
        'project_path': project_path,
        'fast-orthophoto': '',  # 快速正射影像模式
        'dsm': '',              # 生成数字表面模型
        'orthophoto-resolution': '2',  # 正射影像分辨率2cm
        'verbose': ''           # 详细输出
    }
    
    if options:
        default_options.update(options)
    
    print(f"开始处理项目: {project_path}")
    print(f"处理选项: {default_options}")
    
    # 启动处理
    result = client.execute_batch(**default_options)
    
    if result['status'] != 'success':
        print(f"启动失败: {result['message']}")
        return None
    
    task_id = result['task_id']
    print(f"任务ID: {task_id}")
    
    # 实时监控进度
    def monitor_progress():
        last_log_length = 0
        
        while True:
            # 检查任务状态
            status_result = client.get_task_status(task_id)
            if status_result['status'] != 'success':
                break
            
            task_status = status_result['task']['status']
            
            # 获取新的日志内容
            log_result = client.get_task_log(task_id)
            if log_result['status'] == 'success':
                log_content = log_result['log']
                
                # 只显示新增的日志内容
                if len(log_content) > last_log_length:
                    new_content = log_content[last_log_length:]
                    print(new_content, end='')
                    last_log_length = len(log_content)
            
            if task_status in ['completed', 'failed']:
                break
            
            time.sleep(10)  # 每10秒检查一次
    
    # 开始监控
    monitor_progress()
    
    # 获取最终状态
    final_status = client.get_task_status(task_id)
    if final_status['status'] == 'success':
        task = final_status['task']
        if task['status'] == 'completed':
            print(f"\n✅ 处理完成! 退出代码: {task['exit_code']}")
            return task_id
        else:
            print(f"\n❌ 处理失败! 退出代码: {task['exit_code']}")
            return None
    
    return None

# 使用示例
task_id = process_drone_images(
    client,
    "D:/drone_projects/survey_2024_01_15",
    {
        'orthophoto-resolution': '1',  # 1cm分辨率
        'dsm': '',
        'dtm': '',
        'texturing-data-term': 'area'
    }
)

if task_id:
    print(f"处理成功完成，任务ID: {task_id}")
```

### 批量处理多个项目

```python
def batch_process_projects(client, project_list, max_concurrent=2):
    """批量处理多个项目"""
    import threading
    from queue import Queue
    
    results = {}
    project_queue = Queue()
    
    # 将项目添加到队列
    for project in project_list:
        project_queue.put(project)
    
    def worker():
        while not project_queue.empty():
            project = project_queue.get()
            try:
                print(f"开始处理项目: {project['path']}")
                
                # 启动处理
                result = client.execute_batch(**project['options'])
                
                if result['status'] == 'success':
                    task_id = result['task_id']
                    
                    # 等待完成
                    success, message = client.wait_for_completion(
                        task_id, 
                        timeout=project.get('timeout', 3600)
                    )
                    
                    results[project['name']] = {
                        'task_id': task_id,
                        'success': success,
                        'message': message
                    }
                    
                    print(f"项目 {project['name']} 处理{'成功' if success else '失败'}: {message}")
                else:
                    results[project['name']] = {
                        'success': False,
                        'message': result['message']
                    }
                    print(f"项目 {project['name']} 启动失败: {result['message']}")
                    
            except Exception as e:
                results[project['name']] = {
                    'success': False,
                    'message': str(e)
                }
                print(f"项目 {project['name']} 处理出错: {e}")
            
            finally:
                project_queue.task_done()
    
    # 启动工作线程
    threads = []
    for i in range(min(max_concurrent, len(project_list))):
        t = threading.Thread(target=worker)
        t.start()
        threads.append(t)
    
    # 等待所有线程完成
    for t in threads:
        t.join()
    
    return results

# 使用示例
projects = [
    {
        'name': 'survey_area_1',
        'path': 'D:/projects/survey_area_1',
        'options': {
            'project_path': 'D:/projects/survey_area_1',
            'fast-orthophoto': '',
            'orthophoto-resolution': '2'
        },
        'timeout': 1800  # 30分钟
    },
    {
        'name': 'survey_area_2', 
        'path': 'D:/projects/survey_area_2',
        'options': {
            'project_path': 'D:/projects/survey_area_2',
            'dsm': '',
            'dtm': '',
            'orthophoto-resolution': '1'
        },
        'timeout': 3600  # 60分钟
    }
]

batch_results = batch_process_projects(client, projects, max_concurrent=2)

# 输出结果摘要
print("\n批处理结果摘要:")
for project_name, result in batch_results.items():
    status = "✅ 成功" if result['success'] else "❌ 失败"
    print(f"{project_name}: {status} - {result['message']}")
```

## 最佳实践

### 1. 任务监控和通知

```python
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

class TaskNotifier:
    def __init__(self, smtp_server, smtp_port, username, password):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
    
    def send_notification(self, to_email, subject, message):
        """发送邮件通知"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.username
            msg['To'] = to_email
            msg['Subject'] = subject
            
            msg.attach(MIMEText(message, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.username, self.password)
            server.send_message(msg)
            server.quit()
            
            return True
        except Exception as e:
            print(f"发送邮件失败: {e}")
            return False

def monitor_task_with_notification(client, task_id, notifier, email):
    """带通知的任务监控"""
    
    # 获取任务信息
    status_result = client.get_task_status(task_id)
    if status_result['status'] != 'success':
        return
    
    task_info = status_result['task']
    project_path = task_info['args'].get('project_path', '未知项目')
    
    # 发送开始通知
    notifier.send_notification(
        email,
        f"ODM处理开始 - {project_path}",
        f"任务ID: {task_id}\n项目路径: {project_path}\n开始时间: {task_info['start_time']}"
    )
    
    # 监控任务
    success, message = client.wait_for_completion(task_id, timeout=7200)
    
    # 发送完成通知
    if success:
        notifier.send_notification(
            email,
            f"ODM处理完成 - {project_path}",
            f"任务ID: {task_id}\n项目路径: {project_path}\n处理结果: 成功\n{message}"
        )
    else:
        # 获取错误日志
        log_result = client.get_task_log(task_id)
        error_log = log_result.get('log', '无法获取日志')[-1000:]  # 最后1000字符
        
        notifier.send_notification(
            email,
            f"ODM处理失败 - {project_path}",
            f"任务ID: {task_id}\n项目路径: {project_path}\n处理结果: 失败\n错误信息: {message}\n\n错误日志:\n{error_log}"
        )
```

### 2. 任务队列管理

```python
import json
import os
from datetime import datetime

class TaskQueue:
    def __init__(self, queue_file="task_queue.json"):
        self.queue_file = queue_file
        self.load_queue()
    
    def load_queue(self):
        """加载任务队列"""
        if os.path.exists(self.queue_file):
            with open(self.queue_file, 'r', encoding='utf-8') as f:
                self.queue = json.load(f)
        else:
            self.queue = {
                'pending': [],
                'running': [],
                'completed': [],
                'failed': []
            }
    
    def save_queue(self):
        """保存任务队列"""
        with open(self.queue_file, 'w', encoding='utf-8') as f:
            json.dump(self.queue, f, ensure_ascii=False, indent=2)
    
    def add_task(self, task_config):
        """添加任务到队列"""
        task = {
            'id': None,
            'config': task_config,
            'added_time': datetime.now().isoformat(),
            'priority': task_config.get('priority', 5)  # 1-10，数字越小优先级越高
        }
        
        # 按优先级插入
        inserted = False
        for i, pending_task in enumerate(self.queue['pending']):
            if task['priority'] < pending_task['priority']:
                self.queue['pending'].insert(i, task)
                inserted = True
                break
        
        if not inserted:
            self.queue['pending'].append(task)
        
        self.save_queue()
        return len(self.queue['pending']) - 1
    
    def get_next_task(self):
        """获取下一个待执行任务"""
        if self.queue['pending']:
            return self.queue['pending'].pop(0)
        return None
    
    def update_task_status(self, task, status, task_id=None):
        """更新任务状态"""
        if task_id:
            task['id'] = task_id
        
        task['updated_time'] = datetime.now().isoformat()
        
        # 从running移动到对应状态
        if task in self.queue['running']:
            self.queue['running'].remove(task)
        
        self.queue[status].append(task)
        self.save_queue()

def queue_processor(client, task_queue, max_concurrent=2):
    """队列处理器"""
    import threading
    import time
    
    running_tasks = []
    
    def process_task(task):
        """处理单个任务"""
        try:
            # 启动任务
            result = client.execute_batch(**task['config'])
            
            if result['status'] == 'success':
                task_id = result['task_id']
                task_queue.update_task_status(task, 'running', task_id)
                
                # 等待完成
                success, message = client.wait_for_completion(task_id)
                
                if success:
                    task_queue.update_task_status(task, 'completed')
                else:
                    task_queue.update_task_status(task, 'failed')
            else:
                task_queue.update_task_status(task, 'failed')
                
        except Exception as e:
            print(f"处理任务出错: {e}")
            task_queue.update_task_status(task, 'failed')
        
        finally:
            # 从运行列表中移除
            if task in running_tasks:
                running_tasks.remove(task)
    
    # 主循环
    while True:
        # 检查是否可以启动新任务
        if len(running_tasks) < max_concurrent:
            next_task = task_queue.get_next_task()
            
            if next_task:
                running_tasks.append(next_task)
                
                # 在新线程中处理任务
                thread = threading.Thread(target=process_task, args=(next_task,))
                thread.daemon = True
                thread.start()
        
        time.sleep(5)  # 每5秒检查一次队列
```

### 3. 资源监控

```python
import psutil
import time

def monitor_system_resources(threshold_cpu=80, threshold_memory=80):
    """监控系统资源使用情况"""
    
    while True:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用率
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # 磁盘使用率
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        
        print(f"系统资源: CPU {cpu_percent:.1f}%, 内存 {memory_percent:.1f}%, 磁盘 {disk_percent:.1f}%")
        
        # 检查阈值
        if cpu_percent > threshold_cpu:
            print(f"⚠️  CPU使用率过高: {cpu_percent:.1f}%")
        
        if memory_percent > threshold_memory:
            print(f"⚠️  内存使用率过高: {memory_percent:.1f}%")
        
        time.sleep(30)  # 每30秒检查一次

# 在后台线程中运行资源监控
import threading
monitor_thread = threading.Thread(target=monitor_system_resources)
monitor_thread.daemon = True
monitor_thread.start()
```

## 故障排除

### 常见问题和解决方案

1. **批处理文件无法执行**
   - 检查文件路径是否正确
   - 确认文件具有执行权限
   - 验证批处理文件语法

2. **任务长时间运行无响应**
   - 检查系统资源使用情况
   - 查看任务日志了解进度
   - 考虑增加超时时间

3. **日志文件过大**
   - 实现日志轮转机制
   - 定期清理旧日志文件
   - 压缩存储历史日志

4. **内存不足导致任务失败**
   - 监控系统内存使用
   - 减少并发任务数量
   - 优化批处理脚本

### 调试工具

```python
def debug_task_execution(client, task_id):
    """调试任务执行问题"""
    
    # 获取任务详细信息
    status_result = client.get_task_status(task_id)
    if status_result['status'] != 'success':
        print(f"无法获取任务状态: {status_result['message']}")
        return
    
    task = status_result['task']
    print(f"任务ID: {task['id']}")
    print(f"批处理文件: {task['batch_path']}")
    print(f"参数: {task['args']}")
    print(f"状态: {task['status']}")
    print(f"开始时间: {task['start_time']}")
    print(f"结束时间: {task.get('end_time', '未结束')}")
    print(f"退出代码: {task.get('exit_code', '未知')}")
    
    # 检查批处理文件
    batch_path = task['batch_path']
    if os.path.exists(batch_path):
        print(f"✅ 批处理文件存在: {batch_path}")
        
        # 检查文件权限
        if os.access(batch_path, os.X_OK):
            print("✅ 批处理文件可执行")
        else:
            print("❌ 批处理文件无执行权限")
    else:
        print(f"❌ 批处理文件不存在: {batch_path}")
    
    # 检查项目路径
    project_path = task['args'].get('project_path')
    if project_path:
        if os.path.exists(project_path):
            print(f"✅ 项目路径存在: {project_path}")
            
            # 检查项目内容
            try:
                files = os.listdir(project_path)
                image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.tif', '.tiff'))]
                print(f"项目包含 {len(image_files)} 个图像文件")
            except Exception as e:
                print(f"无法读取项目目录: {e}")
        else:
            print(f"❌ 项目路径不存在: {project_path}")
    
    # 获取并分析日志
    log_result = client.get_task_log(task_id)
    if log_result['status'] == 'success':
        log_content = log_result['log']
        
        # 分析日志中的关键信息
        if 'error' in log_content.lower():
            print("⚠️  日志中发现错误信息")
        
        if 'exception' in log_content.lower():
            print("⚠️  日志中发现异常信息")
        
        if 'completed' in log_content.lower():
            print("✅ 日志显示任务已完成")
        
        # 显示最后几行日志
        log_lines = log_content.split('\n')
        print("\n最后10行日志:")
        for line in log_lines[-10:]:
            if line.strip():
                print(f"  {line}")
    else:
        print(f"无法获取日志: {log_result['message']}")
```

批处理执行API文档已完成，涵盖了所有4个接口的详细说明、使用示例、最佳实践和故障排除。接下来我将创建TIF处理API文档。
