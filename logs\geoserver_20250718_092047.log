2025-07-18 09:20:47,113 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250718_092047.log
2025-07-18 09:20:47,115 - geo_publisher - INFO - 加载了 0 个任务状态
2025-07-18 09:20:47,136 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 09:20:47,136 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 09:20:47,158 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 09:20:47,168 - root - INFO - === GeoServer REST API服务 ===
2025-07-18 09:20:47,168 - root - INFO - 主机: 0.0.0.0
2025-07-18 09:20:47,169 - root - INFO - 端口: 5083
2025-07-18 09:20:47,170 - root - INFO - 调试模式: 禁用
2025-07-18 09:20:47,171 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-18 09:20:47,280 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-18 09:20:47,282 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 09:21:18,853 - geo_publisher - INFO - 启动GeoTIFF发布任务 b84f98a5-2fa7-4f28-8c75-a9bea5862fb3: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=tttt
2025-07-18 09:21:18,854 - geo_task_b84f98a5-2fa7-4f28-8c75-a9bea5862fb3 - INFO - GeoServer发布任务 b84f98a5-2fa7-4f28-8c75-a9bea5862fb3 开始执行
2025-07-18 09:21:18,864 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 09:21:18] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=tttt HTTP/1.1" 200 -
2025-07-18 09:21:18,865 - geo_task_b84f98a5-2fa7-4f28-8c75-a9bea5862fb3 - INFO - 开始时间: 2025-07-18 09:21:18
2025-07-18 09:21:18,869 - geo_task_b84f98a5-2fa7-4f28-8c75-a9bea5862fb3 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "tttt",
  "store_name": null,
  "layer_name": null
}
2025-07-18 09:21:18,872 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 09:21:18,873 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 09:21:18,898 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 09:21:18,900 - root - INFO - 未提供存储名称，使用文件名: 20250705171600
2025-07-18 09:21:18,902 - root - INFO - 未提供图层名称，使用存储名称: 20250705171600
2025-07-18 09:21:18,913 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 09:21:18] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-18 09:21:18,950 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171600\20250705171600.tif
2025-07-18 09:22:09,105 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 09:22:09] "GET /api/geo/status?task_id=b84f98a5-2fa7-4f28-8c75-a9bea5862fb3 HTTP/1.1" 200 -
2025-07-18 09:22:49,325 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 09:22:49] "GET /api/geo/status?task_id=b84f98a5-2fa7-4f28-8c75-a9bea5862fb3 HTTP/1.1" 200 -
