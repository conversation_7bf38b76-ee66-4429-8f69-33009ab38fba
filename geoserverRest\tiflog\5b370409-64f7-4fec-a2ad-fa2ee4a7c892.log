2025-07-24 16:27:09,302 - INFO - ============ TIF处理任务 5b370409-64f7-4fec-a2ad-fa2ee4a7c892 开始执行 ============
2025-07-24 16:27:09,304 - INFO - 开始时间: 2025-07-24 16:27:09
2025-07-24 16:27:09,307 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 16:27:09,309 - INFO - 系统信息:
2025-07-24 16:27:09,309 - INFO -   操作系统: Windows 10.0.19045
2025-07-24 16:27:09,310 - INFO -   Python版本: 3.8.20
2025-07-24 16:27:09,311 - INFO -   GDAL版本: 3.9.2
2025-07-24 16:27:09,311 - INFO -   GPU可用: 否
2025-07-24 16:27:09,313 - INFO - 检查参数有效性...
2025-07-24 16:27:09,314 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:27:09,315 - INFO - 开始执行TIF处理流程...
2025-07-24 16:27:09,315 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:27:09,317 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-24 16:27:09,318 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-24 16:27:14,440 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (16:27:14)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-24 16:27:14,444 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-24 16:27:14,715 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:05,  3.71it/s]
2025-07-24 16:27:14,937 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 18.74it/s]
2025-07-24 16:27:15,072 - ERROR - 
处理数据块:  52%|####################################6                                 | 11/21 [00:00<00:00, 19.81it/s]
2025-07-24 16:27:15,222 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 22.15it/s]
2025-07-24 16:27:15,329 - ERROR - 
处理数据块:  86%|############################################################          | 18/21 [00:00<00:00, 23.63it/s]
2025-07-24 16:27:15,330 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 23.74it/s]
2025-07-24 16:27:15,331 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-24 16:27:15,332 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-24 16:27:15,372 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 523.92it/s]
2025-07-24 16:27:16,494 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.12 秒 (16:27:16)预计总处理时间: 约 6.36 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (16:27:16)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-24 16:27:16,509 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-24 16:27:20,909 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-24 16:27:20,911 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:27:20,912 - ERROR - [A
2025-07-24 16:27:21,028 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:00, 25.95it/s]
2025-07-24 16:27:21,029 - ERROR - [A
2025-07-24 16:27:21,130 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:00, 44.68it/s]
2025-07-24 16:27:21,130 - ERROR - [A
2025-07-24 16:27:21,234 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:00<00:00, 54.73it/s]
2025-07-24 16:27:21,234 - ERROR - [A
2025-07-24 16:27:21,308 - ERROR - [A
2025-07-24 16:28:08,949 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:52<01:44, 52.44s/it]
2025-07-24 16:28:13,977 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-24 16:28:13,979 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:28:13,979 - ERROR - [A
2025-07-24 16:28:14,187 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:04,  4.85it/s]
2025-07-24 16:28:14,188 - ERROR - [A
2025-07-24 16:28:14,295 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:02,  6.77it/s]
2025-07-24 16:28:14,295 - ERROR - [A
2025-07-24 16:28:14,421 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  7.25it/s]
2025-07-24 16:28:14,421 - ERROR - [A
2025-07-24 16:28:14,606 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  6.40it/s]
2025-07-24 16:28:14,606 - ERROR - [A
2025-07-24 16:28:14,809 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:01,  7.85it/s]
2025-07-24 16:28:14,810 - ERROR - [A
2025-07-24 16:28:14,990 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:01,  8.97it/s]
2025-07-24 16:28:14,990 - ERROR - [A
2025-07-24 16:28:15,108 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  8.85it/s]
2025-07-24 16:28:15,108 - ERROR - [A
2025-07-24 16:28:15,274 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:01,  9.90it/s]
2025-07-24 16:28:15,276 - ERROR - [A
2025-07-24 16:28:15,459 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:00, 10.21it/s]
2025-07-24 16:28:15,461 - ERROR - [A
2025-07-24 16:28:15,595 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:01<00:00, 11.40it/s]
2025-07-24 16:28:15,596 - ERROR - [A
2025-07-24 16:28:19,147 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:05<00:02,  1.56it/s]
2025-07-24 16:28:19,148 - ERROR - [A
2025-07-24 16:28:22,610 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:08<00:03,  1.18s/it]
2025-07-24 16:28:22,611 - ERROR - [A
2025-07-24 16:28:27,152 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:13<00:03,  1.90s/it]
2025-07-24 16:28:27,153 - ERROR - [A
2025-07-24 16:28:32,013 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:18<00:02,  2.59s/it]
2025-07-24 16:28:32,013 - ERROR - [A
2025-07-24 16:28:33,113 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:19<00:00,  2.22s/it]
2025-07-24 16:28:33,114 - ERROR - [A
2025-07-24 16:28:33,115 - ERROR - [A
2025-07-24 16:29:45,039 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [02:28<01:18, 78.12s/it]
2025-07-24 16:30:00,669 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-24 16:30:00,670 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:30:00,671 - ERROR - [A
2025-07-24 16:30:00,961 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:05,  3.45it/s]
2025-07-24 16:30:00,963 - ERROR - [A
2025-07-24 16:30:01,074 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:03,  5.38it/s]
2025-07-24 16:30:01,075 - ERROR - [A
2025-07-24 16:30:01,186 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  6.57it/s]
2025-07-24 16:30:01,187 - ERROR - [A
2025-07-24 16:30:01,299 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  7.33it/s]
2025-07-24 16:30:01,300 - ERROR - [A
2025-07-24 16:30:01,422 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:02,  7.60it/s]
2025-07-24 16:30:01,423 - ERROR - [A
2025-07-24 16:30:01,553 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:00, 15.41it/s]
2025-07-24 16:30:01,554 - ERROR - [A
2025-07-24 16:30:01,678 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:00, 20.60it/s]
2025-07-24 16:30:01,679 - ERROR - [A
2025-07-24 16:30:01,789 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [00:01<00:00, 25.01it/s]
2025-07-24 16:30:01,790 - ERROR - [A
2025-07-24 16:30:01,847 - ERROR - [A
2025-07-24 16:32:08,087 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [04:51<00:00, 107.77s/it]
2025-07-24 16:32:08,089 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:51<00:00, 97.19s/it]
2025-07-24 16:33:11,129 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 292.12 秒 (16:32:08)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (16:32:08)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-24 16:33:11,151 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-24 16:33:11,508 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.81it/s]
2025-07-24 16:33:11,510 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.80it/s]
2025-07-24 16:33:11,516 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-24 16:33:23,359 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-24 16:33:23,360 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-24 16:33:24,476 - ERROR - 
处理轮廓:   3%|##3                                                                      | 1/31 [00:01<00:33,  1.12s/it]
2025-07-24 16:33:24,644 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:01<00:16,  1.79it/s]
2025-07-24 16:33:24,776 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:10,  2.75it/s]
2025-07-24 16:33:24,811 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 21.37it/s]
2025-07-24 16:33:25,728 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-24 16:33:25,729 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-24 16:33:26,649 - ERROR - 
写入多边形:   6%|####                                                                   | 2/35 [00:00<00:15,  2.18it/s]
2025-07-24 16:33:27,552 - ERROR - 
写入多边形:   9%|######                                                                 | 3/35 [00:01<00:20,  1.55it/s]
2025-07-24 16:33:29,044 - ERROR - 
写入多边形:  31%|######################                                                | 11/35 [00:03<00:06,  3.76it/s]
2025-07-24 16:33:29,637 - ERROR - 
写入多边形:  34%|########################                                              | 12/35 [00:03<00:07,  3.25it/s]
2025-07-24 16:33:29,645 - ERROR - 
写入多边形: 100%|######################################################################| 35/35 [00:03<00:00,  8.94it/s]
2025-07-24 16:33:29,875 - INFO - 处理完成，耗时: 380.56 秒 (6.34 分钟)
2025-07-24 16:33:29,876 - INFO - 处理结果: 成功
2025-07-24 16:33:29,877 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-24 16:33:29,878 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-24 16:33:29,882 - INFO - TIF处理任务 5b370409-64f7-4fec-a2ad-fa2ee4a7c892 执行成功
2025-07-24 16:33:29,883 - INFO - 完成时间: 2025-07-24 16:33:29
2025-07-24 16:33:29,886 - INFO - 状态: 运行成功
2025-07-24 16:33:29,886 - INFO - ============ 任务执行结束 ============
