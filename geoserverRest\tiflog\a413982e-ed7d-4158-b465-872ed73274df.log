2025-07-18 12:51:50,480 - INFO - TIF处理任务 a413982e-ed7d-4158-b465-872ed73274df 开始执行
2025-07-18 12:51:50,486 - INFO - 开始时间: 2025-07-18 12:51:50
2025-07-18 12:51:50,496 - INFO - 处理参数: {
  "input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-18 12:51:50,522 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-18 12:51:59,068 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171600/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 30192x36956x3估计内存使用: 总计 16.63 GB, 单个处理块 460.69 MB开始创建掩码... (12:51:58)使用全图检测无效区域模式...将处理 37 个数据块...使用多线程处理 (7 线程)...
2025-07-18 12:51:59,074 - ERROR - 
处理数据块:   0%|                                                                               | 0/37 [00:00<?, ?it/s]
2025-07-18 12:51:59,502 - ERROR - 
处理数据块:   3%|#9                                                                     | 1/37 [00:00<00:14,  2.54it/s]
2025-07-18 12:51:59,848 - ERROR - 
处理数据块:  22%|###############3                                                       | 8/37 [00:00<00:02, 12.38it/s]
2025-07-18 12:52:00,097 - ERROR - 
处理数据块:  27%|##################9                                                   | 10/37 [00:00<00:02, 10.79it/s]
2025-07-18 12:52:00,480 - ERROR - 
处理数据块:  41%|############################3                                         | 15/37 [00:01<00:01, 11.80it/s]
2025-07-18 12:52:00,662 - ERROR - 
处理数据块:  51%|###################################9                                  | 19/37 [00:01<00:01, 14.17it/s]
2025-07-18 12:52:01,075 - ERROR - 
处理数据块:  59%|#########################################6                            | 22/37 [00:01<00:01, 11.20it/s]
2025-07-18 12:52:01,258 - ERROR - 
处理数据块:  70%|#################################################1                    | 26/37 [00:02<00:00, 13.50it/s]
2025-07-18 12:52:01,431 - ERROR - 
处理数据块:  76%|####################################################9                 | 28/37 [00:02<00:00, 13.06it/s]
2025-07-18 12:52:01,636 - ERROR - 
处理数据块:  81%|########################################################7             | 30/37 [00:02<00:00, 12.13it/s]
2025-07-18 12:52:01,743 - ERROR - 
处理数据块:  89%|##############################################################4       | 33/37 [00:02<00:00, 14.84it/s]
2025-07-18 12:52:01,878 - ERROR - 
处理数据块:  95%|##################################################################2   | 35/37 [00:02<00:00, 14.85it/s]
2025-07-18 12:52:01,909 - ERROR - 
处理数据块: 100%|######################################################################| 37/37 [00:02<00:00, 13.21it/s]
2025-07-18 12:52:01,913 - INFO - 已完成: 10/37 块 (27.0%)已完成: 20/37 块 (54.1%)已完成: 30/37 块 (81.1%)已完成: 37/37 块 (100.0%)合并处理结果...
2025-07-18 12:52:01,914 - ERROR - 
合并结果:   0%|                                                                                 | 0/37 [00:00<?, ?it/s]
2025-07-18 12:52:02,018 - ERROR - 
合并结果:  78%|#######################################################6               | 29/37 [00:00<00:00, 288.47it/s]
2025-07-18 12:52:02,045 - ERROR - 
合并结果: 100%|#######################################################################| 37/37 [00:00<00:00, 290.13it/s]
2025-07-18 12:52:05,101 - INFO - 合并进度: 20/37 块 (54.1%)合并进度: 37/37 块 (100.0%)统计结果: 总像素 1115775552, 有效像素 250536219 (22.45%), 无效像素 865239333 (77.55%)掩码创建完成，耗时: 6.32 秒 (12:52:05)预计总处理时间: 约 18.96 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif (12:52:05)写入带有nodata值的影像...
2025-07-18 12:52:05,102 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-18 12:52:35,492 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/37 [00:00<?, ?it/s]
2025-07-18 12:52:35,493 - ERROR - [A
2025-07-18 12:52:35,613 - ERROR - 
波段 1/3 写入进度:   5%|###4                                                            | 2/37 [00:00<00:02, 17.39it/s]
2025-07-18 12:52:35,613 - ERROR - [A
2025-07-18 12:52:35,714 - ERROR - 
波段 1/3 写入进度:  22%|#############8                                                  | 8/37 [00:00<00:00, 40.72it/s]
2025-07-18 12:52:35,715 - ERROR - [A
2025-07-18 12:52:35,827 - ERROR - 
波段 1/3 写入进度:  38%|#######################8                                       | 14/37 [00:00<00:00, 46.35it/s]
2025-07-18 12:52:35,828 - ERROR - [A
2025-07-18 12:52:49,094 - ERROR - 
波段 1/3 写入进度:  38%|#######################8                                       | 14/37 [00:13<00:00, 46.35it/s]
2025-07-18 12:52:49,100 - ERROR - [A
2025-07-18 12:52:50,901 - ERROR - 
波段 1/3 写入进度:  49%|##############################6                                | 18/37 [00:15<00:24,  1.29s/it]
2025-07-18 12:52:50,902 - ERROR - [A
2025-07-18 12:52:56,761 - ERROR - 
波段 1/3 写入进度:  51%|################################3                              | 19/37 [00:21<00:32,  1.78s/it]
2025-07-18 12:52:56,762 - ERROR - [A
2025-07-18 12:53:07,785 - ERROR - 
波段 1/3 写入进度:  59%|#####################################4                         | 22/37 [00:32<00:35,  2.38s/it]
2025-07-18 12:53:07,785 - ERROR - [A
2025-07-18 12:53:12,995 - ERROR - 
波段 1/3 写入进度:  62%|#######################################1                       | 23/37 [00:37<00:38,  2.75s/it]
2025-07-18 12:53:12,997 - ERROR - [A
2025-07-18 12:53:19,428 - ERROR - 
波段 1/3 写入进度:  68%|##########################################5                    | 25/37 [00:43<00:34,  2.88s/it]
2025-07-18 12:53:19,428 - ERROR - [A
2025-07-18 12:53:39,166 - ERROR - 
波段 1/3 写入进度:  68%|##########################################5                    | 25/37 [01:03<00:34,  2.88s/it]
2025-07-18 12:53:39,295 - ERROR - [A
2025-07-18 12:54:14,951 - ERROR - 
波段 1/3 写入进度:  70%|############################################2                  | 26/37 [01:39<02:05, 11.44s/it]
2025-07-18 12:54:14,951 - ERROR - [A
2025-07-18 12:55:39,579 - ERROR - 
波段 1/3 写入进度:  73%|#############################################9                 | 27/37 [03:04<04:12, 25.24s/it]
2025-07-18 12:55:39,580 - ERROR - [A
2025-07-18 12:56:41,926 - ERROR - 
波段 1/3 写入进度:  76%|###############################################6               | 28/37 [04:06<04:58, 33.12s/it]
2025-07-18 12:56:41,927 - ERROR - [A
2025-07-18 12:56:54,134 - ERROR - 
波段 1/3 写入进度:  78%|#################################################3             | 29/37 [04:18<03:46, 28.25s/it]
2025-07-18 12:56:54,135 - ERROR - [A
2025-07-18 12:57:05,705 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 30/37 [04:30<02:48, 24.09s/it]
2025-07-18 12:57:05,708 - ERROR - [A
2025-07-18 12:57:20,815 - ERROR - 
波段 1/3 写入进度:  84%|####################################################7          | 31/37 [04:45<02:10, 21.73s/it]
2025-07-18 12:57:20,816 - ERROR - [A
2025-07-18 12:57:32,628 - ERROR - 
波段 1/3 写入进度:  86%|######################################################4        | 32/37 [04:57<01:35, 19.02s/it]
2025-07-18 12:57:32,629 - ERROR - [A
2025-07-18 12:57:45,309 - ERROR - 
波段 1/3 写入进度:  89%|########################################################1      | 33/37 [05:09<01:08, 17.25s/it]
2025-07-18 12:57:45,311 - ERROR - [A
2025-07-18 12:58:00,503 - ERROR - 
波段 1/3 写入进度:  92%|#########################################################8     | 34/37 [05:25<00:49, 16.66s/it]
2025-07-18 12:58:00,504 - ERROR - [A
2025-07-18 12:58:12,775 - ERROR - 
波段 1/3 写入进度:  95%|###########################################################5   | 35/37 [05:37<00:30, 15.39s/it]
2025-07-18 12:58:12,786 - ERROR - [A
2025-07-18 12:58:27,662 - ERROR - 
波段 1/3 写入进度:  97%|#############################################################2 | 36/37 [05:52<00:15, 15.24s/it]
2025-07-18 12:58:27,663 - ERROR - [A
2025-07-18 12:58:35,674 - ERROR - 
波段 1/3 写入进度: 100%|###############################################################| 37/37 [06:00<00:00, 13.11s/it]
2025-07-18 12:58:35,675 - ERROR - [A
2025-07-18 12:58:35,678 - ERROR - [A
2025-07-18 12:59:51,276 - ERROR - 
处理波段:  33%|########################3                                                | 1/3 [07:46<15:32, 466.17s/it]
2025-07-18 13:13:03,592 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [20:58<10:58, 658.02s/it]
2025-07-18 13:28:45,047 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [36:39<00:00, 787.44s/it]
2025-07-18 13:28:45,048 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [36:39<00:00, 733.31s/it]
2025-07-18 13:32:50,737 - INFO - 创建掩码，识别有效区域...轻微平滑处理掩码，减少边缘锯齿...寻找有效区域轮廓...找到 2 个初始轮廓使用所有 2 个轮廓，不进行面积过滤转换轮廓为地理坐标...
2025-07-18 13:32:51,009 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/2 [00:00<?, ?it/s]
2025-07-18 13:32:52,059 - ERROR - 
处理轮廓: 100%|##########################################################################| 2/2 [00:01<00:00,  1.91it/s]
2025-07-18 13:32:52,060 - ERROR - 
处理轮廓: 100%|##########################################################################| 2/2 [00:01<00:00,  1.91it/s]
2025-07-18 13:32:52,266 - INFO - 成功创建 2 个多边形合并多边形...创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.shp写入shapefile特征...处理MultiPolygon，包含 2 个多边形
2025-07-18 13:32:52,268 - ERROR - 
写入多边形:   0%|                                                                                | 0/2 [00:00<?, ?it/s]
2025-07-18 13:32:52,302 - ERROR - 
写入多边形: 100%|########################################################################| 2/2 [00:00<00:00, 70.71it/s]
2025-07-18 13:32:57,902 - INFO - TIF处理任务 a413982e-ed7d-4158-b465-872ed73274df 执行成功
2025-07-18 13:32:57,902 - INFO - 完成时间: 2025-07-18 13:32:57
2025-07-18 13:32:57,905 - INFO - 状态: 运行成功
