'''
Author: 吴博文 <EMAIL>
Date: 2025-07-02 09:05:17
LastEditors: 吴博文 <EMAIL>
LastEditTime: 2025-07-02 17:26:41
FilePath: \geoserverapi\coordinate_query\example_client.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
Description: GeoServer栅格数据坐标查询API客户端示例
'''

import requests
import json
import sys
import os

def main():
    """客户端示例主函数"""
    # API基础URL
    base_url = "http://localhost:5000"
    
    print("GeoServer栅格数据坐标查询API客户端示例")
    print(f"API基础URL: {base_url}\n")
    
    try:
        # 示例1：健康检查
        print("1. 执行健康检查...")
        health_response = requests.get(f"{base_url}/health")
        print("响应:", health_response.json())
        print()
        
        # 示例2：获取工作区中的栅格图层
        workspace = "my_workspace"  # 根据实际情况修改
        print(f"2. 获取工作区 '{workspace}' 中的栅格图层...")
        
        try:
            layers_response = requests.get(f"{base_url}/api/layers?workspace={workspace}")
            layers_data = layers_response.json()
            
            if layers_response.status_code == 200 and layers_data.get('status') == 'success':
                layer_count = layers_data.get('count', 0)
                print(f"找到 {layer_count} 个栅格图层:")
                
                # 只打印前5个图层
                for i, layer in enumerate(layers_data.get('layers', [])[:5]):
                    print(f"  {i+1}. {layer['name']} (存储: {layer['store']})")
                    
                if layer_count > 5:
                    print(f"  ... 以及其他 {layer_count - 5} 个图层")
            else:
                print(f"获取图层失败: {layers_data.get('message', '未知错误')}")
                
        except requests.RequestException as e:
            print(f"请求失败: {str(e)}")
        print()
            
        # 示例3：查询坐标点
        # 使用南宁市附近的坐标作为示例（请根据实际数据修改）
        lat = 22.8167
        lon = 108.3667
        
        print(f"3. 查询坐标点 ({lat}, {lon}) 在工作区 '{workspace}' 中的图层...")
        
        try:
            # 使用GET请求查询坐标点
            query_url = f"{base_url}/api/query?lat={lat}&lon={lon}&workspace={workspace}"
            print(f"请求URL: {query_url}")
            
            query_response = requests.get(query_url)
            query_data = query_response.json()
            
            if query_response.status_code == 200 and query_data.get('status') == 'success':
                layer_count = query_data.get('count', 0)
                print(f"在该坐标点找到 {layer_count} 个有效图层:")
                
                # 打印所有找到的图层
                for i, layer in enumerate(query_data.get('layers', [])):
                    print(f"  {i+1}. {layer['name']} (存储: {layer['store']})")
                    
                    # 打印边界框信息
                    bbox = layer.get('bbox', {})
                    if bbox:
                        print(f"     边界框: ({bbox.get('minx', 'N/A')}, {bbox.get('miny', 'N/A')}) - "
                              f"({bbox.get('maxx', 'N/A')}, {bbox.get('maxy', 'N/A')})")
                    print()
            else:
                print(f"查询失败: {query_data.get('message', '未知错误')}")
                
        except requests.RequestException as e:
            print(f"请求失败: {str(e)}")
        
    except Exception as e:
        print(f"程序错误: {str(e)}")
        return 1
        
    print("\n演示完成")
    return 0

if __name__ == "__main__":
    sys.exit(main()) 