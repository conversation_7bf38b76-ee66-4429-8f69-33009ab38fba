2025-07-01 16:44:30,313 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250701_164430.log
2025-07-01 16:44:30,331 - root - INFO - 开始执行命令: publish-shapefile-directory
2025-07-01 16:44:30,331 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-01 16:44:30,332 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-01 16:44:30,378 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-01 16:44:30,380 - root - INFO - 在目录 'data/20250701/testshp' 中找到 5 个Shapefile文件
2025-07-01 16:44:30,380 - root - INFO - 使用字符集: UTF-8
2025-07-01 16:44:30,382 - root - INFO - 未提供存储名称，使用目录名称作为前缀: testshp
2025-07-01 16:44:30,393 - root - INFO - 工作区 'test_workspace' 不存在，正在创建...
2025-07-01 16:44:30,431 - root - INFO - 成功创建工作区 'test_workspace'
2025-07-01 16:44:30,444 - root - INFO - 在工作区 'test_workspace' 中找到 0 个图层
2025-07-01 16:44:30,445 - root - INFO - 发布前工作区 'test_workspace' 中有 0 个图层
2025-07-01 16:44:30,445 - root - INFO - 正在发布Shapefile: 招商引资片区.shp, 存储名: testshp
2025-07-01 16:44:30,461 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区.shp
2025-07-01 16:44:30,462 - root - INFO - 添加文件到ZIP: 招商引资片区.shp
2025-07-01 16:44:30,580 - root - INFO - 添加文件到ZIP: 招商引资片区.dbf
2025-07-01 16:44:30,582 - root - INFO - 添加文件到ZIP: 招商引资片区.shx
2025-07-01 16:44:30,584 - root - INFO - 添加文件到ZIP: 招商引资片区.prj
2025-07-01 16:44:30,585 - root - INFO - 添加文件到ZIP: 招商引资片区.qix
2025-07-01 16:44:30,586 - root - INFO - 添加文件到ZIP: 招商引资片区.cpg
2025-07-01 16:44:30,587 - root - INFO - 添加文件到ZIP: 招商引资片区.qmd
2025-07-01 16:44:30,588 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:44:30,588 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:44:30,727 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:44:30,781 - root - INFO - 成功创建图层 '招商引资片区'
2025-07-01 16:44:30,794 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/招商引资片区
2025-07-01 16:44:30,794 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区.shp' 成功发布为图层 'test_workspace:招商引资片区'
2025-07-01 16:44:30,796 - root - INFO - 正在发布Shapefile: 招商引资片区交种植区.shp, 存储名: testshp
2025-07-01 16:44:30,810 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区交种植区.shp
2025-07-01 16:44:30,811 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shp
2025-07-01 16:44:31,474 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.dbf
2025-07-01 16:44:31,643 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shx
2025-07-01 16:44:31,657 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.prj
2025-07-01 16:44:31,658 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qix
2025-07-01 16:44:31,677 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.cpg
2025-07-01 16:44:31,678 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qmd
2025-07-01 16:44:31,679 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:44:31,680 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:44:31,877 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:44:31,892 - root - INFO - 图层 '招商引资片区交种植区' 已存在
2025-07-01 16:44:31,908 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/招商引资片区交种植区
2025-07-01 16:44:31,922 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区交种植区.shp' 成功发布为图层 'test_workspace:招商引资片区交种植区'
2025-07-01 16:44:31,924 - root - INFO - 正在发布Shapefile: 招商引资片区交设施农用地.shp, 存储名: testshp
2025-07-01 16:44:31,938 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区交设施农用地.shp
2025-07-01 16:44:31,939 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shp
2025-07-01 16:44:32,082 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.dbf
2025-07-01 16:44:32,102 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shx
2025-07-01 16:44:32,104 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.prj
2025-07-01 16:44:32,105 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qix
2025-07-01 16:44:32,107 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.cpg
2025-07-01 16:44:32,108 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qmd
2025-07-01 16:44:32,109 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:44:32,110 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:44:32,241 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:44:32,336 - root - INFO - 成功创建图层 '招商引资片区交设施农用地'
2025-07-01 16:44:32,350 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/招商引资片区交设施农用地
2025-07-01 16:44:32,350 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区交设施农用地.shp' 成功发布为图层 'test_workspace:招商引资片区交设施农用地'
2025-07-01 16:44:32,352 - root - INFO - 正在发布Shapefile: 种植区.shp, 存储名: testshp
2025-07-01 16:44:32,363 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\种植区.shp
2025-07-01 16:44:32,364 - root - INFO - 添加文件到ZIP: 种植区.shp
2025-07-01 16:44:33,603 - root - INFO - 添加文件到ZIP: 种植区.dbf
2025-07-01 16:44:33,847 - root - INFO - 添加文件到ZIP: 种植区.shx
2025-07-01 16:44:33,877 - root - INFO - 添加文件到ZIP: 种植区.prj
2025-07-01 16:44:33,878 - root - INFO - 添加文件到ZIP: 种植区.qix
2025-07-01 16:44:33,903 - root - INFO - 添加文件到ZIP: 种植区.cpg
2025-07-01 16:44:33,904 - root - INFO - 添加文件到ZIP: 种植区.qmd
2025-07-01 16:44:33,905 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:44:33,905 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:44:34,158 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:44:34,221 - root - INFO - 成功创建图层 '种植区'
2025-07-01 16:44:34,237 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/种植区
2025-07-01 16:44:34,237 - root - INFO - Shapefile 'data/20250701/testshp\种植区.shp' 成功发布为图层 'test_workspace:种植区'
2025-07-01 16:44:34,240 - root - INFO - 正在发布Shapefile: 设施农用地潜力.shp, 存储名: testshp
2025-07-01 16:44:34,254 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\设施农用地潜力.shp
2025-07-01 16:44:34,255 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shp
2025-07-01 16:44:35,310 - root - INFO - 添加文件到ZIP: 设施农用地潜力.dbf
2025-07-01 16:44:35,376 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shx
2025-07-01 16:44:35,385 - root - INFO - 添加文件到ZIP: 设施农用地潜力.prj
2025-07-01 16:44:35,386 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qix
2025-07-01 16:44:35,397 - root - INFO - 添加文件到ZIP: 设施农用地潜力.cpg
2025-07-01 16:44:35,398 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qmd
2025-07-01 16:44:35,399 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:44:35,400 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:44:35,638 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:44:35,725 - root - INFO - 成功创建图层 '设施农用地潜力'
2025-07-01 16:44:35,738 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/设施农用地潜力
2025-07-01 16:44:35,739 - root - INFO - Shapefile 'data/20250701/testshp\设施农用地潜力.shp' 成功发布为图层 'test_workspace:设施农用地潜力'
2025-07-01 16:44:35,754 - root - INFO - 在工作区 'test_workspace' 中找到 5 个图层
2025-07-01 16:44:35,754 - root - INFO - 发布后工作区 'test_workspace' 中有 5 个图层
2025-07-01 16:44:35,755 - root - INFO - 新增了 5 个图层: 招商引资片区交设施农用地, 种植区, 招商引资片区, 招商引资片区交种植区, 设施农用地潜力
2025-07-01 16:44:35,755 - root - INFO - 成功发布了 5 个Shapefile文件
2025-07-01 16:44:35,755 - root - INFO - 成功发布: 5, 失败: 0
2025-07-01 16:44:35,755 - root - INFO - 命令执行完成: publish-shapefile-directory
