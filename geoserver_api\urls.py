"""
GeoServer API URL配置
"""

from django.urls import path, include
from . import views

# 主要API端点
urlpatterns = [
    # 健康检查
    path('health/', views.health_check, name='health_check'),
    
    # 栅格查询API
    path('api/query/', views.query_coordinate, name='query_coordinate'),
    path('api/layers/', views.get_layers, name='get_layers'),
    path('api/test/', views.test_coordinate, name='test_coordinate'),
    path('api/pixel/', views.get_pixel_value, name='get_pixel_value'),
    path('api/query-layers/', views.query_layers_at_coordinate, name='query_layers_at_coordinate'),
    
    # 管理API
    path('api/management/', include([
        path('workspaces/', views.get_workspaces, name='get_workspaces'),
        path('workspaces/create/', views.create_workspace, name='create_workspace'),
        path('workspaces/delete/', views.delete_workspace, name='delete_workspace'),
        path('layers/', views.get_management_layers, name='get_management_layers'),
        path('datastores/', views.get_datastores, name='get_datastores'),
        path('datastores/create/', views.create_datastore, name='create_datastore'),
        path('shapefiles/publish/', views.publish_shapefile, name='publish_shapefile'),
        path('shapefile-directories/publish/', views.publish_shapefile_directory, name='publish_shapefile_directory'),
        path('geotiffs/publish/', views.publish_geotiff, name='publish_geotiff'),
        path('geotiff-directories/publish/', views.publish_geotiff_directory, name='publish_geotiff_directory'),
        path('postgis/publish/', views.publish_postgis, name='publish_postgis'),
        path('layers/delete/', views.delete_layer, name='delete_layer'),
        path('layers/style/', views.set_layer_style, name='set_layer_style'),
        path('batch/execute/', views.execute_batch_file, name='execute_batch_file'),
        path('diagnose/', views.diagnose_geoserver, name='diagnose_geoserver'),
    ])),
    
    # 批处理API
    path('api/batch/', include([
        path('execute/', views.execute_batch, name='execute_batch'),
        path('status/<str:task_id>/', views.get_batch_status, name='get_batch_status'),
        path('log/<str:task_id>/', views.get_batch_log, name='get_batch_log'),
        path('all/', views.get_all_batch_status, name='get_all_batch_status'),
    ])),
    
    # TIF处理API
    path('api/tif/', include([
        path('process/', views.process_tif, name='process_tif'),
        path('execute/', views.execute_tif_process, name='execute_tif_process'),
        path('status/<str:task_id>/', views.get_tif_status, name='get_tif_status'),
        path('log/<str:task_id>/', views.get_tif_log, name='get_tif_log'),
        path('all/', views.get_all_tif_status, name='get_all_tif_status'),
    ])),
    
    # GeoServer异步发布API
    path('api/geo/', include([
        path('shapefile/execute/', views.execute_publish_shapefile, name='execute_publish_shapefile'),
        path('geotiff/execute/', views.execute_publish_geotiff, name='execute_publish_geotiff'),
        path('status/<str:task_id>/', views.get_geo_status, name='get_geo_status'),
        path('log/<str:task_id>/', views.get_geo_log, name='get_geo_log'),
        path('all/', views.get_all_geo_status, name='get_all_geo_status'),
    ])),
    
    # 地图API
    path('api/map/', include([
        path('base-map/', views.get_base_map, name='get_base_map'),
        path('base-map2/', views.get_base_map2, name='get_base_map2'),
        path('base-map3/', views.get_base_map3, name='get_base_map3'),
        path('base-style/', views.get_base_style, name='get_base_style'),
        path('base-style2/', views.get_base_style2, name='get_base_style2'),
        path('base-style3/', views.get_base_style3, name='get_base_style3'),
        path('file/<str:filename>/', views.get_map_file, name='get_map_file'),
        path('logs/<str:log_type>/', views.get_map_logs, name='get_map_logs'),
    ])),
]
