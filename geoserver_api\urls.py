"""
GeoServer API URL配置
"""

from django.urls import path, include
from .views import *

# 主要API端点
urlpatterns = [
    # 健康检查
    path('health/', health_check, name='health_check'),

    # 栅格查询API
    path('api/query/', query_coordinate, name='query_coordinate'),
    path('api/layers/', get_layers, name='get_layers'),
    path('api/all_layers/', get_all_layers, name='get_all_layers'),
    path('api/test/', test_coordinate, name='test_coordinate'),
    path('api/pixel/', get_pixel_value, name='get_pixel_value'),
    path('api/query-layers/', query_layers_at_coordinate, name='query_layers_at_coordinate'),
    
    # 管理API
    path('api/management/', include([
        path('workspaces/', get_workspaces, name='get_workspaces'),
        path('workspaces/create/', create_workspace, name='create_workspace'),
        path('workspaces/delete/', delete_workspace, name='delete_workspace'),
        path('layers/', get_management_layers, name='get_management_layers'),
        path('datastores/', get_datastores, name='get_datastores'),
        path('datastores/create/', create_datastore, name='create_datastore'),
        path('shapefiles/publish/', publish_shapefile, name='publish_shapefile'),
        path('shapefile-directories/publish/', publish_shapefile_directory, name='publish_shapefile_directory'),
        path('geotiffs/publish/', publish_geotiff, name='publish_geotiff'),
        path('geotiff-directories/publish/', publish_geotiff_directory, name='publish_geotiff_directory'),
        path('postgis/publish/', publish_postgis, name='publish_postgis'),
        path('layers/delete/', delete_layer, name='delete_layer'),
        path('layers/style/', set_layer_style, name='set_layer_style'),
        path('batch/execute/', execute_batch_file, name='execute_batch_file'),
        path('diagnose/', diagnose_geoserver, name='diagnose_geoserver'),
    ])),
    
    # 批处理API
    path('api/batch/', include([
        path('execute/', execute_batch, name='execute_batch'),
        path('status/<str:task_id>/', get_batch_status, name='get_batch_status'),
        path('log/<str:task_id>/', get_batch_log, name='get_batch_log'),
        path('all/', get_all_batch_status, name='get_all_batch_status'),
    ])),

    # TIF处理API
    path('api/tif/', include([
        path('process/', process_tif, name='process_tif'),
        path('execute/', execute_tif_process, name='execute_tif_process'),
        path('status/<str:task_id>/', get_tif_status, name='get_tif_status'),
        path('log/<str:task_id>/', get_tif_log, name='get_tif_log'),
        path('all/', get_all_tif_status, name='get_all_tif_status'),
    ])),

    # GeoServer异步发布API
    path('api/geo/', include([
        path('shapefile/execute/', execute_publish_shapefile, name='execute_publish_shapefile'),
        path('shapefile-directory/execute/', execute_publish_shapefile_directory, name='execute_publish_shapefile_directory'),
        path('geotiff/execute/', execute_publish_geotiff, name='execute_publish_geotiff'),
        path('geotiff-directory/execute/', execute_publish_geotiff_directory, name='execute_publish_geotiff_directory'),
        path('structured-geotiff/execute/', execute_publish_structured_geotiff, name='execute_publish_structured_geotiff'),
        path('status/<str:task_id>/', get_geo_status, name='get_geo_status'),
        path('log/<str:task_id>/', get_geo_log, name='get_geo_log'),
        path('all/', get_all_geo_status, name='get_all_geo_status'),
    ])),
    
    # 地图API
    path('api/map/', include([
        path('base-map/', get_base_map, name='get_base_map'),
        path('base-map2/', get_base_map2, name='get_base_map2'),
        path('base-map3/', get_base_map3, name='get_base_map3'),
        path('base-style/', get_base_style, name='get_base_style'),
        path('base-style2/', get_base_style2, name='get_base_style2'),
        path('base-style3/', get_base_style3, name='get_base_style3'),
        path('file/<str:filename>/', get_map_file, name='get_map_file'),
        path('list/', list_map_files, name='list_map_files'),
        path('odm/tasks/', get_odm_tasks, name='get_odm_tasks'),
        path('logs/<str:log_type>/', get_map_logs, name='get_map_logs'),
    ])),
]
