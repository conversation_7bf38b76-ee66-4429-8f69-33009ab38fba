2025-07-07 09:54:00,541 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250707_095400.log
2025-07-07 09:54:00,562 - root - INFO - 开始执行命令: publish-geotiff-directory
2025-07-07 09:54:00,562 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-07 09:54:00,563 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-07 09:54:00,667 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-07 09:54:00,669 - root - INFO - 在目录 'D:/Drone_Project/rasterTest' 中找到 9 个GeoTIFF文件
2025-07-07 09:54:00,669 - root - INFO - 未提供存储名称，使用目录名称作为前缀: rasterTest
2025-07-07 09:54:00,716 - root - INFO - 在工作区 'test_myworkspace' 中找到 6 个图层
2025-07-07 09:54:00,717 - root - INFO - 发布前工作区 'test_myworkspace' 中有 6 个图层
2025-07-07 09:54:00,717 - root - INFO - 正在发布GeoTIFF: id1.tif, 存储名: rasterTest_id1
2025-07-07 09:54:00,734 - root - INFO - 使用绝对路径: D:\Drone_Project\rasterTest\id1.tif
2025-07-07 09:54:01,135 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/rasterTest\id1.tif' 到 'test_myworkspace:rasterTest_id1'
2025-07-07 09:54:01,136 - root - INFO - 图层名称与存储名称不同，尝试重命名图层...
2025-07-07 09:54:01,257 - root - INFO - 成功将图层名称从 'rasterTest_id1' 更改为 'id1'
2025-07-07 09:54:01,323 - root - INFO - 图层验证成功: 直接图层路径: test_myworkspace:id1
2025-07-07 09:54:01,324 - root - INFO - GeoTIFF 'D:/Drone_Project/rasterTest\id1.tif' 成功发布为图层 'test_myworkspace:id1'
2025-07-07 09:54:01,325 - root - INFO - 正在发布GeoTIFF: id2.tif, 存储名: rasterTest_id2
2025-07-07 09:54:01,346 - root - INFO - 使用绝对路径: D:\Drone_Project\rasterTest\id2.tif
2025-07-07 09:54:01,483 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/rasterTest\id2.tif' 到 'test_myworkspace:rasterTest_id2'
2025-07-07 09:54:01,484 - root - INFO - 图层名称与存储名称不同，尝试重命名图层...
2025-07-07 09:54:01,545 - root - INFO - 成功将图层名称从 'rasterTest_id2' 更改为 'id2'
2025-07-07 09:54:01,610 - root - INFO - 图层验证成功: 直接图层路径: test_myworkspace:id2
2025-07-07 09:54:01,611 - root - INFO - GeoTIFF 'D:/Drone_Project/rasterTest\id2.tif' 成功发布为图层 'test_myworkspace:id2'
2025-07-07 09:54:01,612 - root - INFO - 正在发布GeoTIFF: id3.tif, 存储名: rasterTest_id3
2025-07-07 09:54:01,633 - root - INFO - 使用绝对路径: D:\Drone_Project\rasterTest\id3.tif
2025-07-07 09:54:01,786 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/rasterTest\id3.tif' 到 'test_myworkspace:rasterTest_id3'
2025-07-07 09:54:01,787 - root - INFO - 图层名称与存储名称不同，尝试重命名图层...
2025-07-07 09:54:01,957 - root - INFO - 成功将图层名称从 'rasterTest_id3' 更改为 'id3'
2025-07-07 09:54:02,016 - root - INFO - 图层验证成功: 直接图层路径: test_myworkspace:id3
2025-07-07 09:54:02,016 - root - INFO - GeoTIFF 'D:/Drone_Project/rasterTest\id3.tif' 成功发布为图层 'test_myworkspace:id3'
2025-07-07 09:54:02,017 - root - INFO - 正在发布GeoTIFF: id4.tif, 存储名: rasterTest_id4
2025-07-07 09:54:02,039 - root - INFO - 使用绝对路径: D:\Drone_Project\rasterTest\id4.tif
2025-07-07 09:54:02,215 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/rasterTest\id4.tif' 到 'test_myworkspace:rasterTest_id4'
2025-07-07 09:54:02,216 - root - INFO - 图层名称与存储名称不同，尝试重命名图层...
2025-07-07 09:54:02,312 - root - INFO - 成功将图层名称从 'rasterTest_id4' 更改为 'id4'
2025-07-07 09:54:02,370 - root - INFO - 图层验证成功: 直接图层路径: test_myworkspace:id4
2025-07-07 09:54:02,371 - root - INFO - GeoTIFF 'D:/Drone_Project/rasterTest\id4.tif' 成功发布为图层 'test_myworkspace:id4'
2025-07-07 09:54:02,372 - root - INFO - 正在发布GeoTIFF: id5.tif, 存储名: rasterTest_id5
2025-07-07 09:54:02,393 - root - INFO - 使用绝对路径: D:\Drone_Project\rasterTest\id5.tif
2025-07-07 09:54:02,532 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/rasterTest\id5.tif' 到 'test_myworkspace:rasterTest_id5'
2025-07-07 09:54:02,532 - root - INFO - 图层名称与存储名称不同，尝试重命名图层...
2025-07-07 09:54:02,589 - root - INFO - 成功将图层名称从 'rasterTest_id5' 更改为 'id5'
2025-07-07 09:54:02,645 - root - INFO - 图层验证成功: 直接图层路径: test_myworkspace:id5
2025-07-07 09:54:02,646 - root - INFO - GeoTIFF 'D:/Drone_Project/rasterTest\id5.tif' 成功发布为图层 'test_myworkspace:id5'
2025-07-07 09:54:02,646 - root - INFO - 正在发布GeoTIFF: id6.tif, 存储名: rasterTest_id6
2025-07-07 09:54:02,668 - root - INFO - 使用绝对路径: D:\Drone_Project\rasterTest\id6.tif
2025-07-07 09:54:02,828 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/rasterTest\id6.tif' 到 'test_myworkspace:rasterTest_id6'
2025-07-07 09:54:02,828 - root - INFO - 图层名称与存储名称不同，尝试重命名图层...
2025-07-07 09:54:02,887 - root - INFO - 成功将图层名称从 'rasterTest_id6' 更改为 'id6'
2025-07-07 09:54:02,960 - root - INFO - 图层验证成功: 直接图层路径: test_myworkspace:id6
2025-07-07 09:54:02,961 - root - INFO - GeoTIFF 'D:/Drone_Project/rasterTest\id6.tif' 成功发布为图层 'test_myworkspace:id6'
2025-07-07 09:54:02,962 - root - INFO - 正在发布GeoTIFF: id7.tif, 存储名: rasterTest_id7
2025-07-07 09:54:02,980 - root - INFO - 使用绝对路径: D:\Drone_Project\rasterTest\id7.tif
2025-07-07 09:54:03,129 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/rasterTest\id7.tif' 到 'test_myworkspace:rasterTest_id7'
2025-07-07 09:54:03,130 - root - INFO - 图层名称与存储名称不同，尝试重命名图层...
2025-07-07 09:54:03,196 - root - INFO - 成功将图层名称从 'rasterTest_id7' 更改为 'id7'
2025-07-07 09:54:03,243 - root - INFO - 图层验证成功: 直接图层路径: test_myworkspace:id7
2025-07-07 09:54:03,244 - root - INFO - GeoTIFF 'D:/Drone_Project/rasterTest\id7.tif' 成功发布为图层 'test_myworkspace:id7'
2025-07-07 09:54:03,249 - root - INFO - 正在发布GeoTIFF: id8.tif, 存储名: rasterTest_id8
2025-07-07 09:54:03,271 - root - INFO - 使用绝对路径: D:\Drone_Project\rasterTest\id8.tif
2025-07-07 09:54:03,417 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/rasterTest\id8.tif' 到 'test_myworkspace:rasterTest_id8'
2025-07-07 09:54:03,417 - root - INFO - 图层名称与存储名称不同，尝试重命名图层...
2025-07-07 09:54:03,460 - root - INFO - 成功将图层名称从 'rasterTest_id8' 更改为 'id8'
2025-07-07 09:54:03,507 - root - INFO - 图层验证成功: 直接图层路径: test_myworkspace:id8
2025-07-07 09:54:03,508 - root - INFO - GeoTIFF 'D:/Drone_Project/rasterTest\id8.tif' 成功发布为图层 'test_myworkspace:id8'
2025-07-07 09:54:03,509 - root - INFO - 正在发布GeoTIFF: id9.tif, 存储名: rasterTest_id9
2025-07-07 09:54:03,530 - root - INFO - 使用绝对路径: D:\Drone_Project\rasterTest\id9.tif
2025-07-07 09:54:03,711 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/rasterTest\id9.tif' 到 'test_myworkspace:rasterTest_id9'
2025-07-07 09:54:03,712 - root - INFO - 图层名称与存储名称不同，尝试重命名图层...
2025-07-07 09:54:03,763 - root - INFO - 成功将图层名称从 'rasterTest_id9' 更改为 'id9'
2025-07-07 09:54:03,835 - root - INFO - 图层验证成功: 直接图层路径: test_myworkspace:id9
2025-07-07 09:54:03,835 - root - INFO - GeoTIFF 'D:/Drone_Project/rasterTest\id9.tif' 成功发布为图层 'test_myworkspace:id9'
2025-07-07 09:54:03,861 - root - INFO - 在工作区 'test_myworkspace' 中找到 15 个图层
2025-07-07 09:54:03,862 - root - INFO - 发布后工作区 'test_myworkspace' 中有 15 个图层
2025-07-07 09:54:03,862 - root - INFO - 新增了 9 个图层: id7, id4, id2, id6, id5, id3, id9, id8, id1
2025-07-07 09:54:03,863 - root - INFO - 成功发布了 9 个GeoTIFF文件
2025-07-07 09:54:03,863 - root - INFO - 成功发布: 9, 失败: 0
2025-07-07 09:54:03,863 - root - INFO - 命令执行完成: publish-geotiff-directory
