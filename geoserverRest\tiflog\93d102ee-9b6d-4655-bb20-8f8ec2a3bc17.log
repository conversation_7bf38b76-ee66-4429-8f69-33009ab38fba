2025-07-28 09:15:28,298 - INFO - ============ TIF处理任务 93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 开始执行 ============
2025-07-28 09:15:28,308 - INFO - 开始时间: 2025-07-28 09:15:28
2025-07-28 09:15:28,312 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 09:15:28,322 - INFO - 系统信息:
2025-07-28 09:15:28,323 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 09:15:28,324 - INFO -   Python版本: 3.8.20
2025-07-28 09:15:28,326 - INFO -   GDAL版本: 3.9.2
2025-07-28 09:15:28,327 - INFO -   GPU可用: 否
2025-07-28 09:15:28,328 - INFO - 检查参数有效性...
2025-07-28 09:15:28,329 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:15:28,338 - INFO - 开始执行TIF处理流程...
2025-07-28 09:15:28,339 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:15:28,342 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 09:15:28,346 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:15:28,356 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-28 09:15:33,391 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (09:15:33)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 09:15:33,394 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 09:15:33,668 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:05,  3.87it/s]
2025-07-28 09:15:33,978 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 15.71it/s]
2025-07-28 09:15:34,093 - ERROR - 
处理数据块:  67%|##############################################6                       | 14/21 [00:00<00:00, 24.89it/s]
2025-07-28 09:15:34,336 - ERROR - 
处理数据块:  86%|############################################################          | 18/21 [00:00<00:00, 21.27it/s]
2025-07-28 09:15:34,357 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 22.19it/s]
2025-07-28 09:15:34,358 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 09:15:34,359 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 09:15:34,402 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 513.56it/s]
2025-07-28 09:15:35,911 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.15 秒 (09:15:35)预计总处理时间: 约 6.45 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (09:15:35)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 09:15:35,912 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 09:16:21,755 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 09:16:21,763 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:16:21,766 - ERROR - [A
2025-07-28 09:16:21,962 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  5.15it/s]
2025-07-28 09:16:21,963 - ERROR - [A
2025-07-28 09:16:22,062 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:00, 19.84it/s]
2025-07-28 09:16:22,062 - ERROR - [A
2025-07-28 09:16:22,176 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:00<00:00, 33.25it/s]
2025-07-28 09:16:22,177 - ERROR - [A
2025-07-28 09:16:22,295 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:00<00:00, 39.76it/s]
2025-07-28 09:16:22,296 - ERROR - [A
2025-07-28 09:16:22,374 - ERROR - [A
2025-07-28 09:17:13,124 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:37<03:14, 97.21s/it]
2025-07-28 09:17:20,327 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 09:17:20,328 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:17:20,330 - ERROR - [A
2025-07-28 09:17:20,838 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:10,  1.97it/s]
2025-07-28 09:17:20,839 - ERROR - [A
2025-07-28 09:17:21,152 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:07,  2.54it/s]
2025-07-28 09:17:21,153 - ERROR - [A
2025-07-28 09:17:21,495 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:06,  2.70it/s]
2025-07-28 09:17:21,495 - ERROR - [A
2025-07-28 09:17:21,795 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:01<00:05,  2.92it/s]
2025-07-28 09:17:21,795 - ERROR - [A
2025-07-28 09:17:22,139 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:01<00:05,  2.91it/s]
2025-07-28 09:17:22,139 - ERROR - [A
2025-07-28 09:17:22,395 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:04,  3.19it/s]
2025-07-28 09:17:22,395 - ERROR - [A
2025-07-28 09:17:22,702 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:02<00:04,  3.21it/s]
2025-07-28 09:17:22,704 - ERROR - [A
2025-07-28 09:17:22,965 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:02<00:03,  3.38it/s]
2025-07-28 09:17:22,966 - ERROR - [A
2025-07-28 09:17:23,225 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:02<00:03,  3.51it/s]
2025-07-28 09:17:23,225 - ERROR - [A
2025-07-28 09:17:23,478 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:03<00:03,  3.64it/s]
2025-07-28 09:17:23,479 - ERROR - [A
2025-07-28 09:17:23,663 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:03<00:02,  4.04it/s]
2025-07-28 09:17:23,664 - ERROR - [A
2025-07-28 09:17:23,948 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:03<00:02,  3.86it/s]
2025-07-28 09:17:23,949 - ERROR - [A
2025-07-28 09:17:24,319 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:03<00:02,  3.42it/s]
2025-07-28 09:17:24,319 - ERROR - [A
2025-07-28 09:17:24,548 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:04<00:01,  3.65it/s]
2025-07-28 09:17:24,549 - ERROR - [A
2025-07-28 09:17:24,747 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:04<00:01,  3.98it/s]
2025-07-28 09:17:24,748 - ERROR - [A
2025-07-28 09:17:28,015 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:07<00:05,  1.16s/it]
2025-07-28 09:17:28,016 - ERROR - [A
2025-07-28 09:17:33,604 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:13<00:09,  2.49s/it]
2025-07-28 09:17:33,605 - ERROR - [A
2025-07-28 09:17:38,316 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:17<00:09,  3.16s/it]
2025-07-28 09:17:38,317 - ERROR - [A
2025-07-28 09:17:44,929 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:24<00:08,  4.20s/it]
2025-07-28 09:17:44,930 - ERROR - [A
2025-07-28 09:17:51,575 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:31<00:04,  4.93s/it]
2025-07-28 09:17:51,576 - ERROR - [A
2025-07-28 09:17:52,656 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:32<00:00,  3.78s/it]
2025-07-28 09:17:52,657 - ERROR - [A
2025-07-28 09:17:52,659 - ERROR - [A
2025-07-28 09:19:22,864 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [03:46<01:56, 116.34s/it]
2025-07-28 09:27:39,555 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [12:03<00:00, 290.02s/it]
2025-07-28 09:27:39,560 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [12:03<00:00, 241.21s/it]
2025-07-28 09:30:02,097 - INFO - 处理完成，耗时: 873.75 秒 (14.56 分钟)
2025-07-28 09:30:02,099 - INFO - 处理结果: 成功
2025-07-28 09:30:02,103 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-28 09:30:02,104 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-28 09:30:02,109 - INFO - TIF处理任务 93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 执行成功
2025-07-28 09:30:02,110 - INFO - 完成时间: 2025-07-28 09:30:02
2025-07-28 09:30:02,111 - INFO - 状态: 运行成功
2025-07-28 09:30:02,111 - INFO - ============ 任务执行结束 ============
