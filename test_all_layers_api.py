#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试all_layers接口的数据格式一致性
"""

import requests
import json

def test_flask_api():
    """测试Flask版本的API"""
    try:
        url = "http://127.0.0.1:5000/api/all_layers"
        params = {"workspace": "test_workspace"}
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("Flask API 响应:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            return data
        else:
            print(f"Flask API 错误: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"Flask API 请求失败: {str(e)}")
        return None

def test_django_api():
    """测试Django版本的API"""
    try:
        url = "http://127.0.0.1:8001/api/all_layers/"
        params = {"workspace": "test_workspace"}
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("Django API 响应:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            return data
        else:
            print(f"Django API 错误: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"Django API 请求失败: {str(e)}")
        return None

def compare_responses(flask_data, django_data):
    """比较两个API的响应"""
    if not flask_data or not django_data:
        print("无法比较：其中一个API响应为空")
        return
    
    print("\n=== 响应比较 ===")
    
    # 比较基本结构
    flask_keys = set(flask_data.keys())
    django_keys = set(django_data.keys())
    
    if flask_keys == django_keys:
        print("✅ 响应结构一致")
    else:
        print("❌ 响应结构不一致")
        print(f"Flask独有的键: {flask_keys - django_keys}")
        print(f"Django独有的键: {django_keys - flask_keys}")
    
    # 比较layers数组
    if "layers" in flask_data and "layers" in django_data:
        flask_layers = flask_data["layers"]
        django_layers = django_data["layers"]
        
        print(f"Flask图层数量: {len(flask_layers)}")
        print(f"Django图层数量: {len(django_layers)}")
        
        if len(flask_layers) > 0 and len(django_layers) > 0:
            print("\n第一个图层结构比较:")
            flask_layer_keys = set(flask_layers[0].keys())
            django_layer_keys = set(django_layers[0].keys())
            
            if flask_layer_keys == django_layer_keys:
                print("✅ 图层结构一致")
            else:
                print("❌ 图层结构不一致")
                print(f"Flask图层独有的键: {flask_layer_keys - django_layer_keys}")
                print(f"Django图层独有的键: {django_layer_keys - flask_layer_keys}")
            
            print("\nFlask第一个图层:")
            print(json.dumps(flask_layers[0], indent=2, ensure_ascii=False))
            print("\nDjango第一个图层:")
            print(json.dumps(django_layers[0], indent=2, ensure_ascii=False))

if __name__ == "__main__":
    print("开始测试all_layers接口...")
    
    flask_data = test_flask_api()
    print("\n" + "="*50 + "\n")
    django_data = test_django_api()
    
    if flask_data and django_data:
        compare_responses(flask_data, django_data)
    
    print("\n测试完成")
