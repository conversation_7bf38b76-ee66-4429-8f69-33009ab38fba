#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer REST API服务 - 地图配置API模块
提供从127.0.0.1:81获取各种JSON配置文件的接口
"""

import os
import sys
import logging
from flask import Blueprint, request, jsonify
import requests
import time
import json
from datetime import datetime
import re

# 创建日志记录器
map_logger = logging.getLogger("map_api")
if not map_logger.handlers:
    map_logger.setLevel(logging.INFO)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    map_logger.addHandler(console_handler)

    # 文件处理器
    log_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../logs"))
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file = os.path.join(log_dir, f'map_api_{time.strftime("%Y%m%d_%H%M%S")}.log')
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(formatter)
    map_logger.addHandler(file_handler)

# 创建API蓝图
map_api = Blueprint("map_api", __name__)

# 基础配置
BASE_URL = "http://127.0.0.1:81"
TIMEOUT = 10  # 请求超时时间(秒)

# 可用的JSON文件列表
AVAILABLE_JSON_FILES = [
    "baseMap.json",
    "baseMap2.json",
    "baseMap3.json",
    "baseStyle.json",
    "baseStyle2.json",
    "baseStyle3.json",
]


def fetch_json_from_source(filename):
    """
    从源服务器获取JSON文件内容

    参数:
        filename: 要获取的JSON文件名

    返回:
        成功时返回JSON内容和状态码，失败时返回错误信息和状态码
    """
    try:
        map_logger.info(f"请求文件: {filename}")
        url = f"{BASE_URL}/{filename}"

        # 设置请求头，模拟浏览器请求
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "application/json",
        }

        response = requests.get(url, headers=headers, timeout=TIMEOUT)

        # 检查响应状态码
        if response.status_code == 200:
            try:
                # 尝试解析为JSON（确保它是有效的JSON）
                json_content = response.json()
                map_logger.info(
                    f"成功获取文件 {filename}, 内容大小: {len(response.text)} 字节"
                )
                return json_content, 200
            except ValueError:
                # 如果不是有效的JSON，返回原始文本
                map_logger.warning(f"获取的内容不是有效的JSON: {filename}")
                return {
                    "status": "error",
                    "message": f"源服务器返回的内容不是有效的JSON: {filename}",
                }, 500
        else:
            map_logger.error(
                f"获取文件失败: {filename}, 状态码: {response.status_code}"
            )
            return {
                "status": "error",
                "message": f"获取文件失败: {response.status_code}",
            }, response.status_code

    except requests.RequestException as e:
        map_logger.error(f"请求错误: {str(e)}")
        return {"status": "error", "message": f"请求错误: {str(e)}"}, 500
    except Exception as e:
        map_logger.error(f"获取文件时出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return {"status": "error", "message": f"服务器内部错误: {str(e)}"}, 500


def get_directory_listing(path):
    """
    获取指定路径下的目录列表

    参数:
        path: 服务器上的路径

    返回:
        目录列表或错误信息
    """
    try:
        map_logger.info(f"请求目录列表: {path}")
        url = f"{BASE_URL}/{path}"

        # 设置请求头，模拟浏览器请求
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml",
        }

        response = requests.get(url, headers=headers, timeout=TIMEOUT)

        if response.status_code == 200:
            # 尝试从HTML响应中提取目录列表
            # 这里假设Nginx或Apache的目录列表是标准格式
            # 可能需要根据实际输出格式调整
            html_content = response.text

            # 使用正则表达式查找目录名，这里假设目录名都是14位数字（如示例中的20250705171600）
            # 调整正则表达式以匹配实际的目录列表HTML输出
            directory_pattern = r">(\d{14})/</a>"
            directories = re.findall(directory_pattern, html_content)

            if not directories:
                # 如果未找到匹配的目录，尝试更宽松的匹配
                directory_pattern = r">([^/]+)/</a>"
                directories = re.findall(directory_pattern, html_content)

                # 过滤出符合格式的目录名（数字形式的目录）
                directories = [
                    d for d in directories if d.isdigit() and len(d) >= 8
                ]  # 至少8位数字的目录名

            map_logger.info(f"找到 {len(directories)} 个目录")
            return directories, 200
        else:
            map_logger.error(
                f"获取目录列表失败: {path}, 状态码: {response.status_code}"
            )
            return [], response.status_code
    except Exception as e:
        map_logger.error(f"获取目录列表时出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return [], 500


def get_file_content(file_path):
    """
    获取指定文件的内容

    参数:
        file_path: 文件路径

    返回:
        文件内容和状态码
    """
    try:
        map_logger.info(f"请求文件内容: {file_path}")
        url = f"{BASE_URL}/{file_path}"

        # 设置请求头，模拟浏览器请求
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/plain,application/json",
        }

        response = requests.get(url, headers=headers, timeout=TIMEOUT)

        if response.status_code == 200:
            # 尝试获取内容
            content = response.text
            map_logger.info(
                f"成功获取文件内容: {file_path}, 内容大小: {len(content)} 字节"
            )
            return content, 200
        else:
            map_logger.error(
                f"获取文件内容失败: {file_path}, 状态码: {response.status_code}"
            )
            return "", response.status_code

    except Exception as e:
        map_logger.error(f"获取文件内容时出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return "", 500


def delete_file(file_path):
    """
    删除指定的文件

    参数:
        file_path: 要删除的文件路径

    返回:
        是否成功删除
    """
    try:
        map_logger.info(f"请求删除文件: {file_path}")
        url = f"{BASE_URL}/{file_path}"

        # 设置请求头，模拟浏览器请求
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        }

        # 使用DELETE请求删除文件
        response = requests.delete(url, headers=headers, timeout=TIMEOUT)

        if response.status_code == 200 or response.status_code == 204:
            map_logger.info(f"成功删除文件: {file_path}")
            return True, 200
        else:
            map_logger.error(
                f"删除文件失败: {file_path}, 状态码: {response.status_code}"
            )
            return False, response.status_code

    except Exception as e:
        map_logger.error(f"删除文件时出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return False, 500


def parse_task_info(task_info):
    """
    解析TaskInfo.json文件内容，提取任务信息

    参数:
        task_info: TaskInfo.json的内容

    返回:
        解析后的任务信息
    """
    try:
        # 尝试提取任务开始时间用于排序
        if (
            isinstance(task_info, dict)
            and "Task" in task_info
            and isinstance(task_info["Task"], list)
            and len(task_info["Task"]) > 0
        ):
            task = task_info["Task"][0]
            start_time = task.get("startTime", "")
            if not start_time and "odm_process" in task and task["odm_process"]:
                start_time = task["odm_process"].get("start_time", "")
            return task, start_time
    except Exception as e:
        map_logger.error(f"解析任务信息时出错: {str(e)}")

    # 如果解析失败，返回空信息
    return {}, ""


def get_task_info(task_id):
    """
    获取指定任务的信息

    参数:
        task_id: 任务ID（目录名）

    返回:
        任务信息字典
    """
    task_path = f"ODM/Input/{task_id}"
    task_info_path = f"{task_path}/TaskInfo.json"

    # 初始化任务信息结构
    task_info = {
        "task_id": task_id,
        "start_time": "",
        "end_time": "",
        "status": "未开始",
        "has_task_info": False,
        "details": {},
    }

    try:
        # 尝试获取TaskInfo.json
        json_content, status_code = fetch_json_from_source(task_info_path)

        # 如果成功获取TaskInfo.json
        if status_code == 200 and isinstance(json_content, dict):
            task_info["has_task_info"] = True

            # 解析任务信息
            task_detail, start_time = parse_task_info(json_content)

            if task_detail:
                task_info["details"] = task_detail
                task_info["start_time"] = start_time
                task_info["end_time"] = task_detail.get("endTime", "")
                task_info["status"] = task_detail.get("status", "进行中")

            # 将原始数据也添加到返回结果中
            task_info["raw_data"] = json_content

    except Exception as e:
        map_logger.error(f"获取任务 {task_id} 的信息时出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())

    return task_info


@map_api.route("/base-map", methods=["GET"])
def get_base_map():
    """获取baseMap.json文件内容"""
    content, status_code = fetch_json_from_source("baseMap.json")
    return jsonify(content), status_code


@map_api.route("/base-map2", methods=["GET"])
def get_base_map2():
    """获取baseMap2.json文件内容"""
    content, status_code = fetch_json_from_source("baseMap2.json")
    return jsonify(content), status_code


@map_api.route("/base-map3", methods=["GET"])
def get_base_map3():
    """获取baseMap3.json文件内容"""
    content, status_code = fetch_json_from_source("baseMap3.json")
    return jsonify(content), status_code


@map_api.route("/base-style", methods=["GET"])
def get_base_style():
    """获取baseStyle.json文件内容"""
    content, status_code = fetch_json_from_source("baseStyle.json")
    return jsonify(content), status_code


@map_api.route("/base-style2", methods=["GET"])
def get_base_style2():
    """获取baseStyle2.json文件内容"""
    content, status_code = fetch_json_from_source("baseStyle2.json")
    return jsonify(content), status_code


@map_api.route("/base-style3", methods=["GET"])
def get_base_style3():
    """获取baseStyle3.json文件内容"""
    content, status_code = fetch_json_from_source("baseStyle3.json")
    return jsonify(content), status_code


@map_api.route("/list", methods=["GET"])
def list_available_files():
    """列出所有可用的JSON配置文件"""
    try:
        return jsonify(
            {
                "status": "success",
                "available_files": AVAILABLE_JSON_FILES,
                "endpoints": {
                    "baseMap.json": "/api/map/base-map",
                    "baseMap2.json": "/api/map/base-map2",
                    "baseMap3.json": "/api/map/base-map3",
                    "baseStyle.json": "/api/map/base-style",
                    "baseStyle2.json": "/api/map/base-style2",
                    "baseStyle3.json": "/api/map/base-style3",
                },
            }
        )
    except Exception as e:
        map_logger.error(f"列出可用文件时出错: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@map_api.route("/file/<filename>", methods=["GET"])
def get_file_by_name(filename):
    """
    根据文件名获取对应的JSON文件内容

    参数:
        filename: 文件名（不需要.json后缀）
    """
    try:
        # 确保文件名有.json后缀
        if not filename.endswith(".json"):
            filename = f"{filename}.json"

        # 检查文件名是否在允许列表中
        if filename not in AVAILABLE_JSON_FILES:
            map_logger.warning(f"请求的文件不在允许列表中: {filename}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"请求的文件不可用: {filename}",
                        "available_files": AVAILABLE_JSON_FILES,
                    }
                ),
                404,
            )

        content, status_code = fetch_json_from_source(filename)
        return jsonify(content), status_code

    except Exception as e:
        map_logger.error(f"获取文件时出错: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@map_api.route("/odm/tasks", methods=["GET"])
def get_odm_tasks():
    """
    获取ODM任务列表

    该接口从127.0.0.1:81/ODM/Input/目录下获取所有任务信息，
    并按状态和时间排序（运行中的任务在最前面，未开始的任务在中间，已完成的任务在最后面并按开始时间排序）

    查询参数:
        limit: 限制返回的任务数量，默认返回所有任务

    返回:
        排序后的ODM任务列表
    """
    try:
        map_logger.info("开始获取ODM任务列表")

        # 获取目录列表
        directories, status_code = get_directory_listing("ODM/Input")

        if status_code != 200 or not directories:
            map_logger.warning(f"获取目录列表失败或目录为空，状态码: {status_code}")
            return jsonify(
                {
                    "status": "warning",
                    "message": "获取任务列表失败或没有任务",
                    "tasks": [],
                }
            )

        # 获取每个任务的详细信息
        tasks = []
        for task_id in directories:
            # 判断目录名是否符合日期时间格式（14位数字）
            if re.match(r"^\d{14}$", task_id):
                task_info = get_task_info(task_id)
                tasks.append(task_info)
            else:
                map_logger.debug(f"跳过非标准格式的目录: {task_id}")

        map_logger.info(f"获取到 {len(tasks)} 个任务信息")

        # 分类任务状态
        running_tasks = []
        not_started_tasks = []
        completed_tasks = []

        for task in tasks:
            # 判断任务状态
            if task.get("has_task_info"):
                # 有TaskInfo.json文件
                status = task.get("status", "").lower()

                # 如果status为空字符串或"进行中"，认为是正在运行的任务
                if status == "" or status == "进行中" or "running" in status:
                    running_tasks.append(task)
                elif (
                    status == "完成"
                    or status == "已完成"
                    or "complete" in status
                    or "finished" in status
                ):
                    completed_tasks.append(task)
                else:
                    # 其他状态也视为运行中
                    running_tasks.append(task)
            else:
                # 没有TaskInfo.json文件，认为是未开始的任务
                not_started_tasks.append(task)

        # 对各类任务分别排序
        # 运行中的任务按开始时间倒序排列
        running_tasks.sort(
            key=lambda x: x["start_time"] if x["start_time"] else "0", reverse=True
        )
        # 未开始的任务按任务ID倒序排列
        not_started_tasks.sort(key=lambda x: x["task_id"], reverse=True)
        # 已完成的任务按开始时间倒序排列
        completed_tasks.sort(
            key=lambda x: x["start_time"] if x["start_time"] else "0", reverse=True
        )

        # 合并任务列表：运行中 > 未开始 > 已完成
        sorted_tasks = running_tasks + not_started_tasks + completed_tasks

        map_logger.info(
            f"任务排序结果: 运行中({len(running_tasks)}) > 未开始({len(not_started_tasks)}) > 已完成({len(completed_tasks)})"
        )

        # 处理limit参数
        limit = request.args.get("limit")
        if limit and limit.isdigit():
            limit = int(limit)
            sorted_tasks = sorted_tasks[:limit]
            map_logger.info(f"限制返回前 {limit} 个任务")

        return jsonify(
            {"status": "success", "count": len(sorted_tasks), "tasks": sorted_tasks}
        )

    except Exception as e:
        map_logger.error(f"获取ODM任务列表时出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


def get_log_content(log_type, log_id):
    """
    获取日志文件的内容

    参数:
        log_type: 日志类型 (batlog, geolog, tiflog)
        log_id: 日志ID

    返回:
        日志内容
    """
    try:
        # 构建日志文件的本地路径
        # 假设日志文件位于 geoserverRest/{log_type}/{log_id}.log
        current_dir = os.path.dirname(os.path.abspath(__file__))
        geoserver_rest_dir = os.path.dirname(current_dir)  # 上一级目录是geoserverRest
        log_file_path = os.path.join(geoserver_rest_dir, log_type, f"{log_id}.log")

        map_logger.info(f"读取日志文件: {log_file_path}")

        # 检查文件是否存在
        if not os.path.exists(log_file_path):
            map_logger.error(f"日志文件不存在: {log_file_path}")
            return None, 404

        # 读取文件内容
        with open(log_file_path, "r", encoding="utf-8") as f:
            content = f.read()

        map_logger.info(
            f"成功读取日志文件: {log_file_path}, 内容大小: {len(content)} 字节"
        )
        return content, 200

    except Exception as e:
        map_logger.error(f"读取日志文件时出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return None, 500


@map_api.route("/logs", methods=["GET"])
def get_task_log():
    """
    获取任务日志内容

    查询参数:
        log_type: 日志类型，可选值: batlog, geolog, tiflog
        log_id: 日志ID，通常是任务ID

    返回:
        日志内容
    """
    try:
        # 从查询参数获取log_type和log_id
        log_type = request.args.get("log_type")
        log_id = request.args.get("log_id")

        if not log_type or not log_id:
            return (
                jsonify(
                    {"status": "error", "message": "缺少必要参数 log_type 或 log_id"}
                ),
                400,
            )

        map_logger.info(f"请求获取日志: 类型={log_type}, ID={log_id}")

        # 验证日志类型
        valid_log_types = ["batlog", "geolog", "tiflog"]
        if log_type not in valid_log_types:
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"无效的日志类型: {log_type}，有效类型为: {', '.join(valid_log_types)}",
                    }
                ),
                400,
            )

        # 获取日志文件内容 - 使用新的函数从本地文件系统读取
        content, status_code = get_log_content(log_type, log_id)

        if status_code == 200 and content:
            # 日志内容可能很长，考虑是否需要分页
            return jsonify(
                {
                    "status": "success",
                    "log_type": log_type,
                    "log_id": log_id,
                    "content": content,
                }
            )
        else:
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"获取日志失败，状态码: {status_code}",
                        "log_type": log_type,
                        "log_id": log_id,
                    }
                ),
                404,
            )

    except Exception as e:
        map_logger.error(f"获取日志内容时出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


def get_odm_config():
    """
    读取ODM配置文件中的路径配置

    返回:
        包含配置的字典
    """
    try:
        # 首先尝试获取Task.cfg文件内容
        url = f"{BASE_URL}/ODM/Task.cfg"

        # 设置请求头，模拟浏览器请求
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/plain",
        }

        response = requests.get(url, headers=headers, timeout=TIMEOUT)

        if response.status_code == 200:
            # 解析配置文件内容
            config_content = response.text
            config = {}
            current_section = None

            for line in config_content.splitlines():
                line = line.strip()
                if not line or line.startswith("#"):
                    continue

                # 解析节名
                if line.startswith("[") and line.endswith("]"):
                    current_section = line[1:-1]
                    config[current_section] = {}
                    continue

                # 解析键值对
                if "=" in line and current_section:
                    key, value = line.split("=", 1)
                    config[current_section][key.strip()] = value.strip()

            map_logger.info(f"成功读取ODM配置: {config}")
            return config, True
        else:
            map_logger.error(f"获取ODM配置文件失败, 状态码: {response.status_code}")
            return {}, False

    except Exception as e:
        map_logger.error(f"读取ODM配置文件时出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return {}, False


def delete_task_info_file(task_id):
    """
    删除任务信息文件

    参数:
        task_id: 任务ID

    返回:
        是否成功删除
    """
    try:
        map_logger.info(f"开始删除任务信息文件: {task_id}")

        # 首先获取ODM配置
        config, success = get_odm_config()

        if (
            not success
            or "PATHS" not in config
            or "window_data_path" not in config["PATHS"]
        ):
            map_logger.error("无法获取window_data_path配置")
            return False, "无法获取ODM文件路径配置"

        # 获取本地文件路径
        window_data_path = config["PATHS"]["window_data_path"]
        task_info_path = os.path.join(
            window_data_path, "ODM", "Input", task_id, "TaskInfo.json"
        )

        map_logger.info(f"任务信息文件路径: {task_info_path}")

        # 检查文件是否存在
        if not os.path.exists(task_info_path):
            map_logger.error(f"任务信息文件不存在: {task_info_path}")
            return False, "文件不存在"

        # 删除文件
        os.remove(task_info_path)
        map_logger.info(f"成功删除任务信息文件: {task_info_path}")
        return True, "文件删除成功"

    except Exception as e:
        map_logger.error(f"删除任务信息文件出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return False, str(e)


@map_api.route("/odm/task/delete-info", methods=["GET"])
def delete_task_info():
    """
    删除指定任务的TaskInfo.json文件

    查询参数:
        task_id: 任务ID（目录名）

    返回:
        删除操作的结果
    """
    try:
        # 从查询参数获取task_id
        task_id = request.args.get("task_id")

        if not task_id:
            return jsonify({"status": "error", "message": "缺少必要参数 task_id"}), 400

        map_logger.info(f"请求删除任务信息: {task_id}")

        # 验证任务ID格式
        if not re.match(r"^\d{14}$", task_id):
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"无效的任务ID格式: {task_id}，任务ID应为14位数字",
                    }
                ),
                400,
            )

        # 使用本地文件系统操作删除文件
        success, message = delete_task_info_file(task_id)

        if success:
            return jsonify(
                {
                    "status": "success",
                    "message": f"成功删除任务 {task_id} 的TaskInfo.json文件",
                }
            )
        else:
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"删除TaskInfo.json文件失败: {message}",
                        "task_id": task_id,
                    }
                ),
                500,
            )

    except Exception as e:
        map_logger.error(f"删除任务信息时出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


def rename_task_info_file(task_id):
    """
    重命名任务信息文件为RemoveTask+时间戳.json

    参数:
        task_id: 任务ID

    返回:
        是否成功重命名及结果信息
    """
    try:
        map_logger.info(f"开始重命名任务信息文件: {task_id}")

        # 首先获取ODM配置
        config, success = get_odm_config()

        if (
            not success
            or "PATHS" not in config
            or "window_data_path" not in config["PATHS"]
        ):
            map_logger.error("无法获取window_data_path配置")
            return False, "无法获取ODM文件路径配置"

        # 获取本地文件路径
        window_data_path = config["PATHS"]["window_data_path"]
        task_dir = os.path.join(window_data_path, "ODM", "Input", task_id)
        task_info_path = os.path.join(task_dir, "TaskInfo.json")

        map_logger.info(f"任务信息文件路径: {task_info_path}")

        # 检查文件是否存在
        if not os.path.exists(task_info_path):
            map_logger.error(f"任务信息文件不存在: {task_info_path}")
            return False, "文件不存在"

        # 创建新的文件名（RemoveTask+时间戳.json）
        timestamp = time.strftime("%Y%m%d%H%M%S")
        new_filename = f"RemoveTask{timestamp}.json"
        new_path = os.path.join(task_dir, new_filename)

        # 重命名文件
        os.rename(task_info_path, new_path)
        map_logger.info(f"成功将任务信息文件重命名为: {new_path}")
        return True, new_filename

    except Exception as e:
        map_logger.error(f"重命名任务信息文件出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return False, str(e)


@map_api.route("/odm/task/rename-info", methods=["GET"])
def rename_task_info():
    """
    将指定任务的TaskInfo.json文件重命名为RemoveTask+时间戳.json

    查询参数:
        task_id: 任务ID（目录名）

    返回:
        重命名操作的结果
    """
    try:
        # 从查询参数获取task_id
        task_id = request.args.get("task_id")

        if not task_id:
            return jsonify({"status": "error", "message": "缺少必要参数 task_id"}), 400

        map_logger.info(f"请求重命名任务信息文件: {task_id}")

        # 验证任务ID格式
        if not re.match(r"^\d{14}$", task_id):
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"无效的任务ID格式: {task_id}，任务ID应为14位数字",
                    }
                ),
                400,
            )

        # 重命名任务信息文件
        success, result = rename_task_info_file(task_id)

        if success:
            return jsonify(
                {
                    "status": "success",
                    "message": f"成功将任务 {task_id} 的TaskInfo.json文件重命名为 {result}",
                    "new_filename": result,
                }
            )
        else:
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"重命名TaskInfo.json文件失败: {result}",
                        "task_id": task_id,
                    }
                ),
                500,
            )

    except Exception as e:
        map_logger.error(f"重命名任务信息文件时出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


def reset_task_config():
    """
    重置Task.cfg中的TASK部分，将id和time设置为NONE

    返回:
        是否成功及结果信息
    """
    try:
        map_logger.info("开始重置Task.cfg中的任务信息")

        # 首先获取ODM配置
        config, success = get_odm_config()

        if not success:
            map_logger.error("无法获取ODM配置")
            return False, "无法获取ODM配置文件内容"

        if "PATHS" not in config or "window_data_path" not in config["PATHS"]:
            map_logger.error("配置文件中缺少window_data_path")
            return False, "配置文件中缺少必要的路径信息"

        # 获取本地文件路径
        window_data_path = config["PATHS"]["window_data_path"]
        task_cfg_path = os.path.join(window_data_path, "ODM", "Task.cfg")

        map_logger.info(f"Task.cfg文件路径: {task_cfg_path}")

        # 检查文件是否存在
        if not os.path.exists(task_cfg_path):
            map_logger.error(f"Task.cfg文件不存在: {task_cfg_path}")
            return False, "Task.cfg文件不存在"

        # 读取文件内容
        with open(task_cfg_path, "r", encoding="utf-8") as f:
            lines = f.readlines()

        # 修改[TASK]部分的id和time
        in_task_section = False
        modified = False
        new_content = []

        for line in lines:
            if line.strip() == "[TASK]":
                in_task_section = True
                new_content.append(line)
                continue

            if in_task_section and line.strip().startswith("id ="):
                new_content.append("id = NONE\n")
                modified = True
                continue

            if in_task_section and line.strip().startswith("time ="):
                new_content.append("time = NONE\n")
                modified = True
                continue

            if (
                line.strip()
                and line.strip()[0] == "["
                and line.strip()[-1] == "]"
                and in_task_section
            ):
                # 进入了新的节，离开[TASK]节
                in_task_section = False

            new_content.append(line)

        # 如果未修改，可能是文件格式不符合预期
        if not modified:
            map_logger.warning("未发现需要修改的项，可能是文件格式不符合预期")
            return False, "文件格式不符合预期，未找到id和time项"

        # 写入修改后的内容
        with open(task_cfg_path, "w", encoding="utf-8") as f:
            f.writelines(new_content)

        map_logger.info("成功重置Task.cfg中的任务信息")
        return True, "成功将Task.cfg中的id和time重置为NONE"

    except Exception as e:
        map_logger.error(f"重置Task.cfg任务信息时出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return False, str(e)


@map_api.route("/odm/reset-task-config", methods=["GET"])
def reset_odm_task_config():
    """
    重置ODM Task.cfg中的任务配置，将id和time字段设置为NONE

    返回:
        重置操作的结果
    """
    try:
        map_logger.info("请求重置ODM任务配置")

        # 重置任务配置
        success, message = reset_task_config()

        if success:
            return jsonify({"status": "success", "message": message})
        else:
            return (
                jsonify({"status": "error", "message": f"重置任务配置失败: {message}"}),
                500,
            )

    except Exception as e:
        map_logger.error(f"重置任务配置时出错: {str(e)}")
        import traceback

        map_logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500
