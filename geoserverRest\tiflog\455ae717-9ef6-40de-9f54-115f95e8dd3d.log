2025-07-24 10:07:00,016 - INFO - TIF处理任务 455ae717-9ef6-40de-9f54-115f95e8dd3d 开始执行
2025-07-24 10:07:00,024 - INFO - 开始时间: 2025-07-24 10:07:00
2025-07-24 10:07:00,029 - INFO - 处理参数: {
  "input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 10:07:00,041 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-24 10:07:04,265 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (10:07:04)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-24 10:07:04,317 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-24 10:07:04,677 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:05,  3.90it/s]
2025-07-24 10:07:04,804 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 25.10it/s]
2025-07-24 10:07:04,971 - ERROR - 
处理数据块:  57%|########################################                              | 12/21 [00:00<00:00, 24.60it/s]
2025-07-24 10:07:05,095 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 24.47it/s]
2025-07-24 10:07:05,238 - ERROR - 
处理数据块:  86%|############################################################          | 18/21 [00:00<00:00, 23.27it/s]
2025-07-24 10:07:05,271 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 24.66it/s]
2025-07-24 10:07:05,274 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-24 10:07:05,275 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-24 10:07:05,317 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 514.05it/s]
2025-07-24 10:07:07,580 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.26 秒 (10:07:06)预计总处理时间: 约 6.77 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif (10:07:06)写入带有nodata值的影像...
2025-07-24 10:07:07,581 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-24 10:07:08,435 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 10:07:08,436 - ERROR - [A
2025-07-24 10:07:08,541 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:00, 29.34it/s]
2025-07-24 10:07:08,542 - ERROR - [A
2025-07-24 10:07:08,643 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:00<00:00, 63.80it/s]
2025-07-24 10:07:08,644 - ERROR - [A
2025-07-24 10:07:08,752 - ERROR - 
波段 1/3 写入进度: 100%|###############################################################| 21/21 [00:00<00:00, 72.48it/s]
2025-07-24 10:07:08,753 - ERROR - [A
2025-07-24 10:07:08,757 - ERROR - [A
2025-07-24 10:08:42,086 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:34<03:08, 94.50s/it]
2025-07-24 10:08:45,131 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 10:08:45,132 - ERROR - [A
2025-07-24 10:08:45,297 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  6.16it/s]
2025-07-24 10:08:45,298 - ERROR - [A
2025-07-24 10:08:45,413 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:01, 16.12it/s]
2025-07-24 10:08:45,415 - ERROR - [A
2025-07-24 10:08:45,541 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:00, 19.34it/s]
2025-07-24 10:08:45,541 - ERROR - [A
2025-07-24 10:08:45,658 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 21.71it/s]
2025-07-24 10:08:45,659 - ERROR - [A
2025-07-24 10:08:45,781 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:00<00:00, 22.61it/s]
2025-07-24 10:08:45,782 - ERROR - [A
2025-07-24 10:08:45,902 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:00<00:00, 23.33it/s]
2025-07-24 10:08:45,903 - ERROR - [A
2025-07-24 10:08:46,023 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:00<00:00, 23.81it/s]
2025-07-24 10:08:46,024 - ERROR - [A
2025-07-24 10:08:46,111 - ERROR - [A
2025-07-24 10:21:42,122 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [14:34<08:17, 497.76s/it]
2025-07-24 10:21:43,125 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 10:21:43,126 - ERROR - [A
2025-07-24 10:21:43,315 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  5.44it/s]
2025-07-24 10:21:43,316 - ERROR - [A
2025-07-24 10:21:43,418 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:01, 11.69it/s]
2025-07-24 10:21:43,418 - ERROR - [A
2025-07-24 10:21:43,564 - ERROR - 
波段 3/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:00, 15.82it/s]
2025-07-24 10:21:43,566 - ERROR - [A
2025-07-24 10:21:43,695 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:00, 18.52it/s]
2025-07-24 10:21:43,696 - ERROR - [A
2025-07-24 10:21:43,806 - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 12/21 [00:00<00:00, 21.14it/s]
2025-07-24 10:21:43,807 - ERROR - [A
2025-07-24 10:21:43,941 - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 15/21 [00:00<00:00, 21.54it/s]
2025-07-24 10:21:43,943 - ERROR - [A
2025-07-24 10:21:44,061 - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 18/21 [00:00<00:00, 22.63it/s]
2025-07-24 10:21:44,062 - ERROR - [A
2025-07-24 10:21:44,188 - ERROR - 
波段 3/3 写入进度: 100%|###############################################################| 21/21 [00:01<00:00, 22.90it/s]
2025-07-24 10:21:44,189 - ERROR - [A
2025-07-24 10:21:44,192 - ERROR - [A
2025-07-24 10:26:45,329 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [19:37<00:00, 408.92s/it]
2025-07-24 10:26:45,333 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [19:37<00:00, 392.58s/it]
2025-07-24 10:29:12,674 - INFO - 影像保存完成，耗时: 1179.34 秒 (10:26:45)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp (10:26:45)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif创建掩码，识别有效区域...轻微平滑处理掩码，减少边缘锯齿...寻找有效区域轮廓...找到 2 个初始轮廓使用所有 2 个轮廓，不进行面积过滤转换轮廓为地理坐标...
2025-07-24 10:29:12,702 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/2 [00:00<?, ?it/s]
2025-07-24 10:29:13,358 - ERROR - 
处理轮廓: 100%|##########################################################################| 2/2 [00:00<00:00,  3.12it/s]
2025-07-24 10:29:13,360 - ERROR - 
处理轮廓: 100%|##########################################################################| 2/2 [00:00<00:00,  3.11it/s]
2025-07-24 10:29:13,467 - INFO - 成功创建 2 个多边形合并多边形...已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp写入shapefile特征...处理MultiPolygon，包含 2 个多边形
2025-07-24 10:29:13,468 - ERROR - 
写入多边形:   0%|                                                                                | 0/2 [00:00<?, ?it/s]
2025-07-24 10:29:13,498 - ERROR - 
写入多边形: 100%|########################################################################| 2/2 [00:00<00:00, 93.65it/s]
2025-07-24 10:29:22,941 - INFO - TIF处理任务 455ae717-9ef6-40de-9f54-115f95e8dd3d 执行成功
2025-07-24 10:29:22,942 - INFO - 完成时间: 2025-07-24 10:29:22
2025-07-24 10:29:22,945 - INFO - 状态: 运行成功
