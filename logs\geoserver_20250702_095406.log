2025-07-02 09:54:06,458 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250702_095406.log
2025-07-02 09:54:08,417 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-02 09:54:08,417 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-02 09:54:08,484 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-02 09:54:08,489 - geoserver_query_api - INFO - === GeoServer栅格数据坐标查询API服务 ===
2025-07-02 09:54:08,490 - geoserver_query_api - INFO - 主机: 0.0.0.0
2025-07-02 09:54:08,490 - geoserver_query_api - INFO - 端口: 5000
2025-07-02 09:54:08,501 - geoserver_query_api - INFO - 调试模式: 启用
2025-07-02 09:54:08,513 - werkzeug - WARNING -  * Debugger is active!
2025-07-02 09:54:08,551 - werkzeug - INFO -  * Debugger PIN: 129-639-547
2025-07-02 09:54:10,871 - root - INFO - 开始查询坐标点 (22.77912, 108.36502) 在工作区 'test_myworkspace' 中的图层
2025-07-02 09:54:10,916 - root - INFO - 尝试从WMS GetCapabilities获取 1 个图层的边界框
2025-07-02 09:54:11,336 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-02 09:54:11,338 - root - INFO - 在工作区 'test_myworkspace' 中找到 1 个栅格图层
2025-07-02 09:54:11,368 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-02 09:54:11,368 - root - INFO - 在坐标点 (22.77912, 108.36502) 处找到 1 个有效栅格图层
2025-07-02 09:54:11,369 - werkzeug - INFO - 127.0.0.1 - - [02/Jul/2025 09:54:11] "GET /api/query?lat=22.77912&lon=108.36502&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-02 09:57:03,812 - root - INFO - 开始查询坐标点 (22.77912, 108.36502) 在工作区 'test_myworkspace' 中的图层
2025-07-02 09:57:03,854 - root - INFO - 尝试从WMS GetCapabilities获取 1 个图层的边界框
2025-07-02 09:57:04,176 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-02 09:57:04,178 - root - INFO - 在工作区 'test_myworkspace' 中找到 1 个栅格图层
2025-07-02 09:57:04,214 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-02 09:57:04,214 - root - INFO - 在坐标点 (22.77912, 108.36502) 处找到 1 个有效栅格图层
2025-07-02 09:57:04,215 - werkzeug - INFO - 127.0.0.1 - - [02/Jul/2025 09:57:04] "GET /api/query?lat=22.77912&lon=108.36502&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-02 09:58:22,818 - root - INFO - 开始查询坐标点 (22.27912, 108.36502) 在工作区 'test_myworkspace' 中的图层
2025-07-02 09:58:22,864 - root - INFO - 尝试从WMS GetCapabilities获取 1 个图层的边界框
2025-07-02 09:58:23,185 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-02 09:58:23,188 - root - INFO - 在工作区 'test_myworkspace' 中找到 1 个栅格图层
2025-07-02 09:58:23,188 - root - INFO - 在坐标点 (22.27912, 108.36502) 处找到 0 个有效栅格图层
2025-07-02 09:58:23,189 - werkzeug - INFO - 127.0.0.1 - - [02/Jul/2025 09:58:23] "GET /api/query?lat=22.27912&lon=108.36502&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-02 09:58:27,435 - root - INFO - 开始查询坐标点 (22.87912, 108.36502) 在工作区 'test_myworkspace' 中的图层
2025-07-02 09:58:27,481 - root - INFO - 尝试从WMS GetCapabilities获取 1 个图层的边界框
2025-07-02 09:58:27,792 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-02 09:58:27,795 - root - INFO - 在工作区 'test_myworkspace' 中找到 1 个栅格图层
2025-07-02 09:58:27,821 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-02 09:58:27,821 - root - INFO - 在坐标点 (22.87912, 108.36502) 处找到 1 个有效栅格图层
2025-07-02 09:58:27,822 - werkzeug - INFO - 127.0.0.1 - - [02/Jul/2025 09:58:27] "GET /api/query?lat=22.87912&lon=108.36502&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-02 09:59:07,350 - werkzeug - INFO -  * Detected change in 'D:\\Drone_Project\\geoserverapi\\coordinate_query\\geoserver_query_api.py', reloading
