# 基础API文档

基础API模块提供系统的基本功能，包括健康检查等核心服务。

## 模块概述

基础API模块是整个GeoServer Django API系统的入口点，提供：
- 服务健康状态检查
- 系统基本信息获取
- API服务可用性验证

## API端点列表

### 1. 健康检查

**端点**: `GET /health/`

**描述**: 检查GeoServer REST API服务的运行状态

**参数**: 无

**请求示例**:
```bash
curl -X GET "http://127.0.0.1:5000/health/"
```

**成功响应**:
```json
{
  "status": "ok",
  "message": "GeoServer REST API服务正在运行"
}
```

**响应字段说明**:
- `status`: 服务状态，"ok"表示正常运行
- `message`: 状态描述信息

**使用场景**:
- 系统启动后验证服务是否正常
- 负载均衡器健康检查
- 监控系统状态检查
- 客户端连接前的预检查

**错误处理**:
如果服务出现问题，可能返回500错误或无响应。

## 实现细节

### 视图函数
```python
def health_check(request):
    """健康检查端点"""
    return JsonResponse({
        'status': 'ok',
        'message': 'GeoServer REST API服务正在运行'
    })
```

### 特点
- 无需参数
- 响应速度快
- 不依赖外部服务
- 适合高频调用

## 集成示例

### Python客户端
```python
import requests

def check_service_health(base_url):
    try:
        response = requests.get(f"{base_url}/health/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            return data.get('status') == 'ok'
        return False
    except requests.RequestException:
        return False

# 使用示例
if check_service_health("http://127.0.0.1:5000"):
    print("服务正常运行")
else:
    print("服务不可用")
```

### JavaScript客户端
```javascript
async function checkServiceHealth(baseUrl) {
    try {
        const response = await fetch(`${baseUrl}/health/`);
        if (response.ok) {
            const data = await response.json();
            return data.status === 'ok';
        }
        return false;
    } catch (error) {
        return false;
    }
}

// 使用示例
checkServiceHealth('http://127.0.0.1:5000')
    .then(isHealthy => {
        if (isHealthy) {
            console.log('服务正常运行');
        } else {
            console.log('服务不可用');
        }
    });
```

### Shell脚本
```bash
#!/bin/bash

BASE_URL="http://127.0.0.1:5000"

# 检查服务健康状态
check_health() {
    response=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/health/")
    if [ "$response" = "200" ]; then
        echo "服务正常运行"
        return 0
    else
        echo "服务不可用，HTTP状态码: $response"
        return 1
    fi
}

# 使用示例
if check_health; then
    echo "可以继续执行其他操作"
else
    echo "请检查服务状态"
    exit 1
fi
```

## 监控和告警

### Prometheus监控
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'geoserver-api'
    static_configs:
      - targets: ['127.0.0.1:5000']
    metrics_path: '/health/'
    scrape_interval: 30s
```

### Nagios检查
```bash
# check_geoserver_api.sh
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" "http://127.0.0.1:5000/health/")
if [ "$response" = "200" ]; then
    echo "OK - GeoServer API is running"
    exit 0
else
    echo "CRITICAL - GeoServer API is not responding"
    exit 2
fi
```

## 最佳实践

### 1. 定期健康检查
```python
import time
import requests
from datetime import datetime

def monitor_service(base_url, interval=60):
    """定期监控服务健康状态"""
    while True:
        try:
            response = requests.get(f"{base_url}/health/", timeout=5)
            if response.status_code == 200:
                print(f"[{datetime.now()}] 服务正常")
            else:
                print(f"[{datetime.now()}] 服务异常: HTTP {response.status_code}")
        except Exception as e:
            print(f"[{datetime.now()}] 连接失败: {e}")
        
        time.sleep(interval)
```

### 2. 负载均衡配置
```nginx
# nginx.conf
upstream geoserver_api {
    server 127.0.0.1:5000;
    server 127.0.0.1:5001 backup;
}

server {
    listen 80;
    
    location /health/ {
        proxy_pass http://geoserver_api;
        proxy_connect_timeout 1s;
        proxy_read_timeout 1s;
    }
    
    location / {
        proxy_pass http://geoserver_api;
        # 健康检查
        proxy_next_upstream error timeout http_500 http_502 http_503;
    }
}
```

### 3. Docker健康检查
```dockerfile
# Dockerfile
FROM python:3.8

# ... 其他配置 ...

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health/ || exit 1
```

## 故障排除

### 常见问题

1. **服务无响应**
   - 检查Django服务是否启动
   - 检查端口是否被占用
   - 检查防火墙设置

2. **响应缓慢**
   - 检查系统资源使用情况
   - 检查网络连接
   - 检查Django配置

3. **间歇性失败**
   - 检查系统负载
   - 检查内存使用情况
   - 检查日志文件

### 调试命令
```bash
# 检查端口占用
netstat -an | grep :5000

# 检查进程状态
ps aux | grep python

# 检查系统资源
top -p $(pgrep -f "manage.py runserver")

# 查看Django日志
tail -f logs/django.log
```

## 扩展功能

基础API模块可以扩展以下功能：
- 系统版本信息
- 服务启动时间
- 系统资源使用情况
- 依赖服务状态检查

### 扩展示例
```python
def enhanced_health_check(request):
    """增强的健康检查"""
    import psutil
    from django.conf import settings
    
    return JsonResponse({
        'status': 'ok',
        'message': 'GeoServer REST API服务正在运行',
        'version': '1.0.0',
        'uptime': get_uptime(),
        'system': {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent
        },
        'geoserver': {
            'url': settings.GEOSERVER_URL,
            'connected': check_geoserver_connection()
        }
    })
```
