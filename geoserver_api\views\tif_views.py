#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TIF处理视图 - TIF文件处理相关功能
与Flask版本完全一致的实现
"""

import os
import logging
import uuid
import time
import datetime
from django.http import JsonResponse

# NumPy兼容性修复 - 处理numpy.bool弃用问题
try:
    import numpy
    if not hasattr(numpy, "bool"):
        numpy.bool = numpy.bool_
except:
    pass

# 检查GPU可用性
HAS_GPU = False
try:
    import torch
    HAS_GPU = torch.cuda.is_available()
except ImportError:
    pass

try:
    import cupy
    HAS_GPU = True
except ImportError:
    pass

# 导入核心模块
from ..core.tif_process import (
    read_tif,
    set_nodata_and_save,
    create_edge_only_mask,
    create_shapefile_from_mask,
    create_shapefile_from_output_tif,
    process_uav_image,
    tif_executor,
)

# 获取日志记录器
tif_logger = logging.getLogger('tif_api')


def process_tif(request):
    """
    处理TIF栅格影像，设置NoData值，提取有效区域shapefile - 与Flask版本完全一致

    该API仍然保留同步处理模式，适用于简单任务或需要立即结果的场景。
    对于大型影像处理，推荐使用/execute异步处理API。

    查询参数:
        input_tif: 输入TIF文件路径（必需）
        output_tif: 输出TIF文件路径（可选，默认为input_tif同目录下的文件名_out.tif）
        output_shp: 输出shapefile文件路径（可选，默认为input_tif同目录下的文件名_out.shp）
        black_threshold: 黑色判定阈值，默认0
        white_threshold: 白色判定阈值，默认255
        nodata_value: NoData值，默认-9999
        simplify_tolerance: 多边形简化参数，默认0.1
        edge_width: 边缘检测宽度(像素)，默认100
        process_mode: 处理模式，'full'检测整个影像，'edge'仅检测边缘，默认'full'
        keep_alpha: 是否保留Alpha通道(如果存在)，默认false
        protect_interior: 是否保护有效区域内部像素，默认false
        use_gpu: 是否使用GPU加速，默认false
        num_gpus: 使用GPU数量，0表示使用所有可用，默认0

    返回:
        处理结果
    """
    try:
        tif_logger.info("收到TIF处理请求 (同步模式)")

        # 获取参数
        input_tif = request.GET.get("input_tif")
        output_tif = request.GET.get("output_tif")
        output_shp = request.GET.get("output_shp")

        # 验证必要参数
        if not input_tif:
            error_msg = "缺少必要参数: input_tif"
            tif_logger.error(error_msg)
            return JsonResponse({"status": "error", "message": error_msg}, status=400)

        # 规范化路径
        input_tif = os.path.normpath(input_tif)
        tif_logger.info(f"处理输入文件: {input_tif}")

        # 如果未提供输出路径，则生成默认路径
        if not output_tif:
            input_dir = os.path.dirname(input_tif)
            input_filename = os.path.basename(input_tif)
            filename_without_ext = os.path.splitext(input_filename)[0]
            output_tif = os.path.join(input_dir, f"{filename_without_ext}_out.tif")

        if not output_shp:
            input_dir = os.path.dirname(input_tif)
            input_filename = os.path.basename(input_tif)
            filename_without_ext = os.path.splitext(input_filename)[0]
            output_shp = os.path.join(input_dir, f"{filename_without_ext}_out.shp")

        # 规范化输出路径
        output_tif = os.path.normpath(output_tif)
        output_shp = os.path.normpath(output_shp)

        tif_logger.info(f"输出TIF: {output_tif}")
        tif_logger.info(f"输出SHP: {output_shp}")

        # 获取可选参数
        black_threshold = int(request.GET.get("black_threshold", 0))
        white_threshold = int(request.GET.get("white_threshold", 255))
        nodata_value = float(request.GET.get("nodata_value", -9999))
        simplify_tolerance = float(request.GET.get("simplify_tolerance", 0.1))
        edge_width = int(request.GET.get("edge_width", 100))
        process_mode = request.GET.get("process_mode", "full")

        # 布尔参数处理
        keep_alpha = request.GET.get("keep_alpha", "false").lower() == "true"
        protect_interior = (
            request.GET.get("protect_interior", "false").lower() == "true"
        )
        use_gpu = request.GET.get("use_gpu", "false").lower() == "true"

        # 检查GPU可用性
        if use_gpu and not HAS_GPU:
            tif_logger.warning("请求使用GPU但GPU不可用，将使用CPU模式")
            use_gpu = False

        # 其他可选参数
        num_gpus = int(request.GET.get("num_gpus", 0))

        # 检查输入文件是否存在
        if not os.path.exists(input_tif):
            error_msg = f"输入文件不存在: {input_tif}"
            tif_logger.error(error_msg)
            return JsonResponse({"status": "error", "message": error_msg}, status=404)

        # 检查输入文件格式
        file_ext = os.path.splitext(input_tif)[1].lower()
        if file_ext not in [".tif", ".tiff"]:
            warning_msg = f"警告: 输入文件扩展名 '{file_ext}' 不是标准的GeoTIFF扩展名"
            tif_logger.warning(warning_msg)

        # 检查文件大小
        try:
            file_size_mb = os.path.getsize(input_tif) / (1024 * 1024)
            tif_logger.info(f"输入文件大小: {file_size_mb:.2f} MB")
            if file_size_mb > 1000:  # 如果文件大于1GB
                tif_logger.warning(
                    f"文件很大 ({file_size_mb:.2f} MB)，同步处理可能耗时较长，建议使用异步API"
                )
        except Exception as e:
            tif_logger.warning(f"无法获取文件大小: {str(e)}")

        # 确保输出目录存在
        output_tif_dir = os.path.dirname(output_tif)
        if output_tif_dir and not os.path.exists(output_tif_dir):
            try:
                os.makedirs(output_tif_dir)
                tif_logger.info(f"创建输出目录: {output_tif_dir}")
            except Exception as e:
                error_msg = f"无法创建输出目录 {output_tif_dir}: {str(e)}"
                tif_logger.error(error_msg)
                return JsonResponse({"status": "error", "message": error_msg}, status=500)

        output_shp_dir = os.path.dirname(output_shp)
        if output_shp_dir and not os.path.exists(output_shp_dir):
            try:
                os.makedirs(output_shp_dir)
                tif_logger.info(f"创建输出目录: {output_shp_dir}")
            except Exception as e:
                error_msg = f"无法创建输出目录 {output_shp_dir}: {str(e)}"
                tif_logger.error(error_msg)
                return JsonResponse({"status": "error", "message": error_msg}, status=500)

        # 开始处理
        start_time = time.time()
        tif_logger.info("开始处理TIF文件...")

        try:
            # 调用核心处理函数
            result = process_uav_image(
                input_tif=input_tif,
                output_tif=output_tif,
                output_shp=output_shp,
                black_threshold=black_threshold,
                white_threshold=white_threshold,
                nodata_value=nodata_value,
                simplify_tolerance=simplify_tolerance,
                edge_width=edge_width,
                process_mode=process_mode,
                keep_alpha=keep_alpha,
                protect_interior=protect_interior,
                use_gpu=use_gpu,
                num_gpus=num_gpus,
            )

            processing_time = time.time() - start_time
            tif_logger.info(f"TIF处理完成，耗时: {processing_time:.2f}秒")

            # 检查输出文件是否成功创建
            output_files_status = {
                "output_tif_exists": os.path.exists(output_tif),
                "output_shp_exists": os.path.exists(output_shp),
            }

            # 获取输出文件大小
            if output_files_status["output_tif_exists"]:
                output_tif_size = os.path.getsize(output_tif) / (1024 * 1024)
                output_files_status["output_tif_size_mb"] = round(output_tif_size, 2)

            return JsonResponse({
                "status": "success",
                "message": "TIF文件处理完成",
                "input_file": input_tif,
                "output_tif": output_tif,
                "output_shp": output_shp,
                "processing_time_seconds": round(processing_time, 2),
                "parameters": {
                    "black_threshold": black_threshold,
                    "white_threshold": white_threshold,
                    "nodata_value": nodata_value,
                    "simplify_tolerance": simplify_tolerance,
                    "edge_width": edge_width,
                    "process_mode": process_mode,
                    "keep_alpha": keep_alpha,
                    "protect_interior": protect_interior,
                    "use_gpu": use_gpu,
                    "num_gpus": num_gpus,
                },
                "output_files": output_files_status,
                "result": result,
            })

        except Exception as processing_error:
            processing_time = time.time() - start_time
            error_msg = f"TIF处理失败: {str(processing_error)}"
            tif_logger.error(f"{error_msg} (耗时: {processing_time:.2f}秒)")
            return JsonResponse({
                "status": "error",
                "message": error_msg,
                "processing_time_seconds": round(processing_time, 2),
            }, status=500)

    except Exception as e:
        tif_logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            "status": "error",
            "message": f"服务器内部错误: {str(e)}"
        }, status=500)


def execute_tif_process(request):
    """
    异步处理TIF文件
    
    查询参数:
        input_tif: 输入TIF文件路径
        output_tif: 输出TIF文件路径 (可选)
        output_shp: 输出Shapefile路径 (可选)
        nodata_value: NoData值 (可选，默认: -9999)
        edge_width: 边缘宽度 (可选，默认: 100)
        use_gpu: 是否使用GPU加速 (可选，默认: false)
    """
    try:
        input_tif = request.GET.get('input_tif')
        if not input_tif:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: input_tif'
            }, status=400)
        
        if not os.path.exists(input_tif):
            return JsonResponse({
                'status': 'error',
                'message': f'TIF文件不存在: {input_tif}'
            }, status=404)
        
        # 收集处理参数
        params = {}
        for key, value in request.GET.items():
            if key != 'input_tif' and value:
                params[key] = value
        
        # 异步处理TIF文件
        task_id = tif_executor.process_tif_async(input_tif, **params)
        
        return JsonResponse({
            'status': 'success',
            'message': 'TIF处理任务已启动',
            'task_id': task_id
        })
        
    except Exception as e:
        tif_logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_tif_status(request):
    """
    获取TIF处理任务状态

    查询参数:
        task_id: 任务ID
    """
    try:
        task_id = request.GET.get('task_id')
        if not task_id:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: task_id'
            }, status=400)

        task = tif_executor.get_task_status(task_id)
        if not task:
            return JsonResponse({
                'status': 'error',
                'message': f'任务 {task_id} 不存在'
            }, status=404)
        
        return JsonResponse({
            'status': 'success',
            'task': task
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_tif_log(request):
    """
    获取TIF处理任务日志

    查询参数:
        task_id: 任务ID
    """
    try:
        task_id = request.GET.get('task_id')
        if not task_id:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: task_id'
            }, status=400)

        log_content = tif_executor.get_task_log(task_id)
        if log_content is None:
            return JsonResponse({
                'status': 'error',
                'message': f'任务 {task_id} 的日志不存在'
            }, status=404)
        
        return JsonResponse({
            'status': 'success',
            'task_id': task_id,
            'log': log_content
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_all_tif_status(request):
    """
    获取所有TIF处理任务状态
    """
    try:
        tasks = tif_executor.get_all_tasks()
        return JsonResponse({
            'status': 'success',
            'count': len(tasks),
            'tasks': tasks
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)
