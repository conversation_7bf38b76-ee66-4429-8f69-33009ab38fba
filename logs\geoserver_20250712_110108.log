2025-07-12 11:01:08,332 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250712_110108.log
2025-07-12 11:01:08,335 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 11:01:08,335 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 11:01:08,351 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 11:01:08,360 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 11:01:08,360 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 11:01:08,380 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 11:01:08,384 - root - INFO - === GeoServer REST API服务 ===
2025-07-12 11:01:08,384 - root - INFO - 主机: 0.0.0.0
2025-07-12 11:01:08,385 - root - INFO - 端口: 5083
2025-07-12 11:01:08,385 - root - INFO - 调试模式: 禁用
2025-07-12 11:01:08,385 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-12 11:01:08,397 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-12 11:01:08,400 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 11:02:25,521 - root - INFO - 工作区 'tttt' 不存在，正在创建...
2025-07-12 11:02:25,562 - root - INFO - 成功创建工作区: tttt
2025-07-12 11:02:25,562 - root - INFO - 成功创建工作区 'tttt'
2025-07-12 11:02:25,578 - root - INFO - 在目录 D:/Drone_Project/geoserverapi/data/20250701 中找到 2 个GeoTIFF文件
2025-07-12 11:02:25,579 - root - INFO - 正在发布GeoTIFF: D:/Drone_Project/geoserverapi/data/20250701\nanning.tif
2025-07-12 11:02:25,608 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/20250701/file.geotiff
2025-07-12 11:02:25,791 - root - INFO - 成功通过REST API发布GeoTIFF: 201
2025-07-12 11:02:25,792 - root - INFO - 成功发布GeoTIFF: tttt:20250701:nanning
2025-07-12 11:02:25,806 - root - INFO - 图层验证成功: tttt:nanning
2025-07-12 11:02:25,811 - root - INFO - 正在发布GeoTIFF: D:/Drone_Project/geoserverapi/data/20250701\nanning2.tif
2025-07-12 11:02:25,845 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/20250701/file.geotiff
2025-07-12 11:02:26,026 - root - INFO - 成功通过REST API发布GeoTIFF: 201
2025-07-12 11:02:26,027 - root - INFO - 成功发布GeoTIFF: tttt:20250701:nanning2
2025-07-12 11:02:26,042 - root - INFO - 图层验证成功: tttt:nanning2
2025-07-12 11:02:26,046 - root - INFO - 成功发布 2/2 个GeoTIFF文件
2025-07-12 11:02:26,047 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 11:02:26] "GET /api/management/geotiff-directories/publish?directory=D:/Drone_Project/geoserverapi/data/20250701&workspace=tttt&charset=UTF-8 HTTP/1.1" 200 -
2025-07-12 11:09:37,060 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-12 11:09:37,211 - root - INFO - 在工作区 'test_myworkspace' 中找到 5 个矢量图层
2025-07-12 11:09:37,212 - root - INFO - 在工作区 'test_myworkspace' 中找到 16 个图层（11 个栅格，5 个矢量）
2025-07-12 11:09:37,216 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 11:09:37] "GET /api/all_layers?workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-12 11:10:01,536 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-12 11:10:01,684 - root - INFO - 在工作区 'test_myworkspace' 中找到 5 个矢量图层
2025-07-12 11:10:01,686 - root - INFO - 在工作区 'test_myworkspace' 中找到 16 个图层（11 个栅格，5 个矢量）
2025-07-12 11:10:01,693 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 11:10:01] "GET /api/all_layers?workspace=test_myworkspace HTTP/1.1" 200 -
