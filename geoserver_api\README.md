# GeoServer Django API 模块

本模块是GeoServer管理和栅格数据查询的Django实现，提供了完整的RESTful API服务。从Flask框架成功迁移到Django框架，保持所有API功能不变。

## 项目概述

GeoServer Django API是一个功能完整的地理信息系统（GIS）数据管理和服务发布工具，支持：

- **栅格数据查询**: 查询指定坐标点的栅格图层数据
- **GeoServer管理**: 工作区、图层、数据存储的完整管理
- **异步任务处理**: 批处理执行、TIF处理、GeoServer发布的异步任务管理
- **地图配置管理**: 基础地图和样式配置的动态获取
- **ODM任务管理**: OpenDroneMap任务的监控和管理

## 模块结构

```
geoserver_api/
├── views/                     # 视图模块（按功能拆分）
│   ├── __init__.py           # 视图导入和统一管理
│   ├── base_views.py         # 基础视图（健康检查）
│   ├── raster_views.py       # 栅格查询视图
│   ├── management_views.py   # GeoServer管理视图
│   ├── batch_views.py        # 批处理执行视图
│   ├── tif_views.py          # TIF处理视图
│   ├── geo_views.py          # GeoServer异步发布视图
│   └── map_views.py          # 地图配置视图
├── core/                     # 核心功能模块
│   ├── __init__.py
│   ├── manager.py            # GeoServer管理器
│   ├── raster_query.py       # 栅格数据查询引擎
│   ├── batch_executor.py     # 批处理执行器
│   ├── tif_process.py        # TIF处理器
│   └── geo_publisher.py      # GeoServer异步发布器
├── docs/                     # 详细文档目录
│   ├── base_api.md          # 基础API文档
│   ├── raster_api.md        # 栅格查询API文档
│   ├── management_api.md    # 管理API文档
│   ├── batch_api.md         # 批处理API文档
│   ├── tif_api.md           # TIF处理API文档
│   ├── geo_api.md           # GeoServer发布API文档
│   └── map_api.md           # 地图API文档
├── urls.py                   # URL配置
├── models.py                 # Django模型
├── apps.py                   # Django应用配置
└── README.md                 # 本文档
```

## 快速开始

### 1. 环境要求

- Python 3.8.20
- Django 4.2.16
- Django REST Framework 3.14.0
- GeoServer (运行中)
- Conda环境: geoserverapi

### 2. 启动服务

```bash
# 激活conda环境
conda activate geoserverapi

# 启动Django开发服务器
python manage.py runserver 127.0.0.1:5000

# 或使用批处理脚本
start_django_api.bat
```

### 3. 健康检查

```bash
curl http://127.0.0.1:5000/health/
```

预期响应：
```json
{
  "status": "ok",
  "message": "GeoServer REST API服务正在运行"
}
```

## API模块概览

### 1. 基础功能模块
- **健康检查**: 服务状态检查
- **文档**: [docs/base_api.md](docs/base_api.md)

### 2. 栅格查询模块
- **坐标查询**: 查询指定坐标的栅格值
- **图层管理**: 获取工作区图层列表
- **相交分析**: 图层相交情况分析
- **文档**: [docs/raster_api.md](docs/raster_api.md)

### 3. GeoServer管理模块
- **工作区管理**: 创建、删除、查询工作区
- **图层发布**: Shapefile、GeoTIFF发布
- **数据存储**: PostGIS数据存储管理
- **文档**: [docs/management_api.md](docs/management_api.md)

### 4. 批处理执行模块
- **异步执行**: 批处理文件异步执行
- **状态监控**: 任务状态实时查询
- **日志管理**: 执行日志查看
- **文档**: [docs/batch_api.md](docs/batch_api.md)

### 5. TIF处理模块
- **影像处理**: TIF文件处理和转换
- **异步任务**: 长时间处理任务管理
- **进度跟踪**: 处理进度实时监控
- **文档**: [docs/tif_api.md](docs/tif_api.md)

### 6. GeoServer发布模块
- **异步发布**: Shapefile、GeoTIFF异步发布
- **批量操作**: 目录批量发布
- **任务管理**: 发布任务状态管理
- **文档**: [docs/geo_api.md](docs/geo_api.md)

### 7. 地图配置模块
- **配置获取**: 基础地图和样式配置
- **ODM集成**: OpenDroneMap任务管理
- **日志查看**: 各模块日志统一查看
- **文档**: [docs/map_api.md](docs/map_api.md)

## 核心特性

### 统一参数获取
所有API端点都使用 `request.GET.get('参数名')` 方式获取参数，支持：
- 必选参数验证和错误提示
- 可选参数默认值处理
- 参数类型转换和验证

### 异步任务处理
支持长时间运行任务的异步处理：
1. 提交任务，返回task_id
2. 使用task_id查询任务状态
3. 获取任务执行日志
4. 任务完成后获取结果

### 完整的错误处理
统一的错误响应格式：
```json
{
  "status": "error",
  "message": "错误描述"
}
```

### 日志系统
分模块的日志管理：
- 批处理日志: `geoserverRest/batlog/`
- TIF处理日志: `geoserverRest/tiflog/`
- GeoServer发布日志: `geoserverRest/geolog/`

## 配置管理

### Django Settings配置
```python
# GeoServer连接配置
GEOSERVER_URL = "http://localhost:8083/geoserver"
GEOSERVER_USER = "admin"
GEOSERVER_PASSWORD = "geoserver"

# 默认工作区和数据存储
DEFAULT_WORKSPACE = "default_workspace"
DEFAULT_DATASTORE = "default_datastore"
```

### 环境变量支持
```bash
export GEOSERVER_URL="http://localhost:8083/geoserver"
export GEOSERVER_USER="admin"
export GEOSERVER_PASSWORD="geoserver"
export DEBUG="True"
```

## 兼容性说明

### 与Flask版本完全兼容
- ✅ 所有API端点路径保持不变
- ✅ 参数格式和响应格式保持不变
- ✅ 异步任务处理机制保持不变
- ✅ 核心业务逻辑保持不变

### 并行运行支持
Django版本和Flask版本可以并行运行：
- Django版本: `http://127.0.0.1:5000`
- Flask版本: `http://127.0.0.1:5001`

## 开发指南

### 添加新的API端点
1. 在相应的views文件中添加视图函数
2. 在urls.py中添加URL路由
3. 在__init__.py中添加导入
4. 更新相应的文档

### 扩展核心功能
1. 在core模块中添加新的功能类
2. 在views中调用新的功能
3. 添加相应的测试用例

## 测试和部署

### 开发环境测试
```bash
python test_django_api.py
```

### 生产环境部署
```bash
# 使用Gunicorn
gunicorn geoserver_django.wsgi:application --bind 0.0.0.0:5000

# 使用Nginx反向代理
# 配置Nginx指向Gunicorn服务
```

## 技术支持

### 常见问题
1. **GeoServer连接失败**: 检查GeoServer服务状态和配置
2. **任务执行失败**: 查看相应模块的日志文件
3. **权限问题**: 确保对日志目录有写入权限

### 日志查看
```bash
# Django主日志
tail -f logs/django.log

# 批处理日志
tail -f geoserverRest/batlog/batch_status.json

# TIF处理日志
tail -f geoserverRest/tiflog/tif_status.json
```

## 版本信息

- **当前版本**: Django 4.2.16
- **Python版本**: 3.8.20
- **迁移状态**: 完成（从Flask迁移）
- **API兼容性**: 100%兼容Flask版本

---

详细的API文档请参考 `docs/` 目录下的各模块文档。
