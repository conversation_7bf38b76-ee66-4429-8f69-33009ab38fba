# GeoServer Django API 模块

本模块是GeoServer管理和栅格数据查询的Django实现，提供了完整的RESTful API服务。

## 模块结构

```
geoserver_api/
├── views/                     # 视图模块
│   ├── __init__.py           # 视图导入
│   ├── base_views.py         # 基础视图（健康检查）
│   ├── raster_views.py       # 栅格查询视图
│   ├── management_views.py   # GeoServer管理视图
│   ├── batch_views.py        # 批处理执行视图
│   ├── tif_views.py          # TIF处理视图
│   ├── geo_views.py          # GeoServer异步发布视图
│   └── map_views.py          # 地图配置视图
├── core/                     # 核心功能模块
│   ├── __init__.py
│   ├── manager.py            # GeoServer管理器
│   ├── raster_query.py       # 栅格数据查询
│   ├── batch_executor.py     # 批处理执行器
│   ├── tif_process.py        # TIF处理器
│   └── geo_publisher.py      # GeoServer发布器
├── urls.py                   # URL配置
├── models.py                 # Django模型
├── apps.py                   # Django应用配置
└── README.md                 # 本文档
```

## 功能模块

### 1. 基础功能 (base_views.py)
- **健康检查**: `GET /health/`

### 2. 栅格查询功能 (raster_views.py)
- **查询坐标点栅格值**: `GET /api/query/?lat=纬度&lon=经度&workspace=工作区`
- **获取栅格图层列表**: `GET /api/layers/?workspace=工作区`
- **获取所有图层列表**: `GET /api/all_layers/?workspace=工作区`
- **测试坐标点数据**: `GET /api/test/?lat=纬度&lon=经度&workspace=工作区&layer=图层`
- **获取像素值**: `GET /api/pixel/?lat=纬度&lon=经度&workspace=工作区&layer=图层`
- **查询坐标点图层**: `GET /api/query-layers/?lat=纬度&lon=经度&workspace=工作区`

### 3. GeoServer管理功能 (management_views.py)
- **工作区管理**:
  - 获取工作区列表: `GET /api/management/workspaces/`
  - 创建工作区: `GET /api/management/workspaces/create/?name=工作区名`
  - 删除工作区: `GET /api/management/workspaces/delete/?name=工作区名&recurse=true`

- **图层管理**:
  - 获取图层列表: `GET /api/management/layers/?workspace=工作区`
  - 发布Shapefile: `GET /api/management/shapefiles/publish/?file=文件路径&workspace=工作区`
  - 发布Shapefile目录: `GET /api/management/shapefile-directories/publish/?directory=目录路径&workspace=工作区`
  - 发布GeoTIFF: `GET /api/management/geotiffs/publish/?file=文件路径&workspace=工作区`
  - 发布GeoTIFF目录: `GET /api/management/geotiff-directories/publish/?directory=目录路径&workspace=工作区`

- **数据存储管理**:
  - 获取数据存储: `GET /api/management/datastores/?workspace=工作区`
  - 创建PostGIS数据存储: `GET /api/management/datastores/create/?name=存储名&workspace=工作区&host=主机&port=端口&database=数据库&username=用户名&password=密码`

- **诊断功能**:
  - GeoServer诊断: `GET /api/management/diagnose/`

### 4. 批处理执行功能 (batch_views.py)
- **执行批处理**: `GET /api/batch/execute/?batch_path=批处理路径&project_path=项目路径`
- **查询任务状态**: `GET /api/batch/status/{task_id}/`
- **获取任务日志**: `GET /api/batch/log/{task_id}/`
- **获取所有任务**: `GET /api/batch/all/`

### 5. TIF处理功能 (tif_views.py)
- **同步处理TIF**: `GET /api/tif/process/?input_tif=输入文件&output_tif=输出文件&nodata_value=-9999`
- **异步处理TIF**: `GET /api/tif/execute/?input_tif=输入文件&output_tif=输出文件`
- **查询处理状态**: `GET /api/tif/status/{task_id}/`
- **获取处理日志**: `GET /api/tif/log/{task_id}/`
- **获取所有处理任务**: `GET /api/tif/all/`

### 6. GeoServer异步发布功能 (geo_views.py)
- **异步发布Shapefile**: `GET /api/geo/shapefile/execute/?path=文件路径&workspace=工作区`
- **异步发布Shapefile目录**: `GET /api/geo/shapefile-directory/execute/?directory=目录路径&workspace=工作区`
- **异步发布GeoTIFF**: `GET /api/geo/geotiff/execute/?path=文件路径&workspace=工作区`
- **异步发布GeoTIFF目录**: `GET /api/geo/geotiff-directory/execute/?directory=目录路径&workspace=工作区`
- **异步发布结构化GeoTIFF**: `GET /api/geo/structured-geotiff/execute/?root_directory=根目录&workspace=工作区`
- **查询发布状态**: `GET /api/geo/status/{task_id}/`
- **获取发布日志**: `GET /api/geo/log/{task_id}/`
- **获取所有发布任务**: `GET /api/geo/all/`

### 7. 地图配置功能 (map_views.py)
- **获取基础地图配置**:
  - baseMap.json: `GET /api/map/base-map/`
  - baseMap2.json: `GET /api/map/base-map2/`
  - baseMap3.json: `GET /api/map/base-map3/`

- **获取样式配置**:
  - baseStyle.json: `GET /api/map/base-style/`
  - baseStyle2.json: `GET /api/map/base-style2/`
  - baseStyle3.json: `GET /api/map/base-style3/`

- **通用文件访问**:
  - 根据文件名获取: `GET /api/map/file/{filename}/`
  - 列出可用文件: `GET /api/map/list/`

- **ODM任务管理**:
  - 获取ODM任务: `GET /api/map/odm/tasks/?limit=10`

- **日志查看**:
  - 获取日志: `GET /api/map/logs/{log_type}/`

## 参数获取方式

所有API端点都使用 `request.GET.get('参数名')` 的方式获取传入的参数，支持：

- **必选参数**: 如果缺少会返回400错误
- **可选参数**: 提供默认值或为空
- **路径参数**: 如task_id、filename等通过URL路径传递

## 响应格式

所有API端点返回JSON格式的响应：

### 成功响应
```json
{
  "status": "success",
  "message": "操作成功",
  "data": {...}
}
```

### 错误响应
```json
{
  "status": "error",
  "message": "错误描述"
}
```

## 异步任务

支持异步任务的功能模块：
- 批处理执行
- TIF文件处理
- GeoServer发布

异步任务流程：
1. 提交任务，返回task_id
2. 使用task_id查询任务状态
3. 获取任务执行日志
4. 任务完成后获取结果

## 日志系统

各模块的日志文件位置：
- 批处理日志: `geoserverRest/batlog/`
- TIF处理日志: `geoserverRest/tiflog/`
- GeoServer发布日志: `geoserverRest/geolog/`

## 配置

核心配置通过Django settings.py管理：
- `GEOSERVER_URL`: GeoServer服务地址
- `GEOSERVER_USER`: GeoServer用户名
- `GEOSERVER_PASSWORD`: GeoServer密码
- `DEFAULT_WORKSPACE`: 默认工作区
- `DEFAULT_DATASTORE`: 默认数据存储

## 使用示例

```python
# 查询栅格值
GET /api/query/?lat=39.9042&lon=116.4074&workspace=test

# 创建工作区
GET /api/management/workspaces/create/?name=new_workspace

# 发布Shapefile
GET /api/management/shapefiles/publish/?file=D:/data/boundary.shp&workspace=test

# 异步发布GeoTIFF
GET /api/geo/geotiff/execute/?path=D:/data/image.tif&workspace=test

# 查询任务状态
GET /api/geo/status/550e8400-e29b-41d4-a716-446655440000/
```

## 扩展功能

模块设计支持轻松扩展：
1. 在相应的views文件中添加新的视图函数
2. 在urls.py中添加新的URL路由
3. 在core模块中添加新的核心功能
4. 更新__init__.py中的导入列表

## 兼容性

本Django版本完全兼容原Flask版本的API接口，可以无缝替换使用。
