#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer REST API服务 - 管理API模块
提供GeoServer资源管理的API端点
"""

import os
import sys
import logging
from flask import Blueprint, request, jsonify

# 导入核心模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from core.manager import GeoServerManager, logger

# 创建API蓝图
management_api = Blueprint("management_api", __name__)

# 初始化GeoServer管理器
manager = GeoServerManager()


@management_api.route("/workspaces", methods=["GET"])
def get_workspaces():
    """
    获取所有工作区列表

    返回:
        工作区列表
    """
    try:
        workspaces = manager.get_workspaces()
        return jsonify(
            {"status": "success", "count": len(workspaces), "workspaces": workspaces}
        )
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/workspaces/create", methods=["GET"])
def create_workspace():
    """
    创建工作区

    查询参数:
        name: 工作区名称

    返回:
        创建结果
    """
    try:
        name = request.args.get("name")
        if not name:
            return jsonify({"status": "error", "message": "缺少必要参数: name"}), 400

        result = manager.create_workspace(name)

        if result:
            return jsonify({"status": "success", "message": f"工作区 {name} 创建成功"})
        else:
            return (
                jsonify({"status": "error", "message": f"工作区 {name} 创建失败"}),
                500,
            )

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/workspaces/delete", methods=["GET"])
def delete_workspace():
    """
    删除工作区

    查询参数:
        name: 工作区名称

    返回:
        删除结果
    """
    try:
        name = request.args.get("name")
        if not name:
            return jsonify({"status": "error", "message": "缺少必要参数: name"}), 400

        result = manager.delete_workspace(name)

        if result:
            return jsonify({"status": "success", "message": f"工作区 {name} 删除成功"})
        else:
            return (
                jsonify({"status": "error", "message": f"工作区 {name} 删除失败"}),
                500,
            )

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/datastores", methods=["GET"])
def get_datastores():
    """
    获取指定工作区中的数据存储

    查询参数:
        workspace: 工作区名称

    返回:
        数据存储列表
    """
    try:
        workspace = request.args.get("workspace")
        if not workspace:
            return (
                jsonify({"status": "error", "message": "缺少必要参数: workspace"}),
                400,
            )

        datastores = manager.get_datastores(workspace)
        return jsonify(
            {"status": "success", "count": len(datastores), "datastores": datastores}
        )
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/datastores/create", methods=["GET"])
def create_datastore():
    """
    创建PostGIS数据存储

    查询参数:
        name: 数据存储名称
        workspace: 工作区名称
        host: 数据库主机地址
        port: 数据库端口
        database: 数据库名称
        username: 用户名
        password: 密码

    返回:
        创建结果
    """
    try:
        name = request.args.get("name")
        workspace = request.args.get("workspace")
        host = request.args.get("host", "localhost")
        port = request.args.get("port", 5432)
        database = request.args.get("database")
        username = request.args.get("username")
        password = request.args.get("password")

        # 验证必要参数
        if not all([name, workspace, database, username, password]):
            missing_params = []
            if not name:
                missing_params.append("name")
            if not workspace:
                missing_params.append("workspace")
            if not database:
                missing_params.append("database")
            if not username:
                missing_params.append("username")
            if not password:
                missing_params.append("password")

            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f'缺少必要参数: {", ".join(missing_params)}',
                    }
                ),
                400,
            )

        # 创建连接参数
        conn_params = {
            "host": host,
            "port": port,
            "database": database,
            "username": username,
            "password": password,
        }

        # 创建数据存储
        result = manager.create_datastore(name, workspace, conn_params)

        if result:
            return jsonify(
                {
                    "status": "success",
                    "message": f"数据存储 {name} 在工作区 {workspace} 中创建成功",
                }
            )
        else:
            return (
                jsonify({"status": "error", "message": f"数据存储 {name} 创建失败"}),
                500,
            )

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/layers/bbox", methods=["GET"])
def get_layer_bbox():
    """
    获取图层的边界框信息

    查询参数:
        workspace: 工作区名称 (字符串)
        layer: 图层名称 (字符串)

    返回:
        图层边界框信息
    """
    try:
        # 获取查询参数
        workspace = request.args.get("workspace")
        layer_name = request.args.get("layer")

        # 验证必要参数
        if not all([workspace, layer_name]):
            missing_params = []
            if not workspace:
                missing_params.append("workspace")
            if not layer_name:
                missing_params.append("layer")

            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f'缺少必要参数: {", ".join(missing_params)}',
                    }
                ),
                400,
            )

        # 获取图层边界框信息
        bbox_info = manager.get_layer_bbox(layer_name, workspace)

        if not bbox_info:
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"无法获取图层 {workspace}:{layer_name} 的边界框信息",
                    }
                ),
                404,
            )

        return jsonify(
            {
                "status": "success",
                "layer": layer_name,
                "workspace": workspace,
                "bbox": bbox_info.get("bbox", {}),
            }
        )

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/coveragestores", methods=["GET"])
def get_coveragestores():
    """
    获取指定工作区中的栅格数据存储

    查询参数:
        workspace: 工作区名称

    返回:
        栅格数据存储列表
    """
    try:
        workspace = request.args.get("workspace")
        if not workspace:
            return (
                jsonify({"status": "error", "message": "缺少必要参数: workspace"}),
                400,
            )

        coveragestores = manager.get_coveragestores(workspace)
        return jsonify(
            {
                "status": "success",
                "count": len(coveragestores),
                "coveragestores": coveragestores,
            }
        )
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/shapefiles/publish", methods=["GET"])
def publish_shapefile():
    """
    发布Shapefile

    查询参数:
        file: Shapefile文件路径
        workspace: 工作区名称
        store: 存储名称
        layer: 图层名称
        charset: 字符集 (默认: UTF-8)

    返回:
        发布结果
    """
    try:
        file_path = request.args.get("file")
        workspace = request.args.get("workspace")
        store = request.args.get("store")
        layer = request.args.get("layer")
        charset = request.args.get("charset", "UTF-8")

        # 验证必要参数
        if not file_path:
            return jsonify({"status": "error", "message": "缺少必要参数: file"}), 400

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return (
                jsonify(
                    {"status": "error", "message": f"Shapefile文件不存在: {file_path}"}
                ),
                404,
            )

        # 确保工作区存在
        if not manager.check_workspace_exists(workspace):
            logger.info(f"工作区 '{workspace}' 不存在，正在创建...")
            if not manager.create_workspace(workspace):
                return (
                    jsonify(
                        {"status": "error", "message": f"无法创建工作区 {workspace}"}
                    ),
                    500,
                )
            logger.info(f"成功创建工作区 '{workspace}'")

        # 发布Shapefile
        result = manager.publish_shapefile(file_path, workspace, store, layer, charset)

        if result:
            return jsonify(
                {
                    "status": "success",
                    "message": f"Shapefile {os.path.basename(file_path)} 发布成功为 {workspace}:{layer}",
                }
            )
        else:
            return (
                jsonify(
                    {"status": "error", "message": f"Shapefile {file_path} 发布失败"}
                ),
                500,
            )

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/shapefile-directories/publish", methods=["GET"])
def publish_shapefile_directory():
    """
    发布目录中所有Shapefile

    查询参数:
        directory: Shapefile目录路径
        workspace: 工作区名称
        store: 存储名称
        charset: 字符集 (默认: UTF-8)

    返回:
        发布结果
    """
    try:
        directory = request.args.get("directory")
        workspace = request.args.get("workspace")
        store = request.args.get("store")
        charset = request.args.get("charset", "UTF-8")

        # 验证必要参数
        if not directory:
            return (
                jsonify({"status": "error", "message": "缺少必要参数: directory"}),
                400,
            )

        # 检查目录是否存在
        if not os.path.exists(directory) or not os.path.isdir(directory):
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"目录不存在或不是有效目录: {directory}",
                    }
                ),
                404,
            )

        # 确保工作区存在
        if not manager.check_workspace_exists(workspace):
            logger.info(f"工作区 '{workspace}' 不存在，正在创建...")
            if not manager.create_workspace(workspace):
                return (
                    jsonify(
                        {"status": "error", "message": f"无法创建工作区 {workspace}"}
                    ),
                    500,
                )
            logger.info(f"成功创建工作区 '{workspace}'")

        # 发布目录
        result = manager.publish_shapefile_directory(
            directory, workspace, store, charset
        )

        if result and isinstance(result, dict):
            success_count = len(result.get("success", []))
            failed_count = len(result.get("failed", []))

            return jsonify(
                {
                    "status": "success" if success_count > 0 else "error",
                    "message": f"发布了 {success_count} 个Shapefile，失败 {failed_count} 个",
                    "details": result,
                }
            )
        else:
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"目录 {directory} 中的Shapefile发布失败",
                    }
                ),
                500,
            )

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/geotiffs/publish", methods=["GET"])
def publish_geotiff():
    """
    发布GeoTIFF

    查询参数:
        file: GeoTIFF文件路径
        workspace: 工作区名称
        store: 存储名称
        layer: 图层名称

    返回:
        发布结果
    """
    try:
        file_path = request.args.get("file")
        workspace = request.args.get("workspace")
        store = request.args.get("store")
        layer = request.args.get("layer")

        # 验证必要参数
        if not file_path:
            return jsonify({"status": "error", "message": "缺少必要参数: file"}), 400

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return (
                jsonify(
                    {"status": "error", "message": f"GeoTIFF文件不存在: {file_path}"}
                ),
                404,
            )

        # 确保工作区存在
        if not manager.check_workspace_exists(workspace):
            logger.info(f"工作区 '{workspace}' 不存在，正在创建...")
            if not manager.create_workspace(workspace):
                return (
                    jsonify(
                        {"status": "error", "message": f"无法创建工作区 {workspace}"}
                    ),
                    500,
                )
            logger.info(f"成功创建工作区 '{workspace}'")

        # 发布GeoTIFF
        result = manager.publish_geotiff(file_path, workspace, store, layer)

        if result:
            return jsonify(
                {
                    "status": "success",
                    "message": f"GeoTIFF {os.path.basename(file_path)} 发布成功为 {workspace}:{layer or store}",
                }
            )
        else:
            return (
                jsonify(
                    {"status": "error", "message": f"GeoTIFF {file_path} 发布失败"}
                ),
                500,
            )

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/geotiff-directories/publish", methods=["GET"])
def publish_geotiff_directory():
    """
    发布目录中所有GeoTIFF

    查询参数:
        directory: GeoTIFF目录路径
        workspace: 工作区名称
        store: 存储名称前缀

    返回:
        发布结果
    """
    try:
        directory = request.args.get("directory")
        workspace = request.args.get("workspace")
        store = request.args.get("store")

        # 验证必要参数
        if not directory:
            return (
                jsonify({"status": "error", "message": "缺少必要参数: directory"}),
                400,
            )

        # 检查目录是否存在
        if not os.path.exists(directory) or not os.path.isdir(directory):
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"目录不存在或不是有效目录: {directory}",
                    }
                ),
                404,
            )

        # 确保工作区存在
        if not manager.check_workspace_exists(workspace):
            logger.info(f"工作区 '{workspace}' 不存在，正在创建...")
            if not manager.create_workspace(workspace):
                return (
                    jsonify(
                        {"status": "error", "message": f"无法创建工作区 {workspace}"}
                    ),
                    500,
                )
            logger.info(f"成功创建工作区 '{workspace}'")

        # 发布目录
        result = manager.publish_geotiff_directory(directory, workspace, store)

        if result and isinstance(result, dict):
            success_count = len(result.get("success", []))
            failed_count = len(result.get("failed", []))

            return jsonify(
                {
                    "status": "success" if success_count > 0 else "error",
                    "message": f"发布了 {success_count} 个GeoTIFF，失败 {failed_count} 个",
                    "details": result,
                }
            )
        else:
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"目录 {directory} 中的GeoTIFF发布失败",
                    }
                ),
                500,
            )

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/postgis-tables/publish", methods=["GET"])
def publish_postgis_table():
    """
    发布PostGIS表

    查询参数:
        table: 表名称
        store: 数据存储名称
        workspace: 工作区名称
        layer: 图层名称
        geometry_column: 几何字段名 (默认: geom)
        srid: 空间参考ID (默认: 4326)

    返回:
        发布结果
    """
    try:
        table = request.args.get("table")
        store = request.args.get("store")
        workspace = request.args.get("workspace")
        layer = request.args.get("layer")
        geometry_column = request.args.get("geometry_column", "geom")
        srid = int(request.args.get("srid", 4326))

        # 验证必要参数
        missing_params = []
        if not table:
            missing_params.append("table")
        if not store:
            missing_params.append("store")
        if not workspace:
            missing_params.append("workspace")

        if missing_params:
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f'缺少必要参数: {", ".join(missing_params)}',
                    }
                ),
                400,
            )

        # 如果未提供layer，使用table作为layer名称
        if not layer:
            layer = table

        # 确保工作区存在
        if not manager.check_workspace_exists(workspace):
            logger.info(f"工作区 '{workspace}' 不存在，正在创建...")
            if not manager.create_workspace(workspace):
                return (
                    jsonify(
                        {"status": "error", "message": f"无法创建工作区 {workspace}"}
                    ),
                    500,
                )
            logger.info(f"成功创建工作区 '{workspace}'")

        # 发布PostGIS表
        result = manager.publish_postgis_layer(
            table, workspace, store, layer, geometry_column, srid
        )

        if result:
            return jsonify(
                {
                    "status": "success",
                    "message": f"PostGIS表 {table} 发布成功为 {workspace}:{layer}",
                }
            )
        else:
            return (
                jsonify({"status": "error", "message": f"PostGIS表 {table} 发布失败"}),
                500,
            )

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/layers/style", methods=["GET"])
def set_layer_style():
    """
    设置图层样式

    查询参数:
        workspace: 工作区
        layer: 图层
        style: 样式名称

    返回:
        设置结果
    """
    try:
        workspace = request.args.get("workspace")
        layer = request.args.get("layer")
        style = request.args.get("style")

        # 验证必要参数
        if not all([workspace, layer, style]):
            missing_params = []
            if not workspace:
                missing_params.append("workspace")
            if not layer:
                missing_params.append("layer")
            if not style:
                missing_params.append("style")

            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f'缺少必要参数: {", ".join(missing_params)}',
                    }
                ),
                400,
            )

        # 设置图层样式
        result = manager.set_layer_style(layer, style, workspace)

        if result:
            return jsonify(
                {
                    "status": "success",
                    "message": f"图层 {workspace}:{layer} 样式设置为 {style} 成功",
                }
            )
        else:
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"图层 {workspace}:{layer} 样式设置失败",
                    }
                ),
                500,
            )

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/layers/delete", methods=["GET"])
def delete_layer():
    """
    删除图层

    查询参数:
        workspace: 工作区
        layer: 图层

    返回:
        删除结果
    """
    try:
        workspace = request.args.get("workspace")
        layer = request.args.get("layer")

        # 验证必要参数
        if not all([workspace, layer]):
            missing_params = []
            if not workspace:
                missing_params.append("workspace")
            if not layer:
                missing_params.append("layer")

            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f'缺少必要参数: {", ".join(missing_params)}',
                    }
                ),
                400,
            )

        # 删除图层
        result = manager.delete_layer(layer, workspace)

        if result:
            return jsonify(
                {"status": "success", "message": f"图层 {workspace}:{layer} 删除成功"}
            )
        else:
            return (
                jsonify(
                    {"status": "error", "message": f"图层 {workspace}:{layer} 删除失败"}
                ),
                500,
            )

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/batch", methods=["GET"])
def batch_process():
    """
    批量处理操作

    查询参数:
        file: 批处理文件路径

    返回:
        处理结果
    """
    try:
        file_path = request.args.get("file")

        # 验证必要参数
        if not file_path:
            return jsonify({"status": "error", "message": "缺少必要参数: file"}), 400

        # 执行批处理
        # 为了安全，我们需要确保批处理文件存在且可读
        if not os.path.exists(file_path):
            return (
                jsonify(
                    {"status": "error", "message": f"批处理文件 {file_path} 不存在"}
                ),
                404,
            )

        try:
            with open(file_path, "r") as f:
                pass  # 测试文件是否可读
        except Exception as e:
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"无法读取批处理文件 {file_path}: {str(e)}",
                    }
                ),
                500,
            )

        # 使用管理器执行批处理文件
        try:
            from geoserverRest.cli.command import execute_batch_file

            result = execute_batch_file(file_path, manager, logger)

            return jsonify(
                {
                    "status": "success" if result else "error",
                    "message": "批处理执行完成" if result else "批处理执行失败",
                }
            )
        except Exception as e:
            return (
                jsonify({"status": "error", "message": f"批处理执行错误: {str(e)}"}),
                500,
            )

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@management_api.route("/diagnose", methods=["GET"])
def diagnose_geoserver():
    """
    诊断GeoServer

    查询参数:
        workspace: 工作区名称 (可选)
        detail: 是否返回详细信息 (可选，默认: false)

    返回:
        诊断结果
    """
    try:
        workspace = request.args.get("workspace")
        detail = request.args.get("detail", "false").lower() == "true"

        # 基本诊断信息
        result = {
            "status": "success",
            "server_info": {
                "url": manager.geoserver_url,
                "version": manager.get_geoserver_version(),
                "status": "running" if manager.is_server_running() else "not running",
            },
        }

        # 如果服务器未运行，则返回基本信息
        if result["server_info"]["status"] != "running":
            return jsonify(result)

        # 添加工作区信息
        workspaces = manager.get_workspaces()
        result["workspaces"] = {"count": len(workspaces), "list": workspaces}

        # 如果指定了工作区，则添加该工作区的详细信息
        if workspace:
            # 检查工作区是否存在
            if workspace not in workspaces:
                return (
                    jsonify(
                        {"status": "error", "message": f"工作区 {workspace} 不存在"}
                    ),
                    404,
                )

            # 获取该工作区的所有图层
            layers = manager.list_layers(workspace)

            workspace_info = {
                "name": workspace,
                "layers_count": len(layers),
                "layers": layers,
            }

            # 如果需要详细信息，则获取每个图层的详细信息
            if detail:
                datastores = manager.get_datastores(workspace)
                coveragestores = manager.get_coveragestores(workspace)

                workspace_info["datastores"] = datastores
                workspace_info["coveragestores"] = coveragestores

                # 如果图层数量不多，可以获取每个图层的边界框信息
                if len(layers) <= 20:  # 限制处理的图层数量，避免请求过多
                    layer_details = {}
                    for layer in layers:
                        bbox = manager.get_layer_bbox(layer, workspace)
                        layer_details[layer] = {
                            "bbox": bbox.get("bbox", {}) if bbox else None
                        }
                    workspace_info["layer_details"] = layer_details

            result["workspace_detail"] = workspace_info

        return jsonify(result)

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500
