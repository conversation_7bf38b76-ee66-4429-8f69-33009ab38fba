"""
测试配置文件是否正确加载
"""
import os
import sys

# 打印当前Python版本
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")
print("-" * 50)

# 检查环境变量文件
env_file = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_file):
    print(f"找到.env文件: {env_file}")
else:
    print(f"未找到.env文件: {env_file}")

# 检查配置文件导入
try:
    import config
    print("成功导入config模块")
    print(f"GeoServer URL: {config.GEOSERVER_URL}")
    print(f"GeoServer User: {config.GEOSERVER_USER}")
    print(f"默认工作区: {config.DEFAULT_WORKSPACE}")
except ImportError as e:
    print(f"导入config模块失败: {e}")

print("-" * 50)

# 检查依赖项
required_packages = ['requests', 'geoserver_rest', 'dotenv', 'yaml']
for package in required_packages:
    try:
        __import__(package.replace('-', '_'))
        print(f"✅ {package}已安装")
    except ImportError:
        print(f"❌ {package}未安装")

print("-" * 50)
print("配置检查完成") 