2025-08-04 11:17:50,356 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250804_111750.log
2025-08-04 11:17:50,384 - geo_publisher - INFO - 加载了 36 个任务状态
2025-08-04 11:17:50,489 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-04 11:17:50,489 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-04 11:17:50,511 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-04 11:17:50,520 - root - INFO - === GeoServer REST API服务 ===
2025-08-04 11:17:50,521 - root - INFO - 主机: 0.0.0.0
2025-08-04 11:17:50,521 - root - INFO - 端口: 5083
2025-08-04 11:17:50,522 - root - INFO - 调试模式: 禁用
2025-08-04 11:17:50,523 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-08-04 11:17:50,562 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-08-04 11:17:50,565 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-04 11:18:18,016 - batch_executor - INFO - 启动任务 e9dd7a97-5572-451b-bba5-08f189ca02c3: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:18:18,017 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:18:18] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:18:18,043 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:18:18] "GET /api/batch/status?task_id=e9dd7a97-5572-451b-bba5-08f189ca02c3 HTTP/1.1" 200 -
2025-08-04 11:19:20,142 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:19:20] "GET /api/batch/status?task_id=e9dd7a97-5572-451b-bba5-08f189ca02c3 HTTP/1.1" 200 -
2025-08-04 11:20:22,344 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:20:22] "GET /api/batch/status?task_id=e9dd7a97-5572-451b-bba5-08f189ca02c3 HTTP/1.1" 200 -
2025-08-04 11:21:24,535 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:21:24] "GET /api/batch/status?task_id=e9dd7a97-5572-451b-bba5-08f189ca02c3 HTTP/1.1" 200 -
2025-08-04 11:22:26,515 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:22:26] "GET /api/batch/status?task_id=e9dd7a97-5572-451b-bba5-08f189ca02c3 HTTP/1.1" 200 -
2025-08-04 11:23:28,632 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:23:28] "GET /api/batch/status?task_id=e9dd7a97-5572-451b-bba5-08f189ca02c3 HTTP/1.1" 200 -
2025-08-04 11:24:32,220 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:24:32] "GET /api/batch/status?task_id=e9dd7a97-5572-451b-bba5-08f189ca02c3 HTTP/1.1" 200 -
2025-08-04 11:25:34,357 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:25:34] "GET /api/batch/status?task_id=e9dd7a97-5572-451b-bba5-08f189ca02c3 HTTP/1.1" 200 -
2025-08-04 11:25:40,495 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-08-04 11:25:40,502 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 11:25:40,504 - tif_api - INFO - 输入文件大小: 32.16 MB
2025-08-04 11:25:40,505 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-04 11:25:40,507 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp
2025-08-04 11:25:40,509 - tif_api - INFO - 黑色阈值: 0
2025-08-04 11:25:40,511 - tif_api - INFO - 白色阈值: 255
2025-08-04 11:25:40,512 - tif_api - INFO - 启动异步TIF处理任务...
2025-08-04 11:25:41,946 - tif_executor - INFO - 启动TIF处理任务 1bfb6367-c531-4a87-ab9b-bf935c260be6: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp
2025-08-04 11:25:41,946 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - ============ TIF处理任务 1bfb6367-c531-4a87-ab9b-bf935c260be6 开始执行 ============
2025-08-04 11:25:41,947 - tif_api - INFO - 异步任务启动成功，任务ID: 1bfb6367-c531-4a87-ab9b-bf935c260be6
2025-08-04 11:25:41,953 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 开始时间: 2025-08-04 11:25:41
2025-08-04 11:25:41,955 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:25:41] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-08-04 11:25:41,955 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171599\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-08-04 11:25:41,959 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 系统信息:
2025-08-04 11:25:41,986 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 11:25:41,987 - tif_api - INFO - 查询任务: 1bfb6367-c531-4a87-ab9b-bf935c260be6
2025-08-04 11:25:41,961 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO -   操作系统: Windows 10.0.19045
2025-08-04 11:25:42,110 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO -   Python版本: 3.8.20
2025-08-04 11:25:42,111 - tif_api - INFO - 任务 1bfb6367-c531-4a87-ab9b-bf935c260be6 状态查询成功，当前状态: 正在运行
2025-08-04 11:25:42,115 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO -   GDAL版本: 3.9.2
2025-08-04 11:25:42,115 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:25:42] "GET /api/tif/status?task_id=1bfb6367-c531-4a87-ab9b-bf935c260be6 HTTP/1.1" 200 -
2025-08-04 11:25:42,116 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO -   GPU可用: 否
2025-08-04 11:25:42,117 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 检查参数有效性...
2025-08-04 11:25:42,119 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 11:25:42,120 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 开始执行TIF处理流程...
2025-08-04 11:25:42,121 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 11:25:42,122 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-04 11:25:42,124 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp
2025-08-04 11:25:42,137 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-08-04 11:25:42,998 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171599/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171599/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=5439, 高=5151, 波段数=4开始读取影像数据，大小约: 427.49 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759518.3524715091, 0.0728352753743704, 0.0, 2488560.9880124633, 0.0, -0.07283435420913706)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171599/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 5439x5151x3估计内存使用: 总计 427.49 MB, 单个处理块 82.99 MB开始创建掩码... (11:25:42)使用全图检测无效区域模式...将处理 6 个数据块...使用多线程处理 (7 线程)...
2025-08-04 11:25:43,003 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
处理数据块:   0%|                                                                                | 0/6 [00:00<?, ?it/s]
2025-08-04 11:25:43,043 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
处理数据块: 100%|################################################################################| 6/6 [00:00<?, ?it/s]
2025-08-04 11:25:43,045 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 已完成: 6/6 块 (100.0%)合并处理结果...
2025-08-04 11:25:43,046 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
合并结果:   0%|                                                                                  | 0/6 [00:00<?, ?it/s]
2025-08-04 11:25:43,050 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
合并结果: 100%|########################################################################| 6/6 [00:00<00:00, 1485.24it/s]
2025-08-04 11:25:43,189 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 合并进度: 6/6 块 (100.0%)统计结果: 总像素 28016289, 有效像素 16204432 (57.84%), 无效像素 11811857 (42.16%)掩码创建完成，耗时: 0.36 秒 (11:25:43)预计总处理时间: 约 1.07 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif (11:25:43)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif使用已处理过的影像信息影像信息: 宽=5439, 高=5151, 波段数=3掩码形状: (5151, 5439), 数据类型: uint8获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif, 尺寸: 5439x5151x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-08-04 11:25:43,193 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-08-04 11:25:43,662 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=64.98, std=63.52应用掩码: 11811857 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 6 个数据块
2025-08-04 11:25:43,662 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
波段 1/3 写入进度:   0%|                                                                         | 0/6 [00:00<?, ?it/s]
2025-08-04 11:25:43,667 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - [A
2025-08-04 11:25:44,061 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
波段 1/3 写入进度:  17%|##########8                                                      | 1/6 [00:00<00:01,  2.54it/s]
2025-08-04 11:25:44,061 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - [A
2025-08-04 11:25:44,115 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - [A
2025-08-04 11:25:50,514 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:07<00:14,  7.31s/it]
2025-08-04 11:25:51,092 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=74.00, std=70.05应用掩码: 11811857 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 6 个数据块
2025-08-04 11:25:51,093 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
波段 2/3 写入进度:   0%|                                                                         | 0/6 [00:00<?, ?it/s]
2025-08-04 11:25:51,099 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - [A
2025-08-04 11:25:51,233 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
波段 2/3 写入进度:  33%|#####################6                                           | 2/6 [00:00<00:00, 14.97it/s]
2025-08-04 11:25:51,234 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - [A
2025-08-04 11:25:51,382 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
波段 2/3 写入进度:  83%|######################################################1          | 5/6 [00:00<00:00, 18.41it/s]
2025-08-04 11:25:51,391 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - [A
2025-08-04 11:25:51,402 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - [A
2025-08-04 11:26:00,797 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [00:17<00:09,  9.06s/it]
2025-08-04 11:26:01,159 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=58.21, std=56.98应用掩码: 11811857 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 6 个数据块
2025-08-04 11:26:01,161 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
波段 3/3 写入进度:   0%|                                                                         | 0/6 [00:00<?, ?it/s]
2025-08-04 11:26:01,169 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - [A
2025-08-04 11:26:01,237 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - [A
2025-08-04 11:26:13,336 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [00:30<00:00, 10.65s/it]
2025-08-04 11:26:13,336 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [00:30<00:00, 10.04s/it]
2025-08-04 11:26:14,673 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif影像保存完成，耗时: 30.20 秒 (11:26:13)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp (11:26:13)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp使用NoData值: -9999, 简化容差: 0.1读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tifTIF文件信息: 宽=5439, 高=5151, 波段数=3开始读取影像数据，大小约: 320.62 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759518.3524715091, 0.0728352753743704, 0.0, 2488560.9880124633, 0.0, -0.07283435420913706)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif提取TIF文件信息...TIF文件信息: 宽=5439, 高=5151, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 11811857 个无效像素检查波段 2...波段 2 检测到 11811857 个无效像素检查波段 3...波段 3 检测到 11811857 个无效像素掩码统计: 总像素数 28016289, 有效像素数 16204432 (57.84%), 无效像素数 11811857 (42.16%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 1 个初始轮廓轮廓面积统计: 最小=16198389.00, 最大=16198389.00, 平均=16198389.00, 总数=1将 1 个轮廓转换为地理坐标...
2025-08-04 11:26:14,675 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-08-04 11:26:14,752 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00, 16.86it/s]
2025-08-04 11:26:14,786 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-08-04 11:26:15,686 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 4 个轮廓，转换为地理坐标...
2025-08-04 11:26:15,687 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/4 [00:00<?, ?it/s]
2025-08-04 11:26:15,780 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
处理轮廓: 100%|##########################################################################| 4/4 [00:00<00:00, 46.17it/s]
2025-08-04 11:26:15,868 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 处理了 20631 个坐标点合并并简化 4 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp写入shapefile特征...处理MultiPolygon，包含 5 个多边形
2025-08-04 11:26:15,868 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
写入多边形:   0%|                                                                                | 0/5 [00:00<?, ?it/s]
2025-08-04 11:26:15,879 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - ERROR - 
写入多边形: 100%|#######################################################################| 5/5 [00:00<00:00, 998.83it/s]
2025-08-04 11:26:15,910 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 处理完成，耗时: 33.78 秒 (0.56 分钟)
2025-08-04 11:26:15,911 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 处理结果: 成功
2025-08-04 11:26:15,915 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif, 大小: 107.15 MB
2025-08-04 11:26:15,918 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp, 大小: 0.92 KB
2025-08-04 11:26:15,923 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - TIF处理任务 1bfb6367-c531-4a87-ab9b-bf935c260be6 执行成功
2025-08-04 11:26:15,924 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 完成时间: 2025-08-04 11:26:15
2025-08-04 11:26:15,925 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - 状态: 运行成功
2025-08-04 11:26:15,925 - tif_task_1bfb6367-c531-4a87-ab9b-bf935c260be6 - INFO - ============ 任务执行结束 ============
2025-08-04 11:26:44,306 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 11:26:44,310 - tif_api - INFO - 查询任务: 1bfb6367-c531-4a87-ab9b-bf935c260be6
2025-08-04 11:26:44,701 - tif_api - INFO - 任务 1bfb6367-c531-4a87-ab9b-bf935c260be6 状态查询成功，当前状态: 运行成功
2025-08-04 11:26:44,707 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:26:44] "GET /api/tif/status?task_id=1bfb6367-c531-4a87-ab9b-bf935c260be6 HTTP/1.1" 200 -
2025-08-04 11:26:47,592 - geo_task_2a879d67-a3ba-466a-b935-d7aff885c034 - INFO - GeoServer发布任务 2a879d67-a3ba-466a-b935-d7aff885c034 开始执行
2025-08-04 11:26:47,589 - geo_publisher - INFO - 启动GeoTIFF发布任务 2a879d67-a3ba-466a-b935-d7aff885c034: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif, workspace=testodm
2025-08-04 11:26:47,601 - geo_task_2a879d67-a3ba-466a-b935-d7aff885c034 - INFO - 开始时间: 2025-08-04 11:26:47
2025-08-04 11:26:47,602 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:26:47] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-08-04 11:26:47,604 - geo_task_2a879d67-a3ba-466a-b935-d7aff885c034 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-08-04 11:26:47,606 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-04 11:26:47,607 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-04 11:26:47,706 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-04 11:26:47,710 - root - INFO - 未提供存储名称，使用文件名: 20250705171599
2025-08-04 11:26:47,715 - root - INFO - 未提供图层名称，使用存储名称: 20250705171599
2025-08-04 11:26:47,778 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171599\20250705171599.tif
2025-08-04 11:26:47,843 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:26:47] "GET /api/geo/status?task_id=2a879d67-a3ba-466a-b935-d7aff885c034 HTTP/1.1" 200 -
2025-08-04 11:26:49,649 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif' 到 'testodm:20250705171599'
2025-08-04 11:26:49,851 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171599
2025-08-04 11:26:49,865 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif' 成功发布为图层 'testodm:20250705171599'
2025-08-04 11:26:49,877 - geo_task_2a879d67-a3ba-466a-b935-d7aff885c034 - INFO - GeoServer发布任务 2a879d67-a3ba-466a-b935-d7aff885c034 执行成功
2025-08-04 11:26:49,878 - geo_task_2a879d67-a3ba-466a-b935-d7aff885c034 - INFO - 完成时间: 2025-08-04 11:26:49
2025-08-04 11:26:49,881 - geo_task_2a879d67-a3ba-466a-b935-d7aff885c034 - INFO - 状态: 发布成功
2025-08-04 11:27:10,632 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:27:11,209 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:27:11,250 - map_api - INFO - 找到 4 个目录
2025-08-04 11:27:11,255 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:27:11,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2404 字节
2025-08-04 11:27:11,269 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:27:11,305 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:27:11,311 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:27:11,330 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:27:11,332 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:27:11,358 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:27:11,360 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:27:11,362 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:27:11,374 - werkzeug - INFO - ************** - - [04/Aug/2025 11:27:11] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:27:16,124 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:27:16,130 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:27:16,141 - map_api - INFO - 找到 4 个目录
2025-08-04 11:27:16,143 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:27:16,149 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2404 字节
2025-08-04 11:27:16,157 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:27:16,168 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:27:16,175 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:27:16,184 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:27:16,186 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:27:16,196 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:27:16,207 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:27:16,211 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:27:16,215 - werkzeug - INFO - ************** - - [04/Aug/2025 11:27:16] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:27:19,516 - map_api - INFO - 请求获取日志: 类型=batlog, ID=e9dd7a97-5572-451b-bba5-08f189ca02c3
2025-08-04 11:27:19,520 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\e9dd7a97-5572-451b-bba5-08f189ca02c3.log
2025-08-04 11:27:19,524 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\e9dd7a97-5572-451b-bba5-08f189ca02c3.log, 内容大小: 137202 字节
2025-08-04 11:27:19,528 - werkzeug - INFO - ************** - - [04/Aug/2025 11:27:19] "GET /api/map/logs?log_type=batlog&log_id=e9dd7a97-5572-451b-bba5-08f189ca02c3 HTTP/1.1" 200 -
2025-08-04 11:27:50,855 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:27:50] "GET /api/geo/status?task_id=2a879d67-a3ba-466a-b935-d7aff885c034 HTTP/1.1" 200 -
2025-08-04 11:28:12,369 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=1bfb6367-c531-4a87-ab9b-bf935c260be6
2025-08-04 11:28:12,374 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1bfb6367-c531-4a87-ab9b-bf935c260be6.log
2025-08-04 11:28:12,379 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\1bfb6367-c531-4a87-ab9b-bf935c260be6.log, 内容大小: 10080 字节
2025-08-04 11:28:12,382 - werkzeug - INFO - ************** - - [04/Aug/2025 11:28:12] "GET /api/map/logs?log_type=tiflog&log_id=1bfb6367-c531-4a87-ab9b-bf935c260be6 HTTP/1.1" 200 -
2025-08-04 11:28:16,139 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:28:16,145 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:28:16,156 - map_api - INFO - 找到 4 个目录
2025-08-04 11:28:16,158 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:28:16,168 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2592 字节
2025-08-04 11:28:16,174 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:28:16,180 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:28:16,187 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:28:16,194 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:28:16,197 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:28:16,208 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:28:16,209 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:28:16,210 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(4)
2025-08-04 11:28:16,214 - werkzeug - INFO - ************** - - [04/Aug/2025 11:28:16] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:29:16,131 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:29:16,136 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:29:16,164 - map_api - INFO - 找到 4 个目录
2025-08-04 11:29:16,171 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:29:16,183 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2592 字节
2025-08-04 11:29:16,186 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:29:16,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:29:16,255 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:29:16,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:29:16,273 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:29:16,327 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:29:16,331 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:29:16,332 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(4)
2025-08-04 11:29:16,335 - werkzeug - INFO - ************** - - [04/Aug/2025 11:29:16] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:29:48,384 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:29:48,391 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:29:48,403 - map_api - INFO - 找到 4 个目录
2025-08-04 11:29:48,405 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:29:48,509 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-04 11:29:48,515 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:29:48,522 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:29:48,524 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:29:48,533 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:29:48,539 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:29:48,549 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:29:48,555 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:29:48,558 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-04 11:29:48,562 - werkzeug - INFO - ************** - - [04/Aug/2025 11:29:48] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:29:54,317 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:29:54,321 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:29:54,331 - map_api - INFO - 找到 4 个目录
2025-08-04 11:29:54,333 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:29:54,346 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-04 11:29:54,348 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:29:54,353 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:29:54,356 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:29:54,381 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:29:54,384 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:29:54,391 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:29:54,393 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:29:54,393 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-04 11:29:54,396 - werkzeug - INFO - ************** - - [04/Aug/2025 11:29:54] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:29:55,316 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:29:55,319 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:29:55,353 - map_api - INFO - 找到 4 个目录
2025-08-04 11:29:55,358 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:29:55,363 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-04 11:29:55,365 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:29:55,371 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:29:55,373 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:29:55,396 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:29:55,398 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:29:55,407 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:29:55,408 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:29:55,409 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-04 11:29:55,412 - werkzeug - INFO - ************** - - [04/Aug/2025 11:29:55] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:29:56,773 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:29:56,777 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:29:56,799 - map_api - INFO - 找到 4 个目录
2025-08-04 11:29:56,801 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:29:56,827 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-04 11:29:56,830 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:29:56,858 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:29:56,862 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:29:56,890 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:29:56,893 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:29:56,922 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:29:56,925 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:29:56,929 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-04 11:29:56,931 - werkzeug - INFO - ************** - - [04/Aug/2025 11:29:56] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:29:56,935 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:29:56,942 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:29:56,957 - map_api - INFO - 找到 4 个目录
2025-08-04 11:29:56,959 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:29:56,967 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-04 11:29:56,975 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:29:56,979 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:29:56,980 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:29:57,014 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:29:57,018 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:29:57,045 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:29:57,048 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:29:57,051 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-04 11:29:57,053 - werkzeug - INFO - ************** - - [04/Aug/2025 11:29:57] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:30:03,939 - batch_executor - INFO - 启动任务 8fe60cee-5933-43bf-8cc9-769052e5d803: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:30:03,941 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:30:03] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:30:04,172 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:30:04] "GET /api/batch/status?task_id=8fe60cee-5933-43bf-8cc9-769052e5d803 HTTP/1.1" 200 -
2025-08-04 11:30:49,165 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:30:49,166 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:30:49,197 - map_api - INFO - 找到 4 个目录
2025-08-04 11:30:49,198 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:30:49,225 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 1884 字节
2025-08-04 11:30:49,226 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:30:49,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:30:49,242 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:30:49,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:30:49,248 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:30:49,272 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:30:49,273 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:30:49,274 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:30:49,275 - werkzeug - INFO - ************** - - [04/Aug/2025 11:30:49] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:31:06,008 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:31:06] "GET /api/batch/status?task_id=8fe60cee-5933-43bf-8cc9-769052e5d803 HTTP/1.1" 200 -
2025-08-04 11:31:10,284 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:31:10,288 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:31:10,298 - map_api - INFO - 找到 4 个目录
2025-08-04 11:31:10,299 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:31:10,311 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 1884 字节
2025-08-04 11:31:10,313 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:31:10,333 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:31:10,336 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:31:10,342 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:31:10,345 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:31:10,352 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:31:10,355 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:31:10,356 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:31:10,359 - werkzeug - INFO - ************** - - [04/Aug/2025 11:31:10] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:31:12,975 - map_api - INFO - 请求获取日志: 类型=batlog, ID=8fe60cee-5933-43bf-8cc9-769052e5d803
2025-08-04 11:31:12,980 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\8fe60cee-5933-43bf-8cc9-769052e5d803.log
2025-08-04 11:31:12,983 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\8fe60cee-5933-43bf-8cc9-769052e5d803.log, 内容大小: 7082 字节
2025-08-04 11:31:12,985 - werkzeug - INFO - ************** - - [04/Aug/2025 11:31:12] "GET /api/map/logs?log_type=batlog&log_id=8fe60cee-5933-43bf-8cc9-769052e5d803 HTTP/1.1" 200 -
2025-08-04 11:31:29,238 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:31:29,243 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:31:29,256 - map_api - INFO - 找到 4 个目录
2025-08-04 11:31:29,258 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:31:29,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 1884 字节
2025-08-04 11:31:29,276 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:31:29,283 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:31:29,286 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:31:29,292 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:31:29,295 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:31:29,311 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:31:29,313 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:31:29,314 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:31:29,319 - werkzeug - INFO - ************** - - [04/Aug/2025 11:31:29] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:31:44,712 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:31:44,718 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:31:44,732 - map_api - INFO - 找到 4 个目录
2025-08-04 11:31:44,734 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:31:44,741 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 1884 字节
2025-08-04 11:31:44,746 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:31:44,755 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:31:44,758 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:31:44,767 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:31:44,774 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:31:44,784 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:31:44,791 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:31:44,803 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:31:44,814 - werkzeug - INFO - ************** - - [04/Aug/2025 11:31:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:31:46,949 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:31:46,954 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:31:46,967 - map_api - INFO - 找到 4 个目录
2025-08-04 11:31:46,969 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:31:46,983 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 1884 字节
2025-08-04 11:31:46,989 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:31:46,998 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:31:46,999 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:31:47,013 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:31:47,018 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:31:47,047 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:31:47,051 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:31:47,053 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:31:47,055 - werkzeug - INFO - ************** - - [04/Aug/2025 11:31:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:31:49,030 - map_api - INFO - 请求获取日志: 类型=batlog, ID=8fe60cee-5933-43bf-8cc9-769052e5d803
2025-08-04 11:31:49,035 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\8fe60cee-5933-43bf-8cc9-769052e5d803.log
2025-08-04 11:31:49,039 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\8fe60cee-5933-43bf-8cc9-769052e5d803.log, 内容大小: 7082 字节
2025-08-04 11:31:49,042 - werkzeug - INFO - ************** - - [04/Aug/2025 11:31:49] "GET /api/map/logs?log_type=batlog&log_id=8fe60cee-5933-43bf-8cc9-769052e5d803 HTTP/1.1" 200 -
2025-08-04 11:32:07,964 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:32:07,970 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:32:08,003 - map_api - INFO - 找到 4 个目录
2025-08-04 11:32:08,007 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:32:08,028 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 1884 字节
2025-08-04 11:32:08,032 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:32:08,040 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:32:08,042 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:32:08,053 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:32:08,055 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:32:08,062 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:32:08,064 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:32:08,066 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:32:08,068 - werkzeug - INFO - ************** - - [04/Aug/2025 11:32:08] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:32:08,109 - batch_executor - INFO - 启动任务 08b2e6c8-1965-44b1-bb3c-3dc0537e35ee: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:32:08,110 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:32:08] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:32:08,135 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:32:08] "GET /api/batch/status?task_id=08b2e6c8-1965-44b1-bb3c-3dc0537e35ee HTTP/1.1" 200 -
2025-08-04 11:32:10,384 - map_api - INFO - 请求获取日志: 类型=batlog, ID=8fe60cee-5933-43bf-8cc9-769052e5d803
2025-08-04 11:32:10,386 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\8fe60cee-5933-43bf-8cc9-769052e5d803.log
2025-08-04 11:32:10,387 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\8fe60cee-5933-43bf-8cc9-769052e5d803.log, 内容大小: 7082 字节
2025-08-04 11:32:10,388 - werkzeug - INFO - ************** - - [04/Aug/2025 11:32:10] "GET /api/map/logs?log_type=batlog&log_id=8fe60cee-5933-43bf-8cc9-769052e5d803 HTTP/1.1" 200 -
2025-08-04 11:32:28,181 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:32:28,183 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:32:28,201 - map_api - INFO - 找到 4 个目录
2025-08-04 11:32:28,202 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:32:28,206 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 1944 字节
2025-08-04 11:32:28,207 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:32:28,213 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:32:28,217 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:32:28,245 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:32:28,247 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:32:28,276 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:32:28,280 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:32:28,283 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:32:28,285 - werkzeug - INFO - ************** - - [04/Aug/2025 11:32:28] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:32:30,373 - map_api - INFO - 请求获取日志: 类型=batlog, ID=08b2e6c8-1965-44b1-bb3c-3dc0537e35ee
2025-08-04 11:32:30,380 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\08b2e6c8-1965-44b1-bb3c-3dc0537e35ee.log
2025-08-04 11:32:30,383 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\08b2e6c8-1965-44b1-bb3c-3dc0537e35ee.log, 内容大小: 4879 字节
2025-08-04 11:32:30,389 - werkzeug - INFO - ************** - - [04/Aug/2025 11:32:30] "GET /api/map/logs?log_type=batlog&log_id=08b2e6c8-1965-44b1-bb3c-3dc0537e35ee HTTP/1.1" 200 -
2025-08-04 11:32:32,134 - map_api - INFO - 请求获取日志: 类型=batlog, ID=8fe60cee-5933-43bf-8cc9-769052e5d803
2025-08-04 11:32:32,138 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\8fe60cee-5933-43bf-8cc9-769052e5d803.log
2025-08-04 11:32:32,143 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\8fe60cee-5933-43bf-8cc9-769052e5d803.log, 内容大小: 7082 字节
2025-08-04 11:32:32,146 - werkzeug - INFO - ************** - - [04/Aug/2025 11:32:32] "GET /api/map/logs?log_type=batlog&log_id=8fe60cee-5933-43bf-8cc9-769052e5d803 HTTP/1.1" 200 -
2025-08-04 11:32:35,853 - map_api - INFO - 请求获取日志: 类型=batlog, ID=08b2e6c8-1965-44b1-bb3c-3dc0537e35ee
2025-08-04 11:32:35,858 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\08b2e6c8-1965-44b1-bb3c-3dc0537e35ee.log
2025-08-04 11:32:35,861 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\08b2e6c8-1965-44b1-bb3c-3dc0537e35ee.log, 内容大小: 4879 字节
2025-08-04 11:32:35,864 - werkzeug - INFO - ************** - - [04/Aug/2025 11:32:35] "GET /api/map/logs?log_type=batlog&log_id=08b2e6c8-1965-44b1-bb3c-3dc0537e35ee HTTP/1.1" 200 -
2025-08-04 11:32:44,715 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:32:44,719 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:32:44,732 - map_api - INFO - 找到 4 个目录
2025-08-04 11:32:44,734 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:32:44,759 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 1944 字节
2025-08-04 11:32:44,763 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:32:44,806 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:32:44,814 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:32:44,838 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:32:44,844 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:32:44,870 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:32:44,873 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:32:44,876 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:32:44,885 - werkzeug - INFO - ************** - - [04/Aug/2025 11:32:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:32:54,164 - map_api - INFO - 请求获取日志: 类型=batlog, ID=08b2e6c8-1965-44b1-bb3c-3dc0537e35ee
2025-08-04 11:32:54,169 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\08b2e6c8-1965-44b1-bb3c-3dc0537e35ee.log
2025-08-04 11:32:54,171 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\08b2e6c8-1965-44b1-bb3c-3dc0537e35ee.log, 内容大小: 4879 字节
2025-08-04 11:32:54,173 - werkzeug - INFO - ************** - - [04/Aug/2025 11:32:54] "GET /api/map/logs?log_type=batlog&log_id=08b2e6c8-1965-44b1-bb3c-3dc0537e35ee HTTP/1.1" 200 -
2025-08-04 11:32:58,965 - map_api - INFO - 请求获取日志: 类型=batlog, ID=08b2e6c8-1965-44b1-bb3c-3dc0537e35ee
2025-08-04 11:32:58,971 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\08b2e6c8-1965-44b1-bb3c-3dc0537e35ee.log
2025-08-04 11:32:58,973 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\08b2e6c8-1965-44b1-bb3c-3dc0537e35ee.log, 内容大小: 4879 字节
2025-08-04 11:32:58,976 - werkzeug - INFO - ************** - - [04/Aug/2025 11:32:58] "GET /api/map/logs?log_type=batlog&log_id=08b2e6c8-1965-44b1-bb3c-3dc0537e35ee HTTP/1.1" 200 -
2025-08-04 11:33:09,990 - batch_executor - INFO - 启动任务 5bca7da7-7ecf-4e87-b047-511c085bbcaa: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:33:09,991 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:33:09] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:33:10,018 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:33:10] "GET /api/batch/status?task_id=5bca7da7-7ecf-4e87-b047-511c085bbcaa HTTP/1.1" 200 -
2025-08-04 11:33:25,301 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:33:25,302 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:33:25,312 - map_api - INFO - 找到 4 个目录
2025-08-04 11:33:25,313 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:33:25,316 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2004 字节
2025-08-04 11:33:25,317 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:33:25,323 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:33:25,325 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:33:25,329 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:33:25,329 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:33:25,333 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:33:25,334 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:33:25,340 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:33:25,341 - werkzeug - INFO - ************** - - [04/Aug/2025 11:33:25] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:33:27,324 - map_api - INFO - 请求获取日志: 类型=batlog, ID=5bca7da7-7ecf-4e87-b047-511c085bbcaa
2025-08-04 11:33:27,325 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\5bca7da7-7ecf-4e87-b047-511c085bbcaa.log
2025-08-04 11:33:27,327 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\5bca7da7-7ecf-4e87-b047-511c085bbcaa.log, 内容大小: 4879 字节
2025-08-04 11:33:27,331 - werkzeug - INFO - ************** - - [04/Aug/2025 11:33:27] "GET /api/map/logs?log_type=batlog&log_id=5bca7da7-7ecf-4e87-b047-511c085bbcaa HTTP/1.1" 200 -
2025-08-04 11:33:34,492 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:33:34,496 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:33:34,527 - map_api - INFO - 找到 4 个目录
2025-08-04 11:33:34,531 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:33:34,555 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2004 字节
2025-08-04 11:33:34,559 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:33:34,564 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:33:34,566 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:33:34,573 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:33:34,576 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:33:34,603 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:33:34,607 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:33:34,611 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:33:34,613 - werkzeug - INFO - ************** - - [04/Aug/2025 11:33:34] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:33:44,717 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:33:44,721 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:33:44,736 - map_api - INFO - 找到 4 个目录
2025-08-04 11:33:44,738 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:33:44,746 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2004 字节
2025-08-04 11:33:44,751 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:33:44,759 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:33:44,762 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:33:44,775 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:33:44,777 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:33:44,790 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:33:44,791 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:33:44,798 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:33:44,805 - werkzeug - INFO - ************** - - [04/Aug/2025 11:33:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:34:11,983 - batch_executor - INFO - 启动任务 925c4f4c-34e9-474f-85c4-f161e99356fa: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:34:11,985 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:34:11] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:34:12,149 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:34:12] "GET /api/batch/status?task_id=925c4f4c-34e9-474f-85c4-f161e99356fa HTTP/1.1" 200 -
2025-08-04 11:34:16,997 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:34:16,997 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:34:17,006 - map_api - INFO - 找到 4 个目录
2025-08-04 11:34:17,008 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:34:17,033 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2064 字节
2025-08-04 11:34:17,036 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:34:17,064 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:34:17,066 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:34:17,095 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:34:17,096 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:34:17,101 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:34:17,102 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:34:17,103 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:34:17,104 - werkzeug - INFO - ************** - - [04/Aug/2025 11:34:17] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:34:36,133 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:34:36,134 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:34:36,154 - map_api - INFO - 找到 4 个目录
2025-08-04 11:34:36,160 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:34:36,168 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2064 字节
2025-08-04 11:34:36,171 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:34:36,196 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:34:36,198 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:34:36,228 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:34:36,235 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:34:36,276 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:34:36,279 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:34:36,288 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:34:36,295 - werkzeug - INFO - ************** - - [04/Aug/2025 11:34:36] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:34:44,726 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:34:44,732 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:34:44,741 - map_api - INFO - 找到 4 个目录
2025-08-04 11:34:44,742 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:34:44,748 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2064 字节
2025-08-04 11:34:44,749 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:34:44,756 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:34:44,760 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:34:44,766 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:34:44,768 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:34:44,773 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:34:44,776 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:34:44,779 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:34:44,781 - werkzeug - INFO - ************** - - [04/Aug/2025 11:34:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:34:48,598 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:34:48,603 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:34:48,617 - map_api - INFO - 找到 4 个目录
2025-08-04 11:34:48,622 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:34:48,634 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2064 字节
2025-08-04 11:34:48,637 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:34:48,643 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:34:48,645 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:34:48,652 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:34:48,660 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:34:48,666 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:34:48,668 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:34:48,674 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:34:48,677 - werkzeug - INFO - ************** - - [04/Aug/2025 11:34:48] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:34:52,276 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:34:52,279 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:34:52,286 - map_api - INFO - 找到 4 个目录
2025-08-04 11:34:52,288 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:34:52,299 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2064 字节
2025-08-04 11:34:52,302 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:34:52,307 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:34:52,310 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:34:52,315 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:34:52,316 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:34:52,322 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:34:52,325 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:34:52,326 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:34:52,328 - werkzeug - INFO - ************** - - [04/Aug/2025 11:34:52] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:34:58,125 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:34:58,130 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:34:58,142 - map_api - INFO - 找到 4 个目录
2025-08-04 11:34:58,144 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:34:58,151 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2064 字节
2025-08-04 11:34:58,153 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:34:58,160 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:34:58,165 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:34:58,172 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:34:58,173 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:34:58,187 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:34:58,188 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:34:58,189 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:34:58,195 - werkzeug - INFO - ************** - - [04/Aug/2025 11:34:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:34:58,917 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:34:58,920 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:34:58,929 - map_api - INFO - 找到 4 个目录
2025-08-04 11:34:58,930 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:34:58,937 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2064 字节
2025-08-04 11:34:58,938 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:34:58,945 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:34:58,949 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:34:58,956 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:34:58,961 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:34:58,968 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:34:58,970 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:34:58,971 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:34:58,980 - werkzeug - INFO - ************** - - [04/Aug/2025 11:34:58] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:35:14,241 - batch_executor - INFO - 启动任务 d65d1543-f56f-43e4-8920-c6f9c2cbd8bb: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:35:14,242 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:35:14] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:35:14,265 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:35:14] "GET /api/batch/status?task_id=d65d1543-f56f-43e4-8920-c6f9c2cbd8bb HTTP/1.1" 200 -
2025-08-04 11:35:45,168 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:35:45,169 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:35:45,175 - map_api - INFO - 找到 4 个目录
2025-08-04 11:35:45,176 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:35:45,197 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2124 字节
2025-08-04 11:35:45,199 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:35:45,213 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:35:45,215 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:35:45,219 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:35:45,221 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:35:45,244 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:35:45,246 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:35:45,246 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:35:45,247 - werkzeug - INFO - ************** - - [04/Aug/2025 11:35:45] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:35:49,813 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:35:49,814 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:35:49,823 - map_api - INFO - 找到 4 个目录
2025-08-04 11:35:49,837 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:35:49,871 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2124 字节
2025-08-04 11:35:49,872 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:35:49,879 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:35:49,884 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:35:49,891 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:35:49,893 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:35:49,905 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:35:49,907 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:35:49,908 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:35:49,910 - werkzeug - INFO - ************** - - [04/Aug/2025 11:35:49] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:36:08,236 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:36:08,241 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:36:08,252 - map_api - INFO - 找到 4 个目录
2025-08-04 11:36:08,257 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:36:08,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2124 字节
2025-08-04 11:36:08,266 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:36:08,284 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:36:08,285 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:36:08,299 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:36:08,303 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:36:08,309 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:36:08,310 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:36:08,311 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:36:08,313 - werkzeug - INFO - ************** - - [04/Aug/2025 11:36:08] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:36:09,954 - map_api - INFO - 请求获取日志: 类型=batlog, ID=d65d1543-f56f-43e4-8920-c6f9c2cbd8bb
2025-08-04 11:36:09,957 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\d65d1543-f56f-43e4-8920-c6f9c2cbd8bb.log
2025-08-04 11:36:09,960 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\d65d1543-f56f-43e4-8920-c6f9c2cbd8bb.log, 内容大小: 4879 字节
2025-08-04 11:36:09,963 - werkzeug - INFO - ************** - - [04/Aug/2025 11:36:09] "GET /api/map/logs?log_type=batlog&log_id=d65d1543-f56f-43e4-8920-c6f9c2cbd8bb HTTP/1.1" 200 -
2025-08-04 11:36:16,243 - batch_executor - INFO - 启动任务 5392a6f4-6b47-4876-9114-4b21e04801fc: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:36:16,245 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:36:16] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:36:16,269 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:36:16] "GET /api/batch/status?task_id=5392a6f4-6b47-4876-9114-4b21e04801fc HTTP/1.1" 200 -
2025-08-04 11:36:25,440 - map_api - INFO - 请求重命名任务信息文件: 20250705171601
2025-08-04 11:36:25,443 - map_api - INFO - 开始重命名任务信息文件: 20250705171601
2025-08-04 11:36:25,461 - map_api - INFO - 成功读取ODM配置: {'PATHS': {'airflow_data_path': '/opt/airflow/data', 'window_data_path': 'D:/Drone_Project/nginxData'}, 'API': {'geoserver_host': 'host.docker.internal', 'geoserver_hd_port': '5083'}, 'GEOSERVER': {'odm_workspace': 'testodm'}, 'TASK': {'id': '20250705171599', 'time': '2025-08-04 03:30:01'}}
2025-08-04 11:36:25,463 - map_api - INFO - 任务信息文件路径: D:/Drone_Project/nginxData\ODM\Input\20250705171601\TaskInfo.json
2025-08-04 11:36:25,467 - map_api - INFO - 成功将任务信息文件重命名为: D:/Drone_Project/nginxData\ODM\Input\20250705171601\RemoveTask20250804113625.json
2025-08-04 11:36:25,467 - werkzeug - INFO - ************** - - [04/Aug/2025 11:36:25] "GET /api/map/odm/task/rename-info?task_id=20250705171601 HTTP/1.1" 200 -
2025-08-04 11:36:25,479 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:36:25,480 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:36:25,489 - map_api - INFO - 找到 4 个目录
2025-08-04 11:36:25,490 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:36:25,518 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2184 字节
2025-08-04 11:36:25,521 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:36:25,525 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:36:25,532 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:36:25,543 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:36:25,546 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:36:25,553 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:36:25,559 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:36:25,564 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:36:25,571 - werkzeug - INFO - ************** - - [04/Aug/2025 11:36:25] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:36:27,661 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:36:27,665 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:36:27,691 - map_api - INFO - 找到 4 个目录
2025-08-04 11:36:27,695 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:36:27,719 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2184 字节
2025-08-04 11:36:27,723 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:36:27,751 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:36:27,753 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:36:27,780 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:36:27,783 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:36:27,811 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:36:27,815 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:36:27,816 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:36:27,818 - werkzeug - INFO - ************** - - [04/Aug/2025 11:36:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:36:44,726 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:36:44,730 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:36:44,761 - map_api - INFO - 找到 4 个目录
2025-08-04 11:36:44,765 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:36:44,787 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2184 字节
2025-08-04 11:36:44,792 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:36:44,818 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:36:44,822 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:36:44,849 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:36:44,852 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:36:44,881 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:36:44,884 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:36:44,885 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:36:44,888 - werkzeug - INFO - ************** - - [04/Aug/2025 11:36:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:36:48,853 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:36:48,857 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:36:48,866 - map_api - INFO - 找到 4 个目录
2025-08-04 11:36:48,868 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:36:48,874 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2184 字节
2025-08-04 11:36:48,876 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:36:48,910 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:36:48,912 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:36:48,920 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:36:48,922 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:36:48,942 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:36:48,946 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:36:48,947 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:36:48,949 - werkzeug - INFO - ************** - - [04/Aug/2025 11:36:48] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:36:53,589 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:36:53,595 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:36:53,623 - map_api - INFO - 找到 4 个目录
2025-08-04 11:36:53,627 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:36:53,631 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2184 字节
2025-08-04 11:36:53,633 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:36:53,654 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:36:53,657 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:36:53,667 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:36:53,672 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:36:53,698 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:36:53,702 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:36:53,703 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:36:53,706 - werkzeug - INFO - ************** - - [04/Aug/2025 11:36:53] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:37:18,410 - batch_executor - INFO - 启动任务 2ec48cf3-78ba-4eb6-86bf-b0ab3578c75f: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:37:18,412 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:37:18] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:37:18,438 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:37:18] "GET /api/batch/status?task_id=2ec48cf3-78ba-4eb6-86bf-b0ab3578c75f HTTP/1.1" 200 -
2025-08-04 11:37:44,722 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:37:44,723 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:37:44,732 - map_api - INFO - 找到 4 个目录
2025-08-04 11:37:44,733 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:37:44,737 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2244 字节
2025-08-04 11:37:44,738 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:37:44,743 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:37:44,744 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:37:44,749 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:37:44,753 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:37:44,758 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:37:44,759 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:37:44,760 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:37:44,761 - werkzeug - INFO - ************** - - [04/Aug/2025 11:37:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:37:47,220 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:37:47,221 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:37:47,232 - map_api - INFO - 找到 4 个目录
2025-08-04 11:37:47,234 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:37:47,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2244 字节
2025-08-04 11:37:47,247 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:37:47,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:37:47,258 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:37:47,265 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:37:47,267 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:37:47,274 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:37:47,277 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:37:47,278 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:37:47,280 - werkzeug - INFO - ************** - - [04/Aug/2025 11:37:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:38:19,764 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:38:19,768 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:38:19,790 - map_api - INFO - 找到 4 个目录
2025-08-04 11:38:19,791 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:38:19,817 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2244 字节
2025-08-04 11:38:19,821 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:38:19,826 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:38:19,829 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:38:19,832 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:38:19,836 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:38:19,863 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:38:19,866 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:38:19,868 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:38:19,870 - werkzeug - INFO - ************** - - [04/Aug/2025 11:38:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:38:20,835 - batch_executor - INFO - 启动任务 d29e812f-71ec-4ef2-9d85-3314946fbb61: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:38:20,837 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:38:20] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:38:20,859 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:38:20] "GET /api/batch/status?task_id=d29e812f-71ec-4ef2-9d85-3314946fbb61 HTTP/1.1" 200 -
2025-08-04 11:38:44,712 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:38:44,714 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:38:44,746 - map_api - INFO - 找到 4 个目录
2025-08-04 11:38:44,747 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:38:44,772 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2304 字节
2025-08-04 11:38:44,775 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:38:44,805 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:38:44,806 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:38:44,835 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:38:44,836 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:38:44,842 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:38:44,844 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:38:44,844 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:38:44,846 - werkzeug - INFO - ************** - - [04/Aug/2025 11:38:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:39:05,292 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:39:05,293 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:39:05,306 - map_api - INFO - 找到 4 个目录
2025-08-04 11:39:05,308 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:39:05,325 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2304 字节
2025-08-04 11:39:05,327 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:39:05,334 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:39:05,336 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:39:05,357 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:39:05,361 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:39:05,369 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:39:05,370 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:39:05,377 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:39:05,379 - werkzeug - INFO - ************** - - [04/Aug/2025 11:39:05] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:39:22,955 - batch_executor - INFO - 启动任务 ea79a80e-cd07-41da-8de2-3882e4bb1fab: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:39:22,956 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:39:22] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:39:22,980 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:39:22] "GET /api/batch/status?task_id=ea79a80e-cd07-41da-8de2-3882e4bb1fab HTTP/1.1" 200 -
2025-08-04 11:39:37,885 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:39:37,886 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:39:37,909 - map_api - INFO - 找到 4 个目录
2025-08-04 11:39:37,910 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:39:37,914 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2364 字节
2025-08-04 11:39:37,915 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:39:37,937 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:39:37,939 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:39:37,953 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:39:37,954 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:39:37,969 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:39:37,970 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:39:37,971 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:39:37,972 - werkzeug - INFO - ************** - - [04/Aug/2025 11:39:37] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:39:44,722 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:39:44,723 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:39:44,755 - map_api - INFO - 找到 4 个目录
2025-08-04 11:39:44,758 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:39:44,763 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2364 字节
2025-08-04 11:39:44,764 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:39:44,783 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:39:44,787 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:39:44,815 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:39:44,819 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:39:44,846 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:39:44,850 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:39:44,851 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:39:44,855 - werkzeug - INFO - ************** - - [04/Aug/2025 11:39:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:40:25,082 - batch_executor - INFO - 启动任务 90c9ece2-9bd5-4842-9695-e70cecda3058: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:40:25,095 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:40:25] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:40:26,312 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:40:26] "GET /api/batch/status?task_id=90c9ece2-9bd5-4842-9695-e70cecda3058 HTTP/1.1" 200 -
2025-08-04 11:40:36,199 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:40:36,200 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:40:36,208 - map_api - INFO - 找到 4 个目录
2025-08-04 11:40:36,209 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:40:36,229 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:40:36,231 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:40:36,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:40:36,237 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:40:36,260 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:40:36,262 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:40:36,277 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:40:36,280 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:40:36,281 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:40:36,282 - werkzeug - INFO - ************** - - [04/Aug/2025 11:40:36] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:40:37,820 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:40:37,821 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:40:37,832 - map_api - INFO - 找到 4 个目录
2025-08-04 11:40:37,834 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:40:37,840 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:40:37,846 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:40:37,871 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:40:37,875 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:40:37,879 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:40:37,881 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:40:37,885 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:40:37,891 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:40:37,893 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:40:37,895 - werkzeug - INFO - ************** - - [04/Aug/2025 11:40:37] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:40:40,201 - map_api - INFO - 请求获取日志: 类型=batlog, ID=90c9ece2-9bd5-4842-9695-e70cecda3058
2025-08-04 11:40:40,205 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\90c9ece2-9bd5-4842-9695-e70cecda3058.log
2025-08-04 11:40:40,208 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\90c9ece2-9bd5-4842-9695-e70cecda3058.log, 内容大小: 4879 字节
2025-08-04 11:40:40,210 - werkzeug - INFO - ************** - - [04/Aug/2025 11:40:40] "GET /api/map/logs?log_type=batlog&log_id=90c9ece2-9bd5-4842-9695-e70cecda3058 HTTP/1.1" 200 -
2025-08-04 11:40:43,693 - map_api - INFO - 请求获取日志: 类型=batlog, ID=5392a6f4-6b47-4876-9114-4b21e04801fc
2025-08-04 11:40:43,697 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\5392a6f4-6b47-4876-9114-4b21e04801fc.log
2025-08-04 11:40:43,700 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\5392a6f4-6b47-4876-9114-4b21e04801fc.log, 内容大小: 4879 字节
2025-08-04 11:40:43,702 - werkzeug - INFO - ************** - - [04/Aug/2025 11:40:43] "GET /api/map/logs?log_type=batlog&log_id=5392a6f4-6b47-4876-9114-4b21e04801fc HTTP/1.1" 200 -
2025-08-04 11:40:44,714 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:40:44,718 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:40:44,727 - map_api - INFO - 找到 4 个目录
2025-08-04 11:40:44,730 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:40:44,737 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:40:44,738 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:40:44,745 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:40:44,748 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:40:44,753 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:40:44,754 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:40:44,760 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:40:44,764 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:40:44,765 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:40:44,767 - werkzeug - INFO - ************** - - [04/Aug/2025 11:40:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:40:47,668 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:40:47,671 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:40:47,683 - map_api - INFO - 找到 4 个目录
2025-08-04 11:40:47,685 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:40:47,693 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:40:47,696 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:40:47,704 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:40:47,705 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:40:47,732 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:40:47,738 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:40:47,747 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:40:47,753 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:40:47,754 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:40:47,756 - werkzeug - INFO - ************** - - [04/Aug/2025 11:40:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:40:51,460 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:40:51,466 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:40:51,475 - map_api - INFO - 找到 4 个目录
2025-08-04 11:40:51,477 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:40:51,498 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:40:51,500 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:40:51,508 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:40:51,512 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:40:51,526 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:40:51,533 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:40:51,539 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:40:51,540 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:40:51,542 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:40:51,552 - werkzeug - INFO - ************** - - [04/Aug/2025 11:40:51] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:40:53,607 - map_api - INFO - 请求获取日志: 类型=batlog, ID=90c9ece2-9bd5-4842-9695-e70cecda3058
2025-08-04 11:40:53,610 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\90c9ece2-9bd5-4842-9695-e70cecda3058.log
2025-08-04 11:40:53,612 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\90c9ece2-9bd5-4842-9695-e70cecda3058.log, 内容大小: 4879 字节
2025-08-04 11:40:53,614 - werkzeug - INFO - ************** - - [04/Aug/2025 11:40:53] "GET /api/map/logs?log_type=batlog&log_id=90c9ece2-9bd5-4842-9695-e70cecda3058 HTTP/1.1" 200 -
2025-08-04 11:41:00,470 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:41:00,475 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:41:00,485 - map_api - INFO - 找到 4 个目录
2025-08-04 11:41:00,491 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:41:00,497 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:41:00,503 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:41:00,529 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:41:00,532 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:41:00,539 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 11:41:00,540 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:41:00,561 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:41:00,564 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:41:00,565 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 11:41:00,568 - werkzeug - INFO - ************** - - [04/Aug/2025 11:41:00] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:41:11,126 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-08-04 11:41:11,129 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 11:41:11,133 - tif_api - INFO - 输入文件大小: 222.52 MB
2025-08-04 11:41:11,134 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-04 11:41:11,136 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-08-04 11:41:11,138 - tif_api - INFO - 黑色阈值: 0
2025-08-04 11:41:11,139 - tif_api - INFO - 白色阈值: 255
2025-08-04 11:41:11,140 - tif_api - INFO - 启动异步TIF处理任务...
2025-08-04 11:41:11,144 - tif_executor - INFO - 启动TIF处理任务 add2250d-60e9-44b3-a6ac-ade953e72018: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-08-04 11:41:11,145 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - ============ TIF处理任务 add2250d-60e9-44b3-a6ac-ade953e72018 开始执行 ============
2025-08-04 11:41:11,145 - tif_api - INFO - 异步任务启动成功，任务ID: add2250d-60e9-44b3-a6ac-ade953e72018
2025-08-04 11:41:11,147 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 开始时间: 2025-08-04 11:41:11
2025-08-04 11:41:11,149 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:41:11] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-08-04 11:41:11,166 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-08-04 11:41:11,176 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 系统信息:
2025-08-04 11:41:11,177 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO -   操作系统: Windows 10.0.19045
2025-08-04 11:41:11,178 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO -   Python版本: 3.8.20
2025-08-04 11:41:11,178 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO -   GDAL版本: 3.9.2
2025-08-04 11:41:11,179 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO -   GPU可用: 否
2025-08-04 11:41:11,180 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 检查参数有效性...
2025-08-04 11:41:11,182 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 11:41:11,191 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 开始执行TIF处理流程...
2025-08-04 11:41:11,198 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 11:41:11,198 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 11:41:11,199 - tif_api - INFO - 查询任务: add2250d-60e9-44b3-a6ac-ade953e72018
2025-08-04 11:41:11,200 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-04 11:41:11,201 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-08-04 11:41:11,277 - tif_api - INFO - 任务 add2250d-60e9-44b3-a6ac-ade953e72018 状态查询成功，当前状态: 正在运行
2025-08-04 11:41:11,283 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:41:11] "GET /api/tif/status?task_id=add2250d-60e9-44b3-a6ac-ade953e72018 HTTP/1.1" 200 -
2025-08-04 11:41:13,557 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:41:13,561 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:41:13,588 - map_api - INFO - 找到 4 个目录
2025-08-04 11:41:13,590 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:41:13,599 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:41:13,603 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:41:13,618 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:41:13,623 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:41:13,645 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 11:41:13,654 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:41:13,663 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:41:13,673 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:41:13,684 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 11:41:13,698 - werkzeug - INFO - ************** - - [04/Aug/2025 11:41:13] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:41:14,980 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:41:14,984 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:41:14,992 - map_api - INFO - 找到 4 个目录
2025-08-04 11:41:14,995 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:41:15,007 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:41:15,008 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:41:15,015 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:41:15,017 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:41:15,023 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 11:41:15,025 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:41:15,033 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:41:15,035 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:41:15,036 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 11:41:15,039 - werkzeug - INFO - ************** - - [04/Aug/2025 11:41:15] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:41:17,573 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17007, 高=20364, 波段数=4开始读取影像数据，大小约: 5284.58 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (11:41:17)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-08-04 11:41:17,598 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-08-04 11:41:17,989 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:07,  2.59it/s]
2025-08-04 11:41:18,325 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:01, 12.71it/s]
2025-08-04 11:41:18,402 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:41:18,492 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:41:18,599 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 17.72it/s]
2025-08-04 11:41:18,636 - map_api - INFO - 找到 4 个目录
2025-08-04 11:41:18,638 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:41:18,705 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理数据块:  95%|##################################################################6   | 20/21 [00:01<00:00, 22.96it/s]
2025-08-04 11:41:18,707 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:01<00:00, 19.02it/s]
2025-08-04 11:41:18,712 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-08-04 11:41:18,713 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-08-04 11:41:18,766 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 411.76it/s]
2025-08-04 11:41:18,842 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:41:18,847 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:41:18,944 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:41:18,950 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:41:19,062 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 11:41:19,068 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:41:19,141 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:41:19,147 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:41:19,151 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 11:41:19,156 - werkzeug - INFO - ************** - - [04/Aug/2025 11:41:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:41:25,896 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 2.87 秒 (11:41:20)预计总处理时间: 约 8.61 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif (11:41:20)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif使用已处理过的影像信息影像信息: 宽=17007, 高=20364, 波段数=3掩码形状: (20364, 17007), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 尺寸: 17007x20364x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-08-04 11:41:25,898 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-08-04 11:41:35,018 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=42.58, std=63.34应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-08-04 11:41:35,023 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-08-04 11:41:35,025 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:41:35,156 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:00, 23.14it/s]
2025-08-04 11:41:35,157 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:41:35,366 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:00, 16.91it/s]
2025-08-04 11:41:35,367 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:41:35,551 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:00<00:00, 14.06it/s]
2025-08-04 11:41:35,552 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:41:35,773 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 11.80it/s]
2025-08-04 11:41:35,774 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:41:35,952 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:00<00:00, 11.59it/s]
2025-08-04 11:41:35,953 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:41:36,230 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:01<00:00,  9.72it/s]
2025-08-04 11:41:36,231 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:41:36,494 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:01<00:00,  8.93it/s]
2025-08-04 11:41:36,494 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:41:36,645 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:01<00:00,  8.40it/s]
2025-08-04 11:41:36,649 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:41:36,806 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:01<00:00,  7.83it/s]
2025-08-04 11:41:36,807 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:41:36,967 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:01<00:00,  7.39it/s]
2025-08-04 11:41:36,967 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:41:37,163 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:02<00:00,  6.65it/s]
2025-08-04 11:41:37,164 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:41:37,209 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:41:45,464 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:41:45,474 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:41:45,562 - map_api - INFO - 找到 4 个目录
2025-08-04 11:41:45,577 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:41:45,616 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:41:45,625 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:41:45,696 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:41:45,702 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:41:45,716 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 11:41:45,719 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:41:45,751 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:41:45,757 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:41:45,759 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 11:41:45,781 - werkzeug - INFO - ************** - - [04/Aug/2025 11:41:45] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:42:14,108 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 11:42:14,114 - tif_api - INFO - 查询任务: add2250d-60e9-44b3-a6ac-ade953e72018
2025-08-04 11:42:14,125 - tif_api - INFO - 任务 add2250d-60e9-44b3-a6ac-ade953e72018 状态查询成功，当前状态: 正在运行
2025-08-04 11:42:14,129 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:42:14] "GET /api/tif/status?task_id=add2250d-60e9-44b3-a6ac-ade953e72018 HTTP/1.1" 200 -
2025-08-04 11:42:26,906 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:00<02:01, 61.00s/it]
2025-08-04 11:42:33,453 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=45.79, std=65.41应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-08-04 11:42:33,472 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-08-04 11:42:33,473 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:34,681 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:01<00:24,  1.21s/it]
2025-08-04 11:42:34,682 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:35,003 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:01<00:13,  1.46it/s]
2025-08-04 11:42:35,003 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:35,243 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:08,  2.07it/s]
2025-08-04 11:42:35,244 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:35,607 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:02<00:07,  2.30it/s]
2025-08-04 11:42:35,608 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:35,736 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:02<00:05,  3.08it/s]
2025-08-04 11:42:35,737 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:35,868 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:03,  3.85it/s]
2025-08-04 11:42:35,869 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:36,081 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:02<00:03,  4.09it/s]
2025-08-04 11:42:36,082 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:36,248 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:02<00:02,  4.55it/s]
2025-08-04 11:42:36,249 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:36,390 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:02<00:02,  5.13it/s]
2025-08-04 11:42:36,390 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:36,667 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:03<00:02,  4.53it/s]
2025-08-04 11:42:36,667 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:37,115 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:03<00:02,  3.45it/s]
2025-08-04 11:42:37,133 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:37,430 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:03<00:02,  3.36it/s]
2025-08-04 11:42:37,431 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:37,776 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:04<00:02,  3.20it/s]
2025-08-04 11:42:37,777 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:38,151 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:04<00:02,  3.02it/s]
2025-08-04 11:42:38,151 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:38,436 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:04<00:01,  3.15it/s]
2025-08-04 11:42:38,437 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:38,739 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:05<00:01,  3.19it/s]
2025-08-04 11:42:38,740 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:40,882 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:07<00:03,  1.16it/s]
2025-08-04 11:42:40,882 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:44,396 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:10<00:04,  1.66s/it]
2025-08-04 11:42:44,397 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:46,729 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:42:46,732 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:42:46,809 - map_api - INFO - 找到 4 个目录
2025-08-04 11:42:46,815 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:42:46,839 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:42:46,841 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:42:46,864 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:42:46,867 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:42:46,910 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 11:42:46,914 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:42:46,972 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:42:46,977 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:42:46,980 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 11:42:47,000 - werkzeug - INFO - ************** - - [04/Aug/2025 11:42:47] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:42:50,067 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:16<00:05,  2.86s/it]
2025-08-04 11:42:50,068 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:55,377 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:21<00:03,  3.60s/it]
2025-08-04 11:42:55,377 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:42:55,384 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:43:15,523 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 11:43:15,528 - tif_api - INFO - 查询任务: add2250d-60e9-44b3-a6ac-ade953e72018
2025-08-04 11:43:15,543 - tif_api - INFO - 任务 add2250d-60e9-44b3-a6ac-ade953e72018 状态查询成功，当前状态: 正在运行
2025-08-04 11:43:15,551 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:43:15] "GET /api/tif/status?task_id=add2250d-60e9-44b3-a6ac-ade953e72018 HTTP/1.1" 200 -
2025-08-04 11:43:39,966 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:43:39,972 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:43:39,981 - map_api - INFO - 找到 4 个目录
2025-08-04 11:43:39,983 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:43:39,992 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:43:39,996 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:43:40,003 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:43:40,006 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:43:40,012 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 11:43:40,014 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:43:40,019 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:43:40,026 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:43:40,028 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 11:43:40,031 - werkzeug - INFO - ************** - - [04/Aug/2025 11:43:40] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:43:45,179 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:43:45,182 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:43:45,193 - map_api - INFO - 找到 4 个目录
2025-08-04 11:43:45,195 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:43:45,203 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:43:45,204 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:43:45,210 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:43:45,217 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:43:45,223 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 11:43:45,226 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:43:45,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:43:45,237 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:43:45,241 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 11:43:45,248 - werkzeug - INFO - ************** - - [04/Aug/2025 11:43:45] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:44:13,124 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [02:47<01:27, 87.60s/it]
2025-08-04 11:44:17,066 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 11:44:17,071 - tif_api - INFO - 查询任务: add2250d-60e9-44b3-a6ac-ade953e72018
2025-08-04 11:44:17,082 - tif_api - INFO - 任务 add2250d-60e9-44b3-a6ac-ade953e72018 状态查询成功，当前状态: 正在运行
2025-08-04 11:44:17,091 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:44:17] "GET /api/tif/status?task_id=add2250d-60e9-44b3-a6ac-ade953e72018 HTTP/1.1" 200 -
2025-08-04 11:44:23,758 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=35.76, std=51.77应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-08-04 11:44:23,763 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-08-04 11:44:23,765 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:44:24,482 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:14,  1.40it/s]
2025-08-04 11:44:24,483 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:44:24,809 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:01<00:09,  2.06it/s]
2025-08-04 11:44:24,810 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:44:25,189 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:07,  2.28it/s]
2025-08-04 11:44:25,190 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:44:25,550 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:01<00:06,  2.46it/s]
2025-08-04 11:44:25,550 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:44:25,653 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  5.78it/s]
2025-08-04 11:44:25,653 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:44:25,773 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:02<00:00, 10.51it/s]
2025-08-04 11:44:25,776 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:44:25,882 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:00, 13.63it/s]
2025-08-04 11:44:25,884 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:44:26,004 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 18/21 [00:02<00:00, 17.91it/s]
2025-08-04 11:44:26,005 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:44:26,102 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - [A
2025-08-04 11:45:18,195 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 11:45:18,200 - tif_api - INFO - 查询任务: add2250d-60e9-44b3-a6ac-ade953e72018
2025-08-04 11:45:18,246 - tif_api - INFO - 任务 add2250d-60e9-44b3-a6ac-ade953e72018 状态查询成功，当前状态: 正在运行
2025-08-04 11:45:18,251 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:45:18] "GET /api/tif/status?task_id=add2250d-60e9-44b3-a6ac-ade953e72018 HTTP/1.1" 200 -
2025-08-04 11:45:19,225 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:45:19,231 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:45:19,271 - map_api - INFO - 找到 4 个目录
2025-08-04 11:45:19,275 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:45:19,288 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2464 字节
2025-08-04 11:45:19,290 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:45:19,314 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:45:19,318 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:45:19,326 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 11:45:19,328 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:45:19,346 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:45:19,349 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:45:19,353 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 11:45:19,357 - werkzeug - INFO - ************** - - [04/Aug/2025 11:45:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:45:30,271 - batch_executor - INFO - 启动任务 56354957-f205-4368-8db7-7d71cb69700a: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:45:30,282 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:45:30] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:45:30,300 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:45:30] "GET /api/batch/status?task_id=56354957-f205-4368-8db7-7d71cb69700a HTTP/1.1" 200 -
2025-08-04 11:46:01,288 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:46:01,292 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:46:01,321 - map_api - INFO - 找到 4 个目录
2025-08-04 11:46:01,331 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:46:01,342 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2524 字节
2025-08-04 11:46:01,406 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:46:01,424 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:46:01,426 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:46:01,437 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 11:46:01,446 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:46:01,454 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:46:01,455 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:46:01,455 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 11:46:01,464 - werkzeug - INFO - ************** - - [04/Aug/2025 11:46:01] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:46:02,406 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:46:02,407 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:46:02,422 - map_api - INFO - 找到 4 个目录
2025-08-04 11:46:02,425 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:46:02,441 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2524 字节
2025-08-04 11:46:02,446 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:46:02,459 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:46:02,465 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:46:02,475 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 11:46:02,480 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:46:02,491 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:46:02,493 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:46:02,498 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 11:46:02,502 - werkzeug - INFO - ************** - - [04/Aug/2025 11:46:02] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:46:12,907 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [04:47<00:00, 102.29s/it]
2025-08-04 11:46:12,908 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:47<00:00, 95.67s/it]
2025-08-04 11:46:21,397 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 11:46:21,402 - tif_api - INFO - 查询任务: add2250d-60e9-44b3-a6ac-ade953e72018
2025-08-04 11:46:21,408 - tif_api - INFO - 任务 add2250d-60e9-44b3-a6ac-ade953e72018 状态查询成功，当前状态: 正在运行
2025-08-04 11:46:21,411 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:46:21] "GET /api/tif/status?task_id=add2250d-60e9-44b3-a6ac-ade953e72018 HTTP/1.1" 200 -
2025-08-04 11:46:32,900 - batch_executor - INFO - 启动任务 f85f12b9-1570-4bd2-b02c-a3da53604883: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:46:32,902 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:46:32] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:46:32,926 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:46:32] "GET /api/batch/status?task_id=f85f12b9-1570-4bd2-b02c-a3da53604883 HTTP/1.1" 200 -
2025-08-04 11:46:52,886 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif影像保存完成，耗时: 293.51 秒 (11:46:13)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp (11:46:13)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tifTIF文件信息: 宽=17007, 高=20364, 波段数=3开始读取影像数据，大小约: 3963.44 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif提取TIF文件信息...TIF文件信息: 宽=17007, 高=20364, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 218929698 个无效像素检查波段 2...波段 2 检测到 218929698 个无效像素检查波段 3...波段 3 检测到 218929698 个无效像素掩码统计: 总像素数 346330548, 有效像素数 127400850 (36.79%), 无效像素数 218929698 (63.21%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 1 个初始轮廓轮廓面积统计: 最小=127380512.50, 最大=127380512.50, 平均=127380512.50, 总数=1将 1 个轮廓转换为地理坐标...
2025-08-04 11:46:52,909 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-08-04 11:46:53,425 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.95it/s]
2025-08-04 11:46:53,426 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.94it/s]
2025-08-04 11:46:53,433 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-08-04 11:47:05,835 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-08-04 11:47:05,836 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-08-04 11:47:07,032 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:01<00:17,  1.67it/s]
2025-08-04 11:47:07,167 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:11,  2.47it/s]
2025-08-04 11:47:07,211 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 22.59it/s]
2025-08-04 11:47:07,870 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 处理了 82263 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp写入shapefile特征...处理MultiPolygon，包含 19 个多边形
2025-08-04 11:47:07,873 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
写入多边形:   0%|                                                                               | 0/19 [00:00<?, ?it/s]
2025-08-04 11:47:07,978 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - ERROR - 
写入多边形: 100%|#####################################################################| 19/19 [00:00<00:00, 190.85it/s]
2025-08-04 11:47:08,238 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 处理完成，耗时: 357.04 秒 (5.95 分钟)
2025-08-04 11:47:08,239 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 处理结果: 成功
2025-08-04 11:47:08,244 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 大小: 740.84 MB
2025-08-04 11:47:08,247 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp, 大小: 22.81 KB
2025-08-04 11:47:08,272 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - TIF处理任务 add2250d-60e9-44b3-a6ac-ade953e72018 执行成功
2025-08-04 11:47:08,273 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 完成时间: 2025-08-04 11:47:08
2025-08-04 11:47:08,274 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - 状态: 运行成功
2025-08-04 11:47:08,278 - tif_task_add2250d-60e9-44b3-a6ac-ade953e72018 - INFO - ============ 任务执行结束 ============
2025-08-04 11:47:19,177 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:47:19,182 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:47:19,223 - map_api - INFO - 找到 4 个目录
2025-08-04 11:47:19,228 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:47:20,216 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2584 字节
2025-08-04 11:47:20,220 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:47:20,614 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:47:20,618 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:47:20,641 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 11:47:20,643 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:47:20,667 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:47:20,669 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:47:20,671 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 11:47:20,674 - werkzeug - INFO - ************** - - [04/Aug/2025 11:47:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:47:21,293 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 11:47:21,299 - tif_api - INFO - 查询任务: add2250d-60e9-44b3-a6ac-ade953e72018
2025-08-04 11:47:21,303 - tif_api - INFO - 任务 add2250d-60e9-44b3-a6ac-ade953e72018 状态查询成功，当前状态: 运行成功
2025-08-04 11:47:21,557 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:47:21] "GET /api/tif/status?task_id=add2250d-60e9-44b3-a6ac-ade953e72018 HTTP/1.1" 200 -
2025-08-04 11:47:23,710 - geo_publisher - INFO - 启动GeoTIFF发布任务 b665c195-123c-4a77-b4f4-ee8f3235341b: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-08-04 11:47:23,718 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:47:23] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-08-04 11:47:23,762 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:47:23] "GET /api/geo/status?task_id=b665c195-123c-4a77-b4f4-ee8f3235341b HTTP/1.1" 200 -
2025-08-04 11:47:23,835 - geo_task_b665c195-123c-4a77-b4f4-ee8f3235341b - INFO - GeoServer发布任务 b665c195-123c-4a77-b4f4-ee8f3235341b 开始执行
2025-08-04 11:47:23,838 - geo_task_b665c195-123c-4a77-b4f4-ee8f3235341b - INFO - 开始时间: 2025-08-04 11:47:23
2025-08-04 11:47:23,842 - geo_task_b665c195-123c-4a77-b4f4-ee8f3235341b - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-08-04 11:47:23,847 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-04 11:47:23,848 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-04 11:47:23,984 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-04 11:47:23,989 - root - INFO - 未提供存储名称，使用文件名: 20250705171601
2025-08-04 11:47:23,990 - root - INFO - 未提供图层名称，使用存储名称: 20250705171601
2025-08-04 11:47:24,036 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601.tif
2025-08-04 11:47:28,314 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 到 'testodm:20250705171601'
2025-08-04 11:47:28,475 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171601
2025-08-04 11:47:28,476 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 成功发布为图层 'testodm:20250705171601'
2025-08-04 11:47:28,483 - geo_task_b665c195-123c-4a77-b4f4-ee8f3235341b - INFO - GeoServer发布任务 b665c195-123c-4a77-b4f4-ee8f3235341b 执行成功
2025-08-04 11:47:28,484 - geo_task_b665c195-123c-4a77-b4f4-ee8f3235341b - INFO - 完成时间: 2025-08-04 11:47:28
2025-08-04 11:47:28,485 - geo_task_b665c195-123c-4a77-b4f4-ee8f3235341b - INFO - 状态: 发布成功
2025-08-04 11:47:33,254 - batch_executor - INFO - 启动任务 2581216f-4d7c-43fc-8409-fd0e734c0eb9: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:47:33,256 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:47:33] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:47:33,280 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:47:33] "GET /api/batch/status?task_id=2581216f-4d7c-43fc-8409-fd0e734c0eb9 HTTP/1.1" 200 -
2025-08-04 11:48:19,185 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:48:19,186 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:48:19,219 - map_api - INFO - 找到 4 个目录
2025-08-04 11:48:19,222 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:48:19,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2644 字节
2025-08-04 11:48:19,231 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:48:19,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:48:19,236 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:48:19,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2196 字节
2025-08-04 11:48:19,243 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:48:19,275 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:48:19,277 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:48:19,278 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 11:48:19,278 - werkzeug - INFO - ************** - - [04/Aug/2025 11:48:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:48:26,787 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:48:26] "GET /api/geo/status?task_id=b665c195-123c-4a77-b4f4-ee8f3235341b HTTP/1.1" 200 -
2025-08-04 11:48:37,361 - batch_executor - INFO - 启动任务 22983e9e-4ce9-4bd8-a056-fad517dbaaf5: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:48:37,362 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:48:37] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:48:37,387 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:48:37] "GET /api/batch/status?task_id=22983e9e-4ce9-4bd8-a056-fad517dbaaf5 HTTP/1.1" 200 -
2025-08-04 11:49:19,268 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:49:19,269 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:49:19,278 - map_api - INFO - 找到 4 个目录
2025-08-04 11:49:19,279 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:49:19,283 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2704 字节
2025-08-04 11:49:19,285 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:49:19,289 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:49:19,290 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:49:19,314 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:49:19,316 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:49:19,321 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:49:19,322 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:49:19,323 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:49:19,324 - werkzeug - INFO - ************** - - [04/Aug/2025 11:49:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:49:37,287 - batch_executor - INFO - 启动任务 d98cfa5b-0d5e-413b-8b54-4d72d76481a6: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:49:37,288 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:49:37] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:49:37,311 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:49:37] "GET /api/batch/status?task_id=d98cfa5b-0d5e-413b-8b54-4d72d76481a6 HTTP/1.1" 200 -
2025-08-04 11:50:19,206 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:50:19,207 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:50:19,216 - map_api - INFO - 找到 4 个目录
2025-08-04 11:50:19,217 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:50:19,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2764 字节
2025-08-04 11:50:19,237 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:50:19,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:50:19,253 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:50:19,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:50:19,257 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:50:19,284 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:50:19,286 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:50:19,286 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:50:19,288 - werkzeug - INFO - ************** - - [04/Aug/2025 11:50:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:50:40,935 - batch_executor - INFO - 启动任务 f9858f81-5b11-42e5-883e-88515ca8a7f6: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:50:40,937 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:50:40] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:50:40,960 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:50:40] "GET /api/batch/status?task_id=f9858f81-5b11-42e5-883e-88515ca8a7f6 HTTP/1.1" 200 -
2025-08-04 11:51:19,204 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:51:19,205 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:51:19,214 - map_api - INFO - 找到 4 个目录
2025-08-04 11:51:19,216 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:51:19,222 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2824 字节
2025-08-04 11:51:19,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:51:19,229 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:51:19,230 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:51:19,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:51:19,235 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:51:19,239 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:51:19,243 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:51:19,244 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:51:19,246 - werkzeug - INFO - ************** - - [04/Aug/2025 11:51:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:51:43,620 - batch_executor - INFO - 启动任务 217f76fb-7b38-42de-b8cd-ca5ecb34cdd8: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:51:43,725 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:51:43] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:51:43,799 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:51:43] "GET /api/batch/status?task_id=217f76fb-7b38-42de-b8cd-ca5ecb34cdd8 HTTP/1.1" 200 -
2025-08-04 11:52:19,203 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:52:19,205 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:52:19,214 - map_api - INFO - 找到 4 个目录
2025-08-04 11:52:19,215 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:52:19,220 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2884 字节
2025-08-04 11:52:19,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:52:19,231 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:52:19,232 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:52:19,238 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:52:19,241 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:52:19,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:52:19,248 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:52:19,249 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:52:19,250 - werkzeug - INFO - ************** - - [04/Aug/2025 11:52:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:52:46,463 - batch_executor - INFO - 启动任务 cfc2ab4a-8a53-4bd1-b378-3fdbbc0d6fe8: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:52:46,465 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:52:46] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:52:46,509 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:52:46] "GET /api/batch/status?task_id=cfc2ab4a-8a53-4bd1-b378-3fdbbc0d6fe8 HTTP/1.1" 200 -
2025-08-04 11:53:19,194 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:53:19,195 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:53:19,220 - map_api - INFO - 找到 4 个目录
2025-08-04 11:53:19,221 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:53:19,225 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 2944 字节
2025-08-04 11:53:19,225 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:53:19,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:53:19,248 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:53:19,251 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:53:19,252 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:53:19,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:53:19,256 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:53:19,257 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:53:19,258 - werkzeug - INFO - ************** - - [04/Aug/2025 11:53:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:53:49,361 - batch_executor - INFO - 启动任务 c5743f8d-9b74-469d-b865-f21011e8a03c: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:53:49,362 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:53:49] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:53:49,385 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:53:49] "GET /api/batch/status?task_id=c5743f8d-9b74-469d-b865-f21011e8a03c HTTP/1.1" 200 -
2025-08-04 11:54:19,196 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:54:19,197 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:54:19,207 - map_api - INFO - 找到 4 个目录
2025-08-04 11:54:19,208 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:54:19,213 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3004 字节
2025-08-04 11:54:19,214 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:54:19,224 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:54:19,225 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:54:19,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:54:19,248 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:54:19,263 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:54:19,264 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:54:19,264 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:54:19,265 - werkzeug - INFO - ************** - - [04/Aug/2025 11:54:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:54:52,126 - batch_executor - INFO - 启动任务 c85bf9c9-a151-4312-9367-0e2fe1400494: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
2025-08-04 11:54:52,127 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:54:52] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171599/project&fast-orthophoto HTTP/1.1" 200 -
2025-08-04 11:54:52,151 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 11:54:52] "GET /api/batch/status?task_id=c85bf9c9-a151-4312-9367-0e2fe1400494 HTTP/1.1" 200 -
2025-08-04 11:55:19,296 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:55:19,297 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:55:19,306 - map_api - INFO - 找到 4 个目录
2025-08-04 11:55:19,308 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:55:19,313 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 11:55:19,314 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:55:19,318 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:55:19,320 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:55:19,325 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:55:19,326 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:55:19,332 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:55:19,335 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:55:19,336 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:55:19,337 - werkzeug - INFO - ************** - - [04/Aug/2025 11:55:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:56:19,191 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:56:19,192 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:56:19,202 - map_api - INFO - 找到 4 个目录
2025-08-04 11:56:19,203 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:56:19,209 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 11:56:19,215 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:56:19,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:56:19,243 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:56:19,271 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:56:19,275 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:56:19,302 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:56:19,305 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:56:19,311 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:56:19,313 - werkzeug - INFO - ************** - - [04/Aug/2025 11:56:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:57:19,198 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:57:19,202 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:57:19,214 - map_api - INFO - 找到 4 个目录
2025-08-04 11:57:19,216 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:57:19,222 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 11:57:19,226 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:57:19,233 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:57:19,239 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:57:19,324 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:57:19,328 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:57:19,345 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:57:19,347 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:57:19,351 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:57:19,354 - werkzeug - INFO - ************** - - [04/Aug/2025 11:57:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:58:19,191 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:58:19,195 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:58:19,207 - map_api - INFO - 找到 4 个目录
2025-08-04 11:58:19,208 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:58:19,215 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 11:58:19,216 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:58:19,224 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:58:19,234 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:58:19,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:58:19,259 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:58:19,268 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:58:19,269 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:58:19,270 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:58:19,277 - werkzeug - INFO - ************** - - [04/Aug/2025 11:58:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 11:59:19,202 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 11:59:19,207 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 11:59:19,219 - map_api - INFO - 找到 4 个目录
2025-08-04 11:59:19,223 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 11:59:19,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 11:59:19,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 11:59:19,238 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 11:59:19,241 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 11:59:19,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 11:59:19,250 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 11:59:19,263 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 11:59:19,265 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 11:59:19,271 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 11:59:19,276 - werkzeug - INFO - ************** - - [04/Aug/2025 11:59:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:00:19,197 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:00:19,201 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:00:19,212 - map_api - INFO - 找到 4 个目录
2025-08-04 12:00:19,215 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:00:19,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:00:19,239 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:00:19,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:00:19,270 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:00:19,298 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:00:19,301 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:00:19,329 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:00:19,333 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:00:19,336 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:00:19,339 - werkzeug - INFO - ************** - - [04/Aug/2025 12:00:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:01:19,212 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:01:19,216 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:01:19,240 - map_api - INFO - 找到 4 个目录
2025-08-04 12:01:19,243 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:01:19,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:01:19,251 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:01:19,263 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:01:19,265 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:01:19,270 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:01:19,277 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:01:19,283 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:01:19,288 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:01:19,294 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:01:19,296 - werkzeug - INFO - ************** - - [04/Aug/2025 12:01:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:02:19,191 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:02:19,195 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:02:19,204 - map_api - INFO - 找到 4 个目录
2025-08-04 12:02:19,206 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:02:19,212 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:02:19,219 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:02:19,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:02:19,251 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:02:19,275 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:02:19,279 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:02:19,304 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:02:19,307 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:02:19,310 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:02:19,312 - werkzeug - INFO - ************** - - [04/Aug/2025 12:02:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:03:19,244 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:03:19,249 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:03:19,261 - map_api - INFO - 找到 4 个目录
2025-08-04 12:03:19,262 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:03:19,268 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:03:19,272 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:03:19,284 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:03:19,287 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:03:19,295 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:03:19,297 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:03:19,309 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:03:19,310 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:03:19,315 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:03:19,319 - werkzeug - INFO - ************** - - [04/Aug/2025 12:03:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:04:19,197 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:04:19,202 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:04:19,211 - map_api - INFO - 找到 4 个目录
2025-08-04 12:04:19,213 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:04:19,220 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:04:19,222 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:04:19,232 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:04:19,234 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:04:19,239 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:04:19,241 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:04:19,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:04:19,256 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:04:19,257 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:04:19,259 - werkzeug - INFO - ************** - - [04/Aug/2025 12:04:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:05:19,199 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:05:19,202 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:05:19,211 - map_api - INFO - 找到 4 个目录
2025-08-04 12:05:19,213 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:05:19,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:05:19,240 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:05:19,269 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:05:19,273 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:05:19,281 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:05:19,283 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:05:19,300 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:05:19,301 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:05:19,303 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:05:19,305 - werkzeug - INFO - ************** - - [04/Aug/2025 12:05:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:06:19,199 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:06:19,202 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:06:19,233 - map_api - INFO - 找到 4 个目录
2025-08-04 12:06:19,237 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:06:19,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:06:19,265 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:06:19,292 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:06:19,296 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:06:19,304 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:06:19,305 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:06:19,338 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:06:19,342 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:06:19,345 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:06:19,347 - werkzeug - INFO - ************** - - [04/Aug/2025 12:06:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:07:19,197 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:07:19,201 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:07:19,211 - map_api - INFO - 找到 4 个目录
2025-08-04 12:07:19,213 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:07:19,220 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:07:19,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:07:19,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:07:19,253 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:07:19,282 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:07:19,285 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:07:19,312 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:07:19,315 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:07:19,319 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:07:19,322 - werkzeug - INFO - ************** - - [04/Aug/2025 12:07:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:08:19,200 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:08:19,204 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:08:19,211 - map_api - INFO - 找到 4 个目录
2025-08-04 12:08:19,212 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:08:19,221 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:08:19,222 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:08:19,229 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:08:19,234 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:08:19,263 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:08:19,266 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:08:19,294 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:08:19,298 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:08:19,302 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:08:19,305 - werkzeug - INFO - ************** - - [04/Aug/2025 12:08:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:09:19,198 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:09:19,201 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:09:19,213 - map_api - INFO - 找到 4 个目录
2025-08-04 12:09:19,217 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:09:19,226 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:09:19,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:09:19,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:09:19,242 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:09:19,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:09:19,256 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:09:19,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:09:19,264 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:09:19,266 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:09:19,268 - werkzeug - INFO - ************** - - [04/Aug/2025 12:09:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:10:24,856 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:10:24,860 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:10:24,874 - map_api - INFO - 找到 4 个目录
2025-08-04 12:10:24,875 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:10:24,899 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:10:24,913 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:10:24,921 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:10:24,922 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:10:24,928 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:10:24,930 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:10:24,943 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:10:24,944 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:10:24,946 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:10:24,949 - werkzeug - INFO - ************** - - [04/Aug/2025 12:10:24] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:11:19,199 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:11:19,202 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:11:19,212 - map_api - INFO - 找到 4 个目录
2025-08-04 12:11:19,214 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:11:19,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:11:19,239 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:11:19,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:11:19,271 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:11:19,298 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:11:19,302 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:11:19,329 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:11:19,332 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:11:19,336 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:11:19,338 - werkzeug - INFO - ************** - - [04/Aug/2025 12:11:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:12:19,183 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:12:19,189 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:12:19,198 - map_api - INFO - 找到 4 个目录
2025-08-04 12:12:19,200 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:12:19,205 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:12:19,206 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:12:19,239 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:12:19,243 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:12:19,270 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:12:19,276 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:12:19,301 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:12:19,304 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:12:19,308 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:12:19,310 - werkzeug - INFO - ************** - - [04/Aug/2025 12:12:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:13:19,192 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:13:19,195 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:13:19,205 - map_api - INFO - 找到 4 个目录
2025-08-04 12:13:19,207 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:13:19,233 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:13:19,236 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:13:19,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:13:19,268 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:13:19,275 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:13:19,276 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:13:19,295 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:13:19,299 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:13:19,303 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:13:19,305 - werkzeug - INFO - ************** - - [04/Aug/2025 12:13:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:14:19,198 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:14:19,205 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:14:19,219 - map_api - INFO - 找到 4 个目录
2025-08-04 12:14:19,222 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:14:19,228 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:14:19,231 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:14:19,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:14:19,245 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:14:19,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:14:19,262 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:14:19,272 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:14:19,279 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:14:19,283 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:14:19,286 - werkzeug - INFO - ************** - - [04/Aug/2025 12:14:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:15:19,215 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:15:19,220 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:15:19,232 - map_api - INFO - 找到 4 个目录
2025-08-04 12:15:19,233 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:15:19,244 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:15:19,246 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:15:19,251 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:15:19,254 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:15:19,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:15:19,269 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:15:19,276 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:15:19,277 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:15:19,278 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:15:19,287 - werkzeug - INFO - ************** - - [04/Aug/2025 12:15:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:16:19,190 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:16:19,194 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:16:19,203 - map_api - INFO - 找到 4 个目录
2025-08-04 12:16:19,205 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:16:19,232 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:16:19,238 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:16:19,248 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:16:19,251 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:16:19,278 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:16:19,281 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:16:19,309 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:16:19,313 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:16:19,316 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:16:19,318 - werkzeug - INFO - ************** - - [04/Aug/2025 12:16:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:17:19,207 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:17:19,212 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:17:19,226 - map_api - INFO - 找到 4 个目录
2025-08-04 12:17:19,228 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:17:19,239 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:17:19,241 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:17:19,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:17:19,250 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:17:19,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:17:19,258 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:17:19,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:17:19,268 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:17:19,273 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:17:19,278 - werkzeug - INFO - ************** - - [04/Aug/2025 12:17:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:18:19,194 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:18:19,197 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:18:19,208 - map_api - INFO - 找到 4 个目录
2025-08-04 12:18:19,209 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:18:19,215 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:18:19,220 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:18:19,244 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:18:19,248 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:18:19,277 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:18:19,281 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:18:19,307 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:18:19,313 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:18:19,318 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:18:19,321 - werkzeug - INFO - ************** - - [04/Aug/2025 12:18:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:19:19,194 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:19:19,198 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:19:19,221 - map_api - INFO - 找到 4 个目录
2025-08-04 12:19:19,222 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:19:19,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:19:19,254 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:19:19,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:19:19,262 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:19:19,280 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:19:19,283 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:19:19,290 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:19:19,292 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:19:19,294 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:19:19,304 - werkzeug - INFO - ************** - - [04/Aug/2025 12:19:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:20:19,190 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:20:19,195 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:20:19,204 - map_api - INFO - 找到 4 个目录
2025-08-04 12:20:19,206 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:20:19,210 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:20:19,212 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:20:19,218 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:20:19,222 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:20:19,232 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:20:19,235 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:20:19,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:20:19,268 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:20:19,270 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:20:19,273 - werkzeug - INFO - ************** - - [04/Aug/2025 12:20:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:21:19,189 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:21:19,192 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:21:19,201 - map_api - INFO - 找到 4 个目录
2025-08-04 12:21:19,202 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:21:19,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:21:19,239 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:21:19,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:21:19,270 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:21:19,297 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:21:19,300 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:21:19,328 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:21:19,331 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:21:19,335 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:21:19,339 - werkzeug - INFO - ************** - - [04/Aug/2025 12:21:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:22:19,192 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:22:19,195 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:22:19,218 - map_api - INFO - 找到 4 个目录
2025-08-04 12:22:19,220 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:22:19,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:22:19,253 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:22:19,258 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:22:19,260 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:22:19,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:22:19,269 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:22:19,293 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:22:19,297 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:22:19,301 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:22:19,304 - werkzeug - INFO - ************** - - [04/Aug/2025 12:22:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:23:19,193 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:23:19,197 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:23:19,222 - map_api - INFO - 找到 4 个目录
2025-08-04 12:23:19,223 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:23:19,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:23:19,253 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:23:19,260 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:23:19,261 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:23:19,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:23:19,270 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:23:19,297 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:23:19,300 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:23:19,303 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:23:19,305 - werkzeug - INFO - ************** - - [04/Aug/2025 12:23:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:24:19,187 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:24:19,190 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:24:19,210 - map_api - INFO - 找到 4 个目录
2025-08-04 12:24:19,213 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:24:19,222 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:24:19,229 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:24:19,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:24:19,258 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:24:19,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:24:19,267 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:24:19,274 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:24:19,277 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:24:19,282 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:24:19,284 - werkzeug - INFO - ************** - - [04/Aug/2025 12:24:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:25:25,581 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:25:25,592 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:25:25,603 - map_api - INFO - 找到 4 个目录
2025-08-04 12:25:25,605 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:25:25,611 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:25:25,614 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:25:25,624 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:25:25,629 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:25:25,637 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:25:25,639 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:25:25,653 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:25:25,655 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:25:25,664 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:25:25,667 - werkzeug - INFO - ************** - - [04/Aug/2025 12:25:25] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:26:19,195 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:26:19,199 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:26:19,214 - map_api - INFO - 找到 4 个目录
2025-08-04 12:26:19,220 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:26:19,229 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:26:19,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:26:19,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:26:19,244 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:26:19,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:26:19,263 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:26:19,292 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:26:19,294 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:26:19,298 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:26:19,305 - werkzeug - INFO - ************** - - [04/Aug/2025 12:26:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:27:19,183 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:27:19,186 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:27:19,218 - map_api - INFO - 找到 4 个目录
2025-08-04 12:27:19,223 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:27:19,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:27:19,250 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:27:19,279 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:27:19,282 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:27:19,311 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:27:19,315 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:27:19,341 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:27:19,349 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:27:19,350 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:27:19,352 - werkzeug - INFO - ************** - - [04/Aug/2025 12:27:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:28:19,192 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:28:19,196 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:28:19,207 - map_api - INFO - 找到 4 个目录
2025-08-04 12:28:19,209 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:28:19,215 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:28:19,222 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:28:19,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:28:19,249 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:28:19,258 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:28:19,260 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:28:19,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:28:19,267 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:28:19,274 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:28:19,276 - werkzeug - INFO - ************** - - [04/Aug/2025 12:28:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:29:19,190 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:29:19,193 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:29:19,229 - map_api - INFO - 找到 4 个目录
2025-08-04 12:29:19,233 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:29:19,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:29:19,248 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:29:19,273 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:29:19,276 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:29:19,283 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:29:19,284 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:29:19,305 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:29:19,308 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:29:19,311 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:29:19,316 - werkzeug - INFO - ************** - - [04/Aug/2025 12:29:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:30:19,190 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:30:19,194 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:30:19,223 - map_api - INFO - 找到 4 个目录
2025-08-04 12:30:19,227 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:30:19,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:30:19,239 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:30:19,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:30:19,247 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:30:19,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:30:19,257 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:30:19,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:30:19,269 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:30:19,275 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:30:19,277 - werkzeug - INFO - ************** - - [04/Aug/2025 12:30:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:31:19,205 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:31:19,208 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:31:19,222 - map_api - INFO - 找到 4 个目录
2025-08-04 12:31:19,223 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:31:19,233 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:31:19,236 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:31:19,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:31:19,252 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:31:19,278 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:31:19,282 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:31:19,290 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:31:19,291 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:31:19,296 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:31:19,301 - werkzeug - INFO - ************** - - [04/Aug/2025 12:31:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:32:19,209 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:32:19,211 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:32:19,224 - map_api - INFO - 找到 4 个目录
2025-08-04 12:32:19,226 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:32:19,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:32:19,252 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:32:19,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:32:19,262 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:32:19,274 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:32:19,276 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:32:19,283 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:32:19,284 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:32:19,288 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:32:19,291 - werkzeug - INFO - ************** - - [04/Aug/2025 12:32:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:33:19,194 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:33:19,199 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:33:19,206 - map_api - INFO - 找到 4 个目录
2025-08-04 12:33:19,208 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:33:19,213 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:33:19,214 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:33:19,222 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:33:19,228 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:33:19,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:33:19,236 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:33:19,263 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:33:19,269 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:33:19,273 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:33:19,276 - werkzeug - INFO - ************** - - [04/Aug/2025 12:33:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:34:19,198 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:34:19,202 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:34:19,212 - map_api - INFO - 找到 4 个目录
2025-08-04 12:34:19,212 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:34:19,219 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:34:19,221 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:34:19,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:34:19,234 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:34:19,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:34:19,244 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:34:19,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:34:19,256 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:34:19,257 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:34:19,261 - werkzeug - INFO - ************** - - [04/Aug/2025 12:34:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:35:19,196 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:35:19,200 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:35:19,209 - map_api - INFO - 找到 4 个目录
2025-08-04 12:35:19,211 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:35:19,221 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:35:19,222 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:35:19,227 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:35:19,235 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:35:19,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:35:19,251 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:35:19,257 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:35:19,263 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:35:19,269 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:35:19,271 - werkzeug - INFO - ************** - - [04/Aug/2025 12:35:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:36:19,197 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:36:19,200 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:36:19,212 - map_api - INFO - 找到 4 个目录
2025-08-04 12:36:19,213 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:36:19,222 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:36:19,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:36:19,232 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:36:19,235 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:36:19,245 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:36:19,250 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:36:19,276 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:36:19,279 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:36:19,282 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:36:19,284 - werkzeug - INFO - ************** - - [04/Aug/2025 12:36:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:37:19,198 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:37:19,203 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:37:19,215 - map_api - INFO - 找到 4 个目录
2025-08-04 12:37:19,218 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:37:19,226 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:37:19,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:37:19,244 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:37:19,247 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:37:19,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:37:19,259 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:37:19,269 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:37:19,276 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:37:19,281 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:37:19,283 - werkzeug - INFO - ************** - - [04/Aug/2025 12:37:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:38:19,230 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:38:19,235 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:38:19,251 - map_api - INFO - 找到 4 个目录
2025-08-04 12:38:19,253 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:38:19,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:38:19,266 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:38:19,276 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:38:19,282 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:38:19,293 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:38:19,298 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:38:19,308 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:38:19,313 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:38:19,317 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:38:19,320 - werkzeug - INFO - ************** - - [04/Aug/2025 12:38:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:39:19,195 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:39:19,199 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:39:19,219 - map_api - INFO - 找到 4 个目录
2025-08-04 12:39:19,221 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:39:19,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:39:19,236 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:39:19,268 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:39:19,273 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:39:19,280 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:39:19,282 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:39:19,292 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:39:19,293 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:39:19,295 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:39:19,297 - werkzeug - INFO - ************** - - [04/Aug/2025 12:39:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:40:25,289 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:40:25,303 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:40:25,327 - map_api - INFO - 找到 4 个目录
2025-08-04 12:40:25,329 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:40:25,359 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:40:25,376 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:40:25,383 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:40:25,386 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:40:25,398 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:40:25,400 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:40:25,408 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:40:25,413 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:40:25,413 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:40:25,416 - werkzeug - INFO - ************** - - [04/Aug/2025 12:40:25] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:41:19,209 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:41:19,214 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:41:19,225 - map_api - INFO - 找到 4 个目录
2025-08-04 12:41:19,227 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:41:19,233 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:41:19,234 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:41:19,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:41:19,252 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:41:19,257 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:41:19,259 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:41:19,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:41:19,272 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:41:19,276 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:41:19,278 - werkzeug - INFO - ************** - - [04/Aug/2025 12:41:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:42:19,203 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:42:19,206 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:42:19,218 - map_api - INFO - 找到 4 个目录
2025-08-04 12:42:19,223 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:42:19,233 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:42:19,234 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:42:19,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:42:19,248 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:42:19,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:42:19,255 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:42:19,263 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:42:19,265 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:42:19,271 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:42:19,277 - werkzeug - INFO - ************** - - [04/Aug/2025 12:42:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:43:19,210 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:43:19,214 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:43:19,226 - map_api - INFO - 找到 4 个目录
2025-08-04 12:43:19,228 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:43:19,235 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:43:19,241 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:43:19,251 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:43:19,258 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:43:19,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:43:19,266 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:43:19,280 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:43:19,281 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:43:19,290 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:43:19,292 - werkzeug - INFO - ************** - - [04/Aug/2025 12:43:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:44:19,318 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:44:19,326 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:44:19,338 - map_api - INFO - 找到 4 个目录
2025-08-04 12:44:19,340 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:44:19,349 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:44:19,356 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:44:19,363 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:44:19,365 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:44:19,373 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:44:19,381 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:44:19,391 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:44:19,395 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:44:19,400 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:44:19,403 - werkzeug - INFO - ************** - - [04/Aug/2025 12:44:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:45:19,194 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:45:19,198 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:45:19,212 - map_api - INFO - 找到 4 个目录
2025-08-04 12:45:19,213 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:45:19,228 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:45:19,234 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:45:19,244 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:45:19,251 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:45:19,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:45:19,261 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:45:19,273 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:45:19,276 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:45:19,284 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:45:19,286 - werkzeug - INFO - ************** - - [04/Aug/2025 12:45:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:46:19,207 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:46:19,210 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:46:19,223 - map_api - INFO - 找到 4 个目录
2025-08-04 12:46:19,227 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:46:19,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:46:19,238 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:46:19,251 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:46:19,253 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:46:19,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:46:19,266 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:46:19,273 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:46:19,276 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:46:19,281 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:46:19,284 - werkzeug - INFO - ************** - - [04/Aug/2025 12:46:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:47:19,208 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:47:19,212 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:47:19,224 - map_api - INFO - 找到 4 个目录
2025-08-04 12:47:19,225 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:47:19,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:47:19,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:47:19,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:47:19,247 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:47:19,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:47:19,268 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:47:19,275 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:47:19,276 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:47:19,279 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:47:19,281 - werkzeug - INFO - ************** - - [04/Aug/2025 12:47:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:48:19,193 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:48:19,196 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:48:19,210 - map_api - INFO - 找到 4 个目录
2025-08-04 12:48:19,212 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:48:19,221 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:48:19,222 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:48:19,228 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:48:19,236 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:48:19,257 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:48:19,261 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:48:19,268 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:48:19,269 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:48:19,275 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:48:19,279 - werkzeug - INFO - ************** - - [04/Aug/2025 12:48:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:49:19,210 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:49:19,213 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:49:19,223 - map_api - INFO - 找到 4 个目录
2025-08-04 12:49:19,225 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:49:19,233 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:49:19,234 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:49:19,239 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:49:19,241 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:49:19,252 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:49:19,253 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:49:19,258 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:49:19,261 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:49:19,265 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:49:19,267 - werkzeug - INFO - ************** - - [04/Aug/2025 12:49:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:50:19,197 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:50:19,201 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:50:19,214 - map_api - INFO - 找到 4 个目录
2025-08-04 12:50:19,218 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:50:19,225 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:50:19,226 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:50:19,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:50:19,262 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:50:19,270 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:50:19,271 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:50:19,277 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:50:19,279 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:50:19,283 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:50:19,286 - werkzeug - INFO - ************** - - [04/Aug/2025 12:50:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:51:19,185 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:51:19,188 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:51:19,198 - map_api - INFO - 找到 4 个目录
2025-08-04 12:51:19,200 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:51:19,223 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:51:19,228 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:51:19,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:51:19,243 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:51:19,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:51:19,252 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:51:19,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:51:19,260 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:51:19,264 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:51:19,266 - werkzeug - INFO - ************** - - [04/Aug/2025 12:51:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:52:19,208 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:52:19,213 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:52:19,225 - map_api - INFO - 找到 4 个目录
2025-08-04 12:52:19,227 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:52:19,251 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:52:19,255 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:52:19,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:52:19,263 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:52:19,269 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:52:19,271 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:52:19,279 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:52:19,281 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:52:19,285 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:52:19,291 - werkzeug - INFO - ************** - - [04/Aug/2025 12:52:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:53:19,200 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:53:19,204 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:53:19,213 - map_api - INFO - 找到 4 个目录
2025-08-04 12:53:19,214 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:53:19,220 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:53:19,223 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:53:19,231 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:53:19,237 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:53:19,245 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:53:19,249 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:53:19,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:53:19,260 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:53:19,262 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:53:19,264 - werkzeug - INFO - ************** - - [04/Aug/2025 12:53:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:54:19,225 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:54:19,229 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:54:19,243 - map_api - INFO - 找到 4 个目录
2025-08-04 12:54:19,245 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:54:19,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:54:19,260 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:54:19,271 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:54:19,276 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:54:19,286 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:54:19,291 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:54:19,300 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:54:19,305 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:54:19,309 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:54:19,312 - werkzeug - INFO - ************** - - [04/Aug/2025 12:54:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:55:21,364 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:55:21,370 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:55:21,381 - map_api - INFO - 找到 4 个目录
2025-08-04 12:55:21,383 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:55:21,390 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:55:21,393 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:55:21,403 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:55:21,407 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:55:21,419 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:55:21,422 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:55:21,431 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:55:21,437 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:55:21,440 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:55:21,443 - werkzeug - INFO - ************** - - [04/Aug/2025 12:55:21] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:56:19,204 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:56:19,208 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:56:19,219 - map_api - INFO - 找到 4 个目录
2025-08-04 12:56:19,221 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:56:19,226 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:56:19,228 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:56:19,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:56:19,237 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:56:19,248 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:56:19,252 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:56:19,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:56:19,261 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:56:19,266 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:56:19,272 - werkzeug - INFO - ************** - - [04/Aug/2025 12:56:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:57:19,204 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:57:19,208 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:57:19,219 - map_api - INFO - 找到 4 个目录
2025-08-04 12:57:19,220 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:57:19,244 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:57:19,249 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:57:19,258 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:57:19,265 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:57:19,272 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:57:19,274 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:57:19,284 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:57:19,285 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:57:19,286 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:57:19,289 - werkzeug - INFO - ************** - - [04/Aug/2025 12:57:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:58:19,195 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:58:19,198 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:58:19,207 - map_api - INFO - 找到 4 个目录
2025-08-04 12:58:19,209 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:58:19,231 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:58:19,235 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:58:19,244 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:58:19,246 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:58:19,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:58:19,256 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:58:19,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:58:19,267 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:58:19,271 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:58:19,273 - werkzeug - INFO - ************** - - [04/Aug/2025 12:58:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 12:59:19,230 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 12:59:19,236 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 12:59:19,251 - map_api - INFO - 找到 4 个目录
2025-08-04 12:59:19,253 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 12:59:19,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 12:59:19,266 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 12:59:19,275 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 12:59:19,277 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 12:59:19,289 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 12:59:19,294 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 12:59:19,304 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 12:59:19,306 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 12:59:19,308 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 12:59:19,311 - werkzeug - INFO - ************** - - [04/Aug/2025 12:59:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:00:19,191 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:00:19,195 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:00:19,209 - map_api - INFO - 找到 4 个目录
2025-08-04 13:00:19,212 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:00:19,219 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:00:19,221 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:00:19,233 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:00:19,234 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:00:19,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:00:19,247 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:00:19,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:00:19,259 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:00:19,263 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:00:19,266 - werkzeug - INFO - ************** - - [04/Aug/2025 13:00:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:01:19,197 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:01:19,201 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:01:19,212 - map_api - INFO - 找到 4 个目录
2025-08-04 13:01:19,214 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:01:19,220 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:01:19,221 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:01:19,228 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:01:19,235 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:01:19,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:01:19,246 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:01:19,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:01:19,259 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:01:19,262 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:01:19,265 - werkzeug - INFO - ************** - - [04/Aug/2025 13:01:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:02:19,214 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:02:19,218 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:02:19,231 - map_api - INFO - 找到 4 个目录
2025-08-04 13:02:19,233 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:02:19,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:02:19,245 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:02:19,276 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:02:19,279 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:02:19,285 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:02:19,287 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:02:19,294 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:02:19,297 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:02:19,301 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:02:19,302 - werkzeug - INFO - ************** - - [04/Aug/2025 13:02:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:03:19,188 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:03:19,192 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:03:19,202 - map_api - INFO - 找到 4 个目录
2025-08-04 13:03:19,203 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:03:19,224 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:03:19,228 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:03:19,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:03:19,260 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:03:19,286 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:03:19,290 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:03:19,298 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:03:19,299 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:03:19,300 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:03:19,311 - werkzeug - INFO - ************** - - [04/Aug/2025 13:03:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:04:19,203 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:04:19,207 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:04:19,216 - map_api - INFO - 找到 4 个目录
2025-08-04 13:04:19,217 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:04:19,222 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:04:19,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:04:19,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:04:19,238 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:04:19,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:04:19,251 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:04:19,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:04:19,256 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:04:19,263 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:04:19,265 - werkzeug - INFO - ************** - - [04/Aug/2025 13:04:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:05:19,249 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:05:19,253 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:05:19,266 - map_api - INFO - 找到 4 个目录
2025-08-04 13:05:19,268 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:05:19,300 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:05:19,304 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:05:19,311 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:05:19,313 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:05:19,331 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:05:19,332 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:05:19,339 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:05:19,340 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:05:19,343 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:05:19,345 - werkzeug - INFO - ************** - - [04/Aug/2025 13:05:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:06:19,192 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:06:19,196 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:06:19,206 - map_api - INFO - 找到 4 个目录
2025-08-04 13:06:19,208 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:06:19,215 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:06:19,221 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:06:19,226 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:06:19,231 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:06:19,238 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:06:19,240 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:06:19,273 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:06:19,275 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:06:19,278 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:06:19,280 - werkzeug - INFO - ************** - - [04/Aug/2025 13:06:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:07:19,187 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:07:19,190 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:07:19,202 - map_api - INFO - 找到 4 个目录
2025-08-04 13:07:19,204 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:07:19,211 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:07:19,215 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:07:19,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:07:19,245 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:07:19,272 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:07:19,275 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:07:19,283 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:07:19,284 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:07:19,286 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:07:19,298 - werkzeug - INFO - ************** - - [04/Aug/2025 13:07:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:08:19,192 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:08:19,196 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:08:19,208 - map_api - INFO - 找到 4 个目录
2025-08-04 13:08:19,210 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:08:19,218 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:08:19,220 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:08:19,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:08:19,258 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:08:19,285 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:08:19,288 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:08:19,294 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:08:19,295 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:08:19,297 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:08:19,298 - werkzeug - INFO - ************** - - [04/Aug/2025 13:08:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:09:19,205 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:09:19,209 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:09:19,221 - map_api - INFO - 找到 4 个目录
2025-08-04 13:09:19,224 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:09:19,231 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:09:19,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:09:19,238 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:09:19,242 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:09:19,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:09:19,256 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:09:19,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:09:19,265 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:09:19,272 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:09:19,277 - werkzeug - INFO - ************** - - [04/Aug/2025 13:09:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:10:21,427 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:10:21,445 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:10:21,462 - map_api - INFO - 找到 4 个目录
2025-08-04 13:10:21,464 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:10:21,472 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:10:21,474 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:10:21,485 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:10:21,487 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:10:21,494 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:10:21,495 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:10:21,505 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:10:21,510 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:10:21,513 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:10:21,518 - werkzeug - INFO - ************** - - [04/Aug/2025 13:10:21] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:11:19,181 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:11:19,185 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:11:19,197 - map_api - INFO - 找到 4 个目录
2025-08-04 13:11:19,199 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:11:19,222 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:11:19,227 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:11:19,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:11:19,235 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:11:19,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:11:19,248 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:11:19,254 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:11:19,258 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:11:19,261 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:11:19,264 - werkzeug - INFO - ************** - - [04/Aug/2025 13:11:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:12:19,217 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:12:19,221 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:12:19,232 - map_api - INFO - 找到 4 个目录
2025-08-04 13:12:19,233 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:12:19,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:12:19,253 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:12:19,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:12:19,263 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:12:19,272 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:12:19,274 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:12:19,281 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:12:19,284 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:12:19,289 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:12:19,291 - werkzeug - INFO - ************** - - [04/Aug/2025 13:12:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:13:19,207 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:13:19,212 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:13:19,231 - map_api - INFO - 找到 4 个目录
2025-08-04 13:13:19,233 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:13:19,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:13:19,248 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:13:19,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:13:19,260 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:13:19,270 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:13:19,273 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:13:19,282 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:13:19,287 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:13:19,291 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:13:19,294 - werkzeug - INFO - ************** - - [04/Aug/2025 13:13:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:14:19,194 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:14:19,197 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:14:19,208 - map_api - INFO - 找到 4 个目录
2025-08-04 13:14:19,210 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:14:19,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:14:19,238 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:14:19,245 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:14:19,247 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:14:19,258 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:14:19,260 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:14:19,268 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:14:19,272 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:14:19,276 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:14:19,282 - werkzeug - INFO - ************** - - [04/Aug/2025 13:14:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:15:19,239 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:15:19,244 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:15:19,256 - map_api - INFO - 找到 4 个目录
2025-08-04 13:15:19,257 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:15:19,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:15:19,269 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:15:19,278 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:15:19,282 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:15:19,291 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:15:19,301 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:15:19,314 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:15:19,316 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:15:19,317 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:15:19,319 - werkzeug - INFO - ************** - - [04/Aug/2025 13:15:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 13:16:19,210 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 13:16:19,213 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 13:16:19,225 - map_api - INFO - 找到 4 个目录
2025-08-04 13:16:19,226 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 13:16:19,232 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 13:16:19,238 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 13:16:19,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 13:16:19,248 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 13:16:19,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 13:16:19,258 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 13:16:19,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 13:16:19,267 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 13:16:19,272 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 13:16:19,273 - werkzeug - INFO - ************** - - [04/Aug/2025 13:16:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:54:19,171 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:54:19,173 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:54:19,184 - map_api - INFO - 找到 4 个目录
2025-08-04 14:54:19,186 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:54:19,213 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:54:19,217 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:54:19,226 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:54:19,234 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:54:19,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 14:54:19,248 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:54:19,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:54:19,258 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:54:19,259 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 14:54:19,269 - werkzeug - INFO - ************** - - [04/Aug/2025 14:54:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:54:19,273 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:54:19,280 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:54:19,293 - map_api - INFO - 找到 4 个目录
2025-08-04 14:54:19,295 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:54:19,322 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:54:19,326 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:54:19,334 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:54:19,336 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:54:19,346 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 14:54:19,348 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:54:19,354 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:54:19,357 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:54:19,361 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 14:54:19,364 - werkzeug - INFO - ************** - - [04/Aug/2025 14:54:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:54:21,423 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:54:21,427 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:54:21,437 - map_api - INFO - 找到 4 个目录
2025-08-04 14:54:21,439 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:54:21,459 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:54:21,463 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:54:21,489 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:54:21,493 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:54:21,500 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 14:54:21,501 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:54:21,508 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:54:21,510 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:54:21,514 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 14:54:21,516 - werkzeug - INFO - ************** - - [04/Aug/2025 14:54:21] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:55:22,196 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:55:22,201 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:55:22,214 - map_api - INFO - 找到 4 个目录
2025-08-04 14:55:22,215 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:55:22,223 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:55:22,228 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:55:22,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:55:22,241 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:55:22,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 14:55:22,251 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:55:22,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:55:22,261 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:55:22,263 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 14:55:22,266 - werkzeug - INFO - ************** - - [04/Aug/2025 14:55:22] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:56:22,167 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:56:22,170 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:56:22,180 - map_api - INFO - 找到 4 个目录
2025-08-04 14:56:22,183 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:56:22,192 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:56:22,194 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:56:22,206 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:56:22,208 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:56:22,215 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 14:56:22,216 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:56:22,224 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:56:22,232 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:56:22,237 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 14:56:22,239 - werkzeug - INFO - ************** - - [04/Aug/2025 14:56:22] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:57:22,162 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:57:22,166 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:57:22,177 - map_api - INFO - 找到 4 个目录
2025-08-04 14:57:22,179 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:57:22,186 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:57:22,187 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:57:22,194 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:57:22,195 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:57:22,223 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 14:57:22,227 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:57:22,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:57:22,235 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:57:22,236 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 14:57:22,242 - werkzeug - INFO - ************** - - [04/Aug/2025 14:57:22] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:13,932 - map_api - INFO - 请求重命名任务信息文件: 20250705171601
2025-08-04 14:58:13,936 - map_api - INFO - 开始重命名任务信息文件: 20250705171601
2025-08-04 14:58:13,966 - map_api - INFO - 成功读取ODM配置: {'PATHS': {'airflow_data_path': '/opt/airflow/data', 'window_data_path': 'D:/Drone_Project/nginxData'}, 'API': {'geoserver_host': 'host.docker.internal', 'geoserver_hd_port': '5083'}, 'GEOSERVER': {'odm_workspace': 'testodm'}, 'TASK': {'id': 'NONE', 'time': 'NONE'}}
2025-08-04 14:58:13,972 - map_api - INFO - 任务信息文件路径: D:/Drone_Project/nginxData\ODM\Input\20250705171601\TaskInfo.json
2025-08-04 14:58:13,974 - map_api - INFO - 成功将任务信息文件重命名为: D:/Drone_Project/nginxData\ODM\Input\20250705171601\RemoveTask20250804145813.json
2025-08-04 14:58:13,976 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:13] "GET /api/map/odm/task/rename-info?task_id=20250705171601 HTTP/1.1" 200 -
2025-08-04 14:58:13,982 - map_api - INFO - 请求重置ODM任务配置
2025-08-04 14:58:13,983 - map_api - INFO - 开始重置Task.cfg中的任务信息
2025-08-04 14:58:14,008 - map_api - INFO - 成功读取ODM配置: {'PATHS': {'airflow_data_path': '/opt/airflow/data', 'window_data_path': 'D:/Drone_Project/nginxData'}, 'API': {'geoserver_host': 'host.docker.internal', 'geoserver_hd_port': '5083'}, 'GEOSERVER': {'odm_workspace': 'testodm'}, 'TASK': {'id': 'NONE', 'time': 'NONE'}}
2025-08-04 14:58:14,014 - map_api - INFO - Task.cfg文件路径: D:/Drone_Project/nginxData\ODM\Task.cfg
2025-08-04 14:58:14,015 - map_api - INFO - 成功重置Task.cfg中的任务信息
2025-08-04 14:58:14,018 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:14] "GET /api/map/odm/reset-task-config HTTP/1.1" 200 -
2025-08-04 14:58:14,030 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:14,031 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:14,057 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:14,062 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:14,068 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:14,070 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:14,077 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:14,079 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:14,168 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:14,172 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:14,176 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:14,177 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:14,179 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:14,186 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:14] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:18,327 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:18,330 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:18,342 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:18,345 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:18,355 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:18,357 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:18,362 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:18,363 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:18,370 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:18,372 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:18,379 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:18,388 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:18,389 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:18,391 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:18] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:19,543 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:19,547 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:19,557 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:19,558 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:19,563 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:19,564 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:19,575 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:19,576 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:19,595 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:19,599 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:19,605 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:19,607 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:19,608 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:19,609 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:20,838 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:20,842 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:20,852 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:20,853 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:20,859 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:20,870 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:20,875 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:20,877 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:20,882 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:20,884 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:20,889 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:20,890 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:20,898 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:20,900 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:21,434 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:21,438 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:21,448 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:21,450 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:21,457 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:21,458 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:21,467 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:21,471 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:21,476 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:21,478 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:21,484 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:21,485 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:21,487 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:21,489 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:21] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:22,535 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:22,538 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:22,545 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:22,548 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:22,554 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:22,557 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:22,562 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:22,565 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:22,575 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:22,577 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:22,584 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:22,587 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:22,588 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:22,591 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:22] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:24,207 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:24,212 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:24,222 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:24,224 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:24,233 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:24,237 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:24,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:24,245 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:24,256 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:24,258 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:24,272 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:24,273 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:24,274 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:24,283 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:24] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:28,616 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:28,620 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:28,649 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:28,653 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:28,676 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:28,687 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:28,697 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:28,699 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:28,706 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:28,712 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:28,738 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:28,741 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:28,742 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:28,746 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:28] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:29,729 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:29,732 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:29,763 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:29,767 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:29,773 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:29,778 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:29,784 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:29,786 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:29,789 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:29,797 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:29,803 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:29,805 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:29,811 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:29,815 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:29] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:30,871 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:30,875 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:30,886 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:30,888 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:30,894 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:30,902 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:30,912 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:30,917 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:30,924 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:30,925 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:30,934 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:30,938 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:30,939 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:30,942 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:30] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:32,191 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:32,195 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:32,207 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:32,209 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:32,217 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:32,221 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:32,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:32,236 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:32,258 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:32,274 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:32,289 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:32,294 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:32,299 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:32,303 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:32] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:35,936 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:35,943 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:35,955 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:35,963 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:35,974 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:35,977 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:35,985 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:35,989 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:35,997 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:35,999 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:36,005 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:36,009 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:36,011 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:36,014 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:36] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:37,119 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:37,124 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:37,135 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:37,136 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:37,142 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:37,148 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:37,154 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:37,156 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:37,162 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:37,163 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:37,167 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:37,170 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:37,180 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:37,182 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:37] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:58:41,543 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:58:41,547 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:58:41,567 - map_api - INFO - 找到 4 个目录
2025-08-04 14:58:41,568 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:58:41,575 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:58:41,577 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:58:41,588 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:58:41,590 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:58:41,596 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-08-04 14:58:41,598 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:58:41,625 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:58:41,628 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:58:41,629 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(2)
2025-08-04 14:58:41,631 - werkzeug - INFO - ************** - - [04/Aug/2025 14:58:41] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:59:06,472 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:59:06,477 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:59:06,492 - map_api - INFO - 找到 4 个目录
2025-08-04 14:59:06,494 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:59:06,508 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:59:06,510 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:59:06,518 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:59:06,519 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:59:06,525 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1694 字节
2025-08-04 14:59:06,533 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:59:06,538 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:59:06,541 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:59:06,542 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 14:59:06,545 - werkzeug - INFO - ************** - - [04/Aug/2025 14:59:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:59:10,958 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:59:10,962 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:59:10,973 - map_api - INFO - 找到 4 个目录
2025-08-04 14:59:10,975 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:59:10,983 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:59:10,986 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:59:10,991 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:59:10,994 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:59:11,028 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1694 字节
2025-08-04 14:59:11,033 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:59:11,057 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:59:11,061 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:59:11,062 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 14:59:11,065 - werkzeug - INFO - ************** - - [04/Aug/2025 14:59:11] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:59:12,343 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-08-04 14:59:12,362 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 14:59:12,364 - tif_api - INFO - 输入文件大小: 222.52 MB
2025-08-04 14:59:12,369 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-04 14:59:12,372 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-08-04 14:59:12,374 - tif_api - INFO - 黑色阈值: 0
2025-08-04 14:59:12,375 - tif_api - INFO - 白色阈值: 255
2025-08-04 14:59:12,377 - tif_api - INFO - 启动异步TIF处理任务...
2025-08-04 14:59:12,397 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - ============ TIF处理任务 4275fc11-fafe-4eb1-b54d-8a009b6bf365 开始执行 ============
2025-08-04 14:59:12,398 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 开始时间: 2025-08-04 14:59:12
2025-08-04 14:59:12,399 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-08-04 14:59:12,400 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 系统信息:
2025-08-04 14:59:12,401 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO -   操作系统: Windows 10.0.19045
2025-08-04 14:59:12,402 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO -   Python版本: 3.8.20
2025-08-04 14:59:12,403 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO -   GDAL版本: 3.9.2
2025-08-04 14:59:12,404 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO -   GPU可用: 否
2025-08-04 14:59:12,404 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 检查参数有效性...
2025-08-04 14:59:12,405 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 14:59:12,405 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 开始执行TIF处理流程...
2025-08-04 14:59:12,406 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-08-04 14:59:12,407 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-04 14:59:12,408 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-08-04 14:59:12,397 - tif_executor - INFO - 启动TIF处理任务 4275fc11-fafe-4eb1-b54d-8a009b6bf365: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-08-04 14:59:12,421 - tif_api - INFO - 异步任务启动成功，任务ID: 4275fc11-fafe-4eb1-b54d-8a009b6bf365
2025-08-04 14:59:12,423 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 14:59:12] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-08-04 14:59:12,448 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 14:59:12,450 - tif_api - INFO - 查询任务: 4275fc11-fafe-4eb1-b54d-8a009b6bf365
2025-08-04 14:59:12,452 - tif_api - INFO - 任务 4275fc11-fafe-4eb1-b54d-8a009b6bf365 状态查询成功，当前状态: 正在运行
2025-08-04 14:59:12,454 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 14:59:12] "GET /api/tif/status?task_id=4275fc11-fafe-4eb1-b54d-8a009b6bf365 HTTP/1.1" 200 -
2025-08-04 14:59:21,464 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:59:21,468 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:59:21,482 - map_api - INFO - 找到 4 个目录
2025-08-04 14:59:21,484 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:59:21,490 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:59:21,492 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:59:21,498 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:59:21,502 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:59:21,509 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 14:59:21,515 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:59:21,521 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:59:21,523 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:59:21,527 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 14:59:21,535 - werkzeug - INFO - ************** - - [04/Aug/2025 14:59:21] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:59:27,641 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:59:27,645 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:59:27,681 - map_api - INFO - 找到 4 个目录
2025-08-04 14:59:27,685 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:59:27,692 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:59:27,696 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:59:27,725 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:59:27,729 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:59:27,738 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 14:59:27,742 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:59:27,751 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:59:27,752 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:59:27,753 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 14:59:27,757 - werkzeug - INFO - ************** - - [04/Aug/2025 14:59:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:59:33,701 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17007, 高=20364, 波段数=4开始读取影像数据，大小约: 5284.58 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (14:59:29)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-08-04 14:59:33,733 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-08-04 14:59:34,595 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 14:59:34,616 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 14:59:34,659 - map_api - INFO - 找到 4 个目录
2025-08-04 14:59:34,680 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 14:59:34,713 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 14:59:34,714 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 14:59:34,727 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 14:59:34,728 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 14:59:34,737 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 14:59:34,739 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 14:59:34,748 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 14:59:34,754 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 14:59:34,761 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 14:59:34,764 - werkzeug - INFO - ************** - - [04/Aug/2025 14:59:34] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 14:59:37,936 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:04<01:23,  4.16s/it]
2025-08-04 14:59:38,476 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块:  10%|######7                                                                | 2/21 [00:04<00:38,  2.03s/it]
2025-08-04 14:59:40,235 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:06<00:07,  1.72it/s]
2025-08-04 14:59:40,482 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块:  43%|##############################4                                        | 9/21 [00:06<00:06,  1.90it/s]
2025-08-04 14:59:40,967 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块:  48%|#################################3                                    | 10/21 [00:07<00:05,  1.93it/s]
2025-08-04 14:59:42,382 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块:  52%|####################################6                                 | 11/21 [00:08<00:07,  1.41it/s]
2025-08-04 14:59:42,914 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块:  67%|##############################################6                       | 14/21 [00:09<00:03,  2.20it/s]
2025-08-04 14:59:43,330 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:09<00:02,  2.23it/s]
2025-08-04 14:59:43,487 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块:  76%|#####################################################3                | 16/21 [00:09<00:01,  2.59it/s]
2025-08-04 14:59:43,906 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块:  81%|########################################################6             | 17/21 [00:10<00:01,  2.53it/s]
2025-08-04 14:59:45,157 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块:  86%|############################################################          | 18/21 [00:11<00:01,  1.65it/s]
2025-08-04 14:59:45,448 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块:  90%|###############################################################3      | 19/21 [00:11<00:01,  1.91it/s]
2025-08-04 14:59:45,448 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:11<00:00,  1.80it/s]
2025-08-04 14:59:45,459 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-08-04 14:59:45,460 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-08-04 14:59:45,505 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 488.37it/s]
2025-08-04 14:59:50,445 - map_api - INFO - 请求获取日志: 类型=batlog, ID=c85bf9c9-a151-4312-9367-0e2fe1400494
2025-08-04 14:59:50,450 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\c85bf9c9-a151-4312-9367-0e2fe1400494.log
2025-08-04 14:59:50,780 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\c85bf9c9-a151-4312-9367-0e2fe1400494.log, 内容大小: 4879 字节
2025-08-04 14:59:50,785 - werkzeug - INFO - ************** - - [04/Aug/2025 14:59:50] "GET /api/map/logs?log_type=batlog&log_id=c85bf9c9-a151-4312-9367-0e2fe1400494 HTTP/1.1" 200 -
2025-08-04 14:59:51,449 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 21.70 秒 (14:59:50)预计总处理时间: 约 65.10 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif (14:59:50)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif使用已处理过的影像信息影像信息: 宽=17007, 高=20364, 波段数=3掩码形状: (20364, 17007), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 尺寸: 17007x20364x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-08-04 14:59:51,453 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-08-04 15:00:01,310 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=42.58, std=63.34应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-08-04 15:00:01,311 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-08-04 15:00:01,315 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:01,434 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:01, 17.18it/s]
2025-08-04 15:00:01,434 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:01,617 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:00, 16.61it/s]
2025-08-04 15:00:01,618 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:01,878 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:01, 11.40it/s]
2025-08-04 15:00:01,878 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:02,161 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:01,  9.33it/s]
2025-08-04 15:00:02,162 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:02,463 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:01,  8.20it/s]
2025-08-04 15:00:02,466 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:02,681 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:01<00:01,  7.11it/s]
2025-08-04 15:00:02,683 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:02,953 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:01,  5.91it/s]
2025-08-04 15:00:02,954 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:03,133 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:01<00:01,  5.81it/s]
2025-08-04 15:00:03,134 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:03,307 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:01<00:01,  5.79it/s]
2025-08-04 15:00:03,308 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:03,478 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:02<00:00,  5.82it/s]
2025-08-04 15:00:03,479 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:03,661 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:02<00:00,  5.71it/s]
2025-08-04 15:00:03,661 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:03,827 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:02<00:00,  5.79it/s]
2025-08-04 15:00:03,828 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:04,007 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:02<00:00,  5.72it/s]
2025-08-04 15:00:04,009 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:04,179 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:02<00:00,  5.76it/s]
2025-08-04 15:00:04,179 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:04,225 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:12,519 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 15:00:12,535 - tif_api - INFO - 查询任务: 4275fc11-fafe-4eb1-b54d-8a009b6bf365
2025-08-04 15:00:12,549 - tif_api - INFO - 任务 4275fc11-fafe-4eb1-b54d-8a009b6bf365 状态查询成功，当前状态: 正在运行
2025-08-04 15:00:12,553 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:00:12] "GET /api/tif/status?task_id=4275fc11-fafe-4eb1-b54d-8a009b6bf365 HTTP/1.1" 200 -
2025-08-04 15:00:13,605 - map_api - INFO - 请求获取日志: 类型=batlog, ID=c85bf9c9-a151-4312-9367-0e2fe1400494
2025-08-04 15:00:13,609 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\c85bf9c9-a151-4312-9367-0e2fe1400494.log
2025-08-04 15:00:13,666 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\c85bf9c9-a151-4312-9367-0e2fe1400494.log, 内容大小: 4879 字节
2025-08-04 15:00:13,672 - werkzeug - INFO - ************** - - [04/Aug/2025 15:00:13] "GET /api/map/logs?log_type=batlog&log_id=c85bf9c9-a151-4312-9367-0e2fe1400494 HTTP/1.1" 200 -
2025-08-04 15:00:15,901 - map_api - INFO - 请求获取日志: 类型=batlog, ID=8fe60cee-5933-43bf-8cc9-769052e5d803
2025-08-04 15:00:15,907 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\8fe60cee-5933-43bf-8cc9-769052e5d803.log
2025-08-04 15:00:15,937 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\8fe60cee-5933-43bf-8cc9-769052e5d803.log, 内容大小: 7082 字节
2025-08-04 15:00:15,943 - werkzeug - INFO - ************** - - [04/Aug/2025 15:00:15] "GET /api/map/logs?log_type=batlog&log_id=8fe60cee-5933-43bf-8cc9-769052e5d803 HTTP/1.1" 200 -
2025-08-04 15:00:18,463 - map_api - INFO - 请求获取日志: 类型=batlog, ID=c85bf9c9-a151-4312-9367-0e2fe1400494
2025-08-04 15:00:18,468 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\c85bf9c9-a151-4312-9367-0e2fe1400494.log
2025-08-04 15:00:18,470 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\c85bf9c9-a151-4312-9367-0e2fe1400494.log, 内容大小: 4879 字节
2025-08-04 15:00:18,473 - werkzeug - INFO - ************** - - [04/Aug/2025 15:00:18] "GET /api/map/logs?log_type=batlog&log_id=c85bf9c9-a151-4312-9367-0e2fe1400494 HTTP/1.1" 200 -
2025-08-04 15:00:21,430 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:00:21,436 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:00:21,515 - map_api - INFO - 找到 4 个目录
2025-08-04 15:00:21,521 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:00:21,572 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:00:21,577 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:00:21,628 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:00:21,631 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:00:21,637 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 15:00:21,638 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:00:21,671 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:00:21,674 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:00:21,675 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:00:21,682 - werkzeug - INFO - ************** - - [04/Aug/2025 15:00:21] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:00:29,807 - map_api - INFO - 请求获取日志: 类型=batlog, ID=c85bf9c9-a151-4312-9367-0e2fe1400494
2025-08-04 15:00:29,816 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\c85bf9c9-a151-4312-9367-0e2fe1400494.log
2025-08-04 15:00:29,819 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\c85bf9c9-a151-4312-9367-0e2fe1400494.log, 内容大小: 4879 字节
2025-08-04 15:00:29,821 - werkzeug - INFO - ************** - - [04/Aug/2025 15:00:29] "GET /api/map/logs?log_type=batlog&log_id=c85bf9c9-a151-4312-9367-0e2fe1400494 HTTP/1.1" 200 -
2025-08-04 15:00:34,370 - map_api - INFO - 请求获取日志: 类型=batlog, ID=4d238329-a277-4cee-8b55-99037586a151
2025-08-04 15:00:34,375 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\4d238329-a277-4cee-8b55-99037586a151.log
2025-08-04 15:00:35,203 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\4d238329-a277-4cee-8b55-99037586a151.log, 内容大小: 363540 字节
2025-08-04 15:00:35,212 - werkzeug - INFO - ************** - - [04/Aug/2025 15:00:35] "GET /api/map/logs?log_type=batlog&log_id=4d238329-a277-4cee-8b55-99037586a151 HTTP/1.1" 200 -
2025-08-04 15:00:54,396 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:02<02:05, 62.94s/it]
2025-08-04 15:00:59,253 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=45.79, std=65.41应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-08-04 15:00:59,254 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-08-04 15:00:59,257 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:59,500 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:04,  4.11it/s]
2025-08-04 15:00:59,501 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:59,615 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:03,  5.97it/s]
2025-08-04 15:00:59,616 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:59,724 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  7.13it/s]
2025-08-04 15:00:59,725 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:00:59,877 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:01,  9.61it/s]
2025-08-04 15:00:59,877 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:01:00,021 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:01, 11.13it/s]
2025-08-04 15:01:00,021 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:01:00,172 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:01, 11.85it/s]
2025-08-04 15:01:00,172 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:01:00,318 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:00, 12.46it/s]
2025-08-04 15:01:00,319 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:01:00,475 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:00, 12.55it/s]
2025-08-04 15:01:00,476 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:01:00,835 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:01<00:00,  8.89it/s]
2025-08-04 15:01:00,836 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:01:02,516 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:03<00:01,  2.91it/s]
2025-08-04 15:01:02,517 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:01:05,324 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:06<00:02,  1.25it/s]
2025-08-04 15:01:05,324 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:01:08,788 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:09<00:02,  1.36s/it]
2025-08-04 15:01:08,789 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:01:12,576 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 15:01:12,581 - tif_api - INFO - 查询任务: 4275fc11-fafe-4eb1-b54d-8a009b6bf365
2025-08-04 15:01:12,586 - tif_api - INFO - 任务 4275fc11-fafe-4eb1-b54d-8a009b6bf365 状态查询成功，当前状态: 正在运行
2025-08-04 15:01:12,588 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:01:12] "GET /api/tif/status?task_id=4275fc11-fafe-4eb1-b54d-8a009b6bf365 HTTP/1.1" 200 -
2025-08-04 15:01:13,383 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:14<00:02,  2.10s/it]
2025-08-04 15:01:13,385 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:01:13,399 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:01:21,430 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:01:21,435 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:01:21,447 - map_api - INFO - 找到 4 个目录
2025-08-04 15:01:21,449 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:01:21,456 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:01:21,463 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:01:21,472 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:01:21,478 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:01:21,486 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 15:01:21,491 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:01:21,504 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:01:21,507 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:01:21,522 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:01:21,552 - werkzeug - INFO - ************** - - [04/Aug/2025 15:01:21] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:02:04,961 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:02:04,966 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:02:04,982 - map_api - INFO - 找到 4 个目录
2025-08-04 15:02:04,984 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:02:04,994 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:02:04,997 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:02:05,008 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:02:05,017 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:02:05,027 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 15:02:05,030 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:02:05,038 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:02:05,045 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:02:05,052 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:02:05,061 - werkzeug - INFO - ************** - - [04/Aug/2025 15:02:05] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:02:12,612 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 15:02:12,616 - tif_api - INFO - 查询任务: 4275fc11-fafe-4eb1-b54d-8a009b6bf365
2025-08-04 15:02:12,619 - tif_api - INFO - 任务 4275fc11-fafe-4eb1-b54d-8a009b6bf365 状态查询成功，当前状态: 正在运行
2025-08-04 15:02:12,621 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:02:12] "GET /api/tif/status?task_id=4275fc11-fafe-4eb1-b54d-8a009b6bf365 HTTP/1.1" 200 -
2025-08-04 15:02:14,048 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:02:14,051 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:02:14,078 - map_api - INFO - 找到 4 个目录
2025-08-04 15:02:14,082 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:02:14,087 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:02:14,089 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:02:14,123 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:02:14,127 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:02:14,153 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 15:02:14,157 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:02:14,185 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:02:14,190 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:02:14,191 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:02:14,193 - werkzeug - INFO - ************** - - [04/Aug/2025 15:02:14] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:02:21,432 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:02:21,436 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:02:21,465 - map_api - INFO - 找到 4 个目录
2025-08-04 15:02:21,468 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:02:21,484 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:02:21,489 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:02:21,509 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:02:21,514 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:02:21,520 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 15:02:21,522 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:02:21,528 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:02:21,532 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:02:21,533 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:02:21,536 - werkzeug - INFO - ************** - - [04/Aug/2025 15:02:21] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:02:23,632 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:02:23,638 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:02:23,653 - map_api - INFO - 找到 4 个目录
2025-08-04 15:02:23,684 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:02:23,696 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:02:23,698 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:02:23,706 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:02:23,712 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:02:23,719 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 15:02:23,728 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:02:23,735 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:02:23,737 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:02:23,739 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:02:23,741 - werkzeug - INFO - ************** - - [04/Aug/2025 15:02:23] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:02:27,550 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [02:36<01:20, 80.71s/it]
2025-08-04 15:02:32,828 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=35.76, std=51.77应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-08-04 15:02:32,830 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-08-04 15:02:32,834 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:02:33,063 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:04,  4.40it/s]
2025-08-04 15:02:33,064 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:02:33,225 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  8.41it/s]
2025-08-04 15:02:33,226 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:02:33,348 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 3/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:01, 14.07it/s]
2025-08-04 15:02:33,349 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:02:33,450 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 3/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 21.44it/s]
2025-08-04 15:02:33,450 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:02:33,564 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 15/21 [00:00<00:00, 28.64it/s]
2025-08-04 15:02:33,565 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:02:33,669 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [00:00<00:00, 34.30it/s]
2025-08-04 15:02:33,670 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:02:33,676 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - [A
2025-08-04 15:03:00,803 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:03:00,810 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:03:00,830 - map_api - INFO - 找到 4 个目录
2025-08-04 15:03:00,833 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:03:00,843 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:03:00,848 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:03:00,863 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:03:00,865 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:03:00,883 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 15:03:00,904 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:03:00,914 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:03:00,921 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:03:00,931 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:03:00,942 - werkzeug - INFO - ************** - - [04/Aug/2025 15:03:00] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:03:12,639 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 15:03:12,642 - tif_api - INFO - 查询任务: 4275fc11-fafe-4eb1-b54d-8a009b6bf365
2025-08-04 15:03:12,646 - tif_api - INFO - 任务 4275fc11-fafe-4eb1-b54d-8a009b6bf365 状态查询成功，当前状态: 正在运行
2025-08-04 15:03:12,648 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:03:12] "GET /api/tif/status?task_id=4275fc11-fafe-4eb1-b54d-8a009b6bf365 HTTP/1.1" 200 -
2025-08-04 15:03:22,168 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:03:22,173 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:03:22,184 - map_api - INFO - 找到 4 个目录
2025-08-04 15:03:22,186 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:03:22,191 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:03:22,199 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:03:22,204 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:03:22,206 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:03:22,224 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 15:03:22,229 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:03:22,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:03:22,236 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:03:22,237 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:03:22,240 - werkzeug - INFO - ************** - - [04/Aug/2025 15:03:22] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:04:12,660 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 15:04:12,664 - tif_api - INFO - 查询任务: 4275fc11-fafe-4eb1-b54d-8a009b6bf365
2025-08-04 15:04:13,437 - tif_api - INFO - 任务 4275fc11-fafe-4eb1-b54d-8a009b6bf365 状态查询成功，当前状态: 正在运行
2025-08-04 15:04:13,444 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:04:13] "GET /api/tif/status?task_id=4275fc11-fafe-4eb1-b54d-8a009b6bf365 HTTP/1.1" 200 -
2025-08-04 15:04:28,988 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:37<00:00, 99.31s/it]
2025-08-04 15:04:28,989 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:37<00:00, 92.51s/it]
2025-08-04 15:05:00,189 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:05:00,194 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:05:00,212 - map_api - INFO - 找到 4 个目录
2025-08-04 15:05:00,217 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:05:00,274 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:05:00,281 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:05:00,492 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:05:00,499 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:05:00,509 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 15:05:00,511 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:05:00,722 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:05:00,727 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:05:00,730 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:05:00,736 - werkzeug - INFO - ************** - - [04/Aug/2025 15:05:00] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:05:13,471 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 15:05:13,474 - tif_api - INFO - 查询任务: 4275fc11-fafe-4eb1-b54d-8a009b6bf365
2025-08-04 15:05:07,640 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif影像保存完成，耗时: 278.58 秒 (15:04:29)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp (15:04:29)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tifTIF文件信息: 宽=17007, 高=20364, 波段数=3开始读取影像数据，大小约: 3963.44 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif提取TIF文件信息...TIF文件信息: 宽=17007, 高=20364, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 218929698 个无效像素检查波段 2...波段 2 检测到 218929698 个无效像素检查波段 3...波段 3 检测到 218929698 个无效像素掩码统计: 总像素数 346330548, 有效像素数 127400850 (36.79%), 无效像素数 218929698 (63.21%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 1 个初始轮廓轮廓面积统计: 最小=127380512.50, 最大=127380512.50, 平均=127380512.50, 总数=1将 1 个轮廓转换为地理坐标...
2025-08-04 15:05:16,238 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-08-04 15:05:16,222 - tif_api - INFO - 任务 4275fc11-fafe-4eb1-b54d-8a009b6bf365 状态查询成功，当前状态: 正在运行
2025-08-04 15:05:16,282 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:05:16] "GET /api/tif/status?task_id=4275fc11-fafe-4eb1-b54d-8a009b6bf365 HTTP/1.1" 200 -
2025-08-04 15:05:16,710 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.16it/s]
2025-08-04 15:05:16,710 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.16it/s]
2025-08-04 15:05:17,080 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-08-04 15:05:29,609 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:05:29,638 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:05:29,780 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-08-04 15:05:29,781 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-08-04 15:05:29,803 - map_api - INFO - 找到 4 个目录
2025-08-04 15:05:29,805 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:05:29,888 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:05:29,946 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:05:29,952 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:05:29,954 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:05:29,959 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1920 字节
2025-08-04 15:05:29,960 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:05:29,964 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:05:29,966 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:05:29,972 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:05:29,975 - werkzeug - INFO - ************** - - [04/Aug/2025 15:05:29] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:05:29,984 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:02, 10.02it/s]
2025-08-04 15:05:30,167 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理轮廓:  13%|#########4                                                               | 4/31 [00:00<00:02, 10.52it/s]
2025-08-04 15:05:30,302 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:00<00:00, 81.35it/s]
2025-08-04 15:05:30,303 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:00<00:00, 59.73it/s]
2025-08-04 15:05:31,058 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 处理了 82263 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp写入shapefile特征...处理MultiPolygon，包含 19 个多边形
2025-08-04 15:05:31,060 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
写入多边形:   0%|                                                                               | 0/19 [00:00<?, ?it/s]
2025-08-04 15:05:31,768 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
写入多边形:  26%|##################6                                                    | 5/19 [00:00<00:01,  7.10it/s]
2025-08-04 15:05:31,993 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
写入多边形:  58%|########################################5                             | 11/19 [00:00<00:00, 13.23it/s]
2025-08-04 15:05:31,996 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - ERROR - 
写入多边形: 100%|######################################################################| 19/19 [00:00<00:00, 20.38it/s]
2025-08-04 15:05:32,716 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 处理完成，耗时: 380.31 秒 (6.34 分钟)
2025-08-04 15:05:32,716 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 处理结果: 成功
2025-08-04 15:05:32,718 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 大小: 740.84 MB
2025-08-04 15:05:32,719 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp, 大小: 22.81 KB
2025-08-04 15:05:32,724 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - TIF处理任务 4275fc11-fafe-4eb1-b54d-8a009b6bf365 执行成功
2025-08-04 15:05:32,725 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 完成时间: 2025-08-04 15:05:32
2025-08-04 15:05:32,725 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - 状态: 运行成功
2025-08-04 15:05:32,726 - tif_task_4275fc11-fafe-4eb1-b54d-8a009b6bf365 - INFO - ============ 任务执行结束 ============
2025-08-04 15:06:16,348 - tif_api - INFO - 收到任务状态查询请求
2025-08-04 15:06:16,352 - tif_api - INFO - 查询任务: 4275fc11-fafe-4eb1-b54d-8a009b6bf365
2025-08-04 15:06:16,358 - tif_api - INFO - 任务 4275fc11-fafe-4eb1-b54d-8a009b6bf365 状态查询成功，当前状态: 运行成功
2025-08-04 15:06:16,365 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:06:16] "GET /api/tif/status?task_id=4275fc11-fafe-4eb1-b54d-8a009b6bf365 HTTP/1.1" 200 -
2025-08-04 15:06:19,155 - geo_publisher - INFO - 启动GeoTIFF发布任务 a16f3aff-8432-46d8-a4a5-940d84ed9f50: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-08-04 15:06:19,160 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:06:19] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-08-04 15:06:19,185 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:06:19] "GET /api/geo/status?task_id=a16f3aff-8432-46d8-a4a5-940d84ed9f50 HTTP/1.1" 200 -
2025-08-04 15:06:19,280 - geo_task_a16f3aff-8432-46d8-a4a5-940d84ed9f50 - INFO - GeoServer发布任务 a16f3aff-8432-46d8-a4a5-940d84ed9f50 开始执行
2025-08-04 15:06:19,281 - geo_task_a16f3aff-8432-46d8-a4a5-940d84ed9f50 - INFO - 开始时间: 2025-08-04 15:06:19
2025-08-04 15:06:19,284 - geo_task_a16f3aff-8432-46d8-a4a5-940d84ed9f50 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-08-04 15:06:19,288 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-04 15:06:19,289 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-04 15:06:19,367 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-04 15:06:19,368 - root - INFO - 未提供存储名称，使用文件名: 20250705171601
2025-08-04 15:06:19,373 - root - INFO - 未提供图层名称，使用存储名称: 20250705171601
2025-08-04 15:06:19,398 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601.tif
2025-08-04 15:06:22,170 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:06:22,173 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:06:22,201 - map_api - INFO - 找到 4 个目录
2025-08-04 15:06:22,204 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:06:22,229 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:06:22,233 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:06:22,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:06:22,272 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:06:22,281 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2196 字节
2025-08-04 15:06:22,283 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:06:22,289 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:06:22,296 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:06:22,302 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:06:22,304 - werkzeug - INFO - ************** - - [04/Aug/2025 15:06:22] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:07:19,203 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:07:19] "GET /api/geo/status?task_id=a16f3aff-8432-46d8-a4a5-940d84ed9f50 HTTP/1.1" 200 -
2025-08-04 15:07:31,620 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:07:31,628 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:07:31,667 - map_api - INFO - 找到 4 个目录
2025-08-04 15:07:31,673 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:07:31,693 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:07:31,723 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:07:31,745 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:07:31,756 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:07:31,763 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2196 字节
2025-08-04 15:07:31,765 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:07:31,770 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:07:31,771 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:07:31,772 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:07:31,775 - werkzeug - INFO - ************** - - [04/Aug/2025 15:07:31] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:07:38,224 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:07:38,226 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:07:38,241 - map_api - INFO - 找到 4 个目录
2025-08-04 15:07:38,242 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:07:38,256 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:07:38,258 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:07:38,291 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:07:38,295 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:07:38,323 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2196 字节
2025-08-04 15:07:38,327 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:07:38,335 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:07:38,343 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:07:38,345 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:07:38,350 - werkzeug - INFO - ************** - - [04/Aug/2025 15:07:38] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:07:45,346 - map_api - INFO - 请求获取日志: 类型=batlog, ID=c85bf9c9-a151-4312-9367-0e2fe1400494
2025-08-04 15:07:45,349 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\c85bf9c9-a151-4312-9367-0e2fe1400494.log
2025-08-04 15:07:45,445 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\c85bf9c9-a151-4312-9367-0e2fe1400494.log, 内容大小: 4879 字节
2025-08-04 15:07:45,451 - werkzeug - INFO - ************** - - [04/Aug/2025 15:07:45] "GET /api/map/logs?log_type=batlog&log_id=c85bf9c9-a151-4312-9367-0e2fe1400494 HTTP/1.1" 200 -
2025-08-04 15:08:19,225 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:08:19] "GET /api/geo/status?task_id=a16f3aff-8432-46d8-a4a5-940d84ed9f50 HTTP/1.1" 200 -
2025-08-04 15:08:21,436 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:08:21,440 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:08:25,443 - map_api - INFO - 找到 4 个目录
2025-08-04 15:08:25,446 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:08:25,457 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:08:25,459 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:08:25,467 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:08:25,472 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:08:25,480 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2196 字节
2025-08-04 15:08:25,483 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:08:25,490 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:08:25,496 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:08:25,497 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:08:25,500 - werkzeug - INFO - ************** - - [04/Aug/2025 15:08:25] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:08:32,238 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 到 'testodm:20250705171601'
2025-08-04 15:08:32,363 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171601
2025-08-04 15:08:33,021 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 成功发布为图层 'testodm:20250705171601'
2025-08-04 15:08:33,363 - geo_task_a16f3aff-8432-46d8-a4a5-940d84ed9f50 - INFO - GeoServer发布任务 a16f3aff-8432-46d8-a4a5-940d84ed9f50 执行成功
2025-08-04 15:08:33,378 - geo_task_a16f3aff-8432-46d8-a4a5-940d84ed9f50 - INFO - 完成时间: 2025-08-04 15:08:33
2025-08-04 15:08:33,384 - geo_task_a16f3aff-8432-46d8-a4a5-940d84ed9f50 - INFO - 状态: 发布成功
2025-08-04 15:09:21,426 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:09:21,428 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:09:21,439 - map_api - INFO - 找到 4 个目录
2025-08-04 15:09:21,441 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:09:21,450 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:09:21,453 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:09:21,472 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:09:21,484 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:09:21,504 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2196 字节
2025-08-04 15:09:21,508 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:09:21,523 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:09:21,525 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:09:21,527 - map_api - INFO - 任务排序结果: 运行中(2) > 未开始(0) > 已完成(2)
2025-08-04 15:09:21,541 - werkzeug - INFO - ************** - - [04/Aug/2025 15:09:21] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:09:25,463 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:09:25] "GET /api/geo/status?task_id=a16f3aff-8432-46d8-a4a5-940d84ed9f50 HTTP/1.1" 200 -
2025-08-04 15:10:23,558 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:10:23,564 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:10:23,576 - map_api - INFO - 找到 4 个目录
2025-08-04 15:10:23,587 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:10:23,604 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:10:23,611 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:10:23,618 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:10:23,625 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:10:23,631 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 15:10:23,633 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:10:23,641 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:10:23,643 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:10:23,644 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 15:10:23,646 - werkzeug - INFO - ************** - - [04/Aug/2025 15:10:23] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:10:24,241 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:10:24,245 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:10:24,256 - map_api - INFO - 找到 4 个目录
2025-08-04 15:10:24,258 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:10:24,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:10:24,269 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:10:24,278 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:10:24,280 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:10:24,285 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 15:10:24,290 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:10:24,302 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:10:24,307 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:10:24,309 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 15:10:24,312 - werkzeug - INFO - ************** - - [04/Aug/2025 15:10:24] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:19:39,135 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:19:39,141 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:19:39,184 - map_api - INFO - 找到 4 个目录
2025-08-04 15:19:39,198 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:19:39,216 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:19:39,227 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:19:39,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:19:39,247 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:19:39,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 15:19:39,263 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:19:39,280 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:19:39,287 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:19:39,292 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 15:19:39,303 - werkzeug - INFO - ************** - - [04/Aug/2025 15:19:39] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:20:12,375 - map_api - INFO - 请求文件: baseMap.json
2025-08-04 15:20:12,418 - map_api - INFO - 成功获取文件 baseMap.json, 内容大小: 3253 字节
2025-08-04 15:20:12,425 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:20:12] "GET /api/map/base-map HTTP/1.1" 200 -
2025-08-04 15:20:13,588 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:20:13,593 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:20:13,606 - map_api - INFO - 找到 4 个目录
2025-08-04 15:20:13,608 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:20:13,638 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:20:13,645 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:20:13,666 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:20:13,670 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:20:13,687 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 15:20:13,701 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:20:13,708 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:20:13,710 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:20:13,720 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 15:20:13,722 - werkzeug - INFO - ************** - - [04/Aug/2025 15:20:13] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:20:19,048 - map_api - INFO - 请求文件: baseMap3.json
2025-08-04 15:20:19,058 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-04 15:20:19,062 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:20:19] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-04 15:20:19,087 - map_api - INFO - 请求文件: baseMap2.json
2025-08-04 15:20:19,121 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-04 15:20:19,130 - map_api - INFO - 请求文件: baseMap3.json
2025-08-04 15:20:19,133 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-04 15:20:19,143 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:20:19] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-04 15:20:19,176 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-04 15:20:19,188 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:20:19] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-04 15:20:19,177 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-04 15:20:19,192 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:20:19] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-04 15:20:19,910 - map_api - INFO - 请求文件: baseMap2.json
2025-08-04 15:20:19,911 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-04 15:20:19,923 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-04 15:20:19,925 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-04 15:20:19,928 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:20:19] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-04 15:20:19,930 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:20:19] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-04 15:20:20,661 - werkzeug - INFO - ************** - - [04/Aug/2025 15:20:20] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-04 15:20:20,674 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-04 15:20:21,481 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-04 15:20:21,493 - werkzeug - INFO - ************** - - [04/Aug/2025 15:20:21] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-04 15:21:03,741 - map_api - INFO - 请求文件: baseMap3.json
2025-08-04 15:21:03,749 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-04 15:21:03,754 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-04 15:21:03,759 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:21:03] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-04 15:21:03,757 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-04 15:21:03,762 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 15:21:03] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-04 15:21:08,980 - werkzeug - INFO - ************** - - [04/Aug/2025 15:21:08] "OPTIONS /api/management/layers/bbox?workspace=testodm&layer=20250705171601 HTTP/1.1" 200 -
2025-08-04 15:21:08,987 - root - INFO - 获取图层 testodm:20250705171601 的信息
2025-08-04 15:21:09,028 - root - INFO - 成功获取图层 testodm:20250705171601 的边界框信息
2025-08-04 15:21:09,030 - werkzeug - INFO - ************** - - [04/Aug/2025 15:21:09] "GET /api/management/layers/bbox?workspace=testodm&layer=20250705171601 HTTP/1.1" 200 -
2025-08-04 15:21:14,170 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:21:14,179 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:21:14,187 - map_api - INFO - 找到 4 个目录
2025-08-04 15:21:14,193 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:21:14,200 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:21:14,202 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:21:14,233 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:21:14,237 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:21:14,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 15:21:14,270 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:21:14,278 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:21:14,281 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:21:14,288 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 15:21:14,290 - werkzeug - INFO - ************** - - [04/Aug/2025 15:21:14] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:22:14,173 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:22:14,178 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:22:14,191 - map_api - INFO - 找到 4 个目录
2025-08-04 15:22:14,192 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:22:14,197 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:22:14,203 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:22:14,210 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:22:14,211 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:22:14,221 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 15:22:14,227 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:22:14,245 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:22:14,248 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:22:14,254 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 15:22:14,256 - werkzeug - INFO - ************** - - [04/Aug/2025 15:22:14] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:23:14,174 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:23:14,178 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:23:14,192 - map_api - INFO - 找到 4 个目录
2025-08-04 15:23:14,193 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:23:14,216 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:23:14,220 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:23:14,227 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:23:14,228 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:23:14,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 15:23:14,254 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:23:14,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:23:14,263 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:23:14,265 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 15:23:14,266 - werkzeug - INFO - ************** - - [04/Aug/2025 15:23:14] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:24:14,170 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:24:14,174 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:24:14,185 - map_api - INFO - 找到 4 个目录
2025-08-04 15:24:14,186 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:24:14,209 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:24:14,214 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:24:14,221 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:24:14,227 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:24:14,234 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 15:24:14,235 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:24:14,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:24:14,243 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:24:14,248 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 15:24:14,250 - werkzeug - INFO - ************** - - [04/Aug/2025 15:24:14] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:25:16,634 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:25:16,643 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:25:16,658 - map_api - INFO - 找到 4 个目录
2025-08-04 15:25:16,665 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:25:16,673 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:25:16,679 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:25:16,684 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:25:16,686 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:25:16,693 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 15:25:16,699 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:25:16,708 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:25:16,712 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:25:16,717 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 15:25:16,721 - werkzeug - INFO - ************** - - [04/Aug/2025 15:25:16] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:26:14,178 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:26:14,183 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:26:14,209 - map_api - INFO - 找到 4 个目录
2025-08-04 15:26:14,211 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:26:14,218 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:26:14,225 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:26:14,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:26:14,232 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:26:14,238 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 15:26:14,241 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:26:14,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:26:14,249 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:26:14,257 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 15:26:14,260 - werkzeug - INFO - ************** - - [04/Aug/2025 15:26:14] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:27:19,206 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:27:19,210 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:27:19,222 - map_api - INFO - 找到 4 个目录
2025-08-04 15:27:19,223 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:27:19,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:27:19,232 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:27:19,264 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:27:19,267 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:27:19,272 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 15:27:19,275 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:27:19,284 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:27:19,287 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:27:19,288 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 15:27:19,294 - werkzeug - INFO - ************** - - [04/Aug/2025 15:27:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 15:28:19,223 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 15:28:19,227 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 15:28:19,237 - map_api - INFO - 找到 4 个目录
2025-08-04 15:28:19,239 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 15:28:19,249 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 15:28:19,251 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 15:28:19,258 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 15:28:19,261 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 15:28:19,286 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 15:28:19,290 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 15:28:19,294 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 15:28:19,295 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 15:28:19,296 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 15:28:19,298 - werkzeug - INFO - ************** - - [04/Aug/2025 15:28:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
