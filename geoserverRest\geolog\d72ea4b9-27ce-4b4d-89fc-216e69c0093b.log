2025-07-18 11:02:19,373 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - INFO - GeoServer发布任务 d72ea4b9-27ce-4b4d-89fc-216e69c0093b 开始执行
2025-07-18 11:02:19,375 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - INFO - 开始时间: 2025-07-18 11:02:19
2025-07-18 11:02:19,377 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "\"tttt\"",
  "store_name": null,
  "layer_name": null
}
2025-07-18 11:02:22,056 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - ERROR - 执行出错: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'
2025-07-18 11:02:22,058 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 352, in _run_with_logging
    result = func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 425, in publish_func
    return manager.publish_geotiff(path, ws, store, layer)
  File "D:\Drone_Project\geoserverapi\geoserver_manager.py", line 607, in publish_geotiff
    self.geo.create_workspace(workspace)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\geo\Geoserver.py", line 396, in create_workspace
    raise GeoserverException(r.status_code, r.content)
geo.Geoserver.GeoserverException: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'

2025-07-18 11:02:22,060 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - ERROR - 完成时间: 2025-07-18 11:02:22
2025-07-18 11:02:22,061 - geo_task_d72ea4b9-27ce-4b4d-89fc-216e69c0093b - ERROR - 状态: 发布失败
