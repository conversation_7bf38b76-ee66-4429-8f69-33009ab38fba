2025-07-14 15:28:06,523 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250714_152806.log
2025-07-14 15:28:06,581 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 15:28:06,581 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 15:28:06,632 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 15:28:06,669 - batch_executor - INFO - 加载了 29 个任务状态
2025-07-14 15:28:06,680 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 15:28:06,680 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 15:28:06,693 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 15:28:06,701 - root - INFO - === GeoServer REST API服务 ===
2025-07-14 15:28:06,702 - root - INFO - 主机: 0.0.0.0
2025-07-14 15:28:06,702 - root - INFO - 端口: 5083
2025-07-14 15:28:06,702 - root - INFO - 调试模式: 禁用
2025-07-14 15:28:06,702 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-14 15:28:06,720 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-14 15:28:06,721 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-14 15:28:18,520 - batch_executor - INFO - 启动任务 5e2c5686-3570-4e12-b24c-b720b1f02f33: D:\Drone_Project\ODM\ODM\run.bat D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project
2025-07-14 15:28:18,522 - werkzeug - INFO - ************** - - [14/Jul/2025 15:28:18] "GET /api/batch/execute?project_path=D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project&fast-orthophoto HTTP/1.1" 200 -
2025-07-14 15:28:29,450 - batch_executor - INFO - 启动任务 d6461d69-6e0c-4c14-bee6-d1ae34679bdd: D:\Drone_Project\ODM\ODM\run.bat D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project
2025-07-14 15:28:29,451 - werkzeug - INFO - ************** - - [14/Jul/2025 15:28:29] "GET /api/batch/execute?project_path=D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project&fast-orthophoto HTTP/1.1" 200 -
2025-07-14 15:30:50,870 - batch_executor - INFO - 启动任务 e1c96dee-a587-477c-bcdb-86d15d82bb59: D:\Drone_Project\ODM\ODM\run.bat D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project
2025-07-14 15:30:50,872 - werkzeug - INFO - ************** - - [14/Jul/2025 15:30:50] "GET /api/batch/execute?project_path=D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project&fast-orthophoto HTTP/1.1" 200 -
2025-07-14 15:36:07,076 - batch_executor - INFO - 启动任务 689cfc93-2e0c-4158-93a4-738fe00f106f: D:\Drone_Project\ODM\ODM\run.bat D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project
2025-07-14 15:36:07,078 - werkzeug - INFO - ************** - - [14/Jul/2025 15:36:07] "GET /api/batch/execute?project_path=D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project&fast-orthophoto HTTP/1.1" 200 -
2025-07-14 15:41:15,308 - batch_executor - INFO - 启动任务 49a1b247-5d9a-4249-94ff-34e2196b7a5c: D:\Drone_Project\ODM\ODM\run.bat D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project
2025-07-14 15:41:15,309 - werkzeug - INFO - ************** - - [14/Jul/2025 15:41:15] "GET /api/batch/execute?project_path=D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project&fast-orthophoto HTTP/1.1" 200 -
2025-07-14 15:47:24,107 - werkzeug - INFO - ************** - - [14/Jul/2025 15:47:24] "GET /api/batch/status?task_id=49a1b247-5d9a-4249-94ff-34e2196b7a5c HTTP/1.1" 200 -
2025-07-14 16:14:01,166 - werkzeug - INFO - ************** - - [14/Jul/2025 16:14:01] "GET /api/batch/status?task_id=49a1b247-5d9a-4249-94ff-34e2196b7a5c HTTP/1.1" 200 -
2025-07-14 16:27:00,162 - werkzeug - INFO - ************** - - [14/Jul/2025 16:27:00] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 16:27:00,225 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 16:27:01,501 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 16:27:01,512 - werkzeug - INFO - ************** - - [14/Jul/2025 16:27:01] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 16:27:29,094 - werkzeug - INFO - ************** - - [14/Jul/2025 16:27:29] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 16:27:29,109 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 16:27:29,153 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 16:27:29,156 - werkzeug - INFO - ************** - - [14/Jul/2025 16:27:29] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
