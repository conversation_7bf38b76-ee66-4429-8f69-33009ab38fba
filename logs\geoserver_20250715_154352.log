2025-07-15 15:43:52,319 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250715_154352.log
2025-07-15 15:43:52,320 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-15 15:43:52,320 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-15 15:43:52,338 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-15 15:43:52,343 - batch_executor - INFO - 加载了 35 个任务状态
2025-07-15 15:43:55,204 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-15 15:43:55,204 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-15 15:43:55,213 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-15 15:43:55,218 - root - INFO - === GeoServer REST API服务 ===
2025-07-15 15:43:55,218 - root - INFO - 主机: 0.0.0.0
2025-07-15 15:43:55,218 - root - INFO - 端口: 5083
2025-07-15 15:43:55,221 - root - INFO - 调试模式: 禁用
2025-07-15 15:43:55,222 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-15 15:43:55,234 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-15 15:43:55,237 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-15 15:46:09,196 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 15:46:09] "GET /api/tif/info?path=D:/Drone_Project/geoserverapi/data/20250701/nanning.tif HTTP/1.1" 200 -
2025-07-15 15:46:09,268 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 15:46:09] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-15 16:09:05,220 - tif_api - WARNING - 请求使用GPU但GPU不可用，将使用CPU模式
2025-07-15 16:09:05,225 - tif_api - INFO - 处理请求 - 输入: D:\Drone_Project\geoserverapi\data\20250701\nanning.tif, 输出TIF: D:\Drone_Project\geoserverapi\data\20250701\nanning_full.tif, 输出SHP: D:\Drone_Project\geoserverapi\data\20250701\nanning_full.shp
2025-07-15 16:09:05,228 - tif_api - INFO - 参数: 黑色阈值=10, 白色阈值=245, NoData值=-9999.0
2025-07-15 16:09:05,235 - tif_api - INFO - 简化参数=0.1, 处理模式=full, 保留Alpha=False
2025-07-15 16:09:05,243 - tif_api - INFO - 保护内部区域=True, 使用GPU=False, GPU数量=0
2025-07-15 16:09:06,628 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 16:09:06] "GET /api/tif/process?input_tif=D:/Drone_Project/geoserverapi/data/20250701/nanning.tif&output_tif=D:/Drone_Project/geoserverapi/data/20250701/nanning_full.tif&output_shp=D:/Drone_Project/geoserverapi/data/20250701/nanning_full.shp&process_mode=full&black_threshold=10&white_threshold=245&protect_interior=true&use_gpu=true HTTP/1.1" 200 -
2025-07-15 16:34:18,031 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 16:34:18] "[31m[1mGET /api/tif/process?input_tif=D:/Drone_Project/geoserverapi/data/20250701/nanning.tif&process_mode=full&black_threshold=10&white_threshold=245&protect_interior=true&use_gpu=true HTTP/1.1[0m" 400 -
