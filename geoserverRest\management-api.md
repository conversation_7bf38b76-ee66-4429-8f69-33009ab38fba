# GeoServer管理API使用说明

本文档介绍GeoServer管理API的使用方法，包括工作区管理、图层发布和管理等功能。

## API端点

### 1. 创建工作区

```
GET /api/management/workspaces/create?name=工作区名称
```

在GeoServer中创建新的工作区。

**必选参数:**
- name: 工作区名称

**示例:**
```
GET /api/management/workspaces/create?name=test
```

**响应:**
```json
{
  "status": "success",
  "message": "工作区 test 创建成功"
}
```

### 2. 删除工作区

```
GET /api/management/workspaces/delete?name=工作区名称
```

从GeoServer中删除指定的工作区。

**必选参数:**
- name: 工作区名称

**示例:**
```
GET /api/management/workspaces/delete?name=test
```

**响应:**
```json
{
  "status": "success",
  "message": "工作区 test 删除成功"
}
```

### 3. 发布Shapefile

```
GET /api/management/shapefiles/publish?file=Shapefile文件路径&workspace=工作区名称&store=存储名称&layer=图层名称&charset=UTF-8
```

将Shapefile文件发布到GeoServer。

**必选参数:**
- file: Shapefile文件路径

**可选参数:**
- workspace: 工作区名称 (如不存在则自动创建)
- store: 存储名称 (如不提供则使用文件名)
- layer: 图层名称 (如不提供则使用文件名)
- charset: 字符集 (默认: UTF-8)

**示例:**
```
GET /api/management/shapefiles/publish?file=D:/data/boundary.shp&workspace=test&store=shapefiles&layer=boundary&charset=UTF-8
```

**响应:**
```json
{
  "status": "success",
  "message": "Shapefile 发布成功",
  "details": {
    "workspace": "test",
    "store": "shapefiles",
    "layer": "boundary"
  }
}
```

### 4. 发布目录中所有Shapefile

```
GET /api/management/shapefile-directories/publish?directory=Shapefile目录路径&workspace=工作区名称&store=存储名称&charset=UTF-8
```

将目录中所有Shapefile文件发布到GeoServer。

**必选参数:**
- directory: Shapefile目录路径

**可选参数:**
- workspace: 工作区名称 (如不存在则自动创建)
- store: 存储名称前缀 (如不提供则使用目录名)
- charset: 字符集 (默认: UTF-8)

**示例:**
```
GET /api/management/shapefile-directories/publish?directory=D:/data/shapefiles&workspace=test&store=shp_collection&charset=UTF-8
```

**响应:**
```json
{
  "status": "success",
  "message": "Shapefile目录发布成功",
  "details": {
    "workspace": "test",
    "total_files": 5,
    "published": 5,
    "failed": 0,
    "layers": ["boundary1", "boundary2", "roads", "buildings", "rivers"]
  }
}
```

### 5. 发布GeoTIFF

```
GET /api/management/geotiffs/publish?file=GeoTIFF文件路径&workspace=工作区名称&store=存储名称&layer=图层名称
```

将GeoTIFF文件发布到GeoServer。

**必选参数:**
- file: GeoTIFF文件路径

**可选参数:**
- workspace: 工作区名称 (如不存在则自动创建)
- store: 存储名称 (如不提供则使用文件名)
- layer: 图层名称 (如不提供则使用存储名称)

**示例:**
```
GET /api/management/geotiffs/publish?file=D:/data/orthophoto.tif&workspace=test&store=geotiffs&layer=ortho
```

**响应:**
```json
{
  "status": "success",
  "message": "GeoTIFF 发布成功",
  "details": {
    "workspace": "test",
    "store": "geotiffs",
    "layer": "ortho"
  }
}
```

### 6. 发布目录中所有GeoTIFF

```
GET /api/management/geotiff-directories/publish?directory=GeoTIFF目录路径&workspace=工作区名称&store=存储名称
```

将目录中所有GeoTIFF文件发布到GeoServer。

**必选参数:**
- directory: GeoTIFF目录路径

**可选参数:**
- workspace: 工作区名称 (如不存在则自动创建)
- store: 存储名称前缀 (如不提供则使用目录名)

**示例:**
```
GET /api/management/geotiff-directories/publish?directory=D:/data/geotiffs&workspace=test&store=tif_collection
```

**响应:**
```json
{
  "status": "success",
  "message": "GeoTIFF目录发布成功",
  "details": {
    "workspace": "test",
    "total_files": 3,
    "published": 3,
    "failed": 0,
    "layers": ["ortho1", "ortho2", "ortho3"]
  }
}
```

### 7. 发布PostGIS表

```
GET /api/management/postgis-tables/publish?table=表名称&store=数据存储名称&workspace=工作区名称&layer=图层名称&geometry_column=geom&srid=4326
```

将PostGIS数据库表发布为GeoServer图层。

**必选参数:**
- table: 表名称
- store: 数据存储名称
- workspace: 工作区名称

**可选参数:**
- layer: 图层名称 (如不提供则使用表名)
- geometry_column: 几何字段名 (默认: geom)
- srid: 空间参考ID (默认: 4326)

**示例:**
```
GET /api/management/postgis-tables/publish?table=cities&store=postgis_store&workspace=test&layer=cities_layer&geometry_column=geom&srid=4326
```

**响应:**
```json
{
  "status": "success",
  "message": "PostGIS表发布成功",
  "details": {
    "workspace": "test",
    "store": "postgis_store",
    "layer": "cities_layer",
    "table": "cities"
  }
}
```

### 8. 创建PostGIS数据存储

```
GET /api/management/datastores/create?name=数据存储名称&workspace=工作区名称&host=localhost&port=5432&database=数据库名称&username=用户名&password=密码
```

在GeoServer中创建PostGIS数据存储。

**必选参数:**
- name: 数据存储名称
- workspace: 工作区名称
- database: 数据库名称
- username: 用户名
- password: 密码

**可选参数:**
- host: 数据库主机地址 (默认: localhost)
- port: 数据库端口 (默认: 5432)

**示例:**
```
GET /api/management/datastores/create?name=postgis_store&workspace=test&host=localhost&port=5432&database=gisdb&username=postgres&password=postgres
```

**响应:**
```json
{
  "status": "success",
  "message": "数据存储 postgis_store 在工作区 test 中创建成功"
}
```

### 9. 设置图层样式

```
GET /api/management/layers/style?workspace=工作区&layer=图层&style=样式名称
```

为图层设置样式。

**必选参数:**
- workspace: 工作区
- layer: 图层
- style: 样式名称

**示例:**
```
GET /api/management/layers/style?workspace=test&layer=ortho1&style=raster_style
```

**响应:**
```json
{
  "status": "success",
  "message": "图层样式设置成功",
  "details": {
    "workspace": "test",
    "layer": "ortho1",
    "style": "raster_style"
  }
}
```

### 10. 删除图层

```
GET /api/management/layers/delete?workspace=工作区&layer=图层
```

删除指定的图层。

**必选参数:**
- workspace: 工作区
- layer: 图层

**示例:**
```
GET /api/management/layers/delete?workspace=test&layer=ortho1
```

**响应:**
```json
{
  "status": "success",
  "message": "图层 test:ortho1 删除成功"
}
```

### 11. 批量处理操作

```
GET /api/management/batch?file=批处理文件路径
```

执行批处理文件中定义的GeoServer操作。

**必选参数:**
- file: 批处理文件路径

**示例:**
```
GET /api/management/batch?file=D:/geoserver_batch.json
```

**响应:**
```json
{
  "status": "success",
  "message": "批处理文件执行成功",
  "details": {
    "total_operations": 5,
    "successful": 5,
    "failed": 0
  }
}
```

### 12. 诊断GeoServer

```
GET /api/management/diagnose?workspace=工作区名称&detail=true
```

诊断GeoServer连接和图层状态。

**可选参数:**
- workspace: 工作区名称
- detail: 是否返回详细信息 (true/false)

**示例:**
```
GET /api/management/diagnose?workspace=test&detail=true
```

**响应:**
```json
{
  "status": "success",
  "geoserver": {
    "url": "http://localhost:8080/geoserver",
    "version": "2.19.0",
    "connected": true
  },
  "workspace": {
    "name": "test",
    "exists": true,
    "datastores": 2,
    "layers": 8
  },
  "layers": [
    {
      "name": "ortho1",
      "store": "geotiffs",
      "type": "raster",
      "status": "available"
    },
    {
      "name": "boundary1",
      "store": "shapefiles",
      "type": "vector",
      "status": "available"
    }
  ]
}
```

### 13. 获取图层边界框信息

```
GET /api/management/layers/bbox?workspace=工作区&layer=图层名称
```

获取图层的边界框（Bounding Box）信息。

**必选参数:**
- workspace: 工作区
- layer: 图层名称

**示例:**
```
GET /api/management/layers/bbox?workspace=test&layer=ortho1
```

**响应:**
```json
{
  "status": "success",
  "layer": "ortho1",
  "workspace": "test",
  "bbox": {
    "minx": 114.123,
    "miny": 30.123,
    "maxx": 114.456,
    "maxy": 30.456,
    "crs": "EPSG:4326"
  }
}
``` 