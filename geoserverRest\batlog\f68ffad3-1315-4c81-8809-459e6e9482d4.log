执行命令: D:\Drone_Project\ODM\ODM\run.bat D:\Drone_Project\dataset\DJITESTIMAGE\20250714140500\project --fast-orthophoto
工作目录: D:\Drone_Project\ODM\ODM
开始时间: 2025-07-14 11:20:04

检测到ODM项目，将激活虚拟环境: D:\Drone_Project\ODM\ODM\venv
创建临时批处理文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\temp_f68ffad3-1315-4c81-8809-459e6e9482d4.bat
内容:
@echo off
call D:\Drone_Project\ODM\ODM\venv\Scripts\activate.bat
D:\Drone_Project\ODM\ODM\run.bat D:\Drone_Project\dataset\DJITESTIMAGE\20250714140500\project --fast-orthophoto

Traceback (most recent call last):
  File "D:\Drone_Project\ODM\ODM\\run.py", line 18, in <module>
    from stages.odm_app import ODMApp
  File "D:\Drone_Project\ODM\ODM\stages\odm_app.py", line 15, in <module>
    from stages.odm_orthophoto import ODMOrthoPhotoStage
  File "D:\Drone_Project\ODM\ODM\stages\odm_orthophoto.py", line 12, in <module>
    from opendm.cutline import compute_cutline
  File "D:\Drone_Project\ODM\ODM\opendm\cutline.py", line 18, in <module>
    from shapely.geometry import LineString, mapping, shape
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\shapely\geometry\__init__.py", line 4, in <module>
    from .base import CAP_STYLE, JOIN_STYLE
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\shapely\geometry\base.py", line 19, in <module>
    from shapely.coords import CoordinateSequence
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\shapely\coords.py", line 8, in <module>
    from shapely.geos import lgeos
  File "D:\Drone_Project\ODM\ODM\venv\lib\site-packages\shapely\geos.py", line 154, in <module>
    _lgeos = CDLL(os.path.join(sys.prefix, 'Library', 'bin', 'geos_c.dll'))
  File "ctypes\__init__.py", line 373, in __init__
FileNotFoundError: Could not find module 'D:\Drone_Project\ODM\ODM\venv\Library\bin\geos_c.dll'. Try using the full path with constructor syntax.
已删除临时批处理文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\temp_f68ffad3-1315-4c81-8809-459e6e9482d4.bat

完成时间: 2025-07-14 11:20:09
返回代码: 1
状态: 运行失败
