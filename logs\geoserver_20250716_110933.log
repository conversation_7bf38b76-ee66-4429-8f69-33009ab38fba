2025-07-16 11:09:33,892 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250716_110933.log
2025-07-16 11:09:33,892 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 11:09:33,893 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 11:09:33,998 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 11:09:34,005 - batch_executor - INFO - 加载了 35 个任务状态
2025-07-16 11:09:38,121 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 11:09:38,121 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 11:09:38,143 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 11:09:38,151 - root - INFO - === GeoServer REST API服务 ===
2025-07-16 11:09:38,151 - root - INFO - 主机: 0.0.0.0
2025-07-16 11:09:38,152 - root - INFO - 端口: 5083
2025-07-16 11:09:38,152 - root - INFO - 调试模式: 禁用
2025-07-16 11:09:38,153 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-16 11:09:38,170 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-16 11:09:38,171 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-16 11:24:47,092 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 11:24:47] "[33mGET /api/tif/process/async?input_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif&output_shp=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 404 -
