"""
GeoServer API Django视图模块
"""

# 导入所有视图函数
from .base_views import health_check
from .raster_views import (
    query_coordinate, get_layers, test_coordinate, 
    get_pixel_value, query_layers_at_coordinate, get_all_layers
)
from .management_views import (
    get_workspaces, create_workspace, delete_workspace,
    get_management_layers, get_datastores, create_datastore,
    publish_shapefile, publish_shapefile_directory,
    publish_geotiff, publish_geotiff_directory,
    publish_postgis, delete_layer, set_layer_style,
    execute_batch_file, diagnose_geoserver
)
from .batch_views import (
    execute_batch, get_batch_status, get_batch_log, get_all_batch_status
)
from .tif_views import (
    process_tif, execute_tif_process, get_tif_status, 
    get_tif_log, get_all_tif_status
)
from .geo_views import (
    execute_publish_shapefile, execute_publish_geotiff,
    execute_publish_shapefile_directory, execute_publish_geotiff_directory,
    execute_publish_structured_geotiff,
    get_geo_status, get_geo_log, get_all_geo_status
)
from .map_views import (
    get_base_map, get_base_map2, get_base_map3,
    get_base_style, get_base_style2, get_base_style3,
    get_map_file, get_map_logs, list_map_files,
    get_odm_tasks
)

__all__ = [
    # 基础视图
    'health_check',
    
    # 栅格查询视图
    'query_coordinate', 'get_layers', 'test_coordinate', 
    'get_pixel_value', 'query_layers_at_coordinate', 'get_all_layers',
    
    # 管理视图
    'get_workspaces', 'create_workspace', 'delete_workspace',
    'get_management_layers', 'get_datastores', 'create_datastore',
    'publish_shapefile', 'publish_shapefile_directory',
    'publish_geotiff', 'publish_geotiff_directory',
    'publish_postgis', 'delete_layer', 'set_layer_style',
    'execute_batch_file', 'diagnose_geoserver',
    
    # 批处理视图
    'execute_batch', 'get_batch_status', 'get_batch_log', 'get_all_batch_status',
    
    # TIF处理视图
    'process_tif', 'execute_tif_process', 'get_tif_status', 
    'get_tif_log', 'get_all_tif_status',
    
    # GeoServer发布视图
    'execute_publish_shapefile', 'execute_publish_geotiff',
    'execute_publish_shapefile_directory', 'execute_publish_geotiff_directory',
    'execute_publish_structured_geotiff',
    'get_geo_status', 'get_geo_log', 'get_all_geo_status',
    
    # 地图视图
    'get_base_map', 'get_base_map2', 'get_base_map3',
    'get_base_style', 'get_base_style2', 'get_base_style3',
    'get_map_file', 'get_map_logs', 'list_map_files',
    'get_odm_tasks',
]
