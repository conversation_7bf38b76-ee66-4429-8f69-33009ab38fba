2025-07-28 09:53:34,858 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250728_095334.log
2025-07-28 09:53:34,885 - geo_publisher - INFO - 加载了 25 个任务状态
2025-07-28 09:53:35,010 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-28 09:53:35,011 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-28 09:53:35,037 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-28 09:53:35,045 - root - INFO - === GeoServer REST API服务 ===
2025-07-28 09:53:35,046 - root - INFO - 主机: 0.0.0.0
2025-07-28 09:53:35,046 - root - INFO - 端口: 5083
2025-07-28 09:53:35,048 - root - INFO - 调试模式: 禁用
2025-07-28 09:53:35,049 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-28 09:53:35,114 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-28 09:53:35,115 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-28 09:53:40,476 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=3fb3bacd-fccd-47a5-90b0-0a8231300ecb
2025-07-28 09:53:40,482 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\3fb3bacd-fccd-47a5-90b0-0a8231300ecb.log
2025-07-28 09:53:41,859 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\tiflog\3fb3bacd-fccd-47a5-90b0-0a8231300ecb.log, 内容大小: 9596 字节
2025-07-28 09:53:41,864 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:53:41] "GET /api/map/logs?log_type=tiflog&log_id=3fb3bacd-fccd-47a5-90b0-0a8231300ecb HTTP/1.1" 200 -
2025-07-28 09:53:44,201 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:53:44,205 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:53:44,217 - map_api - INFO - 找到 3 个目录
2025-07-28 09:53:44,219 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:53:44,473 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:53:44,475 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:53:44,493 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:53:44,497 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:53:44,524 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:53:44,529 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:53:44,530 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:53:44,534 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:53:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:53:57,878 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-28 09:53:57,884 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:53:57,887 - tif_api - INFO - 输入文件大小: 237.55 MB
2025-07-28 09:53:57,888 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 09:53:57,890 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:53:57,892 - tif_api - INFO - 黑色阈值: 0
2025-07-28 09:53:57,893 - tif_api - INFO - 白色阈值: 255
2025-07-28 09:53:57,895 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-28 09:53:57,912 - tif_executor - INFO - 启动TIF处理任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:53:57,914 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - ============ TIF处理任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 开始执行 ============
2025-07-28 09:53:57,914 - tif_api - INFO - 异步任务启动成功，任务ID: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 09:53:57,918 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 开始时间: 2025-07-28 09:53:57
2025-07-28 09:53:57,920 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:53:57] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-28 09:53:57,921 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 09:53:57,924 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 系统信息:
2025-07-28 09:53:57,925 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 09:53:57,928 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO -   Python版本: 3.8.20
2025-07-28 09:53:57,941 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO -   GDAL版本: 3.9.2
2025-07-28 09:53:57,949 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO -   GPU可用: 否
2025-07-28 09:53:57,951 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:53:57,952 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 检查参数有效性...
2025-07-28 09:53:57,952 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 09:53:57,954 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:53:57,956 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 09:53:57,957 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 开始执行TIF处理流程...
2025-07-28 09:53:57,958 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:53:57] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 09:53:57,959 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:53:57,967 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 09:53:57,973 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:53:57,980 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-28 09:54:06,348 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (09:54:06)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 09:54:06,382 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 09:54:06,517 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
处理数据块:  14%|##########1                                                            | 3/21 [00:00<00:00, 22.30it/s]
2025-07-28 09:54:06,769 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 20.55it/s]
2025-07-28 09:54:06,873 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
处理数据块:  52%|####################################6                                 | 11/21 [00:00<00:00, 23.19it/s]
2025-07-28 09:54:07,146 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 18.60it/s]
2025-07-28 09:54:07,264 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
处理数据块:  95%|##################################################################6   | 20/21 [00:00<00:00, 24.40it/s]
2025-07-28 09:54:07,265 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 23.80it/s]
2025-07-28 09:54:07,271 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 09:54:07,273 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 09:54:07,328 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 393.39it/s]
2025-07-28 09:54:08,682 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.55 秒 (09:54:08)预计总处理时间: 约 7.64 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (09:54:08)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 09:54:08,684 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 09:54:59,695 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:54:59,755 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 09:54:59,768 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 09:54:59,771 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:54:59] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 09:55:05,544 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 09:55:05,546 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:55:05,553 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:55:06,040 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:09,  2.06it/s]
2025-07-28 09:55:06,041 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:55:06,549 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:09,  2.00it/s]
2025-07-28 09:55:06,550 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:55:08,044 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:02<00:17,  1.05it/s]
2025-07-28 09:55:08,045 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:55:09,330 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:03<00:18,  1.08s/it]
2025-07-28 09:55:09,330 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:55:10,529 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:04<00:18,  1.13s/it]
2025-07-28 09:55:10,530 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:55:10,790 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:05<00:12,  1.20it/s]
2025-07-28 09:55:10,792 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:55:10,903 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:05<00:02,  3.87it/s]
2025-07-28 09:55:10,904 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:55:11,012 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:05<00:00,  6.45it/s]
2025-07-28 09:55:11,014 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:55:11,123 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:05<00:00, 10.41it/s]
2025-07-28 09:55:11,125 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:55:11,167 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:55:27,245 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:55:27,264 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:55:27,297 - map_api - INFO - 找到 3 个目录
2025-07-28 09:55:27,304 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:55:27,702 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:55:27,706 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:55:27,822 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:55:27,826 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:55:27,833 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 09:55:27,841 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:55:27,844 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:55:27,847 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:55:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:55:54,431 - map_api - INFO - 请求获取日志: 类型=batlog, ID=3b3c5ba9-4cb7-4e16-94ac-161cbe2f9c7a
2025-07-28 09:55:54,435 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\3b3c5ba9-4cb7-4e16-94ac-161cbe2f9c7a.log
2025-07-28 09:55:54,438 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\batlog\3b3c5ba9-4cb7-4e16-94ac-161cbe2f9c7a.log, 内容大小: 11524 字节
2025-07-28 09:55:54,445 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:55:54] "GET /api/map/logs?log_type=batlog&log_id=3b3c5ba9-4cb7-4e16-94ac-161cbe2f9c7a HTTP/1.1" 200 -
2025-07-28 09:56:02,122 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:56:02,127 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 09:56:02,130 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 09:56:02,132 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:56:02] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 09:56:16,988 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
处理波段:  33%|########################3                                                | 1/3 [02:08<04:16, 128.30s/it]
2025-07-28 09:56:27,529 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:56:27,538 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:56:27,578 - map_api - INFO - 找到 3 个目录
2025-07-28 09:56:27,582 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:56:27,590 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:56:27,595 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:56:27,604 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:56:27,605 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:56:27,618 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 09:56:27,619 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:56:27,625 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:56:27,646 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:56:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:57:04,017 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:57:04,048 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 09:57:04,086 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 09:57:04,126 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:57:04] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 09:57:11,931 - map_api - INFO - 请求获取日志: 类型=geolog, ID=f104757d-4597-4d56-98a5-c7a0906b3e8d
2025-07-28 09:57:11,940 - map_api - INFO - 读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\f104757d-4597-4d56-98a5-c7a0906b3e8d.log
2025-07-28 09:57:11,957 - map_api - INFO - 成功读取日志文件: D:\Drone_Project\geoserverapi\geoserverRest\geolog\f104757d-4597-4d56-98a5-c7a0906b3e8d.log, 内容大小: 1739 字节
2025-07-28 09:57:11,969 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:57:11] "GET /api/map/logs?log_type=geolog&log_id=f104757d-4597-4d56-98a5-c7a0906b3e8d HTTP/1.1" 200 -
2025-07-28 09:57:27,224 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:57:27,228 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:57:27,321 - map_api - INFO - 找到 3 个目录
2025-07-28 09:57:27,325 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:57:27,530 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:57:27,534 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:57:27,552 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:57:27,554 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:57:27,567 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 09:57:27,569 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:57:27,570 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:57:27,590 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:57:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:57:35,635 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 09:57:35,639 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:57:35,640 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:57:39,441 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:03<01:16,  3.80s/it]
2025-07-28 09:57:39,442 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:57:41,696 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:06<00:54,  2.89s/it]
2025-07-28 09:57:41,698 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:57:46,447 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:10<01:07,  3.74s/it]
2025-07-28 09:57:46,447 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:57:48,505 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:12<00:52,  3.08s/it]
2025-07-28 09:57:48,505 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:57:51,406 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:57:51,414 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:57:51,431 - map_api - INFO - 找到 3 个目录
2025-07-28 09:57:51,433 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:57:51,442 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:57:51,444 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:57:51,457 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:57:51,459 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:57:51,467 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 09:57:51,468 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:57:51,469 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:57:51,471 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:57:51] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:57:52,490 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:16<00:54,  3.40s/it]
2025-07-28 09:57:52,495 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:57:52,915 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:57:52,920 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:57:52,935 - map_api - INFO - 找到 3 个目录
2025-07-28 09:57:52,939 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:57:52,955 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:57:52,960 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:57:52,968 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:57:52,970 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:57:52,984 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 09:57:52,987 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:57:52,991 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:57:52,995 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:57:52] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:57:55,410 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:19<00:48,  3.24s/it]
2025-07-28 09:57:55,411 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:57:57,580 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:21<00:40,  2.89s/it]
2025-07-28 09:57:57,580 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:57:59,390 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:23<00:33,  2.55s/it]
2025-07-28 09:57:59,390 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:58:00,849 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:25<00:26,  2.21s/it]
2025-07-28 09:58:00,849 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:58:05,474 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:58:05,478 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 09:58:05,757 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:30<00:33,  3.04s/it]
2025-07-28 09:58:05,732 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 09:58:05,780 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:58:05,789 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:58:05] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 09:58:07,949 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:32<00:27,  2.78s/it]
2025-07-28 09:58:07,950 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:58:08,674 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:33<00:19,  2.16s/it]
2025-07-28 09:58:08,675 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:58:11,277 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:35<00:18,  2.29s/it]
2025-07-28 09:58:11,280 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:58:15,742 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:40<00:20,  2.95s/it]
2025-07-28 09:58:15,744 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:58:22,114 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:46<00:23,  3.98s/it]
2025-07-28 09:58:22,115 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 09:58:44,212 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:58:44,848 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:58:44,864 - map_api - INFO - 找到 3 个目录
2025-07-28 09:58:44,866 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:58:44,874 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:58:44,876 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:58:44,882 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:58:44,884 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:58:44,898 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 09:58:44,900 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:58:44,903 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:58:44,911 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:58:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:59:06,339 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:59:06,349 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 09:59:06,608 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 09:59:06,616 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:59:06] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 09:59:21,587 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [01:45<01:43, 20.68s/it]
2025-07-28 09:59:21,589 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:00:07,269 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:00:07,276 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 10:00:08,587 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 10:00:08,591 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:00:08] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 10:00:27,222 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:00:27,225 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:00:27,237 - map_api - INFO - 找到 3 个目录
2025-07-28 10:00:27,238 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:00:27,245 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:00:27,247 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:00:27,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:00:27,255 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:00:27,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:00:27,264 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:00:27,265 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:00:27,273 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:00:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:00:30,698 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [02:55<02:20, 35.25s/it]
2025-07-28 10:00:30,698 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:01:09,115 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:01:09,119 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 10:01:09,386 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 10:01:09,391 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:01:09] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 10:01:19,665 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [03:44<01:58, 39.37s/it]
2025-07-28 10:01:19,666 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:01:27,207 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:01:27,211 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:01:27,221 - map_api - INFO - 找到 3 个目录
2025-07-28 10:01:27,222 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:01:27,229 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:01:27,233 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:01:27,247 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:01:27,249 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:01:27,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:01:27,256 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:01:27,261 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:01:27,265 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:01:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:01:49,619 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [04:13<01:13, 36.54s/it]
2025-07-28 10:01:49,619 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:02:09,688 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:02:09,691 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 10:02:09,853 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 10:02:09,859 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:02:09] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 10:02:15,851 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [04:40<00:33, 33.45s/it]
2025-07-28 10:02:15,852 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:02:17,175 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [04:41<00:00, 23.80s/it]
2025-07-28 10:02:17,176 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:02:17,182 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:02:27,204 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:02:27,209 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:02:27,248 - map_api - INFO - 找到 3 个目录
2025-07-28 10:02:27,256 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:02:27,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:02:27,267 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:02:27,284 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:02:27,289 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:02:27,303 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:02:27,306 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:02:27,309 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:02:27,314 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:02:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:02:44,991 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:02:45,001 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:02:45,028 - map_api - INFO - 找到 3 个目录
2025-07-28 10:02:45,033 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:02:45,046 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:02:45,067 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:02:45,085 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:02:45,094 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:02:45,105 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:02:45,111 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:02:45,115 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:02:45,133 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:02:45] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:02:52,802 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:02:52,810 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:02:52,821 - map_api - INFO - 找到 3 个目录
2025-07-28 10:02:52,830 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:02:52,838 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:02:52,841 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:02:52,846 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:02:52,847 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:02:52,853 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:02:52,859 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:02:52,861 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:02:52,864 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:02:52] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:03:10,144 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:03:10,148 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 10:03:10,201 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 10:03:10,209 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:03:10] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 10:03:43,707 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:03:43,721 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:03:43,740 - map_api - INFO - 找到 3 个目录
2025-07-28 10:03:43,742 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:03:43,756 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:03:43,772 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:03:43,784 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:03:43,791 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:03:43,798 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:03:43,800 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:03:43,801 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:03:43,805 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:03:43] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:04:10,433 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:04:10,467 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 10:04:10,517 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 10:04:10,542 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:04:10] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 10:04:44,219 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:04:44,233 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:04:44,286 - map_api - INFO - 找到 3 个目录
2025-07-28 10:04:44,295 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:04:44,327 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:04:44,441 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:04:44,456 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:04:44,470 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:04:44,522 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:04:44,535 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:04:44,542 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:04:44,548 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:04:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:05:10,987 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:05:10,994 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 10:05:11,264 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 10:05:11,270 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:05:11] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 10:05:50,700 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:05:50,707 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:05:50,719 - map_api - INFO - 找到 3 个目录
2025-07-28 10:05:50,720 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:05:50,743 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:05:50,746 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:05:50,752 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:05:50,754 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:05:50,758 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:05:50,767 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:05:50,768 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:05:50,771 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:05:50] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:06:11,977 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:06:11,986 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 10:06:12,099 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 10:06:12,105 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:06:12] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 10:06:44,200 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:06:44,211 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:06:44,233 - map_api - INFO - 找到 3 个目录
2025-07-28 10:06:44,236 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:06:44,257 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:06:44,259 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:06:44,265 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:06:44,267 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:06:44,274 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:06:44,279 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:06:44,283 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:06:44,285 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:06:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:07:11,678 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:07:11,683 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 10:07:11,690 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 10:07:11,692 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:07:11] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 10:07:44,208 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:07:44,213 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:07:44,223 - map_api - INFO - 找到 3 个目录
2025-07-28 10:07:44,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:07:44,232 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:07:44,236 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:07:44,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:07:44,252 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:07:44,257 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:07:44,263 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:07:44,267 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:07:44,269 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:07:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:08:03,170 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [13:54<07:48, 468.23s/it]
2025-07-28 10:08:13,025 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:08:13,041 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 10:08:13,058 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 10:08:13,060 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:08:13] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 10:08:44,079 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:08:44,111 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:08:44,245 - map_api - INFO - 找到 3 个目录
2025-07-28 10:08:44,250 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:08:44,689 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:08:44,967 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:08:45,304 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:08:45,321 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:08:45,332 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:08:45,336 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:08:45,344 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:08:45,382 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:08:45] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:09:00,284 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 10:09:00,308 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 10:09:00,309 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:09:02,382 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:02<00:41,  2.07s/it]
2025-07-28 10:09:02,383 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:09:03,876 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:03<00:32,  1.73s/it]
2025-07-28 10:09:03,876 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:09:05,082 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:04<00:26,  1.49s/it]
2025-07-28 10:09:05,083 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:09:05,855 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:05<00:20,  1.21s/it]
2025-07-28 10:09:05,855 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:09:06,962 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:06<00:18,  1.17s/it]
2025-07-28 10:09:06,963 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:09:07,107 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:06<00:06,  2.04it/s]
2025-07-28 10:09:07,143 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:09:07,244 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:06<00:04,  2.44it/s]
2025-07-28 10:09:07,245 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:09:07,357 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:07<00:02,  3.67it/s]
2025-07-28 10:09:07,358 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:09:07,479 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:07<00:01,  6.00it/s]
2025-07-28 10:09:07,479 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:09:07,620 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [00:07<00:00,  8.36it/s]
2025-07-28 10:09:07,620 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:09:07,766 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [00:07<00:00, 10.63it/s]
2025-07-28 10:09:07,766 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:09:07,842 - tif_task_2b14ab9b-9525-4978-8fb7-2f38496bdfa8 - ERROR - [A
2025-07-28 10:09:14,289 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:09:14,296 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 10:09:27,343 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 10:09:27,362 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:09:27] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 10:09:44,209 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:09:44,213 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:09:44,283 - map_api - INFO - 找到 3 个目录
2025-07-28 10:09:44,287 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:09:44,298 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:09:44,300 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:09:44,307 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:09:44,314 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:09:44,326 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:09:44,330 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:09:44,335 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:09:44,342 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:09:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:10:29,438 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:10:29,441 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 10:10:31,510 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 10:10:31,518 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:10:31] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 10:11:12,312 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:11:12,328 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:11:12,401 - map_api - INFO - 找到 3 个目录
2025-07-28 10:11:12,408 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:11:12,426 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:11:12,434 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:11:12,445 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:11:12,452 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:11:12,471 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:11:12,477 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:11:12,481 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:11:12,485 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:11:12] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:11:14,180 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:11:14,187 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:11:14,222 - map_api - INFO - 找到 3 个目录
2025-07-28 10:11:14,227 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:11:14,236 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:11:14,237 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:11:14,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:11:14,246 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:11:14,261 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:11:14,264 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:11:14,266 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:11:14,269 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:11:14] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:11:32,753 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 10:11:32,757 - tif_api - INFO - 查询任务: 2b14ab9b-9525-4978-8fb7-2f38496bdfa8
2025-07-28 10:11:32,760 - tif_api - INFO - 任务 2b14ab9b-9525-4978-8fb7-2f38496bdfa8 状态查询成功，当前状态: 正在运行
2025-07-28 10:11:32,763 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 10:11:32] "GET /api/tif/status?task_id=2b14ab9b-9525-4978-8fb7-2f38496bdfa8 HTTP/1.1" 200 -
2025-07-28 10:11:35,089 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:11:35,093 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:11:35,105 - map_api - INFO - 找到 3 个目录
2025-07-28 10:11:35,106 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:11:35,119 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:11:35,122 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:11:35,130 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:11:35,132 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:11:35,136 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:11:35,138 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:11:35,144 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:11:35,147 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:11:35] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 10:11:43,695 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 10:11:43,700 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 10:11:43,719 - map_api - INFO - 找到 3 个目录
2025-07-28 10:11:43,725 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 10:11:43,733 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 10:11:43,735 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 10:11:43,770 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 10:11:43,777 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 10:11:43,784 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 10:11:43,790 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 10:11:43,791 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 10:11:43,795 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 10:11:43] "GET /api/map/odm/tasks HTTP/1.1" 200 -
