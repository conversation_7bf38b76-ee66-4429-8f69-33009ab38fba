2025-07-10 08:51:51,221 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250710_085151.log
2025-07-10 08:51:51,226 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-10 08:51:51,228 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-10 08:51:51,718 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-10 08:51:51,723 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-10 08:51:51,723 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-10 08:51:51,741 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-10 08:51:51,746 - root - INFO - === GeoServer REST API服务 ===
2025-07-10 08:51:51,747 - root - INFO - 主机: 0.0.0.0
2025-07-10 08:51:51,747 - root - INFO - 端口: 5083
2025-07-10 08:51:51,747 - root - INFO - 调试模式: 禁用
2025-07-10 08:51:51,747 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-10 08:51:51,763 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-10 08:51:51,764 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-10 08:51:55,318 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 08:51:55,450 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 08:51:55,451 - werkzeug - INFO - ************** - - [10/Jul/2025 08:51:55] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 08:52:06,564 - werkzeug - INFO - ************** - - [10/Jul/2025 08:52:06] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 08:52:06,572 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 08:52:06,644 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 08:52:06,662 - werkzeug - INFO - ************** - - [10/Jul/2025 08:52:06] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 08:52:08,327 - werkzeug - INFO - ************** - - [10/Jul/2025 08:52:08] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-10 08:52:08,353 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-10 08:52:08,408 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-10 08:52:08,411 - werkzeug - INFO - ************** - - [10/Jul/2025 08:52:08] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-10 08:52:35,684 - werkzeug - INFO - ************** - - [10/Jul/2025 08:52:35] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 08:52:35,688 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 08:52:35,720 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 08:52:35,721 - werkzeug - INFO - ************** - - [10/Jul/2025 08:52:35] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 08:52:51,465 - root - INFO - 开始查询坐标点 (22.783657454449667, 108.40018917419367) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-10 08:52:51,926 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-10 08:52:52,312 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-10 08:52:52,428 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-10 08:52:52,428 - root - INFO - 在坐标点 (22.783657454449667, 108.40018917419367) 处找到 2 个有有效数据的栅格图层
2025-07-10 08:52:52,431 - werkzeug - INFO - ************** - - [10/Jul/2025 08:52:52] "GET /api/query_values?lat=22.783657454449667&lon=108.40018917419367&workspace=test_myworkspace HTTP/1.1" 200 -
