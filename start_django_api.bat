@echo off
echo 启动GeoServer Django API服务...
echo.

REM 激活conda环境
call conda activate geoserverapi

REM 检查Django项目
echo 检查Django项目配置...
python manage.py check
if %errorlevel% neq 0 (
    echo Django项目检查失败！
    pause
    exit /b 1
)

REM 应用数据库迁移（如果需要）
echo 应用数据库迁移...
python manage.py migrate --run-syncdb

REM 启动Django开发服务器
echo 启动Django开发服务器...
echo 服务器将在 http://127.0.0.1:5000 启动
echo 按 Ctrl+C 停止服务器
echo.
python manage.py runserver 127.0.0.1:5000

pause
