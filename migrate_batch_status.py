#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
迁移批处理状态文件，将旧的英文状态转换为新的中文状态
"""

import os
import sys
import json
from pathlib import Path

# 添加Django项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')

import django
django.setup()

from geoserver_api.core.batch_executor import BatchStatus

def migrate_batch_status_file():
    """迁移批处理状态文件"""
    
    # 状态文件路径
    from django.conf import settings
    status_file = Path(settings.BASE_DIR) / 'geoserverRest' / 'batlog' / 'batch_status.json'
    
    if not status_file.exists():
        print("状态文件不存在，无需迁移")
        return
    
    print(f"正在迁移状态文件: {status_file}")
    
    try:
        # 读取旧状态
        with open(status_file, 'r', encoding='utf-8') as f:
            tasks = json.load(f)
        
        print(f"找到 {len(tasks)} 个任务")
        
        # 状态映射
        status_mapping = {
            'running': BatchStatus.RUNNING,
            'completed': BatchStatus.SUCCESS,
            'failed': BatchStatus.FAILED,
            'success': BatchStatus.SUCCESS,
        }
        
        # 字段映射
        field_mapping = {
            'id': 'task_id',
            'exit_code': 'return_code',
        }
        
        migrated_count = 0
        
        for task_id, task_info in tasks.items():
            print(f"\n处理任务: {task_id}")
            print(f"  原状态: {task_info.get('status')}")
            
            # 迁移状态值
            old_status = task_info.get('status')
            if old_status in status_mapping:
                new_status = status_mapping[old_status]
                task_info['status'] = new_status
                print(f"  新状态: {new_status}")
                migrated_count += 1
            
            # 迁移字段名
            for old_field, new_field in field_mapping.items():
                if old_field in task_info and new_field not in task_info:
                    task_info[new_field] = task_info.pop(old_field)
                    print(f"  字段迁移: {old_field} -> {new_field}")
            
            # 迁移时间格式
            for time_field in ['start_time', 'end_time']:
                if time_field in task_info and task_info[time_field]:
                    time_value = task_info[time_field]
                    if 'T' in str(time_value):  # ISO格式
                        try:
                            from datetime import datetime
                            dt = datetime.fromisoformat(time_value.replace('Z', '+00:00'))
                            new_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                            task_info[time_field] = new_time
                            print(f"  时间格式迁移: {time_field} = {new_time}")
                        except Exception as e:
                            print(f"  时间格式迁移失败: {e}")
        
        # 保存迁移后的状态
        with open(status_file, 'w', encoding='utf-8') as f:
            json.dump(tasks, f, ensure_ascii=False, indent=2)
        
        print(f"\n迁移完成！共迁移了 {migrated_count} 个任务的状态")
        
        # 显示迁移后的状态
        print("\n迁移后的任务状态:")
        for task_id, task_info in tasks.items():
            print(f"  {task_id}: {task_info.get('status')}")
        
    except Exception as e:
        print(f"迁移失败: {str(e)}")

def clear_batch_status_file():
    """清空批处理状态文件（强制重新开始）"""
    from django.conf import settings
    status_file = Path(settings.BASE_DIR) / 'geoserverRest' / 'batlog' / 'batch_status.json'
    
    if status_file.exists():
        # 备份原文件
        backup_file = status_file.with_suffix('.json.backup')
        status_file.rename(backup_file)
        print(f"已备份原状态文件到: {backup_file}")
        print("状态文件已清空，下次启动时将重新开始")
    else:
        print("状态文件不存在")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "clear":
        clear_batch_status_file()
    else:
        migrate_batch_status_file()
    
    print("\n完成！请重启Django服务器以应用更改。")
