2025-07-10 09:07:36,675 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250710_090736.log
2025-07-10 09:07:36,776 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-10 09:07:36,781 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-10 09:07:39,122 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-10 09:07:39,129 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-10 09:07:39,130 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-10 09:07:39,157 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-10 09:07:39,166 - root - INFO - === GeoServer REST API服务 ===
2025-07-10 09:07:39,166 - root - INFO - 主机: 0.0.0.0
2025-07-10 09:07:39,167 - root - INFO - 端口: 5083
2025-07-10 09:07:39,167 - root - INFO - 调试模式: 禁用
2025-07-10 09:07:39,167 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-10 09:07:39,192 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-10 09:07:39,193 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-10 09:19:07,816 - werkzeug - INFO - ************** - - [10/Jul/2025 09:19:07] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:19:07,824 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 09:19:08,093 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 09:19:08,095 - werkzeug - INFO - ************** - - [10/Jul/2025 09:19:08] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:20:36,076 - werkzeug - INFO - ************** - - [10/Jul/2025 09:20:36] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=id9 HTTP/1.1" 200 -
2025-07-10 09:20:36,083 - root - INFO - 获取图层 test_myworkspace:id9 的信息
2025-07-10 09:20:36,118 - root - INFO - 成功获取图层 test_myworkspace:id9 的边界框信息
2025-07-10 09:20:36,119 - werkzeug - INFO - ************** - - [10/Jul/2025 09:20:36] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id9 HTTP/1.1" 200 -
2025-07-10 09:22:30,269 - werkzeug - INFO - ************** - - [10/Jul/2025 09:22:30] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:22:30,276 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 09:22:30,308 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 09:22:30,309 - werkzeug - INFO - ************** - - [10/Jul/2025 09:22:30] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:30:33,043 - werkzeug - INFO - ************** - - [10/Jul/2025 09:30:33] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:30:33,048 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 09:30:33,111 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 09:30:33,113 - werkzeug - INFO - ************** - - [10/Jul/2025 09:30:33] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:32:42,073 - werkzeug - INFO - ************** - - [10/Jul/2025 09:32:42] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:32:42,081 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 09:32:42,121 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 09:32:42,123 - werkzeug - INFO - ************** - - [10/Jul/2025 09:32:42] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:41:56,568 - werkzeug - INFO - ************** - - [10/Jul/2025 09:41:56] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:41:56,575 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 09:41:56,638 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 09:41:56,640 - werkzeug - INFO - ************** - - [10/Jul/2025 09:41:56] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:52:07,566 - werkzeug - INFO - ************** - - [10/Jul/2025 09:52:07] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:52:07,572 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 09:52:07,634 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 09:52:07,635 - werkzeug - INFO - ************** - - [10/Jul/2025 09:52:07] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:58:39,892 - werkzeug - INFO - ************** - - [10/Jul/2025 09:58:39] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:58:39,899 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 09:58:39,976 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 09:58:39,977 - werkzeug - INFO - ************** - - [10/Jul/2025 09:58:39] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:59:03,963 - werkzeug - INFO - ************** - - [10/Jul/2025 09:59:03] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 09:59:03,968 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 09:59:03,999 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 09:59:04,000 - werkzeug - INFO - ************** - - [10/Jul/2025 09:59:04] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:03:06,560 - werkzeug - INFO - ************** - - [10/Jul/2025 10:03:06] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:03:06,566 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:03:06,602 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:03:06,604 - werkzeug - INFO - ************** - - [10/Jul/2025 10:03:06] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:03:54,849 - werkzeug - INFO - ************** - - [10/Jul/2025 10:03:54] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:03:54,853 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:03:54,885 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:03:54,887 - werkzeug - INFO - ************** - - [10/Jul/2025 10:03:54] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:04:26,982 - root - INFO - 开始查询坐标点 (22.910862670332932, 108.1966427938258) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-10 10:04:27,378 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-10 10:04:27,546 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-10 10:04:27,603 - root - INFO - 图层 'id1' 在该坐标点有有效数据，添加到结果中
2025-07-10 10:04:27,603 - root - INFO - 在坐标点 (22.910862670332932, 108.1966427938258) 处找到 2 个有有效数据的栅格图层
2025-07-10 10:04:27,604 - werkzeug - INFO - ************** - - [10/Jul/2025 10:04:27] "GET /api/query_values?lat=22.910862670332932&lon=108.1966427938258&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-10 10:04:35,372 - root - INFO - 开始查询坐标点 (22.69079683154584, 108.44711021573025) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-10 10:04:35,670 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-10 10:04:35,697 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-10 10:04:35,724 - root - INFO - 图层 'id9' 在该坐标点有有效数据，添加到结果中
2025-07-10 10:04:35,724 - root - INFO - 在坐标点 (22.69079683154584, 108.44711021573025) 处找到 2 个有有效数据的栅格图层
2025-07-10 10:04:35,725 - werkzeug - INFO - ************** - - [10/Jul/2025 10:04:35] "GET /api/query_values?lat=22.69079683154584&lon=108.44711021573025&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-10 10:08:03,215 - werkzeug - INFO - ************** - - [10/Jul/2025 10:08:03] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:08:03,223 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:08:03,263 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:08:03,265 - werkzeug - INFO - ************** - - [10/Jul/2025 10:08:03] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:08:27,150 - werkzeug - INFO - ************** - - [10/Jul/2025 10:08:27] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:08:27,156 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:08:27,190 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:08:27,192 - werkzeug - INFO - ************** - - [10/Jul/2025 10:08:27] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:14:16,697 - werkzeug - INFO - ************** - - [10/Jul/2025 10:14:16] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:14:16,702 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:14:16,772 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:14:16,773 - werkzeug - INFO - ************** - - [10/Jul/2025 10:14:16] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:14:55,595 - werkzeug - INFO - ************** - - [10/Jul/2025 10:14:55] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:14:55,600 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:14:55,627 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:14:55,628 - werkzeug - INFO - ************** - - [10/Jul/2025 10:14:55] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:17:31,700 - werkzeug - INFO - ************** - - [10/Jul/2025 10:17:31] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:17:31,704 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:17:31,734 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:17:31,737 - werkzeug - INFO - ************** - - [10/Jul/2025 10:17:31] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:17:41,949 - werkzeug - INFO - ************** - - [10/Jul/2025 10:17:41] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:17:41,953 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:17:41,976 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:17:41,978 - werkzeug - INFO - ************** - - [10/Jul/2025 10:17:41] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:19:36,403 - werkzeug - INFO - ************** - - [10/Jul/2025 10:19:36] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:19:36,408 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:19:36,438 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:19:36,439 - werkzeug - INFO - ************** - - [10/Jul/2025 10:19:36] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:19:38,831 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:19:38,856 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:19:38,857 - werkzeug - INFO - ************** - - [10/Jul/2025 10:19:38] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:21:15,898 - werkzeug - INFO - ************** - - [10/Jul/2025 10:21:15] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:21:15,903 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:21:15,926 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:21:15,927 - werkzeug - INFO - ************** - - [10/Jul/2025 10:21:15] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:21:20,517 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:21:20,547 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:21:20,548 - werkzeug - INFO - ************** - - [10/Jul/2025 10:21:20] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:22:52,118 - werkzeug - INFO - ************** - - [10/Jul/2025 10:22:52] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:22:52,126 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:22:52,183 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:22:52,243 - werkzeug - INFO - ************** - - [10/Jul/2025 10:22:52] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:23:23,329 - werkzeug - INFO - ************** - - [10/Jul/2025 10:23:23] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:23:23,335 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:23:23,369 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:23:23,373 - werkzeug - INFO - ************** - - [10/Jul/2025 10:23:23] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:25:17,543 - werkzeug - INFO - ************** - - [10/Jul/2025 10:25:17] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:25:17,548 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:25:17,603 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:25:17,605 - werkzeug - INFO - ************** - - [10/Jul/2025 10:25:17] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:25:37,803 - werkzeug - INFO - ************** - - [10/Jul/2025 10:25:37] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:25:37,808 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:25:37,830 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:25:37,832 - werkzeug - INFO - ************** - - [10/Jul/2025 10:25:37] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:25:40,788 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:25:40,812 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:25:40,814 - werkzeug - INFO - ************** - - [10/Jul/2025 10:25:40] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:30:08,350 - werkzeug - INFO - ************** - - [10/Jul/2025 10:30:08] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:30:08,355 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:30:08,388 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:30:08,389 - werkzeug - INFO - ************** - - [10/Jul/2025 10:30:08] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:32:47,824 - werkzeug - INFO - ************** - - [10/Jul/2025 10:32:47] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:32:47,830 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:32:47,853 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:32:47,854 - werkzeug - INFO - ************** - - [10/Jul/2025 10:32:47] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:32:55,220 - werkzeug - INFO - ************** - - [10/Jul/2025 10:32:55] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:32:55,226 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:32:55,255 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:32:55,256 - werkzeug - INFO - ************** - - [10/Jul/2025 10:32:55] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:37:17,772 - werkzeug - INFO - ************** - - [10/Jul/2025 10:37:17] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:37:17,778 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:37:17,831 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:37:17,832 - werkzeug - INFO - ************** - - [10/Jul/2025 10:37:17] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:37:25,009 - werkzeug - INFO - ************** - - [10/Jul/2025 10:37:25] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:37:25,157 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:37:25,191 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:37:25,200 - werkzeug - INFO - ************** - - [10/Jul/2025 10:37:25] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:45:57,892 - werkzeug - INFO - ************** - - [10/Jul/2025 10:45:57] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:45:57,895 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:45:57,946 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:45:57,948 - werkzeug - INFO - ************** - - [10/Jul/2025 10:45:57] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:46:02,712 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:46:02,735 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:46:02,736 - werkzeug - INFO - ************** - - [10/Jul/2025 10:46:02] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:46:14,653 - werkzeug - INFO - ************** - - [10/Jul/2025 10:46:14] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:46:14,658 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:46:14,680 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:46:14,681 - werkzeug - INFO - ************** - - [10/Jul/2025 10:46:14] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:50:16,497 - werkzeug - INFO - ************** - - [10/Jul/2025 10:50:16] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 10:50:16,503 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 10:50:16,525 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 10:50:16,526 - werkzeug - INFO - ************** - - [10/Jul/2025 10:50:16] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:03:46,163 - werkzeug - INFO - ************** - - [10/Jul/2025 11:03:46] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:03:46,168 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 11:03:46,225 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 11:03:46,227 - werkzeug - INFO - ************** - - [10/Jul/2025 11:03:46] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:03:51,649 - werkzeug - INFO - ************** - - [10/Jul/2025 11:03:51] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:03:51,653 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 11:03:51,674 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 11:03:51,675 - werkzeug - INFO - ************** - - [10/Jul/2025 11:03:51] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:04:01,240 - werkzeug - INFO - ************** - - [10/Jul/2025 11:04:01] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:04:01,247 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 11:04:01,278 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 11:04:01,280 - werkzeug - INFO - ************** - - [10/Jul/2025 11:04:01] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:04:23,839 - werkzeug - INFO - ************** - - [10/Jul/2025 11:04:23] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=id1 HTTP/1.1" 200 -
2025-07-10 11:04:23,844 - root - INFO - 获取图层 test_myworkspace:id1 的信息
2025-07-10 11:04:23,867 - root - INFO - 成功获取图层 test_myworkspace:id1 的边界框信息
2025-07-10 11:04:23,868 - werkzeug - INFO - ************** - - [10/Jul/2025 11:04:23] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id1 HTTP/1.1" 200 -
2025-07-10 11:05:43,450 - werkzeug - INFO - ************** - - [10/Jul/2025 11:05:43] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:05:43,459 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 11:05:43,495 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 11:05:43,506 - werkzeug - INFO - ************** - - [10/Jul/2025 11:05:43] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:11:48,826 - werkzeug - INFO - ************** - - [10/Jul/2025 11:11:48] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:11:48,832 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 11:11:48,914 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 11:11:48,916 - werkzeug - INFO - ************** - - [10/Jul/2025 11:11:48] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:12:04,877 - werkzeug - INFO - ************** - - [10/Jul/2025 11:12:04] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=id3 HTTP/1.1" 200 -
2025-07-10 11:12:04,882 - root - INFO - 获取图层 test_myworkspace:id3 的信息
2025-07-10 11:12:05,003 - root - INFO - 成功获取图层 test_myworkspace:id3 的边界框信息
2025-07-10 11:12:05,005 - werkzeug - INFO - ************** - - [10/Jul/2025 11:12:05] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id3 HTTP/1.1" 200 -
2025-07-10 11:12:19,973 - werkzeug - INFO - ************** - - [10/Jul/2025 11:12:19] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=id9 HTTP/1.1" 200 -
2025-07-10 11:12:19,979 - root - INFO - 获取图层 test_myworkspace:id9 的信息
2025-07-10 11:12:20,037 - root - INFO - 成功获取图层 test_myworkspace:id9 的边界框信息
2025-07-10 11:12:20,041 - werkzeug - INFO - ************** - - [10/Jul/2025 11:12:20] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id9 HTTP/1.1" 200 -
2025-07-10 11:39:08,574 - werkzeug - INFO - ************** - - [10/Jul/2025 11:39:08] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:39:08,579 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 11:39:08,632 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 11:39:08,634 - werkzeug - INFO - ************** - - [10/Jul/2025 11:39:08] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:39:25,282 - werkzeug - INFO - ************** - - [10/Jul/2025 11:39:25] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:39:25,287 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 11:39:25,319 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 11:39:25,327 - werkzeug - INFO - ************** - - [10/Jul/2025 11:39:25] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:39:36,347 - werkzeug - INFO - ************** - - [10/Jul/2025 11:39:36] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 11:39:36,365 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 11:39:36,401 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 11:39:36,415 - werkzeug - INFO - ************** - - [10/Jul/2025 11:39:36] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 14:54:04,375 - werkzeug - INFO - ************** - - [10/Jul/2025 14:54:04] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 14:54:04,413 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 14:54:04,505 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 14:54:04,508 - werkzeug - INFO - ************** - - [10/Jul/2025 14:54:04] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 14:54:21,394 - werkzeug - INFO - ************** - - [10/Jul/2025 14:54:21] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 14:54:21,399 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 14:54:21,426 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 14:54:21,428 - werkzeug - INFO - ************** - - [10/Jul/2025 14:54:21] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:01:26,714 - werkzeug - INFO - 192.168.43.161 - - [10/Jul/2025 15:01:26] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:01:26,752 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:01:26,827 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:01:26,828 - werkzeug - INFO - 192.168.43.161 - - [10/Jul/2025 15:01:26] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:01:36,542 - werkzeug - INFO - 192.168.43.161 - - [10/Jul/2025 15:01:36] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:01:36,607 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:01:36,645 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:01:36,648 - werkzeug - INFO - 192.168.43.161 - - [10/Jul/2025 15:01:36] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:02:55,757 - werkzeug - INFO - ************** - - [10/Jul/2025 15:02:55] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:02:55,764 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:02:55,793 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:02:55,794 - werkzeug - INFO - ************** - - [10/Jul/2025 15:02:55] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:03:07,213 - werkzeug - INFO - 192.168.43.161 - - [10/Jul/2025 15:03:07] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:03:07,392 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:03:07,414 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:03:07,415 - werkzeug - INFO - 192.168.43.161 - - [10/Jul/2025 15:03:07] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:11:05,086 - werkzeug - INFO - ************** - - [10/Jul/2025 15:11:05] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:11:05,091 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:11:05,172 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:11:05,174 - werkzeug - INFO - ************** - - [10/Jul/2025 15:11:05] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:21:12,908 - werkzeug - INFO - ************** - - [10/Jul/2025 15:21:12] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:21:12,912 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:21:12,969 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:21:12,971 - werkzeug - INFO - ************** - - [10/Jul/2025 15:21:12] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:21:54,709 - werkzeug - INFO - ************** - - [10/Jul/2025 15:21:54] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:21:54,713 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:21:54,735 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:21:54,737 - werkzeug - INFO - ************** - - [10/Jul/2025 15:21:54] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:22:34,983 - werkzeug - INFO - ************** - - [10/Jul/2025 15:22:34] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:22:34,990 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:22:35,023 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:22:35,025 - werkzeug - INFO - ************** - - [10/Jul/2025 15:22:35] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:22:42,690 - werkzeug - INFO - ************** - - [10/Jul/2025 15:22:42] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:22:42,695 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:22:42,732 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:22:42,734 - werkzeug - INFO - ************** - - [10/Jul/2025 15:22:42] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:31:44,639 - werkzeug - INFO - ************** - - [10/Jul/2025 15:31:44] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:31:44,675 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:31:44,778 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:31:44,779 - werkzeug - INFO - ************** - - [10/Jul/2025 15:31:44] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:37:05,427 - werkzeug - INFO - ************** - - [10/Jul/2025 15:37:05] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:37:05,434 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:37:05,511 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:37:05,512 - werkzeug - INFO - ************** - - [10/Jul/2025 15:37:05] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:37:17,848 - werkzeug - INFO - ************** - - [10/Jul/2025 15:37:17] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:37:17,853 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:37:17,886 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:37:17,888 - werkzeug - INFO - ************** - - [10/Jul/2025 15:37:17] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:37:33,275 - werkzeug - INFO - ************** - - [10/Jul/2025 15:37:33] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:37:33,281 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:37:33,316 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:37:33,317 - werkzeug - INFO - ************** - - [10/Jul/2025 15:37:33] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:37:40,374 - werkzeug - INFO - ************** - - [10/Jul/2025 15:37:40] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:37:40,383 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:37:40,415 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:37:40,416 - werkzeug - INFO - ************** - - [10/Jul/2025 15:37:40] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:38:47,238 - werkzeug - INFO - ************** - - [10/Jul/2025 15:38:47] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:38:47,244 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:38:47,274 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:38:47,275 - werkzeug - INFO - ************** - - [10/Jul/2025 15:38:47] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:39:11,061 - werkzeug - INFO - ************** - - [10/Jul/2025 15:39:11] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:39:11,067 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:39:11,101 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:39:11,103 - werkzeug - INFO - ************** - - [10/Jul/2025 15:39:11] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:39:18,035 - werkzeug - INFO - ************** - - [10/Jul/2025 15:39:18] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:39:18,039 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:39:18,061 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:39:18,062 - werkzeug - INFO - ************** - - [10/Jul/2025 15:39:18] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:39:41,057 - werkzeug - INFO - ************** - - [10/Jul/2025 15:39:41] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:39:41,061 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:39:41,092 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:39:41,093 - werkzeug - INFO - ************** - - [10/Jul/2025 15:39:41] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:39:47,753 - werkzeug - INFO - ************** - - [10/Jul/2025 15:39:47] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:39:47,764 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:39:47,793 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:39:47,795 - werkzeug - INFO - ************** - - [10/Jul/2025 15:39:47] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:41:18,440 - werkzeug - INFO - ************** - - [10/Jul/2025 15:41:18] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:41:18,445 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:41:18,480 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:41:18,482 - werkzeug - INFO - ************** - - [10/Jul/2025 15:41:18] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:47:04,537 - werkzeug - INFO - ************** - - [10/Jul/2025 15:47:04] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:47:04,550 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:47:04,680 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:47:04,684 - werkzeug - INFO - ************** - - [10/Jul/2025 15:47:04] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:47:30,285 - werkzeug - INFO - ************** - - [10/Jul/2025 15:47:30] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:47:30,299 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:47:30,350 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:47:30,354 - werkzeug - INFO - ************** - - [10/Jul/2025 15:47:30] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:48:00,804 - werkzeug - INFO - ************** - - [10/Jul/2025 15:48:00] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:48:00,816 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:48:00,856 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:48:00,859 - werkzeug - INFO - ************** - - [10/Jul/2025 15:48:00] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:49:04,969 - werkzeug - INFO - ************** - - [10/Jul/2025 15:49:04] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:49:04,999 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:49:05,061 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:49:05,066 - werkzeug - INFO - ************** - - [10/Jul/2025 15:49:05] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:49:41,394 - werkzeug - INFO - ************** - - [10/Jul/2025 15:49:41] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:49:41,401 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:49:41,441 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:49:41,443 - werkzeug - INFO - ************** - - [10/Jul/2025 15:49:41] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:49:59,778 - werkzeug - INFO - ************** - - [10/Jul/2025 15:49:59] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:49:59,809 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:49:59,869 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:49:59,888 - werkzeug - INFO - ************** - - [10/Jul/2025 15:49:59] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:51:16,232 - werkzeug - INFO - ************** - - [10/Jul/2025 15:51:16] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:51:16,248 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:51:16,295 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:51:16,302 - werkzeug - INFO - ************** - - [10/Jul/2025 15:51:16] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:51:45,837 - werkzeug - INFO - ************** - - [10/Jul/2025 15:51:45] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:51:45,850 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:51:45,907 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:51:45,909 - werkzeug - INFO - ************** - - [10/Jul/2025 15:51:45] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:52:26,365 - werkzeug - INFO - ************** - - [10/Jul/2025 15:52:26] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:52:26,381 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:52:26,431 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:52:26,433 - werkzeug - INFO - ************** - - [10/Jul/2025 15:52:26] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:52:50,284 - werkzeug - INFO - ************** - - [10/Jul/2025 15:52:50] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:52:50,300 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:52:50,360 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:52:50,380 - werkzeug - INFO - ************** - - [10/Jul/2025 15:52:50] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:53:44,018 - werkzeug - INFO - ************** - - [10/Jul/2025 15:53:44] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:53:44,030 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:53:44,070 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:53:44,072 - werkzeug - INFO - ************** - - [10/Jul/2025 15:53:44] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:54:16,704 - werkzeug - INFO - ************** - - [10/Jul/2025 15:54:16] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:54:16,719 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:54:16,758 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:54:16,760 - werkzeug - INFO - ************** - - [10/Jul/2025 15:54:16] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:54:55,961 - werkzeug - INFO - ************** - - [10/Jul/2025 15:54:55] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:54:55,972 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:54:56,008 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:54:56,010 - werkzeug - INFO - ************** - - [10/Jul/2025 15:54:56] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:55:24,901 - werkzeug - INFO - ************** - - [10/Jul/2025 15:55:24] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:55:24,913 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:55:24,959 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:55:24,963 - werkzeug - INFO - ************** - - [10/Jul/2025 15:55:24] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:56:07,492 - werkzeug - INFO - ************** - - [10/Jul/2025 15:56:07] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:56:07,505 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:56:07,550 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:56:07,552 - werkzeug - INFO - ************** - - [10/Jul/2025 15:56:07] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:56:53,976 - werkzeug - INFO - ************** - - [10/Jul/2025 15:56:53] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:56:53,989 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:56:54,034 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:56:54,036 - werkzeug - INFO - ************** - - [10/Jul/2025 15:56:54] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:57:46,129 - werkzeug - INFO - ************** - - [10/Jul/2025 15:57:46] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 15:57:46,138 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 15:57:46,247 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 15:57:46,249 - werkzeug - INFO - ************** - - [10/Jul/2025 15:57:46] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:00:22,080 - werkzeug - INFO - ************** - - [10/Jul/2025 16:00:22] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:00:22,316 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 16:00:22,742 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 16:00:22,770 - werkzeug - INFO - ************** - - [10/Jul/2025 16:00:22] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:00:58,746 - werkzeug - INFO - ************** - - [10/Jul/2025 16:00:58] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:00:59,055 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 16:00:59,157 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 16:00:59,210 - werkzeug - INFO - ************** - - [10/Jul/2025 16:00:59] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:03:22,201 - werkzeug - INFO - ************** - - [10/Jul/2025 16:03:22] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:03:22,538 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 16:03:22,722 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 16:03:22,894 - werkzeug - INFO - ************** - - [10/Jul/2025 16:03:22] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:04:05,219 - werkzeug - INFO - ************** - - [10/Jul/2025 16:04:05] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:04:05,560 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 16:04:05,656 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 16:04:05,715 - werkzeug - INFO - ************** - - [10/Jul/2025 16:04:05] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
