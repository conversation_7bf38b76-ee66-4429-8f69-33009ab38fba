2025-07-16 11:33:31,876 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250716_113331.log
2025-07-16 11:33:31,876 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 11:33:31,876 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 11:33:31,955 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 11:33:31,962 - batch_executor - INFO - 加载了 35 个任务状态
2025-07-16 11:33:35,080 - tif_executor - INFO - 加载了 2 个任务状态
2025-07-16 11:33:35,099 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 11:33:35,100 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 11:33:35,118 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 11:33:35,124 - root - INFO - === GeoServer REST API服务 ===
2025-07-16 11:33:35,125 - root - INFO - 主机: 0.0.0.0
2025-07-16 11:33:35,126 - root - INFO - 端口: 5083
2025-07-16 11:33:35,127 - root - INFO - 调试模式: 禁用
2025-07-16 11:33:35,127 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-16 11:33:35,144 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-16 11:33:35,145 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-16 11:33:37,922 - tif_executor - INFO - 启动TIF处理任务 58b96ef4-1f7a-464c-be1c-307266bf4b01: D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif -> D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif, D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.shp
2025-07-16 11:33:37,923 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - INFO - TIF处理任务 58b96ef4-1f7a-464c-be1c-307266bf4b01 开始执行
2025-07-16 11:33:37,924 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 11:33:37] "GET /api/tif/execute?input_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif&output_shp=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-16 11:33:37,928 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - INFO - 开始时间: 2025-07-16 11:33:37
2025-07-16 11:33:37,934 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - INFO - 处理参数: {
  "input_tif": "D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif",
  "output_shp": "D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-16 11:33:37,941 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-16 11:34:29,354 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 11:34:29] "GET /api/tif/status?task_id=58b96ef4-1f7a-464c-be1c-307266bf4b01 HTTP/1.1" 200 -
2025-07-16 11:34:41,546 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - INFO - 开始处理影像: D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 38177x48241x3估计内存使用: 总计 27.44 GB, 单个处理块 582.53 MB开始创建掩码... (11:34:05)使用全图检测无效区域模式...将处理 49 个数据块...使用多线程处理 (7 线程)...
2025-07-16 11:34:41,565 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:   0%|                                                                               | 0/49 [00:00<?, ?it/s]
2025-07-16 11:34:45,736 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:   2%|#4                                                                     | 1/49 [00:04<03:19,  4.15s/it]
2025-07-16 11:34:47,331 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:   6%|####3                                                                  | 3/49 [00:05<01:16,  1.67s/it]
2025-07-16 11:34:47,597 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  35%|########################2                                             | 17/49 [00:06<00:06,  4.64it/s]
2025-07-16 11:34:47,776 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  41%|############################5                                         | 20/49 [00:06<00:05,  5.49it/s]
2025-07-16 11:34:48,250 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  47%|################################8                                     | 23/49 [00:06<00:04,  5.67it/s]
2025-07-16 11:34:48,509 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  51%|###################################7                                  | 25/49 [00:06<00:04,  5.95it/s]
2025-07-16 11:34:48,662 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  55%|######################################5                               | 27/49 [00:07<00:03,  6.70it/s]
2025-07-16 11:34:49,197 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  59%|#########################################4                            | 29/49 [00:07<00:03,  5.68it/s]
2025-07-16 11:34:49,341 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  63%|############################################2                         | 31/49 [00:07<00:02,  6.64it/s]
2025-07-16 11:34:49,461 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  67%|###############################################1                      | 33/49 [00:07<00:02,  7.86it/s]
2025-07-16 11:34:49,597 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  71%|##################################################                    | 35/49 [00:08<00:01,  9.01it/s]
2025-07-16 11:34:50,466 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  76%|####################################################8                 | 37/49 [00:08<00:02,  4.97it/s]
2025-07-16 11:34:50,640 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  80%|#######################################################7              | 39/49 [00:09<00:01,  5.93it/s]
2025-07-16 11:34:51,379 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  86%|############################################################          | 42/49 [00:09<00:01,  5.05it/s]
2025-07-16 11:34:52,198 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  88%|#############################################################4        | 43/49 [00:10<00:01,  3.41it/s]
2025-07-16 11:34:52,349 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  92%|################################################################2     | 45/49 [00:10<00:00,  4.41it/s]
2025-07-16 11:34:52,453 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块:  96%|###################################################################1  | 47/49 [00:10<00:00,  5.75it/s]
2025-07-16 11:34:52,456 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理数据块: 100%|######################################################################| 49/49 [00:10<00:00,  4.50it/s]
2025-07-16 11:34:52,471 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - INFO - 已完成: 10/49 块 (20.4%)已完成: 20/49 块 (40.8%)已完成: 30/49 块 (61.2%)已完成: 40/49 块 (81.6%)已完成: 49/49 块 (100.0%)合并处理结果...
2025-07-16 11:34:52,474 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
合并结果:   0%|                                                                                 | 0/49 [00:00<?, ?it/s]
2025-07-16 11:34:52,580 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
合并结果:  24%|#################3                                                     | 12/49 [00:00<00:00, 113.18it/s]
2025-07-16 11:34:52,687 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
合并结果:  59%|##########################################                             | 29/49 [00:00<00:00, 141.09it/s]
2025-07-16 11:34:52,790 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
合并结果:  92%|#################################################################2     | 45/49 [00:00<00:00, 147.63it/s]
2025-07-16 11:34:52,813 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
合并结果: 100%|#######################################################################| 49/49 [00:00<00:00, 145.28it/s]
2025-07-16 11:35:01,627 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - INFO - 合并进度: 20/49 块 (40.8%)合并进度: 40/49 块 (81.6%)合并进度: 49/49 块 (100.0%)统计结果: 总像素 1841696657, 有效像素 440868328 (23.94%), 无效像素 1400828329 (76.06%)掩码创建完成，耗时: 56.09 秒 (11:35:01)预计总处理时间: 约 168.27 秒开始设置nodata值并保存到: D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif (11:35:01)写入带有nodata值的影像...
2025-07-16 11:35:01,628 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-16 11:35:41,708 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/49 [00:00<?, ?it/s]
2025-07-16 11:35:41,724 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:35:42,105 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:   2%|#3                                                              | 1/49 [00:00<00:18,  2.63it/s]
2025-07-16 11:35:42,113 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:35:42,366 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:   4%|##6                                                             | 2/49 [00:00<00:14,  3.23it/s]
2025-07-16 11:35:42,367 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:35:42,618 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:   6%|###9                                                            | 3/49 [00:00<00:13,  3.52it/s]
2025-07-16 11:35:42,619 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:35:42,875 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:   8%|#####2                                                          | 4/49 [00:01<00:12,  3.66it/s]
2025-07-16 11:35:42,876 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:35:43,111 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  10%|######5                                                         | 5/49 [00:01<00:11,  3.85it/s]
2025-07-16 11:35:43,112 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:35:43,335 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  12%|#######8                                                        | 6/49 [00:01<00:10,  4.04it/s]
2025-07-16 11:35:43,336 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:35:43,581 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 7/49 [00:01<00:10,  4.05it/s]
2025-07-16 11:35:43,582 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:35:43,821 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  16%|##########4                                                     | 8/49 [00:02<00:10,  4.09it/s]
2025-07-16 11:35:43,822 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:35:44,055 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  18%|###########7                                                    | 9/49 [00:02<00:09,  4.15it/s]
2025-07-16 11:35:44,056 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:35:44,256 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  20%|############8                                                  | 10/49 [00:02<00:08,  4.36it/s]
2025-07-16 11:35:44,257 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:35:44,430 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  22%|##############1                                                | 11/49 [00:02<00:08,  4.71it/s]
2025-07-16 11:35:44,430 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:35:49,016 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  24%|###############4                                               | 12/49 [00:07<00:57,  1.54s/it]
2025-07-16 11:35:49,026 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:35:58,057 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  27%|################7                                              | 13/49 [00:16<02:17,  3.81s/it]
2025-07-16 11:35:58,058 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:36:07,053 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  29%|##################                                             | 14/49 [00:25<03:08,  5.38s/it]
2025-07-16 11:36:07,054 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:36:12,888 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  31%|###################2                                           | 15/49 [00:31<03:07,  5.52s/it]
2025-07-16 11:36:12,888 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:36:19,037 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  33%|####################5                                          | 16/49 [00:37<03:08,  5.71s/it]
2025-07-16 11:36:19,037 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:36:24,816 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  35%|#####################8                                         | 17/49 [00:43<03:03,  5.73s/it]
2025-07-16 11:36:24,817 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:36:30,014 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  37%|#######################1                                       | 18/49 [00:48<02:52,  5.57s/it]
2025-07-16 11:36:30,015 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:36:37,518 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  39%|########################4                                      | 19/49 [00:55<03:04,  6.15s/it]
2025-07-16 11:36:37,519 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:36:43,035 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  41%|#########################7                                     | 20/49 [01:01<02:52,  5.96s/it]
2025-07-16 11:36:43,035 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:36:51,597 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  43%|###########################                                    | 21/49 [01:09<03:08,  6.74s/it]
2025-07-16 11:36:51,598 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:36:56,696 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  45%|############################2                                  | 22/49 [01:14<02:48,  6.25s/it]
2025-07-16 11:36:56,711 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:37:02,350 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  47%|#############################5                                 | 23/49 [01:20<02:37,  6.07s/it]
2025-07-16 11:37:02,351 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:37:11,082 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  49%|##############################8                                | 24/49 [01:29<02:51,  6.87s/it]
2025-07-16 11:37:11,083 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:37:16,470 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  51%|################################1                              | 25/49 [01:34<02:34,  6.42s/it]
2025-07-16 11:37:16,470 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:37:20,773 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  53%|#################################4                             | 26/49 [01:39<02:13,  5.79s/it]
2025-07-16 11:37:20,773 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:37:28,590 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  55%|##################################7                            | 27/49 [01:46<02:20,  6.40s/it]
2025-07-16 11:37:28,593 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:37:38,213 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 28/49 [01:56<02:34,  7.36s/it]
2025-07-16 11:37:38,214 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:37:44,476 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  59%|#####################################2                         | 29/49 [02:02<02:20,  7.03s/it]
2025-07-16 11:37:44,477 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:37:51,308 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  61%|######################################5                        | 30/49 [02:09<02:12,  6.97s/it]
2025-07-16 11:37:51,309 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:37:57,965 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  63%|#######################################8                       | 31/49 [02:16<02:03,  6.88s/it]
2025-07-16 11:37:57,968 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:38:02,864 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  65%|#########################################1                     | 32/49 [02:21<01:46,  6.28s/it]
2025-07-16 11:38:02,864 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:38:11,183 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  67%|##########################################4                    | 33/49 [02:29<01:50,  6.90s/it]
2025-07-16 11:38:11,184 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:38:16,070 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  69%|###########################################7                   | 34/49 [02:34<01:34,  6.29s/it]
2025-07-16 11:38:16,070 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:38:21,243 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 35/49 [02:39<01:23,  5.96s/it]
2025-07-16 11:38:21,243 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:38:27,574 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  73%|##############################################2                | 36/49 [02:45<01:18,  6.07s/it]
2025-07-16 11:38:27,576 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:38:31,440 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  76%|###############################################5               | 37/49 [02:49<01:04,  5.41s/it]
2025-07-16 11:38:31,441 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:38:38,346 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  78%|################################################8              | 38/49 [02:56<01:04,  5.86s/it]
2025-07-16 11:38:38,347 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:38:42,275 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  80%|##################################################1            | 39/49 [03:00<00:52,  5.28s/it]
2025-07-16 11:38:42,276 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:38:47,410 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  82%|###################################################4           | 40/49 [03:05<00:47,  5.24s/it]
2025-07-16 11:38:47,410 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:38:53,731 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  84%|####################################################7          | 41/49 [03:12<00:44,  5.56s/it]
2025-07-16 11:38:53,732 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:38:59,778 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 42/49 [03:18<00:39,  5.71s/it]
2025-07-16 11:38:59,779 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:39:05,771 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  88%|#######################################################2       | 43/49 [03:24<00:34,  5.79s/it]
2025-07-16 11:39:05,772 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:39:15,645 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  90%|########################################################5      | 44/49 [03:33<00:35,  7.02s/it]
2025-07-16 11:39:15,645 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:39:20,957 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  92%|#########################################################8     | 45/49 [03:39<00:26,  6.51s/it]
2025-07-16 11:39:20,957 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:39:29,256 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  94%|###########################################################1   | 46/49 [03:47<00:21,  7.04s/it]
2025-07-16 11:39:29,257 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:39:34,192 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  96%|############################################################4  | 47/49 [03:52<00:12,  6.41s/it]
2025-07-16 11:39:34,194 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:39:44,132 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度:  98%|#############################################################7 | 48/49 [04:02<00:07,  7.47s/it]
2025-07-16 11:39:44,133 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:39:45,054 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 1/3 写入进度: 100%|###############################################################| 49/49 [04:03<00:00,  5.51s/it]
2025-07-16 11:39:45,055 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:39:45,058 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:40:55,008 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理波段:  33%|########################3                                                | 1/3 [05:53<11:46, 353.38s/it]
2025-07-16 11:41:17,821 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/49 [00:00<?, ?it/s]
2025-07-16 11:41:17,912 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:41:18,457 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:   2%|#3                                                              | 1/49 [00:00<00:25,  1.85it/s]
2025-07-16 11:41:18,458 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:41:18,630 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:   6%|###9                                                            | 3/49 [00:00<00:09,  4.89it/s]
2025-07-16 11:41:18,630 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:41:18,823 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  10%|######5                                                         | 5/49 [00:00<00:06,  6.72it/s]
2025-07-16 11:41:18,824 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:41:19,003 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 7/49 [00:01<00:05,  8.08it/s]
2025-07-16 11:41:19,003 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:41:19,171 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  18%|###########7                                                    | 9/49 [00:01<00:04,  9.18it/s]
2025-07-16 11:41:19,172 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:41:19,328 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  22%|##############1                                                | 11/49 [00:01<00:03, 10.17it/s]
2025-07-16 11:41:19,330 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:41:32,011 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  22%|##############1                                                | 11/49 [00:14<00:03, 10.17it/s]
2025-07-16 11:41:32,017 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:41:36,314 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  24%|###############4                                               | 12/49 [00:18<02:08,  3.48s/it]
2025-07-16 11:41:36,314 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:41:54,061 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  27%|################7                                              | 13/49 [00:36<03:59,  6.65s/it]
2025-07-16 11:41:54,062 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:42:07,483 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  29%|##################                                             | 14/49 [00:49<04:49,  8.28s/it]
2025-07-16 11:42:07,484 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:42:23,672 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  31%|###################2                                           | 15/49 [01:05<05:50, 10.31s/it]
2025-07-16 11:42:23,673 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:42:38,030 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  33%|####################5                                          | 16/49 [01:20<06:15, 11.39s/it]
2025-07-16 11:42:38,034 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:42:53,223 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  35%|#####################8                                         | 17/49 [01:35<06:38, 12.44s/it]
2025-07-16 11:42:53,223 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:43:10,124 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  37%|#######################1                                       | 18/49 [01:52<07:04, 13.71s/it]
2025-07-16 11:43:10,125 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:43:27,309 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  39%|########################4                                      | 19/49 [02:09<07:21, 14.71s/it]
2025-07-16 11:43:27,309 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:43:43,736 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  41%|#########################7                                     | 20/49 [02:25<07:21, 15.21s/it]
2025-07-16 11:43:43,737 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:44:00,210 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  43%|###########################                                    | 21/49 [02:42<07:16, 15.58s/it]
2025-07-16 11:44:00,210 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:44:19,923 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  45%|############################2                                  | 22/49 [03:02<07:33, 16.80s/it]
2025-07-16 11:44:19,923 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:44:34,935 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  47%|#############################5                                 | 23/49 [03:17<07:03, 16.27s/it]
2025-07-16 11:44:34,936 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:44:50,060 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  49%|##############################8                                | 24/49 [03:32<06:38, 15.93s/it]
2025-07-16 11:44:50,061 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:45:05,634 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  51%|################################1                              | 25/49 [03:47<06:19, 15.82s/it]
2025-07-16 11:45:05,635 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:45:20,691 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  53%|#################################4                             | 26/49 [04:02<05:58, 15.59s/it]
2025-07-16 11:45:20,692 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:45:37,107 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  55%|##################################7                            | 27/49 [04:19<05:48, 15.84s/it]
2025-07-16 11:45:37,109 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:45:52,935 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 28/49 [04:35<05:32, 15.84s/it]
2025-07-16 11:45:52,978 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:46:09,670 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  59%|#####################################2                         | 29/49 [04:51<05:22, 16.11s/it]
2025-07-16 11:46:09,670 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:46:26,081 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  61%|######################################5                        | 30/49 [05:08<05:07, 16.20s/it]
2025-07-16 11:46:26,081 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:46:41,630 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  63%|#######################################8                       | 31/49 [05:23<04:48, 16.00s/it]
2025-07-16 11:46:41,631 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:46:56,739 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  65%|#########################################1                     | 32/49 [05:38<04:27, 15.74s/it]
2025-07-16 11:46:56,740 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:47:13,422 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  67%|##########################################4                    | 33/49 [05:55<04:16, 16.02s/it]
2025-07-16 11:47:13,423 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:47:28,493 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  69%|###########################################7                   | 34/49 [06:10<03:56, 15.73s/it]
2025-07-16 11:47:28,494 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:47:45,898 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 35/49 [06:27<03:47, 16.24s/it]
2025-07-16 11:47:45,899 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:48:01,053 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  73%|##############################################2                | 36/49 [06:43<03:26, 15.91s/it]
2025-07-16 11:48:01,053 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:48:15,832 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  76%|###############################################5               | 37/49 [06:57<03:06, 15.57s/it]
2025-07-16 11:48:15,833 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:48:31,914 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  78%|################################################8              | 38/49 [07:13<02:52, 15.72s/it]
2025-07-16 11:48:31,915 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:48:46,877 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  80%|##################################################1            | 39/49 [07:28<02:34, 15.50s/it]
2025-07-16 11:48:46,878 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:49:02,549 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  82%|###################################################4           | 40/49 [07:44<02:19, 15.55s/it]
2025-07-16 11:49:02,550 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:49:19,864 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  84%|####################################################7          | 41/49 [08:01<02:08, 16.08s/it]
2025-07-16 11:49:19,865 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:49:34,896 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 42/49 [08:16<01:50, 15.76s/it]
2025-07-16 11:49:34,896 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:49:53,023 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  88%|#######################################################2       | 43/49 [08:35<01:38, 16.47s/it]
2025-07-16 11:49:53,023 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:50:10,274 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  90%|########################################################5      | 44/49 [08:52<01:23, 16.71s/it]
2025-07-16 11:50:10,274 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:50:29,879 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  92%|#########################################################8     | 45/49 [09:11<01:10, 17.58s/it]
2025-07-16 11:50:29,879 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:50:47,336 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  94%|###########################################################1   | 46/49 [09:29<00:52, 17.54s/it]
2025-07-16 11:50:47,336 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:51:11,578 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  96%|############################################################4  | 47/49 [09:53<00:39, 19.55s/it]
2025-07-16 11:51:11,578 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:51:35,337 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度:  98%|#############################################################7 | 48/49 [10:17<00:20, 20.81s/it]
2025-07-16 11:51:35,338 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:51:39,549 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 49/49 [10:21<00:00, 15.83s/it]
2025-07-16 11:51:39,550 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:51:39,554 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:54:50,419 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [19:48<10:36, 636.93s/it]
2025-07-16 11:55:25,539 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/49 [00:00<?, ?it/s]
2025-07-16 11:55:25,554 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:55:26,329 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:   2%|#3                                                              | 1/49 [00:00<00:37,  1.30it/s]
2025-07-16 11:55:26,330 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:55:26,512 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:   4%|##6                                                             | 2/49 [00:00<00:19,  2.35it/s]
2025-07-16 11:55:26,512 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:55:26,696 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:   6%|###9                                                            | 3/49 [00:01<00:14,  3.17it/s]
2025-07-16 11:55:26,696 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:55:26,937 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:   8%|#####2                                                          | 4/49 [00:01<00:12,  3.50it/s]
2025-07-16 11:55:26,938 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:55:27,198 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  10%|######5                                                         | 5/49 [00:01<00:12,  3.61it/s]
2025-07-16 11:55:27,199 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:55:27,438 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  12%|#######8                                                        | 6/49 [00:01<00:11,  3.78it/s]
2025-07-16 11:55:27,439 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:55:27,666 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 7/49 [00:02<00:10,  3.97it/s]
2025-07-16 11:55:27,666 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:55:27,888 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  16%|##########4                                                     | 8/49 [00:02<00:09,  4.12it/s]
2025-07-16 11:55:27,889 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:55:28,126 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  18%|###########7                                                    | 9/49 [00:02<00:09,  4.14it/s]
2025-07-16 11:55:28,126 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:55:28,377 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  20%|############8                                                  | 10/49 [00:02<00:09,  4.09it/s]
2025-07-16 11:55:28,377 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:55:28,594 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  22%|##############1                                                | 11/49 [00:03<00:08,  4.24it/s]
2025-07-16 11:55:28,594 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:55:43,867 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  24%|###############4                                               | 12/49 [00:18<02:57,  4.81s/it]
2025-07-16 11:55:43,867 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:55:57,368 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  27%|################7                                              | 13/49 [00:31<04:27,  7.44s/it]
2025-07-16 11:55:57,368 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:56:15,993 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  29%|##################                                             | 14/49 [00:50<06:18, 10.82s/it]
2025-07-16 11:56:15,994 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:56:35,069 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  31%|###################2                                           | 15/49 [01:09<07:32, 13.31s/it]
2025-07-16 11:56:35,070 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:56:49,833 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  33%|####################5                                          | 16/49 [01:24<07:33, 13.75s/it]
2025-07-16 11:56:49,834 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:57:07,958 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  35%|#####################8                                         | 17/49 [01:42<08:02, 15.06s/it]
2025-07-16 11:57:07,959 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:57:25,431 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  37%|#######################1                                       | 18/49 [01:59<08:09, 15.79s/it]
2025-07-16 11:57:25,431 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:57:41,162 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  39%|########################4                                      | 19/49 [02:15<07:53, 15.77s/it]
2025-07-16 11:57:41,163 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:57:58,969 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  41%|#########################7                                     | 20/49 [02:33<07:55, 16.38s/it]
2025-07-16 11:57:58,970 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:58:19,131 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  43%|###########################                                    | 21/49 [02:53<08:10, 17.52s/it]
2025-07-16 11:58:19,132 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:58:35,384 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  45%|############################2                                  | 22/49 [03:09<07:42, 17.14s/it]
2025-07-16 11:58:35,385 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:58:50,446 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  47%|#############################5                                 | 23/49 [03:24<07:09, 16.51s/it]
2025-07-16 11:58:50,449 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:59:08,295 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  49%|##############################8                                | 24/49 [03:42<07:02, 16.92s/it]
2025-07-16 11:59:08,296 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:59:24,507 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  51%|################################1                              | 25/49 [03:58<06:40, 16.70s/it]
2025-07-16 11:59:24,507 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:59:39,725 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  53%|#################################4                             | 26/49 [04:14<06:13, 16.26s/it]
2025-07-16 11:59:39,725 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 11:59:57,869 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  55%|##################################7                            | 27/49 [04:32<06:10, 16.82s/it]
2025-07-16 11:59:57,871 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:00:15,486 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 28/49 [04:49<05:58, 17.06s/it]
2025-07-16 12:00:15,486 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:00:31,333 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  59%|#####################################2                         | 29/49 [05:05<05:33, 16.70s/it]
2025-07-16 12:00:31,333 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:00:47,708 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  61%|######################################5                        | 30/49 [05:22<05:15, 16.60s/it]
2025-07-16 12:00:47,708 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:01:10,564 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  63%|#######################################8                       | 31/49 [05:45<05:32, 18.48s/it]
2025-07-16 12:01:10,565 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:01:37,893 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  65%|#########################################1                     | 32/49 [06:12<05:59, 21.13s/it]
2025-07-16 12:01:37,894 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:01:53,104 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  67%|##########################################4                    | 33/49 [06:27<05:09, 19.36s/it]
2025-07-16 12:01:53,104 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:02:07,492 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  69%|###########################################7                   | 34/49 [06:41<04:27, 17.87s/it]
2025-07-16 12:02:07,528 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:02:23,237 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 35/49 [06:57<04:01, 17.23s/it]
2025-07-16 12:02:23,238 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:02:40,205 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  73%|##############################################2                | 36/49 [07:14<03:42, 17.15s/it]
2025-07-16 12:02:40,206 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:02:54,733 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  76%|###############################################5               | 37/49 [07:29<03:16, 16.36s/it]
2025-07-16 12:02:54,735 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:03:08,814 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  78%|################################################8              | 38/49 [07:43<02:52, 15.68s/it]
2025-07-16 12:03:08,814 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:03:22,963 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  80%|##################################################1            | 39/49 [07:57<02:32, 15.22s/it]
2025-07-16 12:03:22,964 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:03:37,832 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  82%|###################################################4           | 40/49 [08:12<02:16, 15.11s/it]
2025-07-16 12:03:37,832 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:03:54,556 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  84%|####################################################7          | 41/49 [08:28<02:04, 15.60s/it]
2025-07-16 12:03:54,556 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:04:12,028 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 42/49 [08:46<01:53, 16.16s/it]
2025-07-16 12:04:12,029 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:04:28,557 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  88%|#######################################################2       | 43/49 [09:02<01:37, 16.27s/it]
2025-07-16 12:04:28,558 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:05:05,740 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  90%|########################################################5      | 44/49 [09:40<01:52, 22.54s/it]
2025-07-16 12:05:05,741 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:05:25,691 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  92%|#########################################################8     | 45/49 [10:00<01:27, 21.77s/it]
2025-07-16 12:05:25,692 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:05:42,243 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  94%|###########################################################1   | 46/49 [10:16<01:00, 20.20s/it]
2025-07-16 12:05:42,246 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:05:56,626 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  96%|############################################################4  | 47/49 [10:31<00:36, 18.46s/it]
2025-07-16 12:05:56,627 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:06:12,446 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度:  98%|#############################################################7 | 48/49 [10:46<00:17, 17.67s/it]
2025-07-16 12:06:12,449 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:06:16,335 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
波段 3/3 写入进度: 100%|###############################################################| 49/49 [10:50<00:00, 13.53s/it]
2025-07-16 12:06:16,336 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:06:16,342 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - [A
2025-07-16 12:09:32,928 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [34:31<00:00, 749.06s/it]
2025-07-16 12:09:32,929 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [34:31<00:00, 690.43s/it]
2025-07-16 12:17:28,372 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - INFO - 影像保存完成，耗时: 2072.47 秒 (12:09:33)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.shp (12:09:33)从处理后的TIF文件提取有效区域: D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif创建掩码，识别有效区域...轻微平滑处理掩码，减少边缘锯齿...寻找有效区域轮廓...找到 1 个初始轮廓使用所有 1 个轮廓，不进行面积过滤转换轮廓为地理坐标...
2025-07-16 12:17:28,398 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-16 12:17:29,500 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:01<00:00,  1.06s/it]
2025-07-16 12:17:29,501 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:01<00:00,  1.07s/it]
2025-07-16 12:17:29,685 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - INFO - 成功创建 1 个多边形合并多边形...创建新shapefile: D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.shp写入shapefile特征...处理单个Polygon
2025-07-16 12:17:29,686 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
写入多边形:   0%|                                                                                | 0/1 [00:00<?, ?it/s]
2025-07-16 12:17:29,752 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - ERROR - 
写入多边形: 100%|########################################################################| 1/1 [00:00<00:00, 16.20it/s]
2025-07-16 12:17:34,964 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - INFO - TIF处理任务 58b96ef4-1f7a-464c-be1c-307266bf4b01 执行成功
2025-07-16 12:17:34,965 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - INFO - 完成时间: 2025-07-16 12:17:34
2025-07-16 12:17:34,968 - tif_task_58b96ef4-1f7a-464c-be1c-307266bf4b01 - INFO - 状态: 运行成功
2025-07-16 12:19:04,128 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 12:19:04] "GET /api/tif/status?task_id=58b96ef4-1f7a-464c-be1c-307266bf4b01 HTTP/1.1" 200 -
2025-07-16 15:37:02,727 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 15:37:02] "GET /api/tif/info?path=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif HTTP/1.1" 200 -
2025-07-16 15:37:02,724 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 15:37:02] "GET /api/tif/info?path=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif HTTP/1.1" 200 -
2025-07-16 15:38:18,336 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - INFO - TIF处理任务 682c5330-92f0-48ac-8d8e-9721547d408d 开始执行
2025-07-16 15:38:18,336 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - INFO - 开始时间: 2025-07-16 15:38:18
2025-07-16 15:38:18,340 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - INFO - 处理参数: {
  "input_tif": "D:/Drone_Project/geoserverapi/data/20250701/nanning.tif",
  "output_tif": "D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.tif",
  "output_shp": "D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-16 15:38:18,315 - tif_executor - INFO - 启动TIF处理任务 682c5330-92f0-48ac-8d8e-9721547d408d: D:/Drone_Project/geoserverapi/data/20250701/nanning.tif -> D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.tif, D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.shp
2025-07-16 15:38:18,350 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 15:38:18] "GET /api/tif/execute?input_tif=D:/Drone_Project/geoserverapi/data/20250701/nanning.tif&output_tif=D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.tif&output_shp=D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-16 15:38:18,793 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - INFO - 开始处理影像: D:/Drone_Project/geoserverapi/data/20250701/nanning.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False影像尺寸: 3840x3328x3估计内存使用: 总计 195.00 MB, 单个处理块 58.59 MB开始创建掩码... (15:38:18)使用全图检测无效区域模式...将处理 4 个数据块...使用多线程处理 (7 线程)...
2025-07-16 15:38:18,815 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
处理数据块:   0%|                                                                                | 0/4 [00:00<?, ?it/s]
2025-07-16 15:38:18,822 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
处理数据块: 100%|######################################################################| 4/4 [00:00<00:00, 1000.01it/s]
2025-07-16 15:38:18,823 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - INFO - 已完成: 4/4 块 (100.0%)合并处理结果...
2025-07-16 15:38:18,824 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
合并结果:   0%|                                                                                  | 0/4 [00:00<?, ?it/s]
2025-07-16 15:38:18,827 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
合并结果: 100%|########################################################################| 4/4 [00:00<00:00, 2000.86it/s]
2025-07-16 15:38:18,908 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - INFO - 合并进度: 4/4 块 (100.0%)统计结果: 总像素 12779520, 有效像素 12779514 (100.00%), 无效像素 6 (0.00%)掩码创建完成，耗时: 0.12 秒 (15:38:18)预计总处理时间: 约 0.35 秒开始设置nodata值并保存到: D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.tif (15:38:18)写入带有nodata值的影像...
2025-07-16 15:38:18,910 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-16 15:38:18,963 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
波段 1/3 写入进度:   0%|                                                                         | 0/4 [00:00<?, ?it/s]
2025-07-16 15:38:18,966 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - [A
2025-07-16 15:38:19,022 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - [A
2025-07-16 15:38:19,127 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:00<00:00,  4.73it/s]
2025-07-16 15:38:19,152 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
波段 2/3 写入进度:   0%|                                                                         | 0/4 [00:00<?, ?it/s]
2025-07-16 15:38:19,153 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - [A
2025-07-16 15:38:19,170 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - [A
2025-07-16 15:38:19,299 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [00:00<00:00,  5.30it/s]
2025-07-16 15:38:19,322 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
波段 3/3 写入进度:   0%|                                                                         | 0/4 [00:00<?, ?it/s]
2025-07-16 15:38:19,323 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - [A
2025-07-16 15:38:19,340 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - [A
2025-07-16 15:38:19,468 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [00:00<00:00,  5.58it/s]
2025-07-16 15:38:19,469 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [00:00<00:00,  5.42it/s]
2025-07-16 15:38:19,896 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - INFO - 影像保存完成，耗时: 0.61 秒 (15:38:19)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.shp (15:38:19)从处理后的TIF文件提取有效区域: D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.tif创建掩码，识别有效区域...轻微平滑处理掩码，减少边缘锯齿...寻找有效区域轮廓...找到 1 个初始轮廓使用所有 1 个轮廓，不进行面积过滤转换轮廓为地理坐标...
2025-07-16 15:38:19,981 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-16 15:38:20,016 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00, 44.42it/s]
2025-07-16 15:38:20,055 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - INFO - 成功创建 1 个多边形合并多边形...创建新shapefile: D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.shp写入shapefile特征...处理单个Polygon
2025-07-16 15:38:20,056 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
写入多边形:   0%|                                                                                | 0/1 [00:00<?, ?it/s]
2025-07-16 15:38:20,064 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - ERROR - 
写入多边形: 100%|#######################################################################| 1/1 [00:00<00:00, 166.56it/s]
2025-07-16 15:38:20,119 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - INFO - TIF处理任务 682c5330-92f0-48ac-8d8e-9721547d408d 执行成功
2025-07-16 15:38:20,121 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - INFO - 完成时间: 2025-07-16 15:38:20
2025-07-16 15:38:20,123 - tif_task_682c5330-92f0-48ac-8d8e-9721547d408d - INFO - 状态: 运行成功
2025-07-16 15:38:30,338 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 15:38:30] "GET /api/tif/status?task_id=682c5330-92f0-48ac-8d8e-9721547d408d HTTP/1.1" 200 -
2025-07-16 15:46:54,494 - werkzeug - INFO - ************** - - [16/Jul/2025 15:46:54] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-16 15:46:54,734 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-16 15:46:54,844 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-16 15:46:54,846 - werkzeug - INFO - ************** - - [16/Jul/2025 15:46:54] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-16 15:46:58,238 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-16 15:46:58,283 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-16 15:46:58,284 - werkzeug - INFO - ************** - - [16/Jul/2025 15:46:58] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-16 15:47:07,279 - werkzeug - INFO - ************** - - [16/Jul/2025 15:47:07] "OPTIONS /api/management/layers/bbox?workspace=tttt&layer=odm_orthophoto_out HTTP/1.1" 200 -
2025-07-16 15:47:07,285 - root - INFO - 获取图层 tttt:odm_orthophoto_out 的信息
2025-07-16 15:47:07,315 - root - INFO - 成功获取图层 tttt:odm_orthophoto_out 的边界框信息
2025-07-16 15:47:07,317 - werkzeug - INFO - ************** - - [16/Jul/2025 15:47:07] "GET /api/management/layers/bbox?workspace=tttt&layer=odm_orthophoto_out HTTP/1.1" 200 -
2025-07-16 16:15:59,229 - werkzeug - INFO - ************** - - [16/Jul/2025 16:15:59] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-16 16:15:59,236 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-16 16:15:59,309 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-16 16:15:59,311 - werkzeug - INFO - ************** - - [16/Jul/2025 16:15:59] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-16 16:16:16,150 - werkzeug - INFO - ************** - - [16/Jul/2025 16:16:16] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-16 16:16:16,157 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-16 16:16:16,188 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-16 16:16:16,189 - werkzeug - INFO - ************** - - [16/Jul/2025 16:16:16] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-16 19:05:18,300 - werkzeug - INFO - ************** - - [16/Jul/2025 19:05:18] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-16 19:05:18,309 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-16 19:05:18,433 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-16 19:05:18,439 - werkzeug - INFO - ************** - - [16/Jul/2025 19:05:18] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-16 19:05:26,049 - werkzeug - INFO - ************** - - [16/Jul/2025 19:05:26] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-16 19:05:26,396 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-16 19:05:26,706 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-16 19:05:26,869 - werkzeug - INFO - ************** - - [16/Jul/2025 19:05:26] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-16 19:05:34,628 - werkzeug - INFO - ************** - - [16/Jul/2025 19:05:34] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-16 19:05:34,683 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-16 19:05:34,746 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-16 19:05:34,756 - werkzeug - INFO - ************** - - [16/Jul/2025 19:05:34] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-16 19:05:45,249 - werkzeug - INFO - ************** - - [16/Jul/2025 19:05:45] "OPTIONS /api/management/layers/bbox?workspace=tttt&layer=odm_orthophoto_out HTTP/1.1" 200 -
2025-07-16 19:05:45,259 - root - INFO - 获取图层 tttt:odm_orthophoto_out 的信息
2025-07-16 19:05:45,349 - root - INFO - 成功获取图层 tttt:odm_orthophoto_out 的边界框信息
2025-07-16 19:05:45,351 - werkzeug - INFO - ************** - - [16/Jul/2025 19:05:45] "GET /api/management/layers/bbox?workspace=tttt&layer=odm_orthophoto_out HTTP/1.1" 200 -
