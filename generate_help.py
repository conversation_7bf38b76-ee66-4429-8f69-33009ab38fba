#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
Author: 吴博文 <EMAIL>
Date: 2025-07-01
Description: 生成GeoServer命令行工具的详细帮助文档
'''

import os
import sys
import argparse
from geoserver_cli import parse_arguments

def generate_markdown_doc(output_file="geoserver_cli_help.md"):
    """生成Markdown格式的帮助文档"""
    # 获取原始命令行解析器
    parser = argparse.ArgumentParser(description='GeoServer命令行工具')
    
    # 通用选项
    parser.add_argument('--log-level', type=str, default='INFO',
                      choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                      help='日志级别')
    parser.add_argument('--log-file', type=str,
                      help='日志文件路径，默认为logs目录下带时间戳的文件')
    
    # 子命令解析器
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 创建工作区命令
    workspace_parser = subparsers.add_parser('create-workspace', help='创建GeoServer工作区')
    workspace_parser.add_argument('--name', type=str, required=True,
                               help='工作区名称')
    
    # 发布Shapefile命令
    shapefile_parser = subparsers.add_parser('publish-shapefile', help='发布Shapefile到GeoServer')
    shapefile_parser.add_argument('--file', type=str, required=True,
                               help='Shapefile文件路径')
    shapefile_parser.add_argument('--workspace', type=str, required=True,
                               help='工作区名称')
    shapefile_parser.add_argument('--store', type=str,
                               help='存储名称，默认使用文件名')
    shapefile_parser.add_argument('--layer', type=str,
                               help='图层名称，默认使用存储名')
    
    # 发布Shapefile目录命令
    shapefile_dir_parser = subparsers.add_parser('publish-shapefile-directory', help='发布目录中所有Shapefile到GeoServer')
    shapefile_dir_parser.add_argument('--directory', type=str, required=True,
                                  help='包含Shapefile文件的目录路径')
    shapefile_dir_parser.add_argument('--workspace', type=str, required=True,
                                  help='工作区名称')
    shapefile_dir_parser.add_argument('--prefix', type=str,
                                  help='存储名称前缀，如果提供则存储名为"prefix_文件名"')
    
    # 发布GeoTIFF命令
    geotiff_parser = subparsers.add_parser('publish-geotiff', help='发布GeoTIFF到GeoServer')
    geotiff_parser.add_argument('--file', type=str, required=True,
                             help='GeoTIFF文件路径')
    geotiff_parser.add_argument('--workspace', type=str, required=True,
                             help='工作区名称')
    geotiff_parser.add_argument('--store', type=str,
                             help='存储名称，默认使用文件名')
    geotiff_parser.add_argument('--layer', type=str,
                             help='图层名称，默认使用存储名')
    
    # 发布PostGIS表命令
    postgis_parser = subparsers.add_parser('publish-postgis', help='发布PostGIS表到GeoServer')
    postgis_parser.add_argument('--table', type=str, required=True,
                             help='数据表名称')
    postgis_parser.add_argument('--workspace', type=str, required=True,
                             help='工作区名称')
    postgis_parser.add_argument('--store', type=str, required=True,
                             help='数据存储名称')
    postgis_parser.add_argument('--layer', type=str,
                             help='图层名称，默认使用表名')
    postgis_parser.add_argument('--geometry-column', type=str, default='geom',
                             help='几何列名称，默认为geom')
    postgis_parser.add_argument('--srid', type=int, default=4326,
                             help='空间参考系统ID，默认为4326')
    
    # 创建PostGIS数据存储命令
    datastore_parser = subparsers.add_parser('create-datastore', help='创建PostGIS数据存储')
    datastore_parser.add_argument('--name', type=str, required=True,
                               help='数据存储名称')
    datastore_parser.add_argument('--workspace', type=str, required=True,
                               help='工作区名称')
    datastore_parser.add_argument('--host', type=str, default='localhost',
                               help='数据库主机，默认为localhost')
    datastore_parser.add_argument('--port', type=int, default=5432,
                               help='数据库端口，默认为5432')
    datastore_parser.add_argument('--database', type=str, required=True,
                               help='数据库名称')
    datastore_parser.add_argument('--username', type=str, required=True,
                               help='数据库用户名')
    datastore_parser.add_argument('--password', type=str, required=True,
                               help='数据库密码')
    
    # 设置图层样式命令
    style_parser = subparsers.add_parser('set-style', help='设置图层样式')
    style_parser.add_argument('--layer', type=str, required=True,
                           help='图层名称')
    style_parser.add_argument('--style', type=str, required=True,
                           help='样式名称')
    style_parser.add_argument('--workspace', type=str, required=True,
                           help='工作区名称')
    
    # 删除图层命令
    delete_layer_parser = subparsers.add_parser('delete-layer', help='删除图层')
    delete_layer_parser.add_argument('--name', type=str, required=True,
                                  help='图层名称')
    delete_layer_parser.add_argument('--workspace', type=str, required=True,
                                  help='工作区名称')
    
    # 删除工作区命令
    delete_workspace_parser = subparsers.add_parser('delete-workspace', help='删除工作区')
    delete_workspace_parser.add_argument('--name', type=str, required=True,
                                      help='工作区名称')
    
    # 批处理命令
    batch_parser = subparsers.add_parser('batch', help='批量处理操作')
    batch_parser.add_argument('--file', type=str, required=True,
                           help='JSON格式的批处理配置文件')

    # 生成markdown文档
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# GeoServer命令行工具使用手册\n\n")
        f.write("GeoServer命令行工具是一个用于自动化操作GeoServer的工具，支持通过命令行或批处理文件执行各种GeoServer操作。\n\n")
        
        f.write("## 全局选项\n\n")
        f.write("以下选项适用于所有命令：\n\n")
        f.write("* `--log-level`: 设置日志级别，可选值为DEBUG、INFO、WARNING、ERROR、CRITICAL，默认为INFO\n")
        f.write("* `--log-file`: 设置日志文件路径，默认为logs目录下带时间戳的文件\n\n")
        
        f.write("## 可用命令\n\n")
        
        # 创建工作区
        f.write("### `create-workspace`: 创建GeoServer工作区\n\n")
        f.write("**参数**：\n\n")
        f.write("* `--name`: 工作区名称（必需）\n\n")
        f.write("**示例**：\n\n")
        f.write("```bash\npython geoserver_cli.py create-workspace --name test_workspace\n```\n\n")
        
        # 发布Shapefile
        f.write("### `publish-shapefile`: 发布Shapefile到GeoServer\n\n")
        f.write("**参数**：\n\n")
        f.write("* `--file`: Shapefile文件路径（必需）\n")
        f.write("* `--workspace`: 工作区名称（必需）\n")
        f.write("* `--store`: 存储名称，默认使用文件名\n")
        f.write("* `--layer`: 图层名称，默认使用存储名\n\n")
        f.write("**示例**：\n\n")
        f.write("```bash\npython geoserver_cli.py publish-shapefile --file data/example.shp --workspace test_workspace --store example_store --layer example_layer\n```\n\n")
        
        # 发布Shapefile目录
        f.write("### `publish-shapefile-directory`: 发布目录中所有Shapefile到GeoServer\n\n")
        f.write("**参数**：\n\n")
        f.write("* `--directory`: 包含Shapefile文件的目录路径（必需）\n")
        f.write("* `--workspace`: 工作区名称（必需）\n")
        f.write("* `--prefix`: 存储名称前缀，如果提供则存储名为\"prefix_文件名\"\n\n")
        f.write("**示例**：\n\n")
        f.write("```bash\npython geoserver_cli.py publish-shapefile-directory --directory data/shapefiles --workspace test_workspace --prefix shp\n```\n\n")
        
        # 发布GeoTIFF
        f.write("### `publish-geotiff`: 发布GeoTIFF到GeoServer\n\n")
        f.write("**参数**：\n\n")
        f.write("* `--file`: GeoTIFF文件路径（必需）\n")
        f.write("* `--workspace`: 工作区名称（必需）\n")
        f.write("* `--store`: 存储名称，默认使用文件名\n")
        f.write("* `--layer`: 图层名称，默认使用存储名\n\n")
        f.write("**示例**：\n\n")
        f.write("```bash\npython geoserver_cli.py publish-geotiff --file data/example.tif --workspace test_workspace --store example_raster --layer example_raster_layer\n```\n\n")
        
        # 发布PostGIS表
        f.write("### `publish-postgis`: 发布PostGIS表到GeoServer\n\n")
        f.write("**参数**：\n\n")
        f.write("* `--table`: 数据表名称（必需）\n")
        f.write("* `--workspace`: 工作区名称（必需）\n")
        f.write("* `--store`: 数据存储名称（必需）\n")
        f.write("* `--layer`: 图层名称，默认使用表名\n")
        f.write("* `--geometry-column`: 几何列名称，默认为geom\n")
        f.write("* `--srid`: 空间参考系统ID，默认为4326\n\n")
        f.write("**示例**：\n\n")
        f.write("```bash\npython geoserver_cli.py publish-postgis --table spatial_table --workspace test_workspace --store postgis_store --layer postgis_layer --geometry-column geom --srid 4326\n```\n\n")
        
        # 创建PostGIS数据存储
        f.write("### `create-datastore`: 创建PostGIS数据存储\n\n")
        f.write("**参数**：\n\n")
        f.write("* `--name`: 数据存储名称（必需）\n")
        f.write("* `--workspace`: 工作区名称（必需）\n")
        f.write("* `--host`: 数据库主机，默认为localhost\n")
        f.write("* `--port`: 数据库端口，默认为5432\n")
        f.write("* `--database`: 数据库名称（必需）\n")
        f.write("* `--username`: 数据库用户名（必需）\n")
        f.write("* `--password`: 数据库密码（必需）\n\n")
        f.write("**示例**：\n\n")
        f.write("```bash\npython geoserver_cli.py create-datastore --name postgis_store --workspace test_workspace --database spatial_db --username postgres --password postgres\n```\n\n")
        
        # 设置图层样式
        f.write("### `set-style`: 设置图层样式\n\n")
        f.write("**参数**：\n\n")
        f.write("* `--layer`: 图层名称（必需）\n")
        f.write("* `--style`: 样式名称（必需）\n")
        f.write("* `--workspace`: 工作区名称（必需）\n\n")
        f.write("**示例**：\n\n")
        f.write("```bash\npython geoserver_cli.py set-style --layer example_layer --style polygon_style --workspace test_workspace\n```\n\n")
        
        # 删除图层
        f.write("### `delete-layer`: 删除图层\n\n")
        f.write("**参数**：\n\n")
        f.write("* `--name`: 图层名称（必需）\n")
        f.write("* `--workspace`: 工作区名称（必需）\n\n")
        f.write("**示例**：\n\n")
        f.write("```bash\npython geoserver_cli.py delete-layer --name example_layer --workspace test_workspace\n```\n\n")
        
        # 删除工作区
        f.write("### `delete-workspace`: 删除工作区\n\n")
        f.write("**参数**：\n\n")
        f.write("* `--name`: 工作区名称（必需）\n\n")
        f.write("**示例**：\n\n")
        f.write("```bash\npython geoserver_cli.py delete-workspace --name test_workspace\n```\n\n")
        
        # 批处理
        f.write("### `batch`: 批量处理操作\n\n")
        f.write("**参数**：\n\n")
        f.write("* `--file`: JSON格式的批处理配置文件（必需）\n\n")
        f.write("**示例**：\n\n")
        f.write("```bash\npython geoserver_cli.py batch --file batch_commands.json\n```\n\n")
        
        f.write("## 批处理文件格式\n\n")
        f.write("批处理文件是一个JSON格式的文件，包含一系列操作，每个操作包含类型和参数。例如：\n\n")
        f.write("```json\n")
        f.write('[\n')
        f.write('  {\n')
        f.write('    "type": "create_workspace",\n')
        f.write('    "params": {\n')
        f.write('      "name": "test_workspace"\n')
        f.write('    }\n')
        f.write('  },\n')
        f.write('  {\n')
        f.write('    "type": "publish_shapefile",\n')
        f.write('    "params": {\n')
        f.write('      "file": "data/example.shp",\n')
        f.write('      "workspace": "test_workspace",\n')
        f.write('      "store": "example_store",\n')
        f.write('      "layer": "example_layer"\n')
        f.write('    }\n')
        f.write('  },\n')
        f.write('  {\n')
        f.write('    "type": "publish_shapefile_directory",\n')
        f.write('    "params": {\n')
        f.write('      "directory": "data/shapefiles",\n')
        f.write('      "workspace": "test_workspace",\n')
        f.write('      "prefix": "shp"\n')
        f.write('    }\n')
        f.write('  }\n')
        f.write(']\n')
        f.write("```\n\n")
        
        f.write("可用的操作类型有：\n\n")
        f.write("* `create_workspace`\n")
        f.write("* `publish_shapefile`\n")
        f.write("* `publish_shapefile_directory`\n")
        f.write("* `publish_geotiff`\n")
        f.write("* `publish_postgis_layer`\n")
        f.write("* `create_datastore`\n")
        f.write("* `set_layer_style`\n")
        f.write("* `delete_layer`\n")
        f.write("* `delete_workspace`\n\n")
        
        f.write("## 日志\n\n")
        f.write("命令执行过程中的所有日志会同时输出到控制台和日志文件。默认情况下，日志文件保存在`logs`目录下，文件名格式为`geoserver_YYYYMMDD_HHMMSS.log`。\n\n")
        
        f.write("可以通过`--log-level`参数设置日志级别，可选值有：\n\n")
        f.write("* `DEBUG`: 调试信息，最详细的日志\n")
        f.write("* `INFO`: 一般信息，默认级别\n")
        f.write("* `WARNING`: 警告信息\n")
        f.write("* `ERROR`: 错误信息\n")
        f.write("* `CRITICAL`: 严重错误信息\n\n")
        
        f.write("可以通过`--log-file`参数指定日志文件的路径。\n\n")
    
    print(f"帮助文档已生成到: {output_file}")

if __name__ == "__main__":
    generate_markdown_doc() 