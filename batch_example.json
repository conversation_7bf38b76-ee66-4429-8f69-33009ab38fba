[{"type": "create_workspace", "params": {"name": "test_workspace"}}, {"type": "publish_shapefile", "params": {"file": "data/20250701/testshp/招商引资片区.shp", "workspace": "test_workspace", "store": "example_store", "layer": "example_layer", "charset": "UTF-8"}}, {"type": "publish_shapefile", "params": {"file": "data/20250701/testshp/种植区.shp", "workspace": "auto_created_workspace", "charset": "UTF-8"}}, {"type": "publish_shapefile_directory", "params": {"directory": "data/20250701/testshp", "workspace": "bulk_workspace", "charset": "UTF-8"}}, {"type": "publish_shapefile_directory", "params": {"directory": "data/20250701/testshp", "workspace": "custom_prefix_workspace", "store": "custom_store_name", "charset": "UTF-8"}}, {"type": "publish_geotiff", "params": {"file": "data/example_raster.tif", "workspace": "test_workspace", "store": "raster_store", "layer": "raster_layer"}}, {"type": "create_datastore", "params": {"name": "postgis_store", "workspace": "test_workspace", "host": "localhost", "port": 5432, "database": "spatial_db", "username": "postgres", "password": "postgres"}}, {"type": "publish_postgis", "params": {"table": "spatial_table", "workspace": "test_workspace", "store": "postgis_store", "layer": "postgis_layer", "geometry_column": "geom", "srid": 4326}}]