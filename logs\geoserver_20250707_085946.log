2025-07-07 08:59:46,184 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250707_085946.log
2025-07-07 08:59:48,698 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-07 08:59:48,698 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-07 08:59:49,548 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-07 08:59:49,553 - geoserver_query_api - INFO - === GeoServer栅格数据坐标查询API服务 ===
2025-07-07 08:59:49,553 - geoserver_query_api - INFO - 主机: 0.0.0.0
2025-07-07 08:59:49,553 - geoserver_query_api - INFO - 端口: 5000
2025-07-07 08:59:49,554 - geoserver_query_api - INFO - 调试模式: 禁用
2025-07-07 08:59:49,568 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-07 08:59:49,569 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-07 09:00:44,357 - root - ERROR - 工作区 'my_workspace' 不存在
2025-07-07 09:00:44,358 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 09:00:44] "GET /api/layers?workspace=my_workspace HTTP/1.1" 200 -
2025-07-07 09:00:44,404 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 09:00:44] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-07 09:04:27,002 - root - INFO - 尝试从WMS GetCapabilities获取 1 个图层的边界框
2025-07-07 09:04:30,017 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 09:04:30,020 - root - INFO - 在工作区 'test_myworkspace' 中找到 1 个栅格图层
2025-07-07 09:04:30,022 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 09:04:30] "GET /api/layers?workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 09:55:45,659 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 09:55:46,062 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 09:55:46,063 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 09:55:46,064 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 09:55:46,066 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 09:55:46,067 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 09:55:46,068 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 09:55:46,069 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 09:55:46,069 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 09:55:46,071 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 09:55:46,072 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 09:55:46,074 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 09:55:46,075 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 09:55:46] "GET /api/layers?workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 10:03:55,642 - root - INFO - 开始查询坐标点 (22.7, 108.3) 在工作区 'test_myworkspace' 中的图层
2025-07-07 10:03:55,837 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 10:03:56,279 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 10:03:56,280 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 10:03:56,281 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 10:03:56,282 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 10:03:56,283 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 10:03:56,284 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 10:03:56,285 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 10:03:56,286 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 10:03:56,287 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 10:03:56,288 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 10:03:56,289 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 10:03:56,644 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 10:03:56,684 - root - INFO - 在图层 'id6' 中找到有效数据
2025-07-07 10:03:56,685 - root - INFO - 在坐标点 (22.7, 108.3) 处找到 2 个有效栅格图层
2025-07-07 10:03:56,686 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 10:03:56] "GET /api/query?lat=22.7&lon=108.3&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 10:49:36,905 - root - INFO - 开始查询坐标点 (22.916359445549773, 108.20361313374931) 在工作区 'test_myworkspace' 中的图层
2025-07-07 10:49:37,088 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 10:49:37,419 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 10:49:37,421 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 10:49:37,422 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 10:49:37,423 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 10:49:37,424 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 10:49:37,425 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 10:49:37,426 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 10:49:37,427 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 10:49:37,428 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 10:49:37,429 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 10:49:37,431 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 10:49:37,459 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 10:49:37,488 - root - INFO - 在图层 'id1' 中找到有效数据
2025-07-07 10:49:37,488 - root - INFO - 在坐标点 (22.916359445549773, 108.20361313374931) 处找到 2 个有效栅格图层
2025-07-07 10:49:37,489 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 10:49:37] "GET /api/query?lat=22.916359445549773&lon=108.20361313374931&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 10:52:57,214 - root - INFO - 开始查询坐标点 (22.791770957082917, 108.33162936752811) 在工作区 'test_myworkspace' 中的图层
2025-07-07 10:52:57,601 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 10:52:58,330 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 10:52:58,331 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 10:52:58,332 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 10:52:58,334 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 10:52:58,335 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 10:52:58,336 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 10:52:58,337 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 10:52:58,340 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 10:52:58,342 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 10:52:58,343 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 10:52:58,344 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 10:52:58,368 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 10:52:58,412 - root - INFO - 在图层 'id5' 中找到有效数据
2025-07-07 10:52:58,413 - root - INFO - 在坐标点 (22.791770957082917, 108.33162936752811) 处找到 2 个有效栅格图层
2025-07-07 10:52:58,414 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 10:52:58] "GET /api/query?lat=22.791770957082917&lon=108.33162936752811&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 10:53:01,688 - root - INFO - 开始查询坐标点 (22.914763760665863, 108.20410203904723) 在工作区 'test_myworkspace' 中的图层
2025-07-07 10:53:01,856 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 10:53:02,183 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 10:53:02,185 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 10:53:02,186 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 10:53:02,187 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 10:53:02,189 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 10:53:02,190 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 10:53:02,191 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 10:53:02,192 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 10:53:02,193 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 10:53:02,194 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 10:53:02,198 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 10:53:02,217 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 10:53:02,247 - root - INFO - 在图层 'id1' 中找到有效数据
2025-07-07 10:53:02,247 - root - INFO - 在坐标点 (22.914763760665863, 108.20410203904723) 处找到 2 个有效栅格图层
2025-07-07 10:53:02,248 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 10:53:02] "GET /api/query?lat=22.914763760665863&lon=108.20410203904723&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 10:53:08,353 - root - INFO - 开始查询坐标点 (22.91338759179493, 108.45965484963584) 在工作区 'test_myworkspace' 中的图层
2025-07-07 10:53:08,507 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 10:53:08,786 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 10:53:08,788 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 10:53:08,789 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 10:53:08,792 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 10:53:08,793 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 10:53:08,794 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 10:53:08,796 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 10:53:08,797 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 10:53:08,798 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 10:53:08,799 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 10:53:08,800 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 10:53:08,821 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 10:53:08,859 - root - INFO - 在图层 'id7' 中找到有效数据
2025-07-07 10:53:08,859 - root - INFO - 在坐标点 (22.91338759179493, 108.45965484963584) 处找到 2 个有效栅格图层
2025-07-07 10:53:08,860 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 10:53:08] "GET /api/query?lat=22.91338759179493&lon=108.45965484963584&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 10:53:11,209 - root - INFO - 开始查询坐标点 (22.698534507967437, 108.21904664785356) 在工作区 'test_myworkspace' 中的图层
2025-07-07 10:53:11,371 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 10:53:11,679 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 10:53:11,680 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 10:53:11,681 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 10:53:11,683 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 10:53:11,684 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 10:53:11,685 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 10:53:11,685 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 10:53:11,686 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 10:53:11,687 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 10:53:11,688 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 10:53:11,689 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 10:53:11,710 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 10:53:11,749 - root - INFO - 在图层 'id3' 中找到有效数据
2025-07-07 10:53:11,749 - root - INFO - 在坐标点 (22.698534507967437, 108.21904664785356) 处找到 2 个有效栅格图层
2025-07-07 10:53:11,751 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 10:53:11] "GET /api/query?lat=22.698534507967437&lon=108.21904664785356&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 10:53:13,518 - root - INFO - 开始查询坐标点 (22.682912402010956, 108.45218254523267) 在工作区 'test_myworkspace' 中的图层
2025-07-07 10:53:13,663 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 10:53:13,940 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 10:53:13,941 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 10:53:13,942 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 10:53:13,943 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 10:53:13,944 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 10:53:13,948 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 10:53:13,949 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 10:53:13,950 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 10:53:13,951 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 10:53:13,952 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 10:53:13,954 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 10:53:13,976 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 10:53:14,016 - root - INFO - 在图层 'id9' 中找到有效数据
2025-07-07 10:53:14,016 - root - INFO - 在坐标点 (22.682912402010956, 108.45218254523267) 处找到 2 个有效栅格图层
2025-07-07 10:53:14,018 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 10:53:14] "GET /api/query?lat=22.682912402010956&lon=108.45218254523267&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 10:53:17,368 - root - INFO - 开始查询坐标点 (22.906506537877917, 108.1976260418978) 在工作区 'test_myworkspace' 中的图层
2025-07-07 10:53:17,523 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 10:53:17,830 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 10:53:17,831 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 10:53:17,832 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 10:53:17,833 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 10:53:17,834 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 10:53:17,835 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 10:53:17,836 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 10:53:17,837 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 10:53:17,840 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 10:53:17,841 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 10:53:17,842 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 10:53:17,863 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 10:53:17,897 - root - INFO - 在图层 'id1' 中找到有效数据
2025-07-07 10:53:17,898 - root - INFO - 在坐标点 (22.906506537877917, 108.1976260418978) 处找到 2 个有效栅格图层
2025-07-07 10:53:17,901 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 10:53:17] "GET /api/query?lat=22.906506537877917&lon=108.1976260418978&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 10:53:25,395 - root - INFO - 开始查询坐标点 (22.884025995232975, 108.26288416701887) 在工作区 'test_myworkspace' 中的图层
2025-07-07 10:53:25,544 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 10:53:25,854 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 10:53:25,855 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 10:53:25,855 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 10:53:25,857 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 10:53:25,858 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 10:53:25,859 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 10:53:25,861 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 10:53:25,862 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 10:53:25,863 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 10:53:25,864 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 10:53:25,865 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 10:53:25,885 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 10:53:25,917 - root - INFO - 在图层 'id1' 中找到有效数据
2025-07-07 10:53:25,917 - root - INFO - 在坐标点 (22.884025995232975, 108.26288416701887) 处找到 2 个有效栅格图层
2025-07-07 10:53:25,917 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 10:53:25] "GET /api/query?lat=22.884025995232975&lon=108.26288416701887&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 10:56:33,476 - root - INFO - 开始查询坐标点 (22.811430327914934, 108.33330662761286) 在工作区 'test_myworkspace' 中的图层
2025-07-07 10:56:33,671 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 10:56:33,951 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 10:56:33,953 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 10:56:33,954 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 10:56:33,955 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 10:56:33,957 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 10:56:33,958 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 10:56:33,959 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 10:56:33,960 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 10:56:33,962 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 10:56:33,964 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 10:56:33,966 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 10:56:33,988 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 10:56:34,024 - root - INFO - 在图层 'id5' 中找到有效数据
2025-07-07 10:56:34,025 - root - INFO - 在坐标点 (22.811430327914934, 108.33330662761286) 处找到 2 个有效栅格图层
2025-07-07 10:56:34,026 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 10:56:34] "GET /api/query?lat=22.811430327914934&lon=108.33330662761286&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 10:56:37,587 - root - INFO - 开始查询坐标点 (22.906550594367644, 108.21215448577965) 在工作区 'test_myworkspace' 中的图层
2025-07-07 10:56:37,727 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 10:56:38,040 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 10:56:38,044 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 10:56:38,045 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 10:56:38,047 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 10:56:38,048 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 10:56:38,049 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 10:56:38,050 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 10:56:38,051 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 10:56:38,054 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 10:56:38,055 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 10:56:38,057 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 10:56:38,082 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 10:56:38,116 - root - INFO - 在图层 'id1' 中找到有效数据
2025-07-07 10:56:38,117 - root - INFO - 在坐标点 (22.906550594367644, 108.21215448577965) 处找到 2 个有效栅格图层
2025-07-07 10:56:38,118 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 10:56:38] "GET /api/query?lat=22.906550594367644&lon=108.21215448577965&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:00:58,712 - root - INFO - 开始查询坐标点 (22.797820674695203, 108.33901523085838) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:00:58,890 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:00:59,194 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:00:59,195 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:00:59,196 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:00:59,197 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:00:59,198 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:00:59,200 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:00:59,201 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:00:59,202 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:00:59,203 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:00:59,204 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:00:59,205 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:00:59,225 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:00:59,255 - root - INFO - 在图层 'id5' 中找到有效数据
2025-07-07 11:00:59,255 - root - INFO - 在坐标点 (22.797820674695203, 108.33901523085838) 处找到 2 个有效栅格图层
2025-07-07 11:00:59,257 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:00:59] "GET /api/query?lat=22.797820674695203&lon=108.33901523085838&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:01:09,434 - root - INFO - 开始查询坐标点 (22.90184629739663, 108.21070389088467) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:01:09,589 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:01:09,893 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:01:09,894 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:01:09,896 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:01:09,897 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:01:09,898 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:01:09,899 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:01:09,900 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:01:09,901 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:01:09,902 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:01:09,906 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:01:09,907 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:01:09,928 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:01:09,962 - root - INFO - 在图层 'id1' 中找到有效数据
2025-07-07 11:01:09,962 - root - INFO - 在坐标点 (22.90184629739663, 108.21070389088467) 处找到 2 个有效栅格图层
2025-07-07 11:01:09,963 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:01:09] "GET /api/query?lat=22.90184629739663&lon=108.21070389088467&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:01:23,143 - root - INFO - 开始查询坐标点 (22.91161431880154, 108.47245902443103) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:01:23,289 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:01:23,625 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:01:23,626 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:01:23,627 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:01:23,628 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:01:23,631 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:01:23,632 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:01:23,633 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:01:23,635 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:01:23,636 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:01:23,637 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:01:23,638 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:01:23,657 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:01:23,687 - root - INFO - 在图层 'id7' 中找到有效数据
2025-07-07 11:01:23,687 - root - INFO - 在坐标点 (22.91161431880154, 108.47245902443103) 处找到 2 个有效栅格图层
2025-07-07 11:01:23,688 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:01:23] "GET /api/query?lat=22.91161431880154&lon=108.47245902443103&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:01:30,802 - root - INFO - 开始查询坐标点 (22.799397413943453, 108.44405944785017) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:01:30,953 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:01:31,269 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:01:31,270 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:01:31,271 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:01:31,273 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:01:31,276 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:01:31,277 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:01:31,278 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:01:31,279 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:01:31,280 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:01:31,281 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:01:31,282 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:01:31,303 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:01:31,343 - root - INFO - 在图层 'id8' 中找到有效数据
2025-07-07 11:01:31,343 - root - INFO - 在坐标点 (22.799397413943453, 108.44405944785017) 处找到 2 个有效栅格图层
2025-07-07 11:01:31,345 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:01:31] "GET /api/query?lat=22.799397413943453&lon=108.44405944785017&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:01:52,230 - root - INFO - 开始查询坐标点 (22.89932206003725, 108.32783402708289) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:01:52,361 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:01:52,665 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:01:52,666 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:01:52,666 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:01:52,667 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:01:52,669 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:01:52,670 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:01:52,671 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:01:52,672 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:01:52,673 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:01:52,674 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:01:52,675 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:01:52,693 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:01:52,731 - root - INFO - 在图层 'id4' 中找到有效数据
2025-07-07 11:01:52,732 - root - INFO - 在坐标点 (22.89932206003725, 108.32783402708289) 处找到 2 个有效栅格图层
2025-07-07 11:01:52,733 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:01:52] "GET /api/query?lat=22.89932206003725&lon=108.32783402708289&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:02:25,648 - root - INFO - 开始查询坐标点 (22.8748176073577, 108.26791326655243) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:02:25,984 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:02:28,092 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:02:28,100 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:02:28,103 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:02:28,105 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:02:28,107 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:02:28,110 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:02:28,112 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:02:28,115 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:02:28,117 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:02:28,138 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:02:28,143 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:02:28,258 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:02:28,474 - root - INFO - 在图层 'id1' 中找到有效数据
2025-07-07 11:02:28,478 - root - INFO - 在坐标点 (22.8748176073577, 108.26791326655243) 处找到 2 个有效栅格图层
2025-07-07 11:02:28,480 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:02:28] "GET /api/query?lat=22.8748176073577&lon=108.26791326655243&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:02:35,034 - root - INFO - 开始查询坐标点 (22.783040217714515, 108.24259272755833) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:02:35,286 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:02:35,716 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:02:35,717 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:02:35,718 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:02:35,719 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:02:35,720 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:02:35,721 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:02:35,722 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:02:35,723 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:02:35,723 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:02:35,724 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:02:35,725 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:02:35,742 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:02:35,775 - root - INFO - 在图层 'id2' 中找到有效数据
2025-07-07 11:02:35,776 - root - INFO - 在坐标点 (22.783040217714515, 108.24259272755833) 处找到 2 个有效栅格图层
2025-07-07 11:02:35,778 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:02:35] "GET /api/query?lat=22.783040217714515&lon=108.24259272755833&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:02:38,424 - root - INFO - 开始查询坐标点 (22.979621481308868, 108.34430216254103) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:02:38,577 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:02:38,887 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:02:38,888 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:02:38,889 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:02:38,890 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:02:38,891 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:02:38,892 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:02:38,894 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:02:38,895 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:02:38,896 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:02:38,897 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:02:38,898 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:02:38,899 - root - INFO - 在坐标点 (22.979621481308868, 108.34430216254103) 处找到 0 个有效栅格图层
2025-07-07 11:02:38,901 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:02:38] "GET /api/query?lat=22.979621481308868&lon=108.34430216254103&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:02:41,425 - root - INFO - 开始查询坐标点 (22.726429981522458, 108.34267913964236) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:02:41,571 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:02:41,872 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:02:41,873 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:02:41,874 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:02:41,875 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:02:41,876 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:02:41,878 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:02:41,879 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:02:41,879 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:02:41,880 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:02:41,881 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:02:41,883 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:02:41,904 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:02:41,937 - root - INFO - 在图层 'id6' 中找到有效数据
2025-07-07 11:02:41,938 - root - INFO - 在坐标点 (22.726429981522458, 108.34267913964236) 处找到 2 个有效栅格图层
2025-07-07 11:02:41,939 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:02:41] "GET /api/query?lat=22.726429981522458&lon=108.34267913964236&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:02:44,152 - root - INFO - 开始查询坐标点 (22.70672309140683, 108.25206036113383) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:02:44,283 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:02:44,611 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:02:44,612 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:02:44,613 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:02:44,614 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:02:44,616 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:02:44,617 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:02:44,618 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:02:44,619 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:02:44,620 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:02:44,621 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:02:44,624 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:02:44,642 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:02:44,675 - root - INFO - 在图层 'id3' 中找到有效数据
2025-07-07 11:02:44,675 - root - INFO - 在坐标点 (22.70672309140683, 108.25206036113383) 处找到 2 个有效栅格图层
2025-07-07 11:02:44,676 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:02:44] "GET /api/query?lat=22.70672309140683&lon=108.25206036113383&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:02:46,605 - root - INFO - 开始查询坐标点 (22.72742772171489, 108.420584304819) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:02:46,743 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:02:47,064 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:02:47,065 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:02:47,066 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:02:47,068 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:02:47,071 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:02:47,072 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:02:47,073 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:02:47,074 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:02:47,074 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:02:47,075 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:02:47,077 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:02:47,095 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:02:47,128 - root - INFO - 在图层 'id9' 中找到有效数据
2025-07-07 11:02:47,128 - root - INFO - 在坐标点 (22.72742772171489, 108.420584304819) 处找到 2 个有效栅格图层
2025-07-07 11:02:47,130 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:02:47] "GET /api/query?lat=22.72742772171489&lon=108.420584304819&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:04:21,253 - root - INFO - 开始查询坐标点 (22.798815666116667, 108.32495302075513) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:04:21,380 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:04:21,662 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:04:21,663 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:04:21,664 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:04:21,668 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:04:21,668 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:04:21,669 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:04:21,670 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:04:21,671 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:04:21,672 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:04:21,672 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:04:21,674 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:04:21,692 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:04:21,720 - root - INFO - 在图层 'id5' 中找到有效数据
2025-07-07 11:04:21,720 - root - INFO - 在坐标点 (22.798815666116667, 108.32495302075513) 处找到 2 个有效栅格图层
2025-07-07 11:04:21,722 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:04:21] "GET /api/query?lat=22.798815666116667&lon=108.32495302075513&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:04:23,775 - root - INFO - 开始查询坐标点 (22.806560099878794, 108.83755052028977) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:04:23,904 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:04:24,235 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:04:24,236 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:04:24,237 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:04:24,239 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:04:24,240 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:04:24,242 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:04:24,243 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:04:24,246 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:04:24,247 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:04:24,247 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:04:24,248 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:04:24,249 - root - INFO - 在坐标点 (22.806560099878794, 108.83755052028977) 处找到 0 个有效栅格图层
2025-07-07 11:04:24,250 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:04:24] "GET /api/query?lat=22.806560099878794&lon=108.83755052028977&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:04:27,976 - root - INFO - 开始查询坐标点 (22.723930137963393, 108.22291372530677) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:04:28,110 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:04:28,468 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:04:28,469 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:04:28,471 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:04:28,472 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:04:28,474 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:04:28,475 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:04:28,476 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:04:28,477 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:04:28,478 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:04:28,480 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:04:28,487 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:04:28,546 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:04:28,580 - root - INFO - 在图层 'id3' 中找到有效数据
2025-07-07 11:04:28,581 - root - INFO - 在坐标点 (22.723930137963393, 108.22291372530677) 处找到 2 个有效栅格图层
2025-07-07 11:04:28,582 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:04:28] "GET /api/query?lat=22.723930137963393&lon=108.22291372530677&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:04:32,697 - root - INFO - 开始查询坐标点 (22.806191327298336, 108.23811958109907) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:04:32,831 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:04:33,098 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:04:33,099 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:04:33,100 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:04:33,104 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:04:33,105 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:04:33,106 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:04:33,108 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:04:33,109 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:04:33,110 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:04:33,112 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:04:33,113 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:04:33,133 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:04:33,163 - root - INFO - 在图层 'id2' 中找到有效数据
2025-07-07 11:04:33,163 - root - INFO - 在坐标点 (22.806191327298336, 108.23811958109907) 处找到 2 个有效栅格图层
2025-07-07 11:04:33,168 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:04:33] "GET /api/query?lat=22.806191327298336&lon=108.23811958109907&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:05:20,540 - root - INFO - 开始查询坐标点 (22.712859830254743, 108.29734238786912) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:05:20,675 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:05:20,969 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:05:20,970 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:05:20,971 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:05:20,972 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:05:20,973 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:05:20,974 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:05:20,975 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:05:20,977 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:05:20,978 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:05:20,979 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:05:20,980 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:05:20,998 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:05:21,026 - root - INFO - 在图层 'id6' 中找到有效数据
2025-07-07 11:05:21,027 - root - INFO - 在坐标点 (22.712859830254743, 108.29734238786912) 处找到 2 个有效栅格图层
2025-07-07 11:05:21,028 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:05:21] "GET /api/query?lat=22.712859830254743&lon=108.29734238786912&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:08:27,260 - root - INFO - 开始查询坐标点 (22.790766757667413, 108.32448190477815) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:08:27,399 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:08:27,670 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:08:27,671 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:08:27,672 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:08:27,673 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:08:27,673 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:08:27,675 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:08:27,676 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:08:27,677 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:08:27,678 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:08:27,678 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:08:27,682 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:08:27,698 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:08:27,733 - root - INFO - 在图层 'id5' 中找到有效数据
2025-07-07 11:08:27,734 - root - INFO - 在坐标点 (22.790766757667413, 108.32448190477815) 处找到 2 个有效栅格图层
2025-07-07 11:08:27,735 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:08:27] "GET /api/query?lat=22.790766757667413&lon=108.32448190477815&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:13:55,232 - root - INFO - 开始查询坐标点 (22.889287254308485, 108.20737625124643) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:13:55,384 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:13:55,680 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:13:55,681 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:13:55,683 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:13:55,684 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:13:55,685 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:13:55,686 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:13:55,687 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:13:55,687 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:13:55,688 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:13:55,689 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:13:55,690 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:13:55,709 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:13:55,737 - root - INFO - 在图层 'id1' 中找到有效数据
2025-07-07 11:13:55,738 - root - INFO - 在坐标点 (22.889287254308485, 108.20737625124643) 处找到 2 个有效栅格图层
2025-07-07 11:13:55,739 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:13:55] "GET /api/query?lat=22.889287254308485&lon=108.20737625124643&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:18:32,528 - root - INFO - 开始查询坐标点 (22.878194393909567, 108.29983088459359) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:18:32,664 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:18:33,004 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:18:33,005 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:18:33,006 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:18:33,008 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:18:33,009 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:18:33,010 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:18:33,011 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:18:33,014 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:18:33,017 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:18:33,018 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:18:33,020 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:18:33,039 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:18:33,074 - root - INFO - 在图层 'id4' 中找到有效数据
2025-07-07 11:18:33,075 - root - INFO - 在坐标点 (22.878194393909567, 108.29983088459359) 处找到 2 个有效栅格图层
2025-07-07 11:18:33,077 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:18:33] "GET /api/query?lat=22.878194393909567&lon=108.29983088459359&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:19:11,175 - root - INFO - 开始查询坐标点 (22.839781258149728, 108.45393176159519) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:19:11,328 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:19:11,659 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:19:11,660 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:19:11,661 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:19:11,662 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:19:11,663 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:19:11,666 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:19:11,667 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:19:11,668 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:19:11,669 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:19:11,670 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:19:11,671 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:19:11,690 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:19:11,724 - root - INFO - 在图层 'id8' 中找到有效数据
2025-07-07 11:19:11,725 - root - INFO - 在坐标点 (22.839781258149728, 108.45393176159519) 处找到 2 个有效栅格图层
2025-07-07 11:19:11,725 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:19:11] "GET /api/query?lat=22.839781258149728&lon=108.45393176159519&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:19:19,708 - root - INFO - 开始查询坐标点 (22.89947875116019, 108.45936634101263) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:19:19,847 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:19:20,148 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:19:20,152 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:19:20,153 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:19:20,154 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:19:20,155 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:19:20,156 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:19:20,157 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:19:20,158 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:19:20,160 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:19:20,161 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:19:20,163 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:19:20,182 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:19:20,215 - root - INFO - 在图层 'id7' 中找到有效数据
2025-07-07 11:19:20,215 - root - INFO - 在坐标点 (22.89947875116019, 108.45936634101263) 处找到 2 个有效栅格图层
2025-07-07 11:19:20,217 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:19:20] "GET /api/query?lat=22.89947875116019&lon=108.45936634101263&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:21:04,139 - root - INFO - 开始查询坐标点 (22.77957162693521, 108.27138808891902) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:21:04,325 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:21:04,607 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:21:04,608 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:21:04,608 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:21:04,609 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:21:04,610 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:21:04,611 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:21:04,613 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:21:04,614 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:21:04,615 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:21:04,616 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:21:04,618 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:21:04,639 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:21:04,674 - root - INFO - 在图层 'id2' 中找到有效数据
2025-07-07 11:21:04,674 - root - INFO - 在坐标点 (22.77957162693521, 108.27138808891902) 处找到 2 个有效栅格图层
2025-07-07 11:21:04,676 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:21:04] "GET /api/query?lat=22.77957162693521&lon=108.27138808891902&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:21:19,207 - root - INFO - 开始查询坐标点 (22.70104614801933, 108.38814868164057) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:21:19,338 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:21:19,670 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:21:19,671 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:21:19,672 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:21:19,673 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:21:19,673 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:21:19,674 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:21:19,675 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:21:19,676 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:21:19,677 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:21:19,678 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:21:19,680 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:21:19,697 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:21:19,724 - root - INFO - 在图层 'id6' 中找到有效数据
2025-07-07 11:21:19,724 - root - INFO - 在坐标点 (22.70104614801933, 108.38814868164057) 处找到 2 个有效栅格图层
2025-07-07 11:21:19,725 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:21:19] "GET /api/query?lat=22.70104614801933&lon=108.38814868164057&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:23:11,698 - root - INFO - 开始查询坐标点 (22.908558614193353, 108.3686048937171) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:23:11,910 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:23:12,861 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:23:12,863 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:23:12,865 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:23:12,867 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:23:12,869 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:23:12,870 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:23:12,871 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:23:12,872 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:23:12,873 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:23:12,874 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:23:12,875 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:23:12,894 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:23:12,931 - root - INFO - 在图层 'id4' 中找到有效数据
2025-07-07 11:23:12,933 - root - INFO - 在坐标点 (22.908558614193353, 108.3686048937171) 处找到 2 个有效栅格图层
2025-07-07 11:23:12,935 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:23:12] "GET /api/query?lat=22.908558614193353&lon=108.3686048937171&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:23:30,945 - root - INFO - 开始查询坐标点 (22.909822676626206, 108.21608182017096) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:23:31,083 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:23:31,423 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:23:31,425 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:23:31,426 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:23:31,427 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:23:31,429 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:23:31,431 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:23:31,433 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:23:31,434 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:23:31,435 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:23:31,436 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:23:31,438 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:23:31,464 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:23:31,498 - root - INFO - 在图层 'id1' 中找到有效数据
2025-07-07 11:23:31,499 - root - INFO - 在坐标点 (22.909822676626206, 108.21608182017096) 处找到 2 个有效栅格图层
2025-07-07 11:23:31,500 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 11:23:31] "GET /api/query?lat=22.909822676626206&lon=108.21608182017096&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:32:30,829 - root - INFO - 开始查询坐标点 (22.804611745362706, 108.38900781925045) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:32:31,022 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:32:31,331 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:32:31,332 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:32:31,333 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:32:31,334 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:32:31,335 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:32:31,338 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:32:31,340 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:32:31,341 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:32:31,342 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:32:31,342 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:32:31,344 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:32:31,365 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:32:31,394 - root - INFO - 在图层 'id5' 中找到有效数据
2025-07-07 11:32:31,394 - root - INFO - 在坐标点 (22.804611745362706, 108.38900781925045) 处找到 2 个有效栅格图层
2025-07-07 11:32:31,395 - werkzeug - INFO - ************** - - [07/Jul/2025 11:32:31] "GET /api/query?lat=22.804611745362706&lon=108.38900781925045&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:32:39,064 - root - INFO - 开始查询坐标点 (22.807728663626364, 108.31675270626282) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:32:39,233 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:32:39,523 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:32:39,526 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:32:39,527 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:32:39,528 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:32:39,529 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:32:39,530 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:32:39,531 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:32:39,532 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:32:39,533 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:32:39,534 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:32:39,535 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:32:39,555 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:32:39,586 - root - INFO - 在图层 'id5' 中找到有效数据
2025-07-07 11:32:39,587 - root - INFO - 在坐标点 (22.807728663626364, 108.31675270626282) 处找到 2 个有效栅格图层
2025-07-07 11:32:39,590 - werkzeug - INFO - ************** - - [07/Jul/2025 11:32:39] "GET /api/query?lat=22.807728663626364&lon=108.31675270626282&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:34:50,592 - root - INFO - 开始查询坐标点 (22.76237214675514, 108.2992873277904) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:34:50,741 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:34:51,055 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:34:51,056 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:34:51,058 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:34:51,061 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:34:51,062 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:34:51,063 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:34:51,065 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:34:51,066 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:34:51,067 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:34:51,070 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:34:51,071 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:34:51,091 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:34:51,128 - root - INFO - 在图层 'id5' 中找到有效数据
2025-07-07 11:34:51,129 - root - INFO - 在坐标点 (22.76237214675514, 108.2992873277904) 处找到 2 个有效栅格图层
2025-07-07 11:34:51,130 - werkzeug - INFO - ************** - - [07/Jul/2025 11:34:51] "GET /api/query?lat=22.76237214675514&lon=108.2992873277904&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:34:53,511 - root - INFO - 开始查询坐标点 (22.792870666048856, 108.29919490633566) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:34:53,664 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:34:53,946 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:34:53,951 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:34:53,952 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:34:53,953 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:34:53,954 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:34:53,955 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:34:53,955 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:34:53,956 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:34:53,957 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:34:53,958 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:34:53,959 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:34:53,977 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:34:54,005 - root - INFO - 在图层 'id5' 中找到有效数据
2025-07-07 11:34:54,006 - root - INFO - 在坐标点 (22.792870666048856, 108.29919490633566) 处找到 2 个有效栅格图层
2025-07-07 11:34:54,007 - werkzeug - INFO - ************** - - [07/Jul/2025 11:34:54] "GET /api/query?lat=22.792870666048856&lon=108.29919490633566&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:34:55,561 - root - INFO - 开始查询坐标点 (22.837158041196545, 108.29318751177787) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:34:55,691 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:34:56,029 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:34:56,030 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:34:56,031 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:34:56,032 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:34:56,033 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:34:56,034 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:34:56,035 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:34:56,037 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:34:56,038 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:34:56,039 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:34:56,041 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:34:56,059 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:34:56,087 - root - INFO - 在图层 'id5' 中找到有效数据
2025-07-07 11:34:56,090 - root - INFO - 在坐标点 (22.837158041196545, 108.29318751177787) 处找到 2 个有效栅格图层
2025-07-07 11:34:56,091 - werkzeug - INFO - ************** - - [07/Jul/2025 11:34:56] "GET /api/query?lat=22.837158041196545&lon=108.29318751177787&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:34:57,503 - root - INFO - 开始查询坐标点 (22.78230770480701, 108.41194908111272) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:34:57,640 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:34:57,954 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:34:57,955 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:34:57,958 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:34:57,959 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:34:57,960 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:34:57,960 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:34:57,963 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:34:57,965 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:34:57,966 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:34:57,966 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:34:57,968 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:34:57,984 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:34:58,019 - root - INFO - 在图层 'id8' 中找到有效数据
2025-07-07 11:34:58,020 - root - INFO - 在坐标点 (22.78230770480701, 108.41194908111272) 处找到 2 个有效栅格图层
2025-07-07 11:34:58,023 - werkzeug - INFO - ************** - - [07/Jul/2025 11:34:58] "GET /api/query?lat=22.78230770480701&lon=108.41194908111272&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:35:02,755 - root - INFO - 开始查询坐标点 (22.859159088481746, 108.34824969061611) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:35:03,073 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:35:04,345 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:35:04,346 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:35:04,347 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:35:04,348 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:35:04,349 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:35:04,349 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:35:04,350 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:35:04,351 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:35:04,351 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:35:04,352 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:35:04,354 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:35:04,369 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:35:04,396 - root - INFO - 在图层 'id4' 中找到有效数据
2025-07-07 11:35:04,396 - root - INFO - 在坐标点 (22.859159088481746, 108.34824969061611) 处找到 2 个有效栅格图层
2025-07-07 11:35:04,397 - werkzeug - INFO - ************** - - [07/Jul/2025 11:35:04] "GET /api/query?lat=22.859159088481746&lon=108.34824969061611&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 11:35:08,950 - root - INFO - 开始查询坐标点 (22.888644708563262, 108.44301509986892) 在工作区 'test_myworkspace' 中的图层
2025-07-07 11:35:09,239 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 11:35:11,231 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 11:35:11,240 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 11:35:11,247 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 11:35:11,250 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 11:35:11,252 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 11:35:11,254 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 11:35:11,258 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 11:35:11,266 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 11:35:11,268 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 11:35:11,270 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 11:35:11,272 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 11:35:11,334 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 11:35:11,421 - root - INFO - 在图层 'id7' 中找到有效数据
2025-07-07 11:35:11,422 - root - INFO - 在坐标点 (22.888644708563262, 108.44301509986892) 处找到 2 个有效栅格图层
2025-07-07 11:35:11,427 - werkzeug - INFO - ************** - - [07/Jul/2025 11:35:11] "GET /api/query?lat=22.888644708563262&lon=108.44301509986892&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 15:07:25,461 - root - INFO - 开始查询坐标点 (22.84478360753893, 108.3072007796976) 在工作区 'test_myworkspace' 中的图层
2025-07-07 15:07:25,747 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 15:07:26,125 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 15:07:26,127 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 15:07:26,129 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 15:07:26,131 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 15:07:26,133 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 15:07:26,134 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 15:07:26,135 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 15:07:26,137 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 15:07:26,139 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 15:07:26,140 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 15:07:26,143 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 15:07:26,163 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 15:07:26,187 - root - INFO - 在图层 'id4' 中找到有效数据
2025-07-07 15:07:26,188 - root - INFO - 在坐标点 (22.84478360753893, 108.3072007796976) 处找到 2 个有效栅格图层
2025-07-07 15:07:26,189 - werkzeug - INFO - ************** - - [07/Jul/2025 15:07:26] "GET /api/query?lat=22.84478360753893&lon=108.3072007796976&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 15:07:31,491 - root - INFO - 开始查询坐标点 (22.877051813277745, 108.43598084519931) 在工作区 'test_myworkspace' 中的图层
2025-07-07 15:07:31,646 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 15:07:31,974 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 15:07:31,975 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 15:07:31,977 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 15:07:31,978 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 15:07:31,979 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 15:07:31,980 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 15:07:31,982 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 15:07:31,985 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 15:07:31,987 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 15:07:31,989 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 15:07:31,992 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 15:07:32,019 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 15:07:32,050 - root - INFO - 在图层 'id7' 中找到有效数据
2025-07-07 15:07:32,051 - root - INFO - 在坐标点 (22.877051813277745, 108.43598084519931) 处找到 2 个有效栅格图层
2025-07-07 15:07:32,052 - werkzeug - INFO - ************** - - [07/Jul/2025 15:07:32] "GET /api/query?lat=22.877051813277745&lon=108.43598084519931&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 15:07:40,259 - root - INFO - 开始查询坐标点 (22.80079792235246, 108.35012746819818) 在工作区 'test_myworkspace' 中的图层
2025-07-07 15:07:40,413 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 15:07:40,738 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 15:07:40,739 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 15:07:40,740 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 15:07:40,741 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 15:07:40,742 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 15:07:40,742 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 15:07:40,743 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 15:07:40,744 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 15:07:40,744 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 15:07:40,746 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 15:07:40,748 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 15:07:40,770 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 15:07:40,806 - root - INFO - 在图层 'id5' 中找到有效数据
2025-07-07 15:07:40,807 - root - INFO - 在坐标点 (22.80079792235246, 108.35012746819818) 处找到 2 个有效栅格图层
2025-07-07 15:07:40,809 - werkzeug - INFO - ************** - - [07/Jul/2025 15:07:40] "GET /api/query?lat=22.80079792235246&lon=108.35012746819818&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 15:07:42,690 - root - INFO - 开始查询坐标点 (22.76534588414232, 108.21963033515645) 在工作区 'test_myworkspace' 中的图层
2025-07-07 15:07:42,831 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 15:07:43,096 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 15:07:43,098 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 15:07:43,098 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 15:07:43,099 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 15:07:43,100 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 15:07:43,101 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 15:07:43,102 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 15:07:43,103 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 15:07:43,104 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 15:07:43,107 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 15:07:43,109 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 15:07:43,134 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 15:07:43,168 - root - INFO - 在图层 'id2' 中找到有效数据
2025-07-07 15:07:43,169 - root - INFO - 在坐标点 (22.76534588414232, 108.21963033515645) 处找到 2 个有效栅格图层
2025-07-07 15:07:43,170 - werkzeug - INFO - ************** - - [07/Jul/2025 15:07:43] "GET /api/query?lat=22.76534588414232&lon=108.21963033515645&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 15:07:45,043 - root - INFO - 开始查询坐标点 (22.716901053974368, 108.24160879966874) 在工作区 'test_myworkspace' 中的图层
2025-07-07 15:07:45,197 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 15:07:45,510 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 15:07:45,510 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 15:07:45,514 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 15:07:45,515 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 15:07:45,515 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 15:07:45,517 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 15:07:45,518 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 15:07:45,520 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 15:07:45,521 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 15:07:45,522 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 15:07:45,524 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 15:07:45,544 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 15:07:45,581 - root - INFO - 在图层 'id3' 中找到有效数据
2025-07-07 15:07:45,581 - root - INFO - 在坐标点 (22.716901053974368, 108.24160879966874) 处找到 2 个有效栅格图层
2025-07-07 15:07:45,582 - werkzeug - INFO - ************** - - [07/Jul/2025 15:07:45] "GET /api/query?lat=22.716901053974368&lon=108.24160879966874&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 15:07:50,251 - root - INFO - 开始查询坐标点 (22.696314706826982, 108.44800031797948) 在工作区 'test_myworkspace' 中的图层
2025-07-07 15:07:50,410 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 15:07:50,729 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 15:07:50,730 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 15:07:50,731 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 15:07:50,732 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 15:07:50,732 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 15:07:50,733 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 15:07:50,734 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 15:07:50,734 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 15:07:50,736 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 15:07:50,737 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 15:07:50,738 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 15:07:50,765 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 15:07:50,801 - root - INFO - 在图层 'id9' 中找到有效数据
2025-07-07 15:07:50,801 - root - INFO - 在坐标点 (22.696314706826982, 108.44800031797948) 处找到 2 个有效栅格图层
2025-07-07 15:07:50,802 - werkzeug - INFO - ************** - - [07/Jul/2025 15:07:50] "GET /api/query?lat=22.696314706826982&lon=108.44800031797948&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 15:08:10,524 - root - INFO - 开始查询坐标点 (22.796758673814765, 108.45610344252626) 在工作区 'test_myworkspace' 中的图层
2025-07-07 15:08:10,673 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 15:08:11,040 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 15:08:11,043 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 15:08:11,046 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 15:08:11,047 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 15:08:11,048 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 15:08:11,048 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 15:08:11,049 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 15:08:11,050 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 15:08:11,052 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 15:08:11,054 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 15:08:11,055 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 15:08:11,076 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 15:08:11,105 - root - INFO - 在图层 'id8' 中找到有效数据
2025-07-07 15:08:11,105 - root - INFO - 在坐标点 (22.796758673814765, 108.45610344252626) 处找到 2 个有效栅格图层
2025-07-07 15:08:11,107 - werkzeug - INFO - ************** - - [07/Jul/2025 15:08:11] "GET /api/query?lat=22.796758673814765&lon=108.45610344252626&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 15:08:24,911 - root - INFO - 开始查询坐标点 (22.73093397649201, 108.33252429940299) 在工作区 'test_myworkspace' 中的图层
2025-07-07 15:08:25,207 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 15:08:28,126 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 15:08:28,282 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 15:08:28,285 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 15:08:28,294 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 15:08:28,323 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 15:08:28,366 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 15:08:28,376 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 15:08:28,382 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 15:08:28,413 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 15:08:28,421 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 15:08:28,424 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 15:08:28,484 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 15:08:28,819 - root - INFO - 在图层 'id6' 中找到有效数据
2025-07-07 15:08:28,821 - root - INFO - 在坐标点 (22.73093397649201, 108.33252429940299) 处找到 2 个有效栅格图层
2025-07-07 15:08:28,823 - werkzeug - INFO - ************** - - [07/Jul/2025 15:08:28] "GET /api/query?lat=22.73093397649201&lon=108.33252429940299&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 16:08:51,817 - root - INFO - 开始查询坐标点 (22.78390374463008, 108.31970509281155) 在工作区 'test_myworkspace' 中的图层
2025-07-07 16:08:51,997 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 16:08:52,260 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 16:08:52,261 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 16:08:52,263 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 16:08:52,263 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 16:08:52,264 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 16:08:52,265 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 16:08:52,266 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 16:08:52,266 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 16:08:52,267 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 16:08:52,268 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 16:08:52,269 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 16:08:52,287 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 16:08:52,327 - root - INFO - 在图层 'id5' 中找到有效数据
2025-07-07 16:08:52,328 - root - INFO - 在坐标点 (22.78390374463008, 108.31970509281155) 处找到 2 个有效栅格图层
2025-07-07 16:08:52,329 - werkzeug - INFO - ************** - - [07/Jul/2025 16:08:52] "GET /api/query?lat=22.78390374463008&lon=108.31970509281155&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 16:23:42,661 - root - INFO - 开始查询坐标点 (22.815259841119385, 108.31506172299497) 在工作区 'test_myworkspace' 中的图层
2025-07-07 16:23:42,855 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 16:23:43,154 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 16:23:43,156 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 16:23:43,157 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 16:23:43,158 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 16:23:43,159 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 16:23:43,161 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 16:23:43,163 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 16:23:43,164 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 16:23:43,165 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 16:23:43,166 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 16:23:43,167 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 16:23:43,185 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 16:23:43,218 - root - INFO - 在图层 'id5' 中找到有效数据
2025-07-07 16:23:43,218 - root - INFO - 在坐标点 (22.815259841119385, 108.31506172299497) 处找到 2 个有效栅格图层
2025-07-07 16:23:43,219 - werkzeug - INFO - ************** - - [07/Jul/2025 16:23:43] "GET /api/query?lat=22.815259841119385&lon=108.31506172299497&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 16:23:52,826 - root - INFO - 开始查询坐标点 (22.8067040896167, 108.44091352643649) 在工作区 'test_myworkspace' 中的图层
2025-07-07 16:23:52,953 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 16:23:53,252 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 16:23:53,255 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 16:23:53,256 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 16:23:53,259 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 16:23:53,260 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 16:23:53,261 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 16:23:53,262 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 16:23:53,263 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 16:23:53,264 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 16:23:53,264 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 16:23:53,265 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 16:23:53,282 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 16:23:53,311 - root - INFO - 在图层 'id8' 中找到有效数据
2025-07-07 16:23:53,311 - root - INFO - 在坐标点 (22.8067040896167, 108.44091352643649) 处找到 2 个有效栅格图层
2025-07-07 16:23:53,313 - werkzeug - INFO - ************** - - [07/Jul/2025 16:23:53] "GET /api/query?lat=22.8067040896167&lon=108.44091352643649&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 16:28:08,882 - root - INFO - 开始查询坐标点 (22.807654756818636, 108.24568841496274) 在工作区 'test_myworkspace' 中的图层
2025-07-07 16:28:09,058 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 16:28:09,381 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 16:28:09,382 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 16:28:09,383 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 16:28:09,384 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 16:28:09,386 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 16:28:09,389 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 16:28:09,390 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 16:28:09,391 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 16:28:09,392 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 16:28:09,392 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 16:28:09,394 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 16:28:09,411 - root - INFO - 在图层 'nanning' 中找到有效数据
2025-07-07 16:28:09,439 - root - INFO - 在图层 'id2' 中找到有效数据
2025-07-07 16:28:09,440 - root - INFO - 在坐标点 (22.807654756818636, 108.24568841496274) 处找到 2 个有效栅格图层
2025-07-07 16:28:09,441 - werkzeug - INFO - ************** - - [07/Jul/2025 16:28:09] "GET /api/query?lat=22.807654756818636&lon=108.24568841496274&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 16:34:20,462 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 16:34:20] "[33mGET /api/pixel_value?lat=22.77912&lon=108.36502&workspace=test_myworkspace&layer=nanning HTTP/1.1[0m" 404 -
