2025-07-14 10:41:50,713 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250714_104150.log
2025-07-14 10:41:50,717 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 10:41:50,717 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 10:41:51,013 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 10:41:51,250 - batch_executor - INFO - 加载了 13 个任务状态
2025-07-14 10:41:51,269 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 10:41:51,271 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 10:41:51,312 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 10:41:51,323 - root - INFO - === GeoServer REST API服务 ===
2025-07-14 10:41:51,325 - root - INFO - 主机: 0.0.0.0
2025-07-14 10:41:51,326 - root - INFO - 端口: 5083
2025-07-14 10:41:51,326 - root - INFO - 调试模式: 禁用
2025-07-14 10:41:51,330 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-14 10:41:51,385 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-14 10:41:51,388 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
