# GeoServer异步发布API文档

GeoServer异步发布API模块提供Shapefile和GeoTIFF文件的异步发布功能，支持单文件发布、批量目录发布和结构化数据发布。

## 模块概述

GeoServer异步发布模块是系统的数据发布核心，提供：
- **异步发布**：Shapefile和GeoTIFF文件的后台异步发布
- **批量操作**：目录批量发布和结构化数据发布
- **任务管理**：发布任务的状态监控和进度跟踪
- **错误处理**：发布失败的错误信息和重试机制
- **日志记录**：详细的发布过程日志和结果统计

## API端点列表

### Shapefile异步发布

#### 1. 异步发布单个Shapefile

**端点**: `GET /api/geo/shapefile/execute/`

**描述**: 异步发布单个Shapefile文件到GeoServer

**参数**:
- `path` (必选): Shapefile文件路径
- `workspace` (必选): 目标工作区名称
- `store` (可选): 存储名称，默认使用文件名
- `layer` (可选): 图层名称，默认使用文件名
- `charset` (可选): 字符集，默认为UTF-8
- `srs` (可选): 空间参考系统，如EPSG:4326

**请求示例**:
```bash
# 基本发布
curl "http://127.0.0.1:5000/api/geo/shapefile/execute/?path=D:/data/boundaries.shp&workspace=test"

# 指定完整参数
curl "http://127.0.0.1:5000/api/geo/shapefile/execute/?path=D:/data/roads.shp&workspace=transport&store=road_network&layer=main_roads&charset=UTF-8&srs=EPSG:4326"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "Shapefile发布任务已启动",
  "task_id": "aa1bb2cc-33dd-44ee-55ff-666677778888"
}
```

#### 2. 异步发布Shapefile目录

**端点**: `GET /api/geo/shapefile-directory/execute/`

**描述**: 异步批量发布目录中的所有Shapefile文件

**参数**:
- `directory` (必选): Shapefile目录路径
- `workspace` (必选): 目标工作区名称
- `store_prefix` (可选): 存储名称前缀，默认为shapefiles
- `charset` (可选): 字符集，默认为UTF-8
- `recursive` (可选): 是否递归搜索子目录，默认为false

**请求示例**:
```bash
# 基本目录发布
curl "http://127.0.0.1:5000/api/geo/shapefile-directory/execute/?directory=D:/shapefiles/&workspace=test"

# 递归发布所有子目录
curl "http://127.0.0.1:5000/api/geo/shapefile-directory/execute/?directory=D:/gis_data/&workspace=complete_dataset&recursive=true&charset=UTF-8"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "Shapefile目录发布任务已启动",
  "task_id": "bb2cc3dd-44ee-55ff-6677-778899aabbcc"
}
```

### GeoTIFF异步发布

#### 3. 异步发布单个GeoTIFF

**端点**: `GET /api/geo/geotiff/execute/`

**描述**: 异步发布单个GeoTIFF文件到GeoServer

**参数**:
- `path` (必选): GeoTIFF文件路径
- `workspace` (必选): 目标工作区名称
- `store` (可选): 存储名称，默认使用文件名
- `layer` (可选): 图层名称，默认使用文件名
- `coverage_name` (可选): 覆盖层名称，默认使用文件名

**请求示例**:
```bash
# 基本发布
curl "http://127.0.0.1:5000/api/geo/geotiff/execute/?path=D:/data/elevation.tif&workspace=terrain"

# 指定完整参数
curl "http://127.0.0.1:5000/api/geo/geotiff/execute/?path=D:/data/ortho.tif&workspace=imagery&store=orthophoto_2024&layer=ortho_jan&coverage_name=january_orthophoto"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "GeoTIFF发布任务已启动",
  "task_id": "cc3dd4ee-55ff-6677-7788-99aabbccddee"
}
```

#### 4. 异步发布GeoTIFF目录

**端点**: `GET /api/geo/geotiff-directory/execute/`

**描述**: 异步批量发布目录中的所有GeoTIFF文件

**参数**:
- `directory` (必选): GeoTIFF目录路径
- `workspace` (必选): 目标工作区名称
- `store_prefix` (可选): 存储名称前缀，默认为geotiffs
- `recursive` (可选): 是否递归搜索子目录，默认为false

**请求示例**:
```bash
# 基本目录发布
curl "http://127.0.0.1:5000/api/geo/geotiff-directory/execute/?directory=D:/rasters/&workspace=raster_data"

# 递归发布
curl "http://127.0.0.1:5000/api/geo/geotiff-directory/execute/?directory=D:/satellite_imagery/&workspace=satellite&recursive=true"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "GeoTIFF目录发布任务已启动",
  "task_id": "dd4ee5ff-6677-7788-99aa-bbccddeeaabb"
}
```

#### 5. 异步发布结构化GeoTIFF

**端点**: `GET /api/geo/structured-geotiff/execute/`

**描述**: 异步发布结构化组织的GeoTIFF数据集

**参数**:
- `root_directory` (必选): 根目录路径
- `workspace` (必选): 目标工作区名称
- `structure_type` (可选): 结构类型，默认为auto
- `naming_pattern` (可选): 命名模式，默认为directory_based

**请求示例**:
```bash
# 基本结构化发布
curl "http://127.0.0.1:5000/api/geo/structured-geotiff/execute/?root_directory=D:/structured_data/&workspace=structured_dataset"

# 指定结构类型
curl "http://127.0.0.1:5000/api/geo/structured-geotiff/execute/?root_directory=D:/time_series/&workspace=temporal_data&structure_type=temporal&naming_pattern=date_based"
```

**成功响应**:
```json
{
  "status": "success",
  "message": "结构化GeoTIFF发布任务已启动",
  "task_id": "ee5ff6aa-7788-99aa-bbcc-ddeeaabbccdd"
}
```

### 任务管理

#### 6. 查询发布任务状态

**端点**: `GET /api/geo/status/`

**描述**: 查询GeoServer发布任务的执行状态和进度

**参数**:
- `task_id` (必选): 任务ID

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/geo/status/?task_id=aa1bb2cc-33dd-44ee-55ff-666677778888"
```

**成功响应**:
```json
{
  "status": "success",
  "task": {
    "id": "aa1bb2cc-33dd-44ee-55ff-666677778888",
    "type": "shapefile",
    "file_path": "D:/data/boundaries.shp",
    "workspace": "test",
    "params": {
      "store": "boundaries",
      "layer": "boundaries",
      "charset": "UTF-8"
    },
    "status": "running",
    "progress": 75,
    "start_time": "2024-01-15T16:30:00.123456",
    "end_time": null,
    "current_step": "创建图层样式",
    "published_layers": [
      "test:boundaries"
    ],
    "total_files": 1,
    "processed_files": 0,
    "failed_files": 0,
    "log_file": "D:\\Drone_Project\\geoserverAPIDJ\\geoserverRest\\geolog\\aa1bb2cc-33dd-44ee-55ff-666677778888.log"
  }
}
```

**任务状态说明**:
- `pending`: 任务等待中
- `running`: 任务执行中
- `completed`: 任务完成
- `failed`: 任务失败
- `partial`: 部分成功（批量操作中部分文件失败）

#### 7. 获取发布任务日志

**端点**: `GET /api/geo/log/`

**描述**: 获取GeoServer发布任务的详细执行日志

**参数**:
- `task_id` (必选): 任务ID

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/geo/log/?task_id=aa1bb2cc-33dd-44ee-55ff-666677778888"
```

**成功响应**:
```json
{
  "status": "success",
  "task_id": "aa1bb2cc-33dd-44ee-55ff-666677778888",
  "log": "开始GeoServer发布任务: aa1bb2cc-33dd-44ee-55ff-666677778888\n任务类型: shapefile\n文件路径: D:/data/boundaries.shp\n目标工作区: test\n开始时间: 2024-01-15T16:30:00.123456\n--------------------------------------------------\n步骤 1/4: 验证文件完整性\n检查文件: D:/data/boundaries.shp\n检查文件: D:/data/boundaries.shx\n检查文件: D:/data/boundaries.dbf\n检查文件: D:/data/boundaries.prj\n✅ 所有必需文件存在\n--------------------------------------------------\n步骤 2/4: 创建数据存储\n数据存储名称: boundaries\n工作区: test\n✅ 数据存储创建成功\n--------------------------------------------------\n步骤 3/4: 发布图层\n图层名称: boundaries\n几何类型: Polygon\n要素数量: 1,234\n边界框: [116.0, 39.0, 117.0, 40.0]\n✅ 图层发布成功\n--------------------------------------------------\n步骤 4/4: 创建图层样式\n样式名称: boundaries_style\n样式类型: SLD\n✅ 样式创建成功\n--------------------------------------------------\n发布完成!\n发布图层: test:boundaries\n总处理时间: 45.2秒\n结束时间: 2024-01-15T16:30:45.345678"
}
```

#### 8. 获取所有发布任务

**端点**: `GET /api/geo/all/`

**描述**: 获取所有GeoServer发布任务的状态列表

**参数**: 无

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/geo/all/"
```

**成功响应**:
```json
{
  "status": "success",
  "count": 5,
  "tasks": [
    {
      "id": "aa1bb2cc-33dd-44ee-55ff-666677778888",
      "type": "shapefile",
      "file_path": "D:/data/boundaries.shp",
      "workspace": "test",
      "status": "completed",
      "progress": 100,
      "start_time": "2024-01-15T16:30:00.123456",
      "end_time": "2024-01-15T16:30:45.345678",
      "published_layers": ["test:boundaries"]
    },
    {
      "id": "bb2cc3dd-44ee-55ff-6677-778899aabbcc",
      "type": "shapefile_directory",
      "file_path": "D:/shapefiles/",
      "workspace": "test",
      "status": "running",
      "progress": 60,
      "start_time": "2024-01-15T16:45:00.000000",
      "end_time": null,
      "total_files": 5,
      "processed_files": 3,
      "failed_files": 0
    },
    {
      "id": "cc3dd4ee-55ff-6677-7788-99aabbccddee",
      "type": "geotiff",
      "file_path": "D:/data/elevation.tif",
      "workspace": "terrain",
      "status": "failed",
      "progress": 25,
      "start_time": "2024-01-15T15:30:00.000000",
      "end_time": "2024-01-15T15:31:30.000000",
      "error": "文件损坏或格式不支持"
    },
    {
      "id": "dd4ee5ff-6677-7788-99aa-bbccddeeaabb",
      "type": "geotiff_directory",
      "file_path": "D:/rasters/",
      "workspace": "raster_data",
      "status": "partial",
      "progress": 100,
      "start_time": "2024-01-15T14:00:00.000000",
      "end_time": "2024-01-15T14:15:30.000000",
      "total_files": 10,
      "processed_files": 8,
      "failed_files": 2
    },
    {
      "id": "ee5ff6aa-7788-99aa-bbcc-ddeeaabbccdd",
      "type": "structured_geotiff",
      "file_path": "D:/structured_data/",
      "workspace": "structured_dataset",
      "status": "pending",
      "progress": 0,
      "start_time": null,
      "end_time": null
    }
  ]
}
```

## 错误处理

### 常见错误响应

**参数缺失**:
```json
{
  "status": "error",
  "message": "缺少必要参数: path, workspace"
}
```

**文件不存在**:
```json
{
  "status": "error",
  "message": "文件不存在: D:/data/nonexistent.shp"
}
```

**目录不存在**:
```json
{
  "status": "error",
  "message": "目录不存在: D:/nonexistent_directory/"
}
```

**工作区不存在**:
```json
{
  "status": "error",
  "message": "工作区 'nonexistent_workspace' 不存在"
}
```

**GeoServer连接失败**:
```json
{
  "status": "error",
  "message": "无法连接到GeoServer: Connection refused"
}
```

**任务不存在**:
```json
{
  "status": "error",
  "message": "任务 aa1bb2cc-33dd-44ee-55ff-666677778888 不存在"
}
```

## 使用示例

### Python客户端示例

```python
import requests
import time
import os

class GeoServerPublisherClient:
    def __init__(self, base_url):
        self.base_url = base_url
    
    def publish_shapefile(self, file_path, workspace, **options):
        """异步发布Shapefile"""
        url = f"{self.base_url}/api/geo/shapefile/execute/"
        params = {
            'path': file_path,
            'workspace': workspace,
            **options
        }
        response = requests.get(url, params=params)
        return response.json()
    
    def publish_shapefile_directory(self, directory, workspace, **options):
        """异步发布Shapefile目录"""
        url = f"{self.base_url}/api/geo/shapefile-directory/execute/"
        params = {
            'directory': directory,
            'workspace': workspace,
            **options
        }
        response = requests.get(url, params=params)
        return response.json()
    
    def publish_geotiff(self, file_path, workspace, **options):
        """异步发布GeoTIFF"""
        url = f"{self.base_url}/api/geo/geotiff/execute/"
        params = {
            'path': file_path,
            'workspace': workspace,
            **options
        }
        response = requests.get(url, params=params)
        return response.json()
    
    def publish_geotiff_directory(self, directory, workspace, **options):
        """异步发布GeoTIFF目录"""
        url = f"{self.base_url}/api/geo/geotiff-directory/execute/"
        params = {
            'directory': directory,
            'workspace': workspace,
            **options
        }
        response = requests.get(url, params=params)
        return response.json()
    
    def publish_structured_geotiff(self, root_directory, workspace, **options):
        """异步发布结构化GeoTIFF"""
        url = f"{self.base_url}/api/geo/structured-geotiff/execute/"
        params = {
            'root_directory': root_directory,
            'workspace': workspace,
            **options
        }
        response = requests.get(url, params=params)
        return response.json()
    
    def get_task_status(self, task_id):
        """获取任务状态"""
        url = f"{self.base_url}/api/geo/status/"
        params = {'task_id': task_id}
        response = requests.get(url, params=params)
        return response.json()
    
    def get_task_log(self, task_id):
        """获取任务日志"""
        url = f"{self.base_url}/api/geo/log/"
        params = {'task_id': task_id}
        response = requests.get(url, params=params)
        return response.json()
    
    def get_all_tasks(self):
        """获取所有任务"""
        url = f"{self.base_url}/api/geo/all/"
        response = requests.get(url)
        return response.json()
    
    def wait_for_completion(self, task_id, timeout=3600, check_interval=10):
        """等待任务完成"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status_result = self.get_task_status(task_id)
            
            if status_result['status'] != 'success':
                return False, f"无法获取任务状态: {status_result['message']}"
            
            task = status_result['task']
            task_status = task['status']
            progress = task.get('progress', 0)
            current_step = task.get('current_step', '处理中')
            
            print(f"任务进度: {progress}% - {current_step}")
            
            if task_status == 'completed':
                published_layers = task.get('published_layers', [])
                return True, f"发布完成，图层: {', '.join(published_layers)}"
            elif task_status == 'failed':
                error_msg = task.get('error', '未知错误')
                return False, f"发布失败: {error_msg}"
            elif task_status == 'partial':
                total = task.get('total_files', 0)
                processed = task.get('processed_files', 0)
                failed = task.get('failed_files', 0)
                return True, f"部分成功: {processed}/{total} 成功，{failed} 失败"
            
            time.sleep(check_interval)
        
        return False, "发布超时"

# 使用示例
client = GeoServerPublisherClient("http://127.0.0.1:5000")

# 1. 发布单个Shapefile
print("发布Shapefile...")
shp_result = client.publish_shapefile(
    "D:/data/boundaries.shp",
    "test_workspace",
    store="boundary_data",
    layer="admin_boundaries",
    charset="UTF-8"
)

if shp_result['status'] == 'success':
    task_id = shp_result['task_id']
    print(f"Shapefile发布任务已启动: {task_id}")
    
    success, message = client.wait_for_completion(task_id)
    print(f"Shapefile发布结果: {message}")

# 2. 批量发布Shapefile目录
print("\n批量发布Shapefile目录...")
dir_result = client.publish_shapefile_directory(
    "D:/shapefiles/",
    "vector_data",
    recursive=True,
    charset="UTF-8"
)

if dir_result['status'] == 'success':
    task_id = dir_result['task_id']
    print(f"目录发布任务已启动: {task_id}")
    
    success, message = client.wait_for_completion(task_id, timeout=1800)
    print(f"目录发布结果: {message}")

# 3. 发布GeoTIFF文件
print("\n发布GeoTIFF...")
tif_result = client.publish_geotiff(
    "D:/data/elevation.tif",
    "raster_data",
    store="elevation_data",
    layer="dem_2024"
)

if tif_result['status'] == 'success':
    task_id = tif_result['task_id']
    print(f"GeoTIFF发布任务已启动: {task_id}")
    
    success, message = client.wait_for_completion(task_id)
    print(f"GeoTIFF发布结果: {message}")
```

### 批量发布项目数据示例

```python
def publish_project_data(client, project_name, data_directory):
    """发布整个项目的数据"""
    
    print(f"开始发布项目: {project_name}")
    
    # 创建工作区（如果不存在）
    workspace = f"project_{project_name}"
    
    published_tasks = []
    
    # 1. 发布Shapefile数据
    shapefile_dir = os.path.join(data_directory, "shapefiles")
    if os.path.exists(shapefile_dir):
        print(f"发布Shapefile目录: {shapefile_dir}")
        
        result = client.publish_shapefile_directory(
            shapefile_dir,
            workspace,
            recursive=True,
            charset="UTF-8"
        )
        
        if result['status'] == 'success':
            published_tasks.append({
                'type': 'shapefile_directory',
                'task_id': result['task_id'],
                'path': shapefile_dir
            })
    
    # 2. 发布GeoTIFF数据
    geotiff_dir = os.path.join(data_directory, "rasters")
    if os.path.exists(geotiff_dir):
        print(f"发布GeoTIFF目录: {geotiff_dir}")
        
        result = client.publish_geotiff_directory(
            geotiff_dir,
            workspace,
            recursive=True
        )
        
        if result['status'] == 'success':
            published_tasks.append({
                'type': 'geotiff_directory',
                'task_id': result['task_id'],
                'path': geotiff_dir
            })
    
    # 3. 发布结构化数据
    structured_dir = os.path.join(data_directory, "structured")
    if os.path.exists(structured_dir):
        print(f"发布结构化数据: {structured_dir}")
        
        result = client.publish_structured_geotiff(
            structured_dir,
            workspace,
            structure_type="auto"
        )
        
        if result['status'] == 'success':
            published_tasks.append({
                'type': 'structured_geotiff',
                'task_id': result['task_id'],
                'path': structured_dir
            })
    
    # 4. 等待所有任务完成
    print(f"\n等待 {len(published_tasks)} 个发布任务完成...")
    
    results = []
    for task in published_tasks:
        print(f"\n监控任务: {task['type']} - {task['path']}")
        success, message = client.wait_for_completion(task['task_id'], timeout=1800)
        
        results.append({
            'type': task['type'],
            'path': task['path'],
            'task_id': task['task_id'],
            'success': success,
            'message': message
        })
        
        print(f"任务结果: {message}")
    
    # 5. 输出总结
    print(f"\n项目 {project_name} 发布完成!")
    successful_tasks = sum(1 for r in results if r['success'])
    print(f"成功: {successful_tasks}/{len(results)} 个任务")
    
    # 获取发布的图层列表
    all_layers = []
    for result in results:
        if result['success']:
            status_result = client.get_task_status(result['task_id'])
            if status_result['status'] == 'success':
                task = status_result['task']
                published_layers = task.get('published_layers', [])
                all_layers.extend(published_layers)
    
    print(f"发布的图层: {', '.join(all_layers)}")
    
    return results

# 使用示例
project_results = publish_project_data(
    client,
    "urban_survey_2024",
    "D:/projects/urban_survey_2024/data/"
)
```

### 进度监控和通知示例

```python
class PublishProgressMonitor:
    def __init__(self, client):
        self.client = client
        self.active_tasks = {}
    
    def add_task(self, task_id, description=""):
        """添加要监控的任务"""
        self.active_tasks[task_id] = {
            'description': description,
            'last_progress': 0,
            'start_time': time.time()
        }
    
    def monitor_all_tasks(self, check_interval=15):
        """监控所有活动任务"""
        
        while self.active_tasks:
            completed_tasks = []
            
            for task_id, task_info in self.active_tasks.items():
                status_result = self.client.get_task_status(task_id)
                
                if status_result['status'] != 'success':
                    print(f"❌ 无法获取任务 {task_id} 的状态")
                    completed_tasks.append(task_id)
                    continue
                
                task = status_result['task']
                current_progress = task.get('progress', 0)
                task_status = task['status']
                current_step = task.get('current_step', '处理中')
                
                # 显示进度更新
                if current_progress != task_info['last_progress']:
                    elapsed = time.time() - task_info['start_time']
                    print(f"[{time.strftime('%H:%M:%S')}] {task_info['description']}: {current_progress}% - {current_step} (耗时: {elapsed:.0f}秒)")
                    task_info['last_progress'] = current_progress
                
                # 检查任务是否完成
                if task_status in ['completed', 'failed', 'partial']:
                    elapsed = time.time() - task_info['start_time']
                    
                    if task_status == 'completed':
                        published_layers = task.get('published_layers', [])
                        print(f"✅ {task_info['description']} 完成! 图层: {', '.join(published_layers)} (总耗时: {elapsed:.0f}秒)")
                    elif task_status == 'partial':
                        total = task.get('total_files', 0)
                        processed = task.get('processed_files', 0)
                        failed = task.get('failed_files', 0)
                        print(f"⚠️  {task_info['description']} 部分完成: {processed}/{total} 成功，{failed} 失败 (总耗时: {elapsed:.0f}秒)")
                    else:  # failed
                        error = task.get('error', '未知错误')
                        print(f"❌ {task_info['description']} 失败: {error} (耗时: {elapsed:.0f}秒)")
                    
                    completed_tasks.append(task_id)
            
            # 移除已完成的任务
            for task_id in completed_tasks:
                del self.active_tasks[task_id]
            
            if self.active_tasks:
                time.sleep(check_interval)
        
        print("🎉 所有发布任务已完成!")

# 使用示例
monitor = PublishProgressMonitor(client)

# 启动多个发布任务
tasks = [
    {
        'func': client.publish_shapefile_directory,
        'args': ("D:/vector_data/", "vector_workspace"),
        'desc': "矢量数据发布"
    },
    {
        'func': client.publish_geotiff_directory,
        'args': ("D:/raster_data/", "raster_workspace"),
        'desc': "栅格数据发布"
    },
    {
        'func': client.publish_structured_geotiff,
        'args': ("D:/time_series/", "temporal_workspace"),
        'desc': "时序数据发布"
    }
]

# 启动所有任务并添加到监控器
for task in tasks:
    result = task['func'](*task['args'])
    if result['status'] == 'success':
        monitor.add_task(result['task_id'], task['desc'])
        print(f"✅ {task['desc']} 任务已启动: {result['task_id']}")
    else:
        print(f"❌ {task['desc']} 启动失败: {result['message']}")

# 开始监控所有任务
monitor.monitor_all_tasks(check_interval=20)
```

## 最佳实践

### 1. 数据预处理检查

```python
def validate_shapefile(file_path):
    """验证Shapefile完整性"""
    
    issues = []
    
    # 检查主文件
    if not os.path.exists(file_path):
        issues.append(f"主文件不存在: {file_path}")
        return issues
    
    base_name = os.path.splitext(file_path)[0]
    
    # 检查必需文件
    required_files = ['.shx', '.dbf']
    for ext in required_files:
        required_file = base_name + ext
        if not os.path.exists(required_file):
            issues.append(f"缺少必需文件: {required_file}")
    
    # 检查可选但重要的文件
    optional_files = ['.prj', '.cpg']
    for ext in optional_files:
        optional_file = base_name + ext
        if not os.path.exists(optional_file):
            if ext == '.prj':
                issues.append(f"警告: 缺少投影文件 {optional_file}")
            elif ext == '.cpg':
                issues.append(f"警告: 缺少编码文件 {optional_file}")
    
    return issues

def validate_geotiff(file_path):
    """验证GeoTIFF文件"""
    
    issues = []
    
    if not os.path.exists(file_path):
        issues.append(f"文件不存在: {file_path}")
        return issues
    
    # 检查文件扩展名
    if not file_path.lower().endswith(('.tif', '.tiff')):
        issues.append(f"文件扩展名不正确: {file_path}")
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    if file_size == 0:
        issues.append(f"文件为空: {file_path}")
    elif file_size > 5 * 1024 * 1024 * 1024:  # 大于5GB
        issues.append(f"警告: 文件过大 ({file_size / (1024**3):.1f} GB): {file_path}")
    
    return issues

def prepare_data_for_publishing(data_directory):
    """准备数据发布前的检查"""
    
    print(f"检查数据目录: {data_directory}")
    
    shapefile_issues = []
    geotiff_issues = []
    
    # 检查Shapefile
    for root, dirs, files in os.walk(data_directory):
        for file in files:
            if file.lower().endswith('.shp'):
                file_path = os.path.join(root, file)
                issues = validate_shapefile(file_path)
                if issues:
                    shapefile_issues.extend([(file_path, issue) for issue in issues])
    
    # 检查GeoTIFF
    for root, dirs, files in os.walk(data_directory):
        for file in files:
            if file.lower().endswith(('.tif', '.tiff')):
                file_path = os.path.join(root, file)
                issues = validate_geotiff(file_path)
                if issues:
                    geotiff_issues.extend([(file_path, issue) for issue in issues])
    
    # 输出检查结果
    if shapefile_issues:
        print("\nShapefile问题:")
        for file_path, issue in shapefile_issues:
            print(f"  {os.path.basename(file_path)}: {issue}")
    
    if geotiff_issues:
        print("\nGeoTIFF问题:")
        for file_path, issue in geotiff_issues:
            print(f"  {os.path.basename(file_path)}: {issue}")
    
    if not shapefile_issues and not geotiff_issues:
        print("✅ 所有数据文件检查通过")
        return True
    else:
        print(f"⚠️  发现 {len(shapefile_issues + geotiff_issues)} 个问题")
        return False
```

### 2. 工作区管理策略

```python
def create_project_workspace_structure(client, project_name, data_types):
    """创建项目工作区结构"""
    
    # 主工作区
    main_workspace = f"project_{project_name}"
    
    # 子工作区按数据类型分类
    workspace_mapping = {
        'vector': f"{main_workspace}_vector",
        'raster': f"{main_workspace}_raster", 
        'temporal': f"{main_workspace}_temporal",
        'reference': f"{main_workspace}_reference"
    }
    
    created_workspaces = []
    
    for data_type in data_types:
        if data_type in workspace_mapping:
            workspace_name = workspace_mapping[data_type]
            
            # 这里需要调用管理API创建工作区
            # create_result = management_client.create_workspace(workspace_name)
            
            print(f"创建工作区: {workspace_name} (用于 {data_type} 数据)")
            created_workspaces.append(workspace_name)
    
    return created_workspaces

def organize_publishing_by_data_type(client, data_directory, project_name):
    """按数据类型组织发布"""
    
    # 扫描数据目录
    data_structure = {
        'shapefiles': [],
        'geotiffs': [],
        'structured': []
    }
    
    for root, dirs, files in os.walk(data_directory):
        for file in files:
            file_path = os.path.join(root, file)
            
            if file.lower().endswith('.shp'):
                data_structure['shapefiles'].append(file_path)
            elif file.lower().endswith(('.tif', '.tiff')):
                data_structure['geotiffs'].append(file_path)
    
    # 检查是否有结构化数据
    structured_dirs = []
    for root, dirs, files in os.walk(data_directory):
        if len(dirs) > 0 and any(d.isdigit() or 'date' in d.lower() for d in dirs):
            structured_dirs.append(root)
    
    if structured_dirs:
        data_structure['structured'] = structured_dirs
    
    # 创建工作区结构
    data_types = [k for k, v in data_structure.items() if v]
    workspaces = create_project_workspace_structure(client, project_name, data_types)
    
    # 按类型发布数据
    publishing_tasks = []
    
    if data_structure['shapefiles']:
        # 按目录分组发布Shapefile
        shapefile_dirs = set(os.path.dirname(shp) for shp in data_structure['shapefiles'])
        
        for shp_dir in shapefile_dirs:
            result = client.publish_shapefile_directory(
                shp_dir,
                f"{project_name}_vector",
                recursive=False
            )
            if result['status'] == 'success':
                publishing_tasks.append(result['task_id'])
    
    if data_structure['geotiffs']:
        # 按目录分组发布GeoTIFF
        geotiff_dirs = set(os.path.dirname(tif) for tif in data_structure['geotiffs'])
        
        for tif_dir in geotiff_dirs:
            result = client.publish_geotiff_directory(
                tif_dir,
                f"{project_name}_raster",
                recursive=False
            )
            if result['status'] == 'success':
                publishing_tasks.append(result['task_id'])
    
    if data_structure['structured']:
        for struct_dir in data_structure['structured']:
            result = client.publish_structured_geotiff(
                struct_dir,
                f"{project_name}_temporal"
            )
            if result['status'] == 'success':
                publishing_tasks.append(result['task_id'])
    
    return publishing_tasks
```

## 故障排除

### 发布失败诊断

```python
def diagnose_publishing_failure(client, task_id):
    """诊断发布失败原因"""
    
    # 获取任务详细信息
    status_result = client.get_task_status(task_id)
    if status_result['status'] != 'success':
        print(f"无法获取任务状态: {status_result['message']}")
        return
    
    task = status_result['task']
    print(f"任务ID: {task['id']}")
    print(f"任务类型: {task['type']}")
    print(f"文件路径: {task['file_path']}")
    print(f"工作区: {task['workspace']}")
    print(f"状态: {task['status']}")
    
    if task['status'] == 'failed':
        error = task.get('error', '未知错误')
        print(f"错误信息: {error}")
        
        # 获取详细日志
        log_result = client.get_task_log(task_id)
        if log_result['status'] == 'success':
            log_content = log_result['log']
            
            # 分析日志中的错误模式
            error_patterns = [
                ('Connection refused', '无法连接到GeoServer'),
                ('File not found', '文件不存在或路径错误'),
                ('Permission denied', '权限不足'),
                ('Invalid format', '文件格式不支持'),
                ('Workspace not found', '工作区不存在'),
                ('Out of memory', '内存不足'),
                ('Timeout', '操作超时')
            ]
            
            for pattern, description in error_patterns:
                if pattern.lower() in log_content.lower():
                    print(f"可能原因: {description}")
                    break
            
            # 显示最后的错误日志
            log_lines = log_content.split('\n')
            error_lines = [line for line in log_lines if 'error' in line.lower() or 'exception' in line.lower()]
            
            if error_lines:
                print("\n错误日志:")
                for line in error_lines[-5:]:  # 最后5行错误
                    print(f"  {line}")
    
    elif task['status'] == 'partial':
        total = task.get('total_files', 0)
        processed = task.get('processed_files', 0)
        failed = task.get('failed_files', 0)
        
        print(f"部分成功: {processed}/{total} 成功，{failed} 失败")
        
        # 获取失败文件的详细信息
        log_result = client.get_task_log(task_id)
        if log_result['status'] == 'success':
            log_content = log_result['log']
            
            # 查找失败的文件
            failed_files = []
            for line in log_content.split('\n'):
                if '失败' in line or 'failed' in line.lower():
                    failed_files.append(line)
            
            if failed_files:
                print("\n失败的文件:")
                for file_info in failed_files[-10:]:  # 最后10个失败文件
                    print(f"  {file_info}")

# 使用示例
diagnose_publishing_failure(client, "aa1bb2cc-33dd-44ee-55ff-666677778888")
```

GeoServer异步发布API文档已完成，涵盖了所有10个接口的详细说明、使用示例、最佳实践和故障排除。最后我将创建地图API文档。
