2025-07-28 09:14:49,578 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250728_091449.log
2025-07-28 09:14:49,622 - geo_publisher - INFO - 加载了 24 个任务状态
2025-07-28 09:14:49,758 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-28 09:14:49,758 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-28 09:14:49,789 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-28 09:14:49,794 - root - INFO - === GeoServer REST API服务 ===
2025-07-28 09:14:49,794 - root - INFO - 主机: 0.0.0.0
2025-07-28 09:14:49,797 - root - INFO - 端口: 5083
2025-07-28 09:14:49,798 - root - INFO - 调试模式: 禁用
2025-07-28 09:14:49,799 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-28 09:14:49,826 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-28 09:14:49,827 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-28 09:15:19,115 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:15:19,120 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:15:19,136 - map_api - INFO - 找到 3 个目录
2025-07-28 09:15:19,137 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:15:19,142 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:15:19,144 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:15:19,151 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:15:19,153 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:15:19,186 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:15:19,190 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:15:19,191 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:15:19,193 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:15:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:15:19,241 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:15:19] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-28 09:15:26,613 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:15:26,627 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:15:26,639 - map_api - INFO - 找到 3 个目录
2025-07-28 09:15:26,640 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:15:26,651 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:15:26,652 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:15:26,660 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:15:26,665 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:15:26,675 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:15:26,682 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:15:26,684 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:15:26,687 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:15:26] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:15:28,254 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-28 09:15:28,259 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:15:28,263 - tif_api - INFO - 输入文件大小: 237.53 MB
2025-07-28 09:15:28,265 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 09:15:28,267 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:15:28,281 - tif_api - INFO - 黑色阈值: 0
2025-07-28 09:15:28,284 - tif_api - INFO - 白色阈值: 255
2025-07-28 09:15:28,287 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-28 09:15:28,297 - tif_executor - INFO - 启动TIF处理任务 93d102ee-9b6d-4655-bb20-8f8ec2a3bc17: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:15:28,298 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - ============ TIF处理任务 93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 开始执行 ============
2025-07-28 09:15:28,299 - tif_api - INFO - 异步任务启动成功，任务ID: 93d102ee-9b6d-4655-bb20-8f8ec2a3bc17
2025-07-28 09:15:28,308 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 开始时间: 2025-07-28 09:15:28
2025-07-28 09:15:28,309 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:15:28] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-28 09:15:28,312 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 09:15:28,322 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 系统信息:
2025-07-28 09:15:28,323 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 09:15:28,324 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO -   Python版本: 3.8.20
2025-07-28 09:15:28,326 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO -   GDAL版本: 3.9.2
2025-07-28 09:15:28,327 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO -   GPU可用: 否
2025-07-28 09:15:28,328 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 检查参数有效性...
2025-07-28 09:15:28,329 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:15:28,338 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 开始执行TIF处理流程...
2025-07-28 09:15:28,339 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:15:28,340 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:15:28,342 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 09:15:28,346 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:15:28,343 - tif_api - INFO - 查询任务: 93d102ee-9b6d-4655-bb20-8f8ec2a3bc17
2025-07-28 09:15:28,349 - tif_api - INFO - 任务 93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 状态查询成功，当前状态: 正在运行
2025-07-28 09:15:28,351 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:15:28] "GET /api/tif/status?task_id=93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 HTTP/1.1" 200 -
2025-07-28 09:15:28,356 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-28 09:15:28,769 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:15:28,773 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:15:28,781 - map_api - INFO - 找到 3 个目录
2025-07-28 09:15:28,783 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:15:28,788 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:15:28,790 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:15:28,797 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:15:28,803 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:15:28,809 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 09:15:28,817 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:15:28,819 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:15:28,822 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:15:28] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:15:33,391 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (09:15:33)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 09:15:33,394 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 09:15:33,668 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:05,  3.87it/s]
2025-07-28 09:15:33,978 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 15.71it/s]
2025-07-28 09:15:34,093 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
处理数据块:  67%|##############################################6                       | 14/21 [00:00<00:00, 24.89it/s]
2025-07-28 09:15:34,336 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
处理数据块:  86%|############################################################          | 18/21 [00:00<00:00, 21.27it/s]
2025-07-28 09:15:34,357 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 22.19it/s]
2025-07-28 09:15:34,358 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 09:15:34,359 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 09:15:34,402 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 513.56it/s]
2025-07-28 09:15:35,911 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.15 秒 (09:15:35)预计总处理时间: 约 6.45 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (09:15:35)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 09:15:35,912 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 09:16:13,399 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:16:13,406 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:16:13,440 - map_api - INFO - 找到 3 个目录
2025-07-28 09:16:13,444 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:16:13,527 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:16:13,533 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:16:13,775 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2631 字节
2025-07-28 09:16:13,780 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:16:14,110 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2188 字节
2025-07-28 09:16:14,121 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:16:14,123 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:16:14,131 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:16:14] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:16:21,755 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 09:16:21,763 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:16:21,766 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:16:21,962 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  5.15it/s]
2025-07-28 09:16:21,963 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:16:22,062 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:00, 19.84it/s]
2025-07-28 09:16:22,062 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:16:22,176 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:00<00:00, 33.25it/s]
2025-07-28 09:16:22,177 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:16:22,295 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:00<00:00, 39.76it/s]
2025-07-28 09:16:22,296 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:16:22,374 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:16:29,192 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:16:29,195 - tif_api - INFO - 查询任务: 93d102ee-9b6d-4655-bb20-8f8ec2a3bc17
2025-07-28 09:16:29,202 - tif_api - INFO - 任务 93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 状态查询成功，当前状态: 正在运行
2025-07-28 09:16:29,207 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:16:29] "GET /api/tif/status?task_id=93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 HTTP/1.1" 200 -
2025-07-28 09:17:03,203 - batch_executor - INFO - 启动任务 00cb79f6-747c-42ed-bcbd-0f8171b7546e: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171601/project
2025-07-28 09:17:03,205 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:17:03] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-28 09:17:03,229 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:17:03] "GET /api/batch/status?task_id=00cb79f6-747c-42ed-bcbd-0f8171b7546e HTTP/1.1" 200 -
2025-07-28 09:17:13,124 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:37<03:14, 97.21s/it]
2025-07-28 09:17:20,327 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 09:17:20,328 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:17:20,330 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:20,838 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:10,  1.97it/s]
2025-07-28 09:17:20,839 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:21,152 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:07,  2.54it/s]
2025-07-28 09:17:21,153 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:21,495 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:06,  2.70it/s]
2025-07-28 09:17:21,495 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:21,795 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:01<00:05,  2.92it/s]
2025-07-28 09:17:21,795 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:22,139 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:01<00:05,  2.91it/s]
2025-07-28 09:17:22,139 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:22,395 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:04,  3.19it/s]
2025-07-28 09:17:22,395 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:22,702 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:02<00:04,  3.21it/s]
2025-07-28 09:17:22,704 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:22,965 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:02<00:03,  3.38it/s]
2025-07-28 09:17:22,966 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:23,225 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:02<00:03,  3.51it/s]
2025-07-28 09:17:23,225 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:23,478 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:03<00:03,  3.64it/s]
2025-07-28 09:17:23,479 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:23,663 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:03<00:02,  4.04it/s]
2025-07-28 09:17:23,664 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:23,948 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:03<00:02,  3.86it/s]
2025-07-28 09:17:23,949 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:24,319 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:03<00:02,  3.42it/s]
2025-07-28 09:17:24,319 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:24,548 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:04<00:01,  3.65it/s]
2025-07-28 09:17:24,549 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:24,747 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:04<00:01,  3.98it/s]
2025-07-28 09:17:24,748 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:27,239 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:17:27,242 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:17:27,299 - map_api - INFO - 找到 3 个目录
2025-07-28 09:17:27,301 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:17:27,326 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:17:27,329 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:17:27,335 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171601/TaskInfo.json, 状态码: 404
2025-07-28 09:17:27,338 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:17:27,345 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:17:27,353 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:17:27,354 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(2) > 已完成(1)
2025-07-28 09:17:27,362 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:17:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:17:28,015 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:07<00:05,  1.16s/it]
2025-07-28 09:17:28,016 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:33,604 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:13<00:09,  2.49s/it]
2025-07-28 09:17:33,605 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:38,316 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:17<00:09,  3.16s/it]
2025-07-28 09:17:38,317 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:44,929 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:24<00:08,  4.20s/it]
2025-07-28 09:17:44,930 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:51,575 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:31<00:04,  4.93s/it]
2025-07-28 09:17:51,576 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:52,656 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:32<00:00,  3.78s/it]
2025-07-28 09:17:52,657 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:17:52,659 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - [A
2025-07-28 09:18:03,552 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:18:03,557 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:18:03,565 - map_api - INFO - 找到 3 个目录
2025-07-28 09:18:03,567 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:18:03,572 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:18:03,576 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:18:03,581 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1658 字节
2025-07-28 09:18:03,583 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:18:03,588 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:18:03,592 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:18:03,593 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:18:03,595 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:18:03] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:18:06,492 - batch_executor - INFO - 启动任务 5fcf1986-3aa3-49b3-8266-5c1f73cdef24: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171601/project
2025-07-28 09:18:06,496 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:18:06] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-28 09:18:06,544 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:18:06] "GET /api/batch/status?task_id=5fcf1986-3aa3-49b3-8266-5c1f73cdef24 HTTP/1.1" 200 -
2025-07-28 09:18:06,760 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:18:06,824 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:18:06,834 - map_api - INFO - 找到 3 个目录
2025-07-28 09:18:06,835 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:18:06,839 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:18:06,840 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:18:06,848 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 09:18:06,860 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:18:06,868 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:18:06,985 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:18:06,986 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:18:06,988 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:18:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:18:11,112 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:18:11,113 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:18:11,121 - map_api - INFO - 找到 3 个目录
2025-07-28 09:18:11,122 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:18:11,136 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:18:11,138 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:18:11,167 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 09:18:11,168 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:18:11,198 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:18:11,199 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:18:11,200 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:18:11,201 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:18:11] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:18:43,612 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:18:43,620 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:18:43,639 - map_api - INFO - 找到 3 个目录
2025-07-28 09:18:43,641 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:18:43,660 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:18:43,663 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:18:43,677 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 09:18:43,685 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:18:43,698 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:18:43,701 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:18:43,706 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:18:43,722 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:18:43] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:18:44,950 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:18:44,958 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:18:45,001 - map_api - INFO - 找到 3 个目录
2025-07-28 09:18:45,006 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:18:45,027 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:18:45,034 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:18:45,047 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 09:18:45,050 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:18:45,058 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:18:45,066 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:18:45,079 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:18:45,089 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:18:45] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:19:07,216 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:19:07] "GET /api/batch/status?task_id=5fcf1986-3aa3-49b3-8266-5c1f73cdef24 HTTP/1.1" 200 -
2025-07-28 09:19:16,741 - tif_api - INFO - 收到TIF处理请求 (异步模式)
2025-07-28 09:19:16,747 - tif_api - INFO - 处理输入文件: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:19:16,749 - tif_api - INFO - 输入文件大小: 222.50 MB
2025-07-28 09:19:16,750 - tif_api - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-07-28 09:19:16,752 - tif_api - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-07-28 09:19:16,754 - tif_api - INFO - 黑色阈值: 0
2025-07-28 09:19:16,763 - tif_api - INFO - 白色阈值: 255
2025-07-28 09:19:16,765 - tif_api - INFO - 启动异步TIF处理任务...
2025-07-28 09:19:16,771 - tif_executor - INFO - 启动TIF处理任务 39c0179c-a598-430b-b16d-1f43fad72ea4: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-07-28 09:19:16,772 - tif_api - INFO - 异步任务启动成功，任务ID: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:19:16,775 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:19:16] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-28 09:19:16,782 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - ============ TIF处理任务 39c0179c-a598-430b-b16d-1f43fad72ea4 开始执行 ============
2025-07-28 09:19:16,783 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 开始时间: 2025-07-28 09:19:16
2025-07-28 09:19:16,784 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 09:19:16,795 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 系统信息:
2025-07-28 09:19:16,795 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 09:19:16,798 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO -   Python版本: 3.8.20
2025-07-28 09:19:16,796 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:19:16,798 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO -   GDAL版本: 3.9.2
2025-07-28 09:19:16,799 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:19:16,801 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO -   GPU可用: 否
2025-07-28 09:19:16,802 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 检查参数有效性...
2025-07-28 09:19:16,803 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 正在运行
2025-07-28 09:19:16,804 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:19:16,805 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:19:16] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:19:16,807 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 开始执行TIF处理流程...
2025-07-28 09:19:16,808 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:19:16,809 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-07-28 09:19:16,811 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-07-28 09:19:21,191 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17007, 高=20364, 波段数=4开始读取影像数据，大小约: 5284.58 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (09:19:20)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 09:19:21,248 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 09:19:21,270 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A
2025-07-28 09:19:22,864 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [03:46<01:56, 116.34s/it]
2025-07-28 09:19:23,162 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:01<00:36,  1.84s/it]
2025-07-28 09:19:23,163 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A
2025-07-28 09:19:23,167 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:01<00:00, 11.39it/s]
2025-07-28 09:19:23,168 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 波段 2/3 处理完成已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 09:19:23,169 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 09:19:23,170 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A
2025-07-28 09:19:23,248 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 271.24it/s]
2025-07-28 09:19:25,154 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 4.20 秒 (09:19:25)预计总处理时间: 约 12.61 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif (09:19:25)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif使用已处理过的影像信息影像信息: 宽=17007, 高=20364, 波段数=3掩码形状: (20364, 17007), 数据类型: uint8获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 尺寸: 17007x20364x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 09:19:25,156 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 09:19:25,161 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A
2025-07-28 09:19:37,892 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 波段 3 所有像素值有效波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=42.58, std=63.34波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 09:19:37,903 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:19:37,904 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:38,213 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:06,  3.25it/s]
2025-07-28 09:19:38,214 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:38,364 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:04,  4.64it/s]
2025-07-28 09:19:38,364 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:38,535 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:03,  5.13it/s]
2025-07-28 09:19:38,535 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:38,641 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  6.26it/s]
2025-07-28 09:19:38,642 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:38,867 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:02,  7.40it/s]
2025-07-28 09:19:38,868 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:39,040 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  6.88it/s]
2025-07-28 09:19:39,043 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:39,194 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:01,  6.77it/s]
2025-07-28 09:19:39,194 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:39,305 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 09:19:39,307 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:19:39,310 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:19:39,359 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  6.53it/s]
2025-07-28 09:19:39,361 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:39,477 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  6.99it/s]
2025-07-28 09:19:39,478 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:39,606 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:01,  7.20it/s]
2025-07-28 09:19:39,607 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:39,726 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:01<00:01,  7.50it/s]
2025-07-28 09:19:39,727 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:39,869 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:01,  7.37it/s]
2025-07-28 09:19:39,870 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:40,096 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:01,  6.14it/s]
2025-07-28 09:19:40,096 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:40,231 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:02<00:00,  6.46it/s]
2025-07-28 09:19:40,232 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:40,370 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:02<00:00,  6.68it/s]
2025-07-28 09:19:40,371 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:40,507 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:01<00:23,  1.20s/it]
2025-07-28 09:19:40,508 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:19:40,523 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:02<00:00,  6.62it/s]
2025-07-28 09:19:40,524 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:40,655 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:02<00:00,  6.89it/s]
2025-07-28 09:19:40,655 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:40,802 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:02<00:00,  6.85it/s]
2025-07-28 09:19:40,804 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:40,924 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:03<00:00,  7.21it/s]
2025-07-28 09:19:40,925 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:40,961 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:19:42,247 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:02<00:28,  1.52s/it]
2025-07-28 09:19:42,247 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:19:43,674 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:19:43,678 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:19:43,745 - map_api - INFO - 找到 3 个目录
2025-07-28 09:19:43,748 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:19:44,055 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:19:44,064 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:19:44,206 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:19:44,213 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:19:44,220 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:19:44,223 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:19:44,228 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:19:44,249 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:19:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:19:44,284 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:04<00:31,  1.75s/it]
2025-07-28 09:19:44,285 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:19:45,177 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:05<00:24,  1.41s/it]
2025-07-28 09:19:45,178 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:19:48,384 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:09<00:32,  2.06s/it]
2025-07-28 09:19:48,385 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:19:53,689 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  29%|##################2                                             | 6/21 [00:14<00:47,  3.16s/it]
2025-07-28 09:19:53,690 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:19:58,280 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:18<00:50,  3.63s/it]
2025-07-28 09:19:58,281 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:20:05,728 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:26<01:02,  4.85s/it]
2025-07-28 09:20:05,729 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:20:09,625 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:30<00:54,  4.55s/it]
2025-07-28 09:20:09,625 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:20:17,330 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  48%|##############################                                 | 10/21 [00:38<01:00,  5.52s/it]
2025-07-28 09:20:17,330 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:20:17,816 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:20:17,818 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:20:18,236 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 正在运行
2025-07-28 09:20:18,242 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:20:18] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:20:26,047 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:46<01:05,  6.50s/it]
2025-07-28 09:20:26,048 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:20:33,868 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 12/21 [00:54<01:02,  6.90s/it]
2025-07-28 09:20:33,868 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:20:40,920 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:15<02:31, 75.75s/it]
2025-07-28 09:20:40,921 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A
2025-07-28 09:20:41,441 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [01:02<00:56,  7.11s/it]
2025-07-28 09:20:41,441 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:20:44,227 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:20:44,228 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:20:44,241 - map_api - INFO - 找到 3 个目录
2025-07-28 09:20:44,245 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:20:45,067 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:20:45,072 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:20:45,602 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:20:45,606 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:20:46,760 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:20:46,765 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:20:46,766 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:20:46,769 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:20:46] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:20:48,973 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [01:09<00:50,  7.23s/it]
2025-07-28 09:20:48,977 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:21:03,961 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 15/21 [01:24<00:57,  9.56s/it]
2025-07-28 09:21:03,986 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:21:16,150 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:21:16,154 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:21:16,330 - map_api - INFO - 找到 3 个目录
2025-07-28 09:21:16,340 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:21:16,406 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:21:16,412 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:21:16,445 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:21:16,449 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:21:16,457 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:21:16,459 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:21:16,465 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:21:16,512 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:21:16] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:21:20,162 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:21:20,169 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:21:23,050 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 正在运行
2025-07-28 09:21:23,059 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:21:23] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:21:27,439 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  76%|################################################               | 16/21 [01:48<01:08, 13.76s/it]
2025-07-28 09:21:27,441 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:21:27,450 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=45.79, std=65.41应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 09:21:27,451 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:21:27,452 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:21:32,402 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:04<01:39,  4.95s/it]
2025-07-28 09:21:32,403 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:21:44,218 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:21:44,221 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:21:44,233 - map_api - INFO - 找到 3 个目录
2025-07-28 09:21:44,236 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:21:44,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:21:44,244 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:21:44,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:21:44,262 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:21:44,269 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:21:44,271 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:21:44,272 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:21:44,277 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:21:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:21:50,082 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:22<03:56, 12.44s/it]
2025-07-28 09:21:50,082 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:21:55,925 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:28<02:49,  9.43s/it]
2025-07-28 09:21:55,926 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:22:07,209 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:39<02:52, 10.16s/it]
2025-07-28 09:22:07,210 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:22:12,617 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:45<02:15,  8.45s/it]
2025-07-28 09:22:12,617 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:22:25,354 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:22:25,355 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:22:26,764 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 正在运行
2025-07-28 09:22:26,768 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:22:26] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:22:44,207 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:22:44,215 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:22:44,231 - map_api - INFO - 找到 3 个目录
2025-07-28 09:22:44,234 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:22:44,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:22:44,250 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:22:44,257 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:22:44,264 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:22:44,273 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:22:44,282 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:22:44,284 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:22:44,290 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:22:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:23:02,102 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [01:34<05:35, 22.40s/it]
2025-07-28 09:23:02,103 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:23:02,164 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [03:22<02:32, 38.11s/it]
2025-07-28 09:23:02,167 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:23:27,361 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:23:27,361 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:23:27,437 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 正在运行
2025-07-28 09:23:27,440 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:23:27] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:23:34,047 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 18/21 [03:54<01:48, 36.24s/it]
2025-07-28 09:23:34,048 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:23:34,233 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [02:06<05:58, 25.58s/it]
2025-07-28 09:23:34,236 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:23:44,253 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:23:44,255 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:23:44,267 - map_api - INFO - 找到 3 个目录
2025-07-28 09:23:44,274 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:23:44,292 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:23:44,847 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:23:44,855 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:23:44,862 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:23:44,869 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:23:44,873 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:23:44,874 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:23:44,882 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:23:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:23:46,631 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [02:19<04:37, 21.38s/it]
2025-07-28 09:23:46,633 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:24:01,139 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:24:01,155 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:24:01,169 - map_api - INFO - 找到 3 个目录
2025-07-28 09:24:01,174 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:24:01,181 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:24:01,189 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:24:01,214 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:24:01,228 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:24:01,398 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:24:01,415 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:24:01,416 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:24:01,420 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:24:01] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:24:07,314 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  90%|#########################################################      | 19/21 [04:28<01:10, 35.34s/it]
2025-07-28 09:24:07,315 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:24:12,244 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [02:44<04:32, 22.71s/it]
2025-07-28 09:24:12,245 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:24:28,489 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:24:28,490 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:24:28,849 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 正在运行
2025-07-28 09:24:28,857 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:24:28] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:25:01,201 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:25:01,206 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:25:01,217 - map_api - INFO - 找到 3 个目录
2025-07-28 09:25:01,219 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:25:01,226 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:25:01,230 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:25:01,237 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:25:01,239 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:25:01,250 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:25:01,252 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:25:01,253 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:25:01,255 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:25:01] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:25:07,211 - map_api - INFO - 请求获取日志: 类型=batlog, ID=2bf1ea13-8bd3-43fa-99cb-bc4dbcdedc8d
2025-07-28 09:25:07,220 - map_api - INFO - 请求文件内容: batlog/2bf1ea13-8bd3-43fa-99cb-bc4dbcdedc8d.log
2025-07-28 09:25:07,228 - map_api - ERROR - 获取文件内容失败: batlog/2bf1ea13-8bd3-43fa-99cb-bc4dbcdedc8d.log, 状态码: 404
2025-07-28 09:25:07,232 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:25:07] "[33mGET /api/map/logs/batlog/2bf1ea13-8bd3-43fa-99cb-bc4dbcdedc8d HTTP/1.1[0m" 404 -
2025-07-28 09:25:13,542 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [05:34<00:44, 44.62s/it]
2025-07-28 09:25:13,543 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:25:14,159 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [03:46<06:22, 34.81s/it]
2025-07-28 09:25:14,159 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:25:22,248 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度: 100%|###############################################################| 21/21 [05:42<00:00, 33.84s/it]
2025-07-28 09:25:22,248 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:25:22,250 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A[A
2025-07-28 09:25:26,610 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [03:59<04:39, 27.97s/it]
2025-07-28 09:25:26,611 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:25:30,114 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:25:30,115 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:25:30,118 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 正在运行
2025-07-28 09:25:30,120 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:25:30] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:25:35,084 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [04:07<03:18, 22.04s/it]
2025-07-28 09:25:35,084 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:25:38,717 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [04:11<02:11, 16.46s/it]
2025-07-28 09:25:38,718 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:25:44,087 - map_api - INFO - 请求获取日志: 类型=geolog, ID=216b7e9c-d10d-43de-844b-d046e24c2205
2025-07-28 09:25:44,088 - map_api - INFO - 请求文件内容: geolog/216b7e9c-d10d-43de-844b-d046e24c2205.log
2025-07-28 09:25:44,094 - map_api - ERROR - 获取文件内容失败: geolog/216b7e9c-d10d-43de-844b-d046e24c2205.log, 状态码: 404
2025-07-28 09:25:44,098 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:25:44] "[33mGET /api/map/logs/geolog/216b7e9c-d10d-43de-844b-d046e24c2205 HTTP/1.1[0m" 404 -
2025-07-28 09:25:46,950 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [04:19<01:37, 13.98s/it]
2025-07-28 09:25:46,951 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:25:51,437 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [04:23<01:06, 11.12s/it]
2025-07-28 09:25:51,438 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:26:01,209 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:26:01,210 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:26:01,219 - map_api - INFO - 找到 3 个目录
2025-07-28 09:26:01,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:26:01,229 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:26:01,230 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:26:01,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:26:01,259 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:26:01,265 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:26:01,267 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:26:01,274 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:26:01,276 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:26:01] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:26:25,804 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [04:58<01:30, 18.11s/it]
2025-07-28 09:26:25,804 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:26:31,155 - map_api - INFO - 请求获取日志: 类型=tiflog, ID=f3a51a13-d851-4c11-8cac-ab5a5c40a9d8
2025-07-28 09:26:31,157 - map_api - INFO - 请求文件内容: tiflog/f3a51a13-d851-4c11-8cac-ab5a5c40a9d8.log
2025-07-28 09:26:31,167 - map_api - ERROR - 获取文件内容失败: tiflog/f3a51a13-d851-4c11-8cac-ab5a5c40a9d8.log, 状态码: 404
2025-07-28 09:26:31,174 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:26:31] "[33mGET /api/map/logs/tiflog/f3a51a13-d851-4c11-8cac-ab5a5c40a9d8 HTTP/1.1[0m" 404 -
2025-07-28 09:26:31,343 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:26:31,347 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:26:32,219 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 正在运行
2025-07-28 09:26:32,225 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:26:32] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:26:36,863 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [05:09<01:03, 15.99s/it]
2025-07-28 09:26:36,863 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:26:49,307 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [05:21<00:44, 14.92s/it]
2025-07-28 09:26:49,318 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:26:55,384 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [05:27<00:24, 12.27s/it]
2025-07-28 09:26:55,385 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:27:00,051 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [05:32<00:09,  9.99s/it]
2025-07-28 09:27:00,068 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:27:00,870 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [05:33<00:00,  7.23s/it]
2025-07-28 09:27:00,871 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:27:00,873 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:27:01,232 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:27:01,239 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:27:01,268 - map_api - INFO - 找到 3 个目录
2025-07-28 09:27:01,274 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:27:01,283 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:27:01,285 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:27:01,294 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:27:01,296 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:27:01,309 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:27:01,311 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:27:01,313 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:27:01,321 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:27:01] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:27:34,603 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:27:34,607 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:27:34,614 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 正在运行
2025-07-28 09:27:34,616 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:27:34] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:27:39,555 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [12:03<00:00, 290.02s/it]
2025-07-28 09:27:39,560 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [12:03<00:00, 241.21s/it]
2025-07-28 09:28:01,198 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:28:01,202 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:28:01,211 - map_api - INFO - 找到 3 个目录
2025-07-28 09:28:01,213 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:28:01,219 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:28:01,221 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:28:01,228 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:28:01,233 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:28:01,242 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:28:01,244 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:28:01,246 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:28:01,248 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:28:01] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:28:14,851 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [08:49<04:58, 298.21s/it]
2025-07-28 09:28:14,852 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A
2025-07-28 09:28:29,192 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 设置波段 3 的NoData值为 -9999设置波段 2 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 724.49 秒 (09:27:39)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (09:27:39)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=35.76, std=51.77应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 09:28:29,224 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:28:30,111 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:17,  1.13it/s]
2025-07-28 09:28:32,646 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:03<00:35,  1.86s/it]
2025-07-28 09:28:34,494 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:05<00:33,  1.85s/it]
2025-07-28 09:28:35,811 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:28:35,818 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:28:36,276 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 正在运行
2025-07-28 09:28:36,300 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:28:36] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:28:36,412 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:07<00:31,  1.88s/it]
2025-07-28 09:28:37,233 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:08<00:23,  1.50s/it]
2025-07-28 09:28:38,366 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  29%|##################2                                             | 6/21 [00:09<00:20,  1.37s/it]
2025-07-28 09:28:41,245 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:12<00:26,  1.87s/it]
2025-07-28 09:28:42,483 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:13<00:21,  1.67s/it]
2025-07-28 09:28:43,474 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:14<00:17,  1.45s/it]
2025-07-28 09:28:45,422 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  48%|##############################                                 | 10/21 [00:16<00:17,  1.61s/it]
2025-07-28 09:28:46,364 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:17<00:14,  1.40s/it]
2025-07-28 09:28:50,616 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 12/21 [00:21<00:20,  2.27s/it]
2025-07-28 09:29:00,093 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:30<00:35,  4.45s/it]
2025-07-28 09:29:01,425 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:29:01,436 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:29:01,555 - map_api - INFO - 找到 3 个目录
2025-07-28 09:29:01,567 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:29:01,652 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:29:01,657 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:29:01,666 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:29:01,668 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:29:01,676 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:29:01,681 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:29:01,683 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:29:01,835 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:29:01] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:29:17,884 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:48<00:59,  8.46s/it]
2025-07-28 09:29:23,403 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:29:23,453 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:29:23,782 - map_api - INFO - 找到 3 个目录
2025-07-28 09:29:23,789 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:29:23,854 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:29:23,861 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:29:23,989 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:29:23,994 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:29:24,014 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:29:24,016 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:29:24,021 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:29:24,090 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:29:24] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:29:38,264 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 15/21 [01:09<01:12, 12.07s/it]
2025-07-28 09:29:38,264 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:29:38,386 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:29:38,433 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 正在运行
2025-07-28 09:29:38,462 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:29:38] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:29:45,251 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-28 09:29:45,258 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-28 09:29:45,299 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:29:46,089 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.27it/s]
2025-07-28 09:29:46,090 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:29:46,092 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  1.26it/s]
2025-07-28 09:29:46,542 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-28 09:29:49,158 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  76%|################################################               | 16/21 [01:19<00:58, 11.72s/it]
2025-07-28 09:29:59,826 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-28 09:29:59,826 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-28 09:29:59,829 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:30:00,020 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:02, 10.55it/s]
2025-07-28 09:30:00,020 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:30:00,822 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [01:31<00:46, 11.70s/it]
2025-07-28 09:30:00,943 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理轮廓:  13%|#########4                                                               | 4/31 [00:01<00:08,  3.22it/s]
2025-07-28 09:30:00,947 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:30:01,005 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 26.39it/s]
2025-07-28 09:30:01,212 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:30:01,217 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:30:01,253 - map_api - INFO - 找到 3 个目录
2025-07-28 09:30:01,257 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:30:01,777 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-28 09:30:01,778 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-28 09:30:01,786 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A[A
2025-07-28 09:30:01,801 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
写入多边形: 100%|####################################################################| 35/35 [00:00<00:00, 2500.27it/s]
2025-07-28 09:30:02,097 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 处理完成，耗时: 873.75 秒 (14.56 分钟)
2025-07-28 09:30:02,099 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 处理结果: 成功
2025-07-28 09:30:02,103 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-28 09:30:02,104 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-28 09:30:02,109 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - TIF处理任务 93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 执行成功
2025-07-28 09:30:02,110 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 完成时间: 2025-07-28 09:30:02
2025-07-28 09:30:02,111 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - 状态: 运行成功
2025-07-28 09:30:02,111 - tif_task_93d102ee-9b6d-4655-bb20-8f8ec2a3bc17 - INFO - ============ 任务执行结束 ============
2025-07-28 09:30:02,145 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:30:02,149 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:30:02,156 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:30:02,158 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:30:02,164 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:30:02,165 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:30:02,166 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:30:02,169 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:30:02] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:30:02,452 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 18/21 [01:33<00:26,  8.68s/it]
2025-07-28 09:30:03,125 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  90%|#########################################################      | 19/21 [01:33<00:12,  6.27s/it]
2025-07-28 09:30:03,354 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [01:34<00:04,  4.46s/it]
2025-07-28 09:30:40,046 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:30:40,050 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:30:40,054 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 正在运行
2025-07-28 09:30:40,057 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:30:40] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:30:54,281 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:30:54,302 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:30:54,321 - map_api - INFO - 找到 3 个目录
2025-07-28 09:30:54,324 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:30:54,343 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:30:54,346 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:30:54,369 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:30:54,372 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:30:54,391 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:30:54,402 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:30:54,409 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:30:54,411 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:30:54] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:31:00,068 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:31:00,075 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:31:00,102 - map_api - INFO - 找到 3 个目录
2025-07-28 09:31:00,200 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:31:00,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:31:00,256 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:31:00,268 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:31:00,272 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:31:00,283 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:31:00,290 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:31:00,292 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:31:00,297 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:31:00] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:31:26,560 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [12:01<00:00, 249.58s/it]
2025-07-28 09:31:26,563 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - [A
2025-07-28 09:31:26,564 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [12:01<00:00, 240.47s/it]
2025-07-28 09:31:41,496 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:31:41,502 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:31:41,508 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 正在运行
2025-07-28 09:31:41,511 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:31:41] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:32:00,667 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:32:00,737 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:32:00,821 - map_api - INFO - 找到 3 个目录
2025-07-28 09:32:00,900 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:32:01,077 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:32:01,082 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:32:01,090 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:32:01,094 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:32:01,100 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:32:01,103 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:32:01,118 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:32:01,121 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:32:01] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:32:02,433 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 处理完成，耗时: 765.62 秒 (12.76 分钟)
2025-07-28 09:32:02,434 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 处理结果: 成功
2025-07-28 09:32:02,437 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 大小: 740.84 MB
2025-07-28 09:32:02,438 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp, 大小: 22.81 KB
2025-07-28 09:32:02,441 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - TIF处理任务 39c0179c-a598-430b-b16d-1f43fad72ea4 执行成功
2025-07-28 09:32:02,442 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 完成时间: 2025-07-28 09:32:02
2025-07-28 09:32:02,443 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - 状态: 运行成功
2025-07-28 09:32:02,443 - tif_task_39c0179c-a598-430b-b16d-1f43fad72ea4 - INFO - ============ 任务执行结束 ============
2025-07-28 09:32:24,716 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:32:24,721 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:32:24,731 - map_api - INFO - 找到 3 个目录
2025-07-28 09:32:24,735 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:32:24,741 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:32:24,742 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:32:24,747 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2128 字节
2025-07-28 09:32:24,752 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:32:24,758 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:32:24,760 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:32:24,761 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:32:24,770 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:32:24] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:32:41,993 - tif_api - INFO - 收到任务状态查询请求
2025-07-28 09:32:41,996 - tif_api - INFO - 查询任务: 39c0179c-a598-430b-b16d-1f43fad72ea4
2025-07-28 09:32:41,999 - tif_api - INFO - 任务 39c0179c-a598-430b-b16d-1f43fad72ea4 状态查询成功，当前状态: 运行成功
2025-07-28 09:32:42,001 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:32:42] "GET /api/tif/status?task_id=39c0179c-a598-430b-b16d-1f43fad72ea4 HTTP/1.1" 200 -
2025-07-28 09:32:43,584 - geo_task_90c2c041-02ea-4446-8ced-a5683c09f88b - INFO - GeoServer发布任务 90c2c041-02ea-4446-8ced-a5683c09f88b 开始执行
2025-07-28 09:32:43,583 - geo_publisher - INFO - 启动GeoTIFF发布任务 90c2c041-02ea-4446-8ced-a5683c09f88b: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-07-28 09:32:43,588 - geo_task_90c2c041-02ea-4446-8ced-a5683c09f88b - INFO - 开始时间: 2025-07-28 09:32:43
2025-07-28 09:32:43,590 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:32:43] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-28 09:32:43,591 - geo_task_90c2c041-02ea-4446-8ced-a5683c09f88b - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-28 09:32:43,594 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-28 09:32:43,595 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-28 09:32:43,628 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:32:43] "GET /api/geo/status?task_id=90c2c041-02ea-4446-8ced-a5683c09f88b HTTP/1.1" 200 -
2025-07-28 09:32:43,715 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-28 09:32:43,716 - root - INFO - 未提供存储名称，使用文件名: 20250705171601
2025-07-28 09:32:43,719 - root - INFO - 未提供图层名称，使用存储名称: 20250705171601
2025-07-28 09:32:45,059 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601.tif
2025-07-28 09:32:48,257 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:32:48,273 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:32:48,304 - map_api - INFO - 找到 3 个目录
2025-07-28 09:32:48,323 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:32:48,332 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:32:48,340 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:32:48,364 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 09:32:48,366 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:32:48,374 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:32:48,376 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:32:48,379 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:32:48,390 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:32:48] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:32:49,376 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:32:49,391 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:32:49,412 - map_api - INFO - 找到 3 个目录
2025-07-28 09:32:49,417 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:32:49,427 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:32:49,436 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:32:49,447 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 09:32:49,449 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:32:49,458 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:32:49,469 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:32:49,474 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:32:49,480 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:32:49] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:33:00,210 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:33:00,214 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:33:00,223 - map_api - INFO - 找到 3 个目录
2025-07-28 09:33:00,225 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:33:00,230 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:33:00,231 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:33:00,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 09:33:00,243 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:33:00,250 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:33:00,253 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:33:00,254 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:33:00,255 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:33:00] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:33:10,575 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:33:10,591 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:33:10,624 - map_api - INFO - 找到 3 个目录
2025-07-28 09:33:10,653 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:33:10,671 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:33:10,674 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:33:10,699 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2414 字节
2025-07-28 09:33:10,706 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:33:10,729 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171602/TaskInfo.json, 状态码: 404
2025-07-28 09:33:10,734 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:33:10,735 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(1) > 已完成(1)
2025-07-28 09:33:10,738 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:33:10] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:33:37,919 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 到 'testodm:20250705171601'
2025-07-28 09:33:38,137 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171601
2025-07-28 09:33:38,138 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif' 成功发布为图层 'testodm:20250705171601'
2025-07-28 09:33:38,144 - geo_task_90c2c041-02ea-4446-8ced-a5683c09f88b - INFO - GeoServer发布任务 90c2c041-02ea-4446-8ced-a5683c09f88b 执行成功
2025-07-28 09:33:38,145 - geo_task_90c2c041-02ea-4446-8ced-a5683c09f88b - INFO - 完成时间: 2025-07-28 09:33:38
2025-07-28 09:33:38,145 - geo_task_90c2c041-02ea-4446-8ced-a5683c09f88b - INFO - 状态: 发布成功
2025-07-28 09:33:44,674 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:33:44] "GET /api/geo/status?task_id=90c2c041-02ea-4446-8ced-a5683c09f88b HTTP/1.1" 200 -
2025-07-28 09:34:02,280 - batch_executor - INFO - 启动任务 ff07c3dc-98af-49e4-a70b-6a1063e1fb27: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
2025-07-28 09:34:02,281 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:34:02] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-28 09:34:02,316 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:34:02] "GET /api/batch/status?task_id=ff07c3dc-98af-49e4-a70b-6a1063e1fb27 HTTP/1.1" 200 -
2025-07-28 09:34:06,861 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:34:06,863 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:34:06,876 - map_api - INFO - 找到 3 个目录
2025-07-28 09:34:06,877 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:34:06,881 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:34:06,882 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:34:06,888 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:34:06,892 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:34:06,908 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 09:34:06,910 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:34:06,913 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:34:06,916 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:34:06] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:34:10,561 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:34:10,562 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:34:10,570 - map_api - INFO - 找到 3 个目录
2025-07-28 09:34:10,572 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:34:10,580 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:34:10,582 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:34:10,588 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:34:10,593 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:34:10,620 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 09:34:10,624 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:34:10,625 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:34:10,626 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:34:10] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:35:03,566 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:35:03] "GET /api/batch/status?task_id=ff07c3dc-98af-49e4-a70b-6a1063e1fb27 HTTP/1.1" 200 -
2025-07-28 09:35:10,570 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:35:10,575 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:35:10,585 - map_api - INFO - 找到 3 个目录
2025-07-28 09:35:10,587 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:35:10,613 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:35:10,624 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:35:10,642 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:35:10,643 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:35:10,662 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 09:35:10,664 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:35:10,681 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:35:10,696 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:35:10] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:35:21,155 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:35:21,159 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:35:21,176 - map_api - INFO - 找到 3 个目录
2025-07-28 09:35:21,200 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:35:21,212 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:35:21,217 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:35:21,224 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:35:21,227 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:35:21,242 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1884 字节
2025-07-28 09:35:21,246 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:35:21,248 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:35:21,252 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:35:21] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:36:04,664 - werkzeug - INFO - 127.0.0.1 - - [28/Jul/2025 09:36:04] "GET /api/batch/status?task_id=ff07c3dc-98af-49e4-a70b-6a1063e1fb27 HTTP/1.1" 200 -
2025-07-28 09:36:10,562 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:36:10,566 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:36:10,581 - map_api - INFO - 找到 3 个目录
2025-07-28 09:36:10,583 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:36:10,622 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:36:10,632 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:36:10,654 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:36:10,659 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:36:10,685 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:36:10,700 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:36:10,708 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:36:10,715 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:36:10] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:37:11,204 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:37:11,207 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:37:11,217 - map_api - INFO - 找到 3 个目录
2025-07-28 09:37:11,219 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:37:11,226 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:37:11,227 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:37:11,246 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:37:11,250 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:37:11,257 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:37:11,260 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:37:11,267 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:37:11,272 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:37:11] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:38:11,202 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:38:11,206 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:38:11,232 - map_api - INFO - 找到 3 个目录
2025-07-28 09:38:11,234 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:38:11,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:38:11,241 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:38:11,260 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:38:11,264 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:38:11,292 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:38:11,295 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:38:11,296 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:38:11,298 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:38:11] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:39:11,196 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:39:11,200 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:39:11,211 - map_api - INFO - 找到 3 个目录
2025-07-28 09:39:11,212 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:39:11,217 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:39:11,219 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:39:11,225 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:39:11,229 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:39:11,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:39:11,258 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:39:11,259 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:39:11,261 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:39:11] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:40:27,211 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:40:27,217 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:40:27,234 - map_api - INFO - 找到 3 个目录
2025-07-28 09:40:27,238 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:40:27,253 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:40:27,255 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:40:27,269 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:40:27,271 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:40:27,281 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:40:27,284 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:40:27,287 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:40:27,296 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:40:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:41:27,217 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:41:27,221 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:41:27,233 - map_api - INFO - 找到 3 个目录
2025-07-28 09:41:27,239 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:41:27,250 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:41:27,254 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:41:27,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:41:27,263 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:41:27,275 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:41:27,278 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:41:27,284 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:41:27,289 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:41:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:42:27,208 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:42:27,213 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:42:27,226 - map_api - INFO - 找到 3 个目录
2025-07-28 09:42:27,231 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:42:27,240 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:42:27,242 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:42:27,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:42:27,266 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:42:27,276 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:42:27,279 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:42:27,280 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:42:27,283 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:42:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:43:23,595 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:43:23,613 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:43:23,632 - map_api - INFO - 找到 3 个目录
2025-07-28 09:43:23,635 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:43:23,644 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:43:23,654 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:43:23,664 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:43:23,672 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:43:23,683 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:43:23,687 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:43:23,688 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:43:23,692 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:43:23] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:43:25,497 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:43:25,502 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:43:25,512 - map_api - INFO - 找到 3 个目录
2025-07-28 09:43:25,514 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:43:25,520 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:43:25,521 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:43:25,541 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:43:25,542 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:43:25,550 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:43:25,551 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:43:25,552 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:43:25,556 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:43:25] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:43:28,922 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:43:28,927 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:43:28,948 - map_api - INFO - 找到 3 个目录
2025-07-28 09:43:28,951 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:43:28,974 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:43:28,978 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:43:29,005 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:43:29,009 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:43:29,016 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:43:29,017 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:43:29,018 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:43:29,021 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:43:29] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:43:35,346 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:43:35,351 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:43:35,363 - map_api - INFO - 找到 3 个目录
2025-07-28 09:43:35,365 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:43:35,372 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:43:35,382 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:43:35,392 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:43:35,394 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:43:35,398 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:43:35,399 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:43:35,400 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:43:35,401 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:43:35] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:43:45,946 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:43:45,950 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:43:45,962 - map_api - INFO - 找到 3 个目录
2025-07-28 09:43:45,967 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:43:45,976 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:43:45,981 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:43:45,986 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:43:45,989 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:43:46,009 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:43:46,014 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:43:46,014 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:43:46,016 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:43:46] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:44:11,213 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:44:11,217 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:44:11,240 - map_api - INFO - 找到 3 个目录
2025-07-28 09:44:11,246 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:44:11,255 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:44:11,258 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:44:11,266 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:44:11,270 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:44:11,279 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:44:11,286 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:44:11,290 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:44:11,295 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:44:11] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:45:27,339 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:45:27,342 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:45:27,353 - map_api - INFO - 找到 3 个目录
2025-07-28 09:45:27,355 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:45:27,361 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:45:27,368 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:45:27,375 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:45:27,377 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:45:27,384 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:45:27,386 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:45:27,388 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:45:27,390 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:45:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:45:49,547 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:45:49,550 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:45:49,566 - map_api - INFO - 找到 3 个目录
2025-07-28 09:45:49,567 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:45:49,576 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:45:49,578 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:45:49,607 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:45:49,616 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:45:49,636 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:45:49,639 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:45:49,642 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:45:49,647 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:45:49] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-28 09:46:11,205 - map_api - INFO - 开始获取ODM任务列表
2025-07-28 09:46:11,207 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-28 09:46:11,637 - map_api - INFO - 找到 3 个目录
2025-07-28 09:46:11,639 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-28 09:46:12,769 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2184 字节
2025-07-28 09:46:12,770 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-28 09:46:12,776 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2511 字节
2025-07-28 09:46:12,778 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-28 09:46:14,049 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 1902 字节
2025-07-28 09:46:14,051 - map_api - INFO - 获取到 3 个任务信息
2025-07-28 09:46:14,051 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(2)
2025-07-28 09:46:14,053 - werkzeug - INFO - 192.168.43.148 - - [28/Jul/2025 09:46:14] "GET /api/map/odm/tasks HTTP/1.1" 200 -
