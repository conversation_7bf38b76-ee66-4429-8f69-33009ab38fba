2025-07-08 10:30:59,086 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250708_103059.log
2025-07-08 10:31:01,007 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-08 10:31:01,008 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-08 10:31:01,506 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-08 10:31:01,511 - geoserver_query_api - INFO - === GeoServer栅格数据坐标查询API服务 ===
2025-07-08 10:31:01,511 - geoserver_query_api - INFO - 主机: 0.0.0.0
2025-07-08 10:31:01,511 - geoserver_query_api - INFO - 端口: 5083
2025-07-08 10:31:01,512 - geoserver_query_api - INFO - 调试模式: 禁用
2025-07-08 10:31:01,522 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-08 10:31:01,524 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 10:32:02,025 - root - INFO - 开始查询坐标点 (22.902539969552294, 108.45801917394803) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-08 10:32:02,339 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 10:32:05,370 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 10:32:05,371 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 10:32:05,375 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 10:32:05,378 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 10:32:05,379 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 10:32:05,381 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 10:32:05,382 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 10:32:05,383 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 10:32:05,385 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 10:32:05,386 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 10:32:05,389 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 10:32:06,061 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:32:06,119 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:32:06,119 - root - INFO - 在坐标点 (22.902539969552294, 108.45801917394803) 处找到 2 个有有效数据的栅格图层
2025-07-08 10:32:06,127 - werkzeug - INFO - ************** - - [08/Jul/2025 10:32:06] "GET /api/query_values?lat=22.902539969552294&lon=108.45801917394803&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 10:32:11,198 - root - INFO - 开始查询坐标点 (22.896211053953323, 108.32958253988933) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-08 10:32:11,426 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 10:32:12,015 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 10:32:12,016 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 10:32:12,019 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 10:32:12,021 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 10:32:12,022 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 10:32:12,023 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 10:32:12,024 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 10:32:12,025 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 10:32:12,027 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 10:32:12,028 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 10:32:12,030 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 10:32:12,094 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:32:12,152 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:32:12,152 - root - INFO - 在坐标点 (22.896211053953323, 108.32958253988933) 处找到 2 个有有效数据的栅格图层
2025-07-08 10:32:12,156 - werkzeug - INFO - ************** - - [08/Jul/2025 10:32:12] "GET /api/query_values?lat=22.896211053953323&lon=108.32958253988933&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 10:32:50,281 - root - INFO - 开始查询坐标点 (22.830435048857865, 108.24950598728077) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-08 10:32:50,463 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 10:32:50,984 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 10:32:50,985 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 10:32:50,988 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 10:32:50,990 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 10:32:50,991 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 10:32:50,992 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 10:32:50,994 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 10:32:50,996 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 10:32:50,997 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 10:32:50,999 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 10:32:51,001 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 10:32:51,036 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:32:51,083 - root - INFO - 图层 'id2' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:32:51,083 - root - INFO - 在坐标点 (22.830435048857865, 108.24950598728077) 处找到 2 个有有效数据的栅格图层
2025-07-08 10:32:51,088 - werkzeug - INFO - ************** - - [08/Jul/2025 10:32:51] "GET /api/query_values?lat=22.830435048857865&lon=108.24950598728077&workspace=test_myworkspace HTTP/1.1" 200 -
