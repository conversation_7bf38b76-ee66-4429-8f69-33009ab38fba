2025-07-14 11:24:28,554 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250714_112428.log
2025-07-14 11:24:28,558 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 11:24:28,558 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 11:24:28,593 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 11:24:28,603 - batch_executor - INFO - 加载了 17 个任务状态
2025-07-14 11:24:28,614 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 11:24:28,614 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 11:24:28,628 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 11:24:28,633 - root - INFO - === GeoServer REST API服务 ===
2025-07-14 11:24:28,634 - root - INFO - 主机: 0.0.0.0
2025-07-14 11:24:28,634 - root - INFO - 端口: 5083
2025-07-14 11:24:28,635 - root - INFO - 调试模式: 禁用
2025-07-14 11:24:28,635 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-14 11:24:28,647 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-14 11:24:28,648 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-14 11:24:31,857 - batch_executor - INFO - 启动任务 f17653ab-ca37-49bc-84c4-646c2840cfcc: D:\Drone_Project\ODM\ODM\run.bat D:\Drone_Project\dataset\DJITESTIMAGE\20250714140500\project --fast-orthophoto
2025-07-14 11:24:31,858 - werkzeug - INFO - ************** - - [14/Jul/2025 11:24:31] "GET /api/batch/execute?batch_path=D:\\Drone_Project\\ODM\\ODM\\run.bat&pos_args=D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250714140500\\project&flags=fast-orthophoto HTTP/1.1" 200 -
