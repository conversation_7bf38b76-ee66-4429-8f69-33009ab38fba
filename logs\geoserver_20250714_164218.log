2025-07-14 16:42:18,134 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250714_164218.log
2025-07-14 16:42:18,136 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 16:42:18,136 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 16:42:19,410 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 16:42:19,489 - batch_executor - INFO - 加载了 34 个任务状态
2025-07-14 16:42:19,504 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 16:42:19,505 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 16:42:19,524 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 16:42:19,532 - root - INFO - === GeoServer REST API服务 ===
2025-07-14 16:42:19,532 - root - INFO - 主机: 0.0.0.0
2025-07-14 16:42:19,532 - root - INFO - 端口: 5083
2025-07-14 16:42:19,533 - root - INFO - 调试模式: 禁用
2025-07-14 16:42:19,533 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-14 16:42:19,560 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-14 16:42:19,560 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-14 16:43:20,671 - batch_executor - INFO - 启动任务 ea5f1341-9f5f-4b0b-9a62-ce5aa87e77b4: D:\Drone_Project\ODM\ODM\run.bat D://Drone_Project//dataset//DJITESTIMAGE//20250705171600//project
2025-07-14 16:43:20,673 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 16:43:20] "GET /api/batch/execute?project_path=D://Drone_Project//dataset//DJITESTIMAGE//20250705171600//project&fast-orthophoto HTTP/1.1" 200 -
2025-07-14 16:43:20,731 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 16:43:20] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-14 16:44:24,092 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 16:44:24] "GET /api/batch/status?task_id=ea5f1341-9f5f-4b0b-9a62-ce5aa87e77b4 HTTP/1.1" 200 -
2025-07-14 16:51:28,961 - werkzeug - INFO - ************** - - [14/Jul/2025 16:51:28] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 16:51:29,047 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 16:51:29,665 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 16:51:29,787 - werkzeug - INFO - ************** - - [14/Jul/2025 16:51:29] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 16:51:33,496 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 16:51:33,630 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 16:51:33,697 - werkzeug - INFO - ************** - - [14/Jul/2025 16:51:33] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 16:52:14,241 - werkzeug - INFO - ************** - - [14/Jul/2025 16:52:14] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 16:52:14,494 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 16:52:14,637 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 16:52:14,652 - werkzeug - INFO - ************** - - [14/Jul/2025 16:52:14] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 17:37:53,630 - werkzeug - INFO - ************** - - [14/Jul/2025 17:37:53] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 17:37:53,643 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 17:37:53,856 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 17:37:53,860 - werkzeug - INFO - ************** - - [14/Jul/2025 17:37:53] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 17:43:39,699 - werkzeug - INFO - 127.0.0.1 - - [14/Jul/2025 17:43:39] "GET /api/batch/status?task_id=ea5f1341-9f5f-4b0b-9a62-ce5aa87e77b4 HTTP/1.1" 200 -
2025-07-14 17:45:25,184 - werkzeug - INFO - ************** - - [14/Jul/2025 17:45:25] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 17:45:25,205 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 17:45:25,442 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 17:45:25,465 - werkzeug - INFO - ************** - - [14/Jul/2025 17:45:25] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 17:45:25,875 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 17:45:25,942 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 17:45:25,952 - werkzeug - INFO - ************** - - [14/Jul/2025 17:45:25] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 17:51:09,415 - werkzeug - INFO - ************** - - [14/Jul/2025 17:51:09] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 17:51:09,427 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 17:51:09,491 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 17:51:09,493 - werkzeug - INFO - ************** - - [14/Jul/2025 17:51:09] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 17:55:56,082 - werkzeug - INFO - ************** - - [14/Jul/2025 17:55:56] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 17:55:56,117 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 17:55:56,198 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 17:55:56,242 - werkzeug - INFO - ************** - - [14/Jul/2025 17:55:56] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 17:57:52,490 - werkzeug - INFO - ************** - - [14/Jul/2025 17:57:52] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 17:57:52,498 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 17:57:52,545 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 17:57:52,558 - werkzeug - INFO - ************** - - [14/Jul/2025 17:57:52] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 17:57:53,024 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 17:57:53,105 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 17:57:53,109 - werkzeug - INFO - ************** - - [14/Jul/2025 17:57:53] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:18:36,146 - werkzeug - INFO - ************** - - [14/Jul/2025 18:18:36] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:18:36,180 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 18:18:36,284 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 18:18:36,301 - werkzeug - INFO - ************** - - [14/Jul/2025 18:18:36] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:20:17,250 - werkzeug - INFO - ************** - - [14/Jul/2025 18:20:17] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:20:17,256 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 18:20:17,292 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 18:20:17,294 - werkzeug - INFO - ************** - - [14/Jul/2025 18:20:17] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:22:51,015 - werkzeug - INFO - ************** - - [14/Jul/2025 18:22:51] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:22:51,032 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 18:22:51,102 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 18:22:51,112 - werkzeug - INFO - ************** - - [14/Jul/2025 18:22:51] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:23:13,346 - werkzeug - INFO - ************** - - [14/Jul/2025 18:23:13] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:23:13,353 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 18:23:13,380 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 18:23:13,381 - werkzeug - INFO - ************** - - [14/Jul/2025 18:23:13] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:26:09,693 - werkzeug - INFO - ************** - - [14/Jul/2025 18:26:09] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:26:09,707 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 18:26:09,756 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 18:26:09,758 - werkzeug - INFO - ************** - - [14/Jul/2025 18:26:09] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:26:10,266 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 18:26:10,312 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 18:26:10,315 - werkzeug - INFO - ************** - - [14/Jul/2025 18:26:10] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:27:39,882 - werkzeug - INFO - ************** - - [14/Jul/2025 18:27:39] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:27:39,912 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 18:27:39,984 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 18:27:40,013 - werkzeug - INFO - ************** - - [14/Jul/2025 18:27:40] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:27:43,689 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 18:27:43,729 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 18:27:43,752 - werkzeug - INFO - ************** - - [14/Jul/2025 18:27:43] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:28:51,980 - werkzeug - INFO - ************** - - [14/Jul/2025 18:28:51] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:28:51,995 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 18:28:52,166 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 18:28:52,182 - werkzeug - INFO - ************** - - [14/Jul/2025 18:28:52] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:28:52,543 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 18:28:52,613 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 18:28:52,765 - werkzeug - INFO - ************** - - [14/Jul/2025 18:28:52] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:37:39,875 - werkzeug - INFO - ************** - - [14/Jul/2025 18:37:39] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-14 18:37:39,888 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-14 18:37:40,011 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-14 18:37:40,074 - werkzeug - INFO - ************** - - [14/Jul/2025 18:37:40] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
