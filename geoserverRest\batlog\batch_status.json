{"e97919ef-7e28-4cef-9ba9-bfa1a842a46c": {"task_id": "e97919ef-7e28-4cef-9ba9-bfa1a842a46c", "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "参数1", "arg2": "参数2"}, "status": "运行失败", "start_time": "2025-07-12 16:08:03", "end_time": "2025-07-12 16:08:03", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\e97919ef-7e28-4cef-9ba9-bfa1a842a46c.log", "error": "'utf-8' codec can't decode byte 0xb2 in position 18: invalid start byte"}, "b826b26a-16e0-4986-980a-350ce23d546f": {"task_id": "b826b26a-16e0-4986-980a-350ce23d546f", "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "参数1", "arg2": "参数2"}, "status": "运行失败", "start_time": "2025-07-12 16:09:15", "end_time": "2025-07-12 16:09:16", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\b826b26a-16e0-4986-980a-350ce23d546f.log", "error": "'utf-8' codec can't decode byte 0xb2 in position 18: invalid start byte"}, "f2964afb-63a6-4dc8-b390-a77c2474e500": {"task_id": "f2964afb-63a6-4dc8-b390-a77c2474e500", "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行失败", "start_time": "2025-07-12 16:09:41", "end_time": "2025-07-12 16:09:41", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\f2964afb-63a6-4dc8-b390-a77c2474e500.log", "error": "'utf-8' codec can't decode bytes in position 8-9: invalid continuation byte"}, "d335d619-139f-458f-942d-9832d84bfe15": {"task_id": "d335d619-139f-458f-942d-9832d84bfe15", "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行失败", "start_time": "2025-07-12 16:09:42", "end_time": "2025-07-12 16:09:42", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\d335d619-139f-458f-942d-9832d84bfe15.log", "error": "'utf-8' codec can't decode bytes in position 8-9: invalid continuation byte"}, "f354168f-7601-4812-bed8-da015777e697": {"task_id": "f354168f-7601-4812-bed8-da015777e697", "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行失败", "start_time": "2025-07-12 16:13:08", "end_time": "2025-07-12 16:13:08", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\f354168f-7601-4812-bed8-da015777e697.log", "error": "'utf-8' codec can't decode bytes in position 8-9: invalid continuation byte"}, "36031c47-27af-413d-920f-0e05d9e51d67": {"task_id": "36031c47-27af-413d-920f-0e05d9e51d67", "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行失败", "start_time": "2025-07-12 16:13:34", "end_time": "2025-07-12 16:13:34", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\36031c47-27af-413d-920f-0e05d9e51d67.log", "error": "'utf-8' codec can't decode bytes in position 8-9: invalid continuation byte"}, "4fe5d6d7-d5fc-4790-9a44-7e3b11ec209b": {"task_id": "4fe5d6d7-d5fc-4790-9a44-7e3b11ec209b", "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行失败", "start_time": "2025-07-12 16:15:41", "end_time": "2025-07-12 16:15:41", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\4fe5d6d7-d5fc-4790-9a44-7e3b11ec209b.log", "error": "'gbk' codec can't decode byte 0x80 in position 11: illegal multibyte sequence"}, "b5f51677-2b03-42fe-a760-fab98935ac3c": {"task_id": "b5f51677-2b03-42fe-a760-fab98935ac3c", "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行失败", "start_time": "2025-07-12 16:18:01", "end_time": "2025-07-12 16:18:01", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\b5f51677-2b03-42fe-a760-fab98935ac3c.log", "error": "'gbk' codec can't decode byte 0x80 in position 11: illegal multibyte sequence"}, "ae525ce7-0792-46d0-a11c-16646f082d00": {"task_id": "ae525ce7-0792-46d0-a11c-16646f082d00", "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行成功", "start_time": "2025-07-12 16:23:23", "end_time": "2025-07-12 16:24:23", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\ae525ce7-0792-46d0-a11c-16646f082d00.log", "return_code": 0}, "c245a134-6280-4221-8a6b-9ef11adc9738": {"task_id": "c245a134-6280-4221-8a6b-9ef11adc9738", "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行成功", "start_time": "2025-07-12 16:25:16", "end_time": "2025-07-12 16:26:16", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\c245a134-6280-4221-8a6b-9ef11adc9738.log", "return_code": 0}, "99160467-19f0-432e-bcb1-71550a346208": {"task_id": "99160467-19f0-432e-bcb1-71550a346208", "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行成功", "start_time": "2025-07-12 16:45:15", "end_time": "2025-07-12 16:45:46", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\99160467-19f0-432e-bcb1-71550a346208.log", "return_code": 0}, "3881ee54-1b17-46be-a1ae-de07d0caeb06": {"task_id": "3881ee54-1b17-46be-a1ae-de07d0caeb06", "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行成功", "start_time": "2025-07-12 16:45:16", "end_time": "2025-07-12 16:45:46", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\3881ee54-1b17-46be-a1ae-de07d0caeb06.log", "return_code": 0}, "185e90d6-dd66-4134-a06d-d58a50ab1a63": {"task_id": "185e90d6-dd66-4134-a06d-d58a50ab1a63", "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行成功", "start_time": "2025-07-12 16:46:00", "end_time": "2025-07-12 16:46:30", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\185e90d6-dd66-4134-a06d-d58a50ab1a63.log", "return_code": 0}, "a1d750b9-5e16-40a8-8c5b-b5c6327e3554": {"task_id": "a1d750b9-5e16-40a8-8c5b-b5c6327e3554", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "pos_args": ["c://user/20250714140500/project/images"], "flags": ["fast-orthophoto"], "kwargs": {}, "working_directory": "D:\\Drone_Project\\ODM\\ODM", "status": "运行成功", "start_time": "2025-07-14 11:06:35", "end_time": "2025-07-14 11:07:00", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\a1d750b9-5e16-40a8-8c5b-b5c6327e3554.log", "return_code": 0}, "f30071ad-d51c-43c0-bb1f-02a8f48b4448": {"task_id": "f30071ad-d51c-43c0-bb1f-02a8f48b4448", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "pos_args": ["D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250714140500\\project"], "flags": ["fast-orthophoto"], "kwargs": {}, "working_directory": "D:\\Drone_Project\\ODM\\ODM", "status": "运行成功", "start_time": "2025-07-14 11:08:57", "end_time": "2025-07-14 11:09:01", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\f30071ad-d51c-43c0-bb1f-02a8f48b4448.log", "return_code": 0}, "f8e80a38-1c1e-4286-9a13-580985be1212": {"task_id": "f8e80a38-1c1e-4286-9a13-580985be1212", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "pos_args": ["D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250714140500\\project"], "flags": ["fast-orthophoto"], "kwargs": {}, "working_directory": "D:\\Drone_Project\\ODM\\ODM", "status": "运行成功", "start_time": "2025-07-14 11:09:52", "end_time": "2025-07-14 11:09:56", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\f8e80a38-1c1e-4286-9a13-580985be1212.log", "return_code": 0}, "f68ffad3-1315-4c81-8809-459e6e9482d4": {"task_id": "f68ffad3-1315-4c81-8809-459e6e9482d4", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "pos_args": ["D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250714140500\\project"], "flags": ["fast-orthophoto"], "kwargs": {}, "working_directory": "D:\\Drone_Project\\ODM\\ODM", "status": "运行失败", "start_time": "2025-07-14 11:20:04", "end_time": "2025-07-14 11:20:09", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\f68ffad3-1315-4c81-8809-459e6e9482d4.log", "return_code": 1}, "f17653ab-ca37-49bc-84c4-646c2840cfcc": {"task_id": "f17653ab-ca37-49bc-84c4-646c2840cfcc", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "pos_args": ["D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250714140500\\project"], "flags": ["fast-orthophoto"], "kwargs": {}, "working_directory": "D:\\Drone_Project\\ODM\\ODM", "status": "运行失败", "start_time": "2025-07-14 11:24:31", "end_time": "2025-07-14 11:24:36", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\f17653ab-ca37-49bc-84c4-646c2840cfcc.log", "return_code": 1}, "4b2e4ee6-281e-4527-a788-547e4b89469f": {"task_id": "4b2e4ee6-281e-4527-a788-547e4b89469f", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "pos_args": ["D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250714140500\\project"], "flags": ["fast-orthophoto"], "kwargs": {}, "working_directory": "D:\\Drone_Project\\ODM\\ODM", "status": "运行失败", "start_time": "2025-07-14 11:28:04", "end_time": "2025-07-14 11:28:09", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\4b2e4ee6-281e-4527-a788-547e4b89469f.log", "return_code": 1}, "bc4aae3c-1c8d-49d3-ac01-d0255c8e49db": {"task_id": "bc4aae3c-1c8d-49d3-ac01-d0255c8e49db", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "pos_args": ["D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250714140500\\project"], "flags": ["fast-orthophoto"], "kwargs": {}, "working_directory": "D:\\Drone_Project\\ODM\\ODM", "status": "运行失败", "start_time": "2025-07-14 11:35:04", "end_time": "2025-07-14 11:35:04", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\bc4aae3c-1c8d-49d3-ac01-d0255c8e49db.log", "error": "'utf-8' codec can't decode byte 0xd5 in position 0: invalid continuation byte"}, "b07d6813-318e-4609-9f0b-053c64c18890": {"task_id": "b07d6813-318e-4609-9f0b-053c64c18890", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "pos_args": ["D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250714140500\\project"], "flags": ["fast-orthophoto"], "kwargs": {}, "working_directory": "D:\\Drone_Project\\ODM\\ODM", "status": "运行失败", "start_time": "2025-07-14 12:01:42", "end_time": "2025-07-14 12:01:47", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\b07d6813-318e-4609-9f0b-053c64c18890.log", "return_code": 1}, "9f2e5dd3-f95b-4956-83de-7d44fc18de64": {"task_id": "9f2e5dd3-f95b-4956-83de-7d44fc18de64", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "pos_args": ["D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250714140500\\project"], "flags": ["fast-orthophoto"], "kwargs": {}, "working_directory": "D:\\Drone_Project\\ODM\\ODM", "status": "运行失败", "start_time": "2025-07-14 12:05:28", "end_time": "2025-07-14 12:05:33", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\9f2e5dd3-f95b-4956-83de-7d44fc18de64.log", "return_code": 1}, "72be7f27-10f8-4af2-b96b-018f79ea52d3": {"task_id": "72be7f27-10f8-4af2-b96b-018f79ea52d3", "batch_path": "D:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行失败", "start_time": "2025-07-14 12:12:01", "end_time": "2025-07-14 12:12:01", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\72be7f27-10f8-4af2-b96b-018f79ea52d3.log", "error": "'utf-8' codec can't decode byte 0xb5 in position 2: invalid start byte"}, "51bfa8b3-beec-41c8-be96-461d36ee8da2": {"task_id": "51bfa8b3-beec-41c8-be96-461d36ee8da2", "batch_path": "D:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行失败", "start_time": "2025-07-14 12:12:19", "end_time": "2025-07-14 12:12:19", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\51bfa8b3-beec-41c8-be96-461d36ee8da2.log", "error": "'utf-8' codec can't decode byte 0xb5 in position 2: invalid start byte"}, "b6d58241-c6e8-4b22-bc16-9dba8b6d4cc1": {"task_id": "b6d58241-c6e8-4b22-bc16-9dba8b6d4cc1", "batch_path": "D:\\Drone_Project\\testdata\\test_script.bat", "args": {"arg1": "v1", "arg2": "v2"}, "status": "运行成功", "start_time": "2025-07-14 12:17:19", "end_time": "2025-07-14 12:17:49", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\b6d58241-c6e8-4b22-bc16-9dba8b6d4cc1.log", "return_code": 0}, "a3e57ec4-1db9-449b-b362-9ed72e410eeb": {"task_id": "a3e57ec4-1db9-449b-b362-9ed72e410eeb", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project"}, "status": "运行成功", "start_time": "2025-07-14 12:30:55", "end_time": "2025-07-14 12:30:59", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\a3e57ec4-1db9-449b-b362-9ed72e410eeb.log", "return_code": 0}, "2bf1ea13-8bd3-43fa-99cb-bc4dbcdedc8d": {"task_id": "2bf1ea13-8bd3-43fa-99cb-bc4dbcdedc8d", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project"}, "status": "运行成功", "start_time": "2025-07-14 12:36:01", "end_time": "2025-07-14 12:36:06", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\2bf1ea13-8bd3-43fa-99cb-bc4dbcdedc8d.log", "return_code": 0}, "dc27d05e-fc6b-457e-bbd4-5129d14033b5": {"task_id": "dc27d05e-fc6b-457e-bbd4-5129d14033b5", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project"}, "status": "运行成功", "start_time": "2025-07-14 15:04:04", "end_time": "2025-07-14 15:04:08", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\dc27d05e-fc6b-457e-bbd4-5129d14033b5.log", "return_code": 0}, "b9fdf65d-9fbb-4db8-9a25-8f62eade5fda": {"task_id": "b9fdf65d-9fbb-4db8-9a25-8f62eade5fda", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project"}, "status": "运行成功", "start_time": "2025-07-14 15:05:09", "end_time": "2025-07-14 15:05:13", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\b9fdf65d-9fbb-4db8-9a25-8f62eade5fda.log", "return_code": 0}, "5e2c5686-3570-4e12-b24c-b720b1f02f33": {"task_id": "5e2c5686-3570-4e12-b24c-b720b1f02f33", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project"}, "status": "运行成功", "start_time": "2025-07-14 15:28:18", "end_time": "2025-07-14 15:28:22", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\5e2c5686-3570-4e12-b24c-b720b1f02f33.log", "return_code": 0}, "d6461d69-6e0c-4c14-bee6-d1ae34679bdd": {"task_id": "d6461d69-6e0c-4c14-bee6-d1ae34679bdd", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project"}, "status": "运行成功", "start_time": "2025-07-14 15:28:29", "end_time": "2025-07-14 15:28:33", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\d6461d69-6e0c-4c14-bee6-d1ae34679bdd.log", "return_code": 0}, "e1c96dee-a587-477c-bcdb-86d15d82bb59": {"task_id": "e1c96dee-a587-477c-bcdb-86d15d82bb59", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project"}, "status": "运行成功", "start_time": "2025-07-14 15:30:50", "end_time": "2025-07-14 15:30:55", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\e1c96dee-a587-477c-bcdb-86d15d82bb59.log", "return_code": 0}, "689cfc93-2e0c-4158-93a4-738fe00f106f": {"task_id": "689cfc93-2e0c-4158-93a4-738fe00f106f", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project"}, "status": "运行成功", "start_time": "2025-07-14 15:36:07", "end_time": "2025-07-14 15:36:11", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\689cfc93-2e0c-4158-93a4-738fe00f106f.log", "return_code": 0}, "49a1b247-5d9a-4249-94ff-34e2196b7a5c": {"task_id": "49a1b247-5d9a-4249-94ff-34e2196b7a5c", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project"}, "status": "正在运行", "start_time": "2025-07-14 15:41:15", "end_time": null, "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\49a1b247-5d9a-4249-94ff-34e2196b7a5c.log"}, "ea5f1341-9f5f-4b0b-9a62-ce5aa87e77b4": {"task_id": "ea5f1341-9f5f-4b0b-9a62-ce5aa87e77b4", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D://Drone_Project//dataset//DJITESTIMAGE//20250705171600//project"}, "status": "运行成功", "start_time": "2025-07-14 16:43:20", "end_time": "2025-07-14 17:36:08", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\ea5f1341-9f5f-4b0b-9a62-ce5aa87e77b4.log", "return_code": 0}, "5e710aa1-c57a-4e74-8159-894fdce5aba9": {"task_id": "5e710aa1-c57a-4e74-8159-894fdce5aba9", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D://Drone_Project//dataset//DJITESTIMAGE//20250705171600//project"}, "status": "运行成功", "start_time": "2025-07-17 09:26:22", "end_time": "2025-07-17 09:27:01", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\5e710aa1-c57a-4e74-8159-894fdce5aba9.log", "return_code": 0}, "9ed3684a-e8e0-49b3-9354-bb465e693de5": {"task_id": "9ed3684a-e8e0-49b3-9354-bb465e693de5", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project"}, "status": "运行成功", "start_time": "2025-07-17 11:36:04", "end_time": "2025-07-17 12:43:55", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\9ed3684a-e8e0-49b3-9354-bb465e693de5.log", "return_code": 0}, "81070a4b-e71f-40fc-bb33-892119f087ed": {"task_id": "81070a4b-e71f-40fc-bb33-892119f087ed", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project"}, "status": "运行成功", "start_time": "2025-07-17 11:36:04", "end_time": "2025-07-17 12:43:55", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\81070a4b-e71f-40fc-bb33-892119f087ed.log", "return_code": 0}, "b6837990-9d23-45c2-bc7e-259ce5103f0e": {"task_id": "b6837990-9d23-45c2-bc7e-259ce5103f0e", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project"}, "status": "运行成功", "start_time": "2025-07-17 11:37:30", "end_time": "2025-07-17 11:40:43", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\b6837990-9d23-45c2-bc7e-259ce5103f0e.log", "return_code": 0}, "fec52faa-7938-49b3-9976-8945a6167739": {"task_id": "fec52faa-7938-49b3-9976-8945a6167739", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project"}, "status": "运行成功", "start_time": "2025-07-17 11:38:20", "end_time": "2025-07-17 11:40:43", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\fec52faa-7938-49b3-9976-8945a6167739.log", "return_code": 0}, "0f79f411-135d-42fa-8272-dd88ef9bfdb3": {"task_id": "0f79f411-135d-42fa-8272-dd88ef9bfdb3", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project"}, "status": "运行成功", "start_time": "2025-07-17 17:30:01", "end_time": "2025-07-17 18:45:27", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\0f79f411-135d-42fa-8272-dd88ef9bfdb3.log", "return_code": 0}, "0100fe85-cb66-4ac7-9ac5-c1a41c1436f2": {"task_id": "0100fe85-cb66-4ac7-9ac5-c1a41c1436f2", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project"}, "status": "运行成功", "start_time": "2025-07-17 17:34:42", "end_time": "2025-07-17 17:36:10", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\0100fe85-cb66-4ac7-9ac5-c1a41c1436f2.log", "return_code": 0}, "775837a8-4aeb-4f08-87df-0f7d1506b0b9": {"task_id": "775837a8-4aeb-4f08-87df-0f7d1506b0b9", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project"}, "status": "运行成功", "start_time": "2025-07-17 17:34:43", "end_time": "2025-07-17 17:36:10", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\775837a8-4aeb-4f08-87df-0f7d1506b0b9.log", "return_code": 0}, "8e04f4cd-e482-4134-8c5b-8448b5184b6c": {"task_id": "8e04f4cd-e482-4134-8c5b-8448b5184b6c", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project"}, "status": "运行成功", "start_time": "2025-07-18 09:53:56", "end_time": "2025-07-18 10:26:21", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\8e04f4cd-e482-4134-8c5b-8448b5184b6c.log", "return_code": 0}, "4d238329-a277-4cee-8b55-99037586a151": {"task_id": "4d238329-a277-4cee-8b55-99037586a151", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171600/project"}, "status": "运行成功", "start_time": "2025-07-18 12:15:35", "end_time": "2025-07-18 12:50:58", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\4d238329-a277-4cee-8b55-99037586a151.log", "return_code": 0}, "b6dd38ff-63bd-4128-9ee1-e087b429ac28": {"task_id": "b6dd38ff-63bd-4128-9ee1-e087b429ac28", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171601/project"}, "status": "运行成功", "start_time": "2025-07-21 10:52:54", "end_time": "2025-07-21 11:10:01", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\b6dd38ff-63bd-4128-9ee1-e087b429ac28.log", "return_code": 0}, "d199ba41-c702-447c-a5ff-cf0269b8a3b1": {"task_id": "d199ba41-c702-447c-a5ff-cf0269b8a3b1", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-21 11:20:02", "end_time": "2025-07-21 11:38:31", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\d199ba41-c702-447c-a5ff-cf0269b8a3b1.log", "return_code": 0}, "f6068397-37b4-4f98-b1b5-7e2af1a23e6c": {"task_id": "f6068397-37b4-4f98-b1b5-7e2af1a23e6c", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-24 09:45:07", "end_time": "2025-07-24 10:06:32", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\f6068397-37b4-4f98-b1b5-7e2af1a23e6c.log", "return_code": 0}, "5ba2fdc2-f850-470c-ba95-b3197e561b48": {"task_id": "5ba2fdc2-f850-470c-ba95-b3197e561b48", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171601/project"}, "status": "运行成功", "start_time": "2025-07-24 11:35:03", "end_time": "2025-07-24 11:35:34", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\5ba2fdc2-f850-470c-ba95-b3197e561b48.log", "return_code": 0}, "39113527-7924-4221-8238-889c356d5c51": {"task_id": "39113527-7924-4221-8238-889c356d5c51", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171601/project"}, "status": "运行成功", "start_time": "2025-07-24 15:15:04", "end_time": "2025-07-24 15:15:32", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\39113527-7924-4221-8238-889c356d5c51.log", "return_code": 0}, "5a392606-e2d0-4ded-afe1-003f5a6bdeb4": {"task_id": "5a392606-e2d0-4ded-afe1-003f5a6bdeb4", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-24 16:11:55", "end_time": "2025-07-24 16:12:19", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\5a392606-e2d0-4ded-afe1-003f5a6bdeb4.log", "return_code": 0}, "d0d93862-342e-461e-a4ac-f77801a11543": {"task_id": "d0d93862-342e-461e-a4ac-f77801a11543", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-24 16:26:05", "end_time": "2025-07-24 16:26:27", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\d0d93862-342e-461e-a4ac-f77801a11543.log", "return_code": 0}, "517df9a1-74d1-442c-bc26-e9bbb77c18fe": {"task_id": "517df9a1-74d1-442c-bc26-e9bbb77c18fe", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-24 16:40:35", "end_time": "2025-07-24 16:43:23", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\517df9a1-74d1-442c-bc26-e9bbb77c18fe.log", "return_code": 0}, "d3004cb4-4fe7-43ae-95ce-ffbef65908db": {"task_id": "d3004cb4-4fe7-43ae-95ce-ffbef65908db", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-24 16:44:08", "end_time": "2025-07-24 16:44:15", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\d3004cb4-4fe7-43ae-95ce-ffbef65908db.log", "return_code": 0}, "cb1d3f41-49fe-4d67-ac7f-1b6ba3f87fd6": {"task_id": "cb1d3f41-49fe-4d67-ac7f-1b6ba3f87fd6", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-24 16:53:13", "end_time": "2025-07-24 16:53:44", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\cb1d3f41-49fe-4d67-ac7f-1b6ba3f87fd6.log", "return_code": 0}, "3b3c5ba9-4cb7-4e16-94ac-161cbe2f9c7a": {"task_id": "3b3c5ba9-4cb7-4e16-94ac-161cbe2f9c7a", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-25 17:07:05", "end_time": "2025-07-25 17:07:47", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\3b3c5ba9-4cb7-4e16-94ac-161cbe2f9c7a.log", "return_code": 0}, "f9c636f8-b5b6-4ff9-b94f-4429f88a8cd8": {"task_id": "f9c636f8-b5b6-4ff9-b94f-4429f88a8cd8", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-25 17:41:04", "end_time": "2025-07-25 17:41:32", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\f9c636f8-b5b6-4ff9-b94f-4429f88a8cd8.log", "return_code": 0}, "550687ac-105b-4e27-97bf-9258c34647cc": {"task_id": "550687ac-105b-4e27-97bf-9258c34647cc", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-28 09:09:02", "end_time": "2025-07-28 09:09:51", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\550687ac-105b-4e27-97bf-9258c34647cc.log", "return_code": 0}, "00cb79f6-747c-42ed-bcbd-0f8171b7546e": {"task_id": "00cb79f6-747c-42ed-bcbd-0f8171b7546e", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171601/project"}, "status": "运行成功", "start_time": "2025-07-28 09:17:03", "end_time": "2025-07-28 09:17:34", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\00cb79f6-747c-42ed-bcbd-0f8171b7546e.log", "return_code": 0}, "5fcf1986-3aa3-49b3-8266-5c1f73cdef24": {"task_id": "5fcf1986-3aa3-49b3-8266-5c1f73cdef24", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171601/project"}, "status": "运行成功", "start_time": "2025-07-28 09:18:06", "end_time": "2025-07-28 09:18:17", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\5fcf1986-3aa3-49b3-8266-5c1f73cdef24.log", "return_code": 0}, "ff07c3dc-98af-49e4-a70b-6a1063e1fb27": {"task_id": "ff07c3dc-98af-49e4-a70b-6a1063e1fb27", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-28 09:34:02", "end_time": "2025-07-28 09:35:04", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\ff07c3dc-98af-49e4-a70b-6a1063e1fb27.log", "return_code": 0}, "caee250a-e3c4-4198-8f89-d60ed6ba6aaa": {"task_id": "caee250a-e3c4-4198-8f89-d60ed6ba6aaa", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171601/project"}, "status": "运行成功", "start_time": "2025-07-28 10:28:04", "end_time": "2025-07-28 10:38:41", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\caee250a-e3c4-4198-8f89-d60ed6ba6aaa.log", "return_code": 0}, "45afaa23-2843-456a-aac6-589d464d4ad8": {"task_id": "45afaa23-2843-456a-aac6-589d464d4ad8", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-28 11:33:03", "end_time": "2025-07-28 11:33:28", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\45afaa23-2843-456a-aac6-589d464d4ad8.log", "return_code": 0}, "e828cd9f-7f0f-4e8e-9a49-f7a1cff7d3b5": {"task_id": "e828cd9f-7f0f-4e8e-9a49-f7a1cff7d3b5", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-28 15:13:03", "end_time": "2025-07-28 15:13:27", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\e828cd9f-7f0f-4e8e-9a49-f7a1cff7d3b5.log", "return_code": 0}, "8e7d569b-27cd-449d-9796-324c44290a43": {"task_id": "8e7d569b-27cd-449d-9796-324c44290a43", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project"}, "status": "运行成功", "start_time": "2025-07-28 16:41:34", "end_time": "2025-07-28 16:42:11", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\8e7d569b-27cd-449d-9796-324c44290a43.log", "return_code": 0}, "78f245de-03a9-475e-af6d-e67ef2b7c708": {"task_id": "78f245de-03a9-475e-af6d-e67ef2b7c708", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171601/project"}, "status": "运行成功", "start_time": "2025-07-28 17:17:20", "end_time": "2025-07-28 17:17:44", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\78f245de-03a9-475e-af6d-e67ef2b7c708.log", "return_code": 0}, "e9dd7a97-5572-451b-bba5-08f189ca02c3": {"task_id": "e9dd7a97-5572-451b-bba5-08f189ca02c3", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:18:17", "end_time": "2025-08-04 11:24:47", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\e9dd7a97-5572-451b-bba5-08f189ca02c3.log", "return_code": 0}, "8fe60cee-5933-43bf-8cc9-769052e5d803": {"task_id": "8fe60cee-5933-43bf-8cc9-769052e5d803", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:30:03", "end_time": "2025-08-04 11:30:36", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\8fe60cee-5933-43bf-8cc9-769052e5d803.log", "return_code": 0}, "08b2e6c8-1965-44b1-bb3c-3dc0537e35ee": {"task_id": "08b2e6c8-1965-44b1-bb3c-3dc0537e35ee", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:32:08", "end_time": "2025-08-04 11:32:12", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\08b2e6c8-1965-44b1-bb3c-3dc0537e35ee.log", "return_code": 0}, "5bca7da7-7ecf-4e87-b047-511c085bbcaa": {"task_id": "5bca7da7-7ecf-4e87-b047-511c085bbcaa", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:33:09", "end_time": "2025-08-04 11:33:14", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\5bca7da7-7ecf-4e87-b047-511c085bbcaa.log", "return_code": 0}, "925c4f4c-34e9-474f-85c4-f161e99356fa": {"task_id": "925c4f4c-34e9-474f-85c4-f161e99356fa", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:34:11", "end_time": "2025-08-04 11:34:16", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\925c4f4c-34e9-474f-85c4-f161e99356fa.log", "return_code": 0}, "d65d1543-f56f-43e4-8920-c6f9c2cbd8bb": {"task_id": "d65d1543-f56f-43e4-8920-c6f9c2cbd8bb", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:35:14", "end_time": "2025-08-04 11:35:18", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\d65d1543-f56f-43e4-8920-c6f9c2cbd8bb.log", "return_code": 0}, "5392a6f4-6b47-4876-9114-4b21e04801fc": {"task_id": "5392a6f4-6b47-4876-9114-4b21e04801fc", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:36:16", "end_time": "2025-08-04 11:36:20", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\5392a6f4-6b47-4876-9114-4b21e04801fc.log", "return_code": 0}, "2ec48cf3-78ba-4eb6-86bf-b0ab3578c75f": {"task_id": "2ec48cf3-78ba-4eb6-86bf-b0ab3578c75f", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:37:18", "end_time": "2025-08-04 11:37:22", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\2ec48cf3-78ba-4eb6-86bf-b0ab3578c75f.log", "return_code": 0}, "d29e812f-71ec-4ef2-9d85-3314946fbb61": {"task_id": "d29e812f-71ec-4ef2-9d85-3314946fbb61", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:38:20", "end_time": "2025-08-04 11:38:25", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\d29e812f-71ec-4ef2-9d85-3314946fbb61.log", "return_code": 0}, "ea79a80e-cd07-41da-8de2-3882e4bb1fab": {"task_id": "ea79a80e-cd07-41da-8de2-3882e4bb1fab", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:39:22", "end_time": "2025-08-04 11:39:27", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\ea79a80e-cd07-41da-8de2-3882e4bb1fab.log", "return_code": 0}, "90c9ece2-9bd5-4842-9695-e70cecda3058": {"task_id": "90c9ece2-9bd5-4842-9695-e70cecda3058", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:40:24", "end_time": "2025-08-04 11:40:33", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\90c9ece2-9bd5-4842-9695-e70cecda3058.log", "return_code": 0}, "56354957-f205-4368-8db7-7d71cb69700a": {"task_id": "56354957-f205-4368-8db7-7d71cb69700a", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:45:30", "end_time": "2025-08-04 11:45:52", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\56354957-f205-4368-8db7-7d71cb69700a.log", "return_code": 0}, "f85f12b9-1570-4bd2-b02c-a3da53604883": {"task_id": "f85f12b9-1570-4bd2-b02c-a3da53604883", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:46:32", "end_time": "2025-08-04 11:46:40", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\f85f12b9-1570-4bd2-b02c-a3da53604883.log", "return_code": 0}, "2581216f-4d7c-43fc-8409-fd0e734c0eb9": {"task_id": "2581216f-4d7c-43fc-8409-fd0e734c0eb9", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:47:33", "end_time": "2025-08-04 11:47:50", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\2581216f-4d7c-43fc-8409-fd0e734c0eb9.log", "return_code": 0}, "22983e9e-4ce9-4bd8-a056-fad517dbaaf5": {"task_id": "22983e9e-4ce9-4bd8-a056-fad517dbaaf5", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:48:37", "end_time": "2025-08-04 11:48:41", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\22983e9e-4ce9-4bd8-a056-fad517dbaaf5.log", "return_code": 0}, "d98cfa5b-0d5e-413b-8b54-4d72d76481a6": {"task_id": "d98cfa5b-0d5e-413b-8b54-4d72d76481a6", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:49:37", "end_time": "2025-08-04 11:49:41", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\d98cfa5b-0d5e-413b-8b54-4d72d76481a6.log", "return_code": 0}, "f9858f81-5b11-42e5-883e-88515ca8a7f6": {"task_id": "f9858f81-5b11-42e5-883e-88515ca8a7f6", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:50:40", "end_time": "2025-08-04 11:50:45", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\f9858f81-5b11-42e5-883e-88515ca8a7f6.log", "return_code": 0}, "217f76fb-7b38-42de-b8cd-ca5ecb34cdd8": {"task_id": "217f76fb-7b38-42de-b8cd-ca5ecb34cdd8", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:51:43", "end_time": "2025-08-04 11:51:47", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\217f76fb-7b38-42de-b8cd-ca5ecb34cdd8.log", "return_code": 0}, "cfc2ab4a-8a53-4bd1-b378-3fdbbc0d6fe8": {"task_id": "cfc2ab4a-8a53-4bd1-b378-3fdbbc0d6fe8", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:52:46", "end_time": "2025-08-04 11:52:51", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\cfc2ab4a-8a53-4bd1-b378-3fdbbc0d6fe8.log", "return_code": 0}, "c5743f8d-9b74-469d-b865-f21011e8a03c": {"task_id": "c5743f8d-9b74-469d-b865-f21011e8a03c", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:53:49", "end_time": "2025-08-04 11:53:53", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\c5743f8d-9b74-469d-b865-f21011e8a03c.log", "return_code": 0}, "c85bf9c9-a151-4312-9367-0e2fe1400494": {"task_id": "c85bf9c9-a151-4312-9367-0e2fe1400494", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "运行成功", "start_time": "2025-08-04 11:54:52", "end_time": "2025-08-04 11:54:56", "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\c85bf9c9-a151-4312-9367-0e2fe1400494.log", "return_code": 0}, "db294e0b-6f09-4135-9b85-d85f23f16dec": {"id": "db294e0b-6f09-4135-9b85-d85f23f16dec", "batch_path": "D:\\Drone_Project\\ODM\\ODM\\run.bat", "args": {"project_path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project"}, "status": "completed", "start_time": "2025-08-05T08:57:41.510586", "end_time": "2025-08-05T09:02:28.855955", "exit_code": 0, "log_file": "D:\\Drone_Project\\geoserverAPIDJ\\geoserverRest\\batlog\\db294e0b-6f09-4135-9b85-d85f23f16dec.log"}}