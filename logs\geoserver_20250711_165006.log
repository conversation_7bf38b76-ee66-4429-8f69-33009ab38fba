2025-07-11 16:50:06,040 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250711_165006.log
2025-07-11 16:50:06,045 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-11 16:50:06,045 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-11 16:50:06,076 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-11 16:50:06,098 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-11 16:50:06,099 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-11 16:50:06,121 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-11 16:50:06,131 - root - INFO - === GeoServer REST API服务 ===
2025-07-11 16:50:06,131 - root - INFO - 主机: 0.0.0.0
2025-07-11 16:50:06,131 - root - INFO - 端口: 5000
2025-07-11 16:50:06,132 - root - INFO - 调试模式: 启用
2025-07-11 16:50:06,132 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5000
2025-07-11 16:50:06,167 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-11 16:50:06,168 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-11 16:50:06,173 - werkzeug - INFO -  * Restarting with stat
