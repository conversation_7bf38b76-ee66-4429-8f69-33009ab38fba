#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer发布视图 - GeoServer异步发布相关功能
"""

import os
import logging
import glob
from django.http import JsonResponse

# 导入核心模块
from ..core.geo_publisher import geo_executor, geo_logger


def execute_publish_shapefile(request):
    """
    异步发布Shapefile到GeoServer - 与Flask版本完全一致

    查询参数:
        path: Shapefile文件路径（必需）
        workspace: 工作区名称（必需）
        store_name: 存储名称（可选）
        layer_name: 图层名称（可选）
        charset: 字符集，默认UTF-8

    返回:
        任务ID及状态信息

    注意:
        如果GeoServer中已存在相同工作区和图层名的图层，系统会自动删除已有图层，然后发布新图层。
        这样可以确保更新数据时能够正确替换旧图层。
    """
    try:
        # 获取参数
        shapefile_path = request.GET.get("path")
        workspace = request.GET.get("workspace")

        # 验证必要参数
        if not shapefile_path or not workspace:
            missing_params = []
            if not shapefile_path:
                missing_params.append("path")
            if not workspace:
                missing_params.append("workspace")

            return JsonResponse({
                "status": "error",
                "message": f'缺少必要参数: {", ".join(missing_params)}',
            }, status=400)

        # 检查文件是否存在
        if not os.path.exists(shapefile_path):
            return JsonResponse({
                "status": "error",
                "message": f"Shapefile文件不存在: {shapefile_path}",
            }, status=404)

        # 可选参数
        store_name = request.GET.get("store_name")
        layer_name = request.GET.get("layer_name")
        charset = request.GET.get("charset", "UTF-8")

        # 执行异步发布任务
        task_id = geo_executor.execute_publish_shapefile(
            shapefile_path, workspace, store_name, layer_name, charset
        )

        if task_id:
            return JsonResponse({
                "status": "success",
                "message": "Shapefile发布任务已启动",
                "task_id": task_id,
            })
        else:
            return JsonResponse({
                "status": "error",
                "message": "启动Shapefile发布任务失败"
            }, status=500)

    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            "status": "error",
            "message": f"服务器内部错误: {str(e)}"
        }, status=500)


def execute_publish_geotiff(request):
    """
    异步发布GeoTIFF到GeoServer - 与Flask版本完全一致

    查询参数:
        path: GeoTIFF文件路径（必需）
        workspace: 工作区名称（必需）
        store_name: 存储名称（可选）
        layer_name: 图层名称（可选）

    返回:
        任务ID及状态信息

    注意:
        如果GeoServer中已存在相同工作区和图层名的图层，系统会自动删除已有图层，然后发布新图层。
        这样可以确保更新数据时能够正确替换旧图层。
    """
    try:
        # 获取参数
        geotiff_path = request.GET.get("path")
        workspace = request.GET.get("workspace")

        # 验证必要参数
        if not geotiff_path or not workspace:
            missing_params = []
            if not geotiff_path:
                missing_params.append("path")
            if not workspace:
                missing_params.append("workspace")

            return JsonResponse({
                "status": "error",
                "message": f'缺少必要参数: {", ".join(missing_params)}',
            }, status=400)

        # 检查文件是否存在
        if not os.path.exists(geotiff_path):
            return JsonResponse({
                "status": "error",
                "message": f"GeoTIFF文件不存在: {geotiff_path}"
            }, status=404)

        # 可选参数
        store_name = request.GET.get("store_name")
        layer_name = request.GET.get("layer_name")

        # 执行异步发布任务
        task_id = geo_executor.execute_publish_geotiff(
            geotiff_path, workspace, store_name, layer_name
        )

        if task_id:
            return JsonResponse({
                "status": "success",
                "message": "GeoTIFF发布任务已启动",
                "task_id": task_id,
            })
        else:
            return JsonResponse({
                "status": "error",
                "message": "启动GeoTIFF发布任务失败"
            }, status=500)

    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            "status": "error",
            "message": f"服务器内部错误: {str(e)}"
        }, status=500)


def execute_publish_shapefile_directory(request):
    """
    异步发布Shapefile目录
    
    查询参数:
        directory: Shapefile目录路径
        workspace: 工作区名称
        store_name: 存储名称前缀 (可选)
        charset: 字符集 (可选，默认: UTF-8)
    """
    try:
        directory = request.GET.get('directory')
        workspace = request.GET.get('workspace')
        
        if not all([directory, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: directory, workspace'
            }, status=400)
        
        if not os.path.exists(directory):
            return JsonResponse({
                'status': 'error',
                'message': f'目录不存在: {directory}'
            }, status=404)
        
        # 查找目录中的所有Shapefile
        shapefiles = glob.glob(os.path.join(directory, "*.shp"))
        
        if not shapefiles:
            return JsonResponse({
                'status': 'error',
                'message': f'目录中没有找到Shapefile: {directory}'
            }, status=404)
        
        # 收集发布参数
        params = {}
        for key, value in request.GET.items():
            if key not in ['directory', 'workspace'] and value:
                params[key] = value
        
        # 为每个Shapefile创建异步发布任务
        task_ids = []
        for shapefile in shapefiles:
            task_id = geo_executor.publish_shapefile_async(shapefile, workspace, **params)
            task_ids.append({
                'file': shapefile,
                'task_id': task_id
            })
        
        return JsonResponse({
            'status': 'success',
            'message': f'已启动 {len(task_ids)} 个Shapefile发布任务',
            'tasks': task_ids
        })
        
    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def execute_publish_geotiff_directory(request):
    """
    异步发布GeoTIFF目录
    
    查询参数:
        directory: GeoTIFF目录路径
        workspace: 工作区名称
        store_name: 存储名称前缀 (可选)
    """
    try:
        directory = request.GET.get('directory')
        workspace = request.GET.get('workspace')
        
        if not all([directory, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: directory, workspace'
            }, status=400)
        
        if not os.path.exists(directory):
            return JsonResponse({
                'status': 'error',
                'message': f'目录不存在: {directory}'
            }, status=404)
        
        # 查找目录中的所有GeoTIFF文件
        geotiffs = []
        for ext in ['*.tif', '*.tiff', '*.TIF', '*.TIFF']:
            geotiffs.extend(glob.glob(os.path.join(directory, ext)))
        
        if not geotiffs:
            return JsonResponse({
                'status': 'error',
                'message': f'目录中没有找到GeoTIFF文件: {directory}'
            }, status=404)
        
        # 收集发布参数
        params = {}
        for key, value in request.GET.items():
            if key not in ['directory', 'workspace'] and value:
                params[key] = value
        
        # 为每个GeoTIFF创建异步发布任务
        task_ids = []
        for geotiff in geotiffs:
            task_id = geo_executor.publish_geotiff_async(geotiff, workspace, **params)
            task_ids.append({
                'file': geotiff,
                'task_id': task_id
            })
        
        return JsonResponse({
            'status': 'success',
            'message': f'已启动 {len(task_ids)} 个GeoTIFF发布任务',
            'tasks': task_ids
        })
        
    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def execute_publish_structured_geotiff(request):
    """
    异步发布特定结构的GeoTIFF文件
    
    查询参数:
        root_directory: 根目录路径
        workspace: 工作区名称
        store_name: 存储名称前缀 (可选)
    """
    try:
        root_directory = request.GET.get('root_directory')
        workspace = request.GET.get('workspace')
        
        if not all([root_directory, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: root_directory, workspace'
            }, status=400)
        
        if not os.path.exists(root_directory):
            return JsonResponse({
                'status': 'error',
                'message': f'根目录不存在: {root_directory}'
            }, status=404)
        
        # 扫描根目录下的子目录
        subdirs = [d for d in os.listdir(root_directory) 
                  if os.path.isdir(os.path.join(root_directory, d))]
        
        if not subdirs:
            return JsonResponse({
                'status': 'error',
                'message': f'根目录中没有找到子目录: {root_directory}'
            }, status=404)
        
        # 查找每个子目录中与子目录同名的GeoTIFF文件
        task_ids = []
        store_name_prefix = request.GET.get('store_name', 'structured_geotiff')
        
        for subdir in subdirs:
            subdir_path = os.path.join(root_directory, subdir)
            geotiff_path = os.path.join(subdir_path, f"{subdir}.tif")
            
            if os.path.exists(geotiff_path):
                params = {
                    'store_name': f"{store_name_prefix}_{subdir}",
                    'layer_name': subdir
                }
                
                task_id = geo_executor.publish_geotiff_async(geotiff_path, workspace, **params)
                task_ids.append({
                    'subdirectory': subdir,
                    'file': geotiff_path,
                    'task_id': task_id
                })
        
        if not task_ids:
            return JsonResponse({
                'status': 'error',
                'message': f'没有找到符合结构要求的GeoTIFF文件'
            }, status=404)
        
        return JsonResponse({
            'status': 'success',
            'message': f'已启动 {len(task_ids)} 个特定结构GeoTIFF发布任务',
            'tasks': task_ids
        })
        
    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_geo_status(request):
    """
    获取GeoServer发布任务状态 - 与Flask版本完全一致

    查询参数:
        task_id: 任务ID
    """
    try:
        task_id = request.GET.get("task_id")
        if not task_id:
            return JsonResponse({
                "status": "error",
                "message": "缺少必要参数: task_id"
            }, status=400)

        task_status = geo_executor.get_task_status(task_id)
        if not task_status:
            return JsonResponse({
                "status": "error",
                "message": f"未找到任务: {task_id}"
            }, status=404)

        return JsonResponse({
            "status": "success",
            "task": task_status
        })

    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            "status": "error",
            "message": f"服务器内部错误: {str(e)}"
        }, status=500)


def get_geo_log(request):
    """
    获取GeoServer发布任务日志

    查询参数:
        task_id: 任务ID
    """
    try:
        task_id = request.GET.get('task_id')
        if not task_id:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: task_id'
            }, status=400)

        log_content = geo_executor.get_task_log(task_id)
        if log_content is None:
            return JsonResponse({
                'status': 'error',
                'message': f'任务 {task_id} 的日志不存在'
            }, status=404)

        return JsonResponse({
            'status': 'success',
            'task_id': task_id,
            'log': log_content
        })

    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_all_geo_status(request):
    """
    获取所有GeoServer发布任务状态
    """
    try:
        tasks = geo_executor.get_all_tasks()
        return JsonResponse({
            'status': 'success',
            'count': len(tasks),
            'tasks': tasks
        })

    except Exception as e:
        geo_logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)
