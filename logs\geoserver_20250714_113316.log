2025-07-14 11:33:16,878 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250714_113316.log
2025-07-14 11:33:16,881 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 11:33:16,881 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 11:33:16,942 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 11:33:16,954 - batch_executor - INFO - 加载了 19 个任务状态
2025-07-14 11:33:16,973 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 11:33:16,974 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 11:33:16,991 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 11:33:16,997 - root - INFO - === GeoServer REST API服务 ===
2025-07-14 11:33:16,997 - root - INFO - 主机: 0.0.0.0
2025-07-14 11:33:16,997 - root - INFO - 端口: 5083
2025-07-14 11:33:16,998 - root - INFO - 调试模式: 禁用
2025-07-14 11:33:16,998 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-14 11:33:17,013 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-14 11:33:17,014 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-14 11:35:04,023 - batch_executor - INFO - 启动任务 bc4aae3c-1c8d-49d3-ac01-d0255c8e49db: D:\Drone_Project\ODM\ODM\run.bat D:\Drone_Project\dataset\DJITESTIMAGE\20250714140500\project --fast-orthophoto
2025-07-14 11:35:04,025 - werkzeug - INFO - ************** - - [14/Jul/2025 11:35:04] "GET /api/batch/execute?batch_path=D:\\Drone_Project\\ODM\\ODM\\run.bat&pos_args=D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250714140500\\project&flags=fast-orthophoto HTTP/1.1" 200 -
2025-07-14 11:35:04,077 - batch_executor - ERROR - 执行任务 bc4aae3c-1c8d-49d3-ac01-d0255c8e49db 时出错: 'utf-8' codec can't decode byte 0xd5 in position 0: invalid continuation byte
