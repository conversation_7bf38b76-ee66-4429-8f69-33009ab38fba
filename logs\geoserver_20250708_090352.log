2025-07-08 09:03:52,429 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250708_090352.log
2025-07-08 09:03:54,588 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-08 09:03:54,588 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-08 09:03:55,671 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-08 09:03:55,676 - geoserver_query_api - INFO - === GeoServer栅格数据坐标查询API服务 ===
2025-07-08 09:03:55,676 - geoserver_query_api - INFO - 主机: 0.0.0.0
2025-07-08 09:03:55,676 - geoserver_query_api - INFO - 端口: 5000
2025-07-08 09:03:55,678 - geoserver_query_api - INFO - 调试模式: 禁用
2025-07-08 09:03:55,688 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-08 09:03:55,689 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 09:03:56,172 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 09:03:59,520 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 09:03:59,523 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 09:03:59,527 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 09:03:59,530 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 09:03:59,531 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 09:03:59,533 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 09:03:59,535 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 09:03:59,537 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 09:03:59,538 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 09:03:59,540 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 09:03:59,542 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 09:03:59,739 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 09:03:59,937 - root - INFO - 在工作区 'test_myworkspace' 中找到 5 个矢量图层
2025-07-08 09:03:59,938 - root - INFO - 在工作区 'test_myworkspace' 中找到 15 个图层（10 个栅格，5 个矢量）
2025-07-08 09:03:59,943 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 09:03:59] "GET /api/all_layers?workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 09:04:00,812 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 09:04:00,816 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 09:04:00,822 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 09:04:00,825 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 09:04:00,826 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 09:04:00,827 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 09:04:00,829 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 09:04:00,833 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 09:04:00,835 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 09:04:00,836 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 09:04:00,839 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 09:04:01,114 - root - INFO - 在工作区 'test_myworkspace' 中找到 5 个矢量图层
2025-07-08 09:04:01,115 - root - INFO - 在工作区 'test_myworkspace' 中找到 15 个图层（10 个栅格，5 个矢量）
2025-07-08 09:04:01,123 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 09:04:01] "GET /api/all_layers?workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 10:11:23,096 - root - INFO - 开始查询坐标点 (22.900601007019077, 108.35050523079788) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-08 10:11:23,324 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 10:11:23,781 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 10:11:23,782 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 10:11:23,785 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 10:11:23,786 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 10:11:23,786 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 10:11:23,787 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 10:11:23,789 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 10:11:23,790 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 10:11:23,791 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 10:11:23,791 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 10:11:23,793 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 10:11:24,064 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:11:24,162 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:11:24,163 - root - INFO - 在坐标点 (22.900601007019077, 108.35050523079788) 处找到 2 个有有效数据的栅格图层
2025-07-08 10:11:24,168 - werkzeug - INFO - ************** - - [08/Jul/2025 10:11:24] "GET /api/query_values?lat=22.900601007019077&lon=108.35050523079788&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 10:15:19,374 - root - INFO - 开始查询坐标点 (22.88411873815687, 108.39934769230898) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-08 10:15:19,569 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 10:15:19,915 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 10:15:19,916 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 10:15:19,921 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 10:15:19,923 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 10:15:19,924 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 10:15:19,926 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 10:15:19,927 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 10:15:19,928 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 10:15:19,930 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 10:15:19,931 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 10:15:19,932 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 10:15:19,965 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:15:20,069 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:15:20,069 - root - INFO - 在坐标点 (22.88411873815687, 108.39934769230898) 处找到 2 个有有效数据的栅格图层
2025-07-08 10:15:20,073 - werkzeug - INFO - ************** - - [08/Jul/2025 10:15:20] "GET /api/query_values?lat=22.88411873815687&lon=108.39934769230898&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 10:15:24,356 - root - INFO - 开始查询坐标点 (22.807386479903286, 108.30441445725923) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-08 10:15:24,602 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 10:15:25,045 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 10:15:25,046 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 10:15:25,049 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 10:15:25,051 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 10:15:25,052 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 10:15:25,053 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 10:15:25,055 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 10:15:25,056 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 10:15:25,058 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 10:15:25,059 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 10:15:25,060 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 10:15:25,083 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:15:25,142 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:15:25,142 - root - INFO - 在坐标点 (22.807386479903286, 108.30441445725923) 处找到 2 个有有效数据的栅格图层
2025-07-08 10:15:25,146 - werkzeug - INFO - ************** - - [08/Jul/2025 10:15:25] "GET /api/query_values?lat=22.807386479903286&lon=108.30441445725923&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 10:26:31,772 - root - INFO - 开始查询坐标点 (22.904150935716217, 108.44941483369612) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-08 10:26:31,963 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 10:26:32,369 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 10:26:32,370 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 10:26:32,374 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 10:26:32,375 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 10:26:32,376 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 10:26:32,377 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 10:26:32,378 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 10:26:32,379 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 10:26:32,380 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 10:26:32,383 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 10:26:32,384 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 10:26:32,411 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:26:32,445 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-08 10:26:32,445 - root - INFO - 在坐标点 (22.904150935716217, 108.44941483369612) 处找到 2 个有有效数据的栅格图层
2025-07-08 10:26:32,452 - werkzeug - INFO - ************** - - [08/Jul/2025 10:26:32] "GET /api/query_values?lat=22.904150935716217&lon=108.44941483369612&workspace=test_myworkspace HTTP/1.1" 200 -
