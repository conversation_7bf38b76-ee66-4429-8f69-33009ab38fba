@echo off

echo Starting GeoServer API service...

REM Return to original directory
cd /d "%~dp0"

REM Initialize conda and activate environment
call conda.bat activate geoserverapi

REM Display current environment
echo Current Python environment:
where python
python --version

REM Run server
cd /d D:\Drone_Project\geoserverapi
python -m geoserverRest.run --host 0.0.0.0 --port 5083

REM Pause to view error messages if server exits
pause 