2025-07-24 15:19:09,135 - INFO - ============ TIF处理任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 开始执行 ============
2025-07-24 15:19:09,142 - INFO - 开始时间: 2025-07-24 15:19:08
2025-07-24 15:19:09,146 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 15:19:09,148 - INFO - 系统信息:
2025-07-24 15:19:09,152 - INFO -   操作系统: Windows 10.0.19045
2025-07-24 15:19:09,331 - INFO -   Python版本: 3.8.20
2025-07-24 15:19:09,338 - INFO -   GDAL版本: 3.9.2
2025-07-24 15:19:09,341 - INFO -   GPU可用: 否
2025-07-24 15:19:09,342 - INFO - 检查参数有效性...
2025-07-24 15:19:09,343 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 15:19:09,343 - INFO - 开始执行TIF处理流程...
2025-07-24 15:19:09,347 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 15:19:09,354 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif
2025-07-24 15:19:09,358 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp
2025-07-24 15:19:09,364 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-24 15:20:29,063 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17007, 高=20364, 波段数=4开始读取影像数据，大小约: 5284.58 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (15:20:28)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-24 15:20:29,069 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-24 15:20:29,839 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:15,  1.33it/s]
2025-07-24 15:20:32,008 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:02<00:04,  2.87it/s]
2025-07-24 15:20:32,307 - ERROR - 
处理数据块:  43%|##############################4                                        | 9/21 [00:03<00:04,  2.90it/s]
2025-07-24 15:20:32,311 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:03<00:00,  6.49it/s]
2025-07-24 15:20:32,330 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-24 15:20:32,332 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-24 15:20:32,410 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 271.26it/s]
2025-07-24 15:20:43,792 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 4.90 秒 (15:20:33)预计总处理时间: 约 14.71 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif (15:20:33)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif使用已处理过的影像信息影像信息: 宽=17007, 高=20364, 波段数=3掩码形状: (20364, 17007), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, 尺寸: 17007x20364x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-24 15:20:43,795 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-24 15:20:58,824 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=42.58, std=63.34应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-24 15:20:58,856 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 15:20:58,863 - ERROR - [A
2025-07-24 15:20:59,053 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  5.32it/s]
2025-07-24 15:20:59,054 - ERROR - [A
2025-07-24 15:20:59,187 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:01, 13.87it/s]
2025-07-24 15:20:59,196 - ERROR - [A
2025-07-24 15:20:59,499 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:01,  9.22it/s]
2025-07-24 15:20:59,500 - ERROR - [A
2025-07-24 15:21:00,082 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:02,  5.60it/s]
2025-07-24 15:21:00,083 - ERROR - [A
2025-07-24 15:21:00,940 - ERROR - 
波段 1/3 写入进度:  43%|###########################4                                    | 9/21 [00:02<00:03,  3.08it/s]
2025-07-24 15:21:00,941 - ERROR - [A
2025-07-24 15:21:01,350 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:02<00:03,  2.90it/s]
2025-07-24 15:21:01,351 - ERROR - [A
2025-07-24 15:21:01,696 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:02<00:03,  2.90it/s]
2025-07-24 15:21:01,698 - ERROR - [A
2025-07-24 15:21:02,028 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:03<00:03,  2.93it/s]
2025-07-24 15:21:02,029 - ERROR - [A
2025-07-24 15:21:02,353 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:03<00:02,  2.97it/s]
2025-07-24 15:21:02,354 - ERROR - [A
2025-07-24 15:21:03,003 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:04<00:02,  2.35it/s]
2025-07-24 15:21:03,004 - ERROR - [A
2025-07-24 15:21:03,317 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:04<00:02,  2.54it/s]
2025-07-24 15:21:03,318 - ERROR - [A
2025-07-24 15:21:03,678 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:04<00:01,  2.61it/s]
2025-07-24 15:21:03,682 - ERROR - [A
2025-07-24 15:21:03,980 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:05<00:01,  2.78it/s]
2025-07-24 15:21:03,981 - ERROR - [A
2025-07-24 15:21:04,274 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:05<00:01,  2.94it/s]
2025-07-24 15:21:04,282 - ERROR - [A
2025-07-24 15:21:04,622 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:05<00:00,  2.92it/s]
2025-07-24 15:21:04,622 - ERROR - [A
2025-07-24 15:21:04,832 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:05<00:00,  3.30it/s]
2025-07-24 15:21:04,833 - ERROR - [A
2025-07-24 15:21:04,868 - ERROR - [A
2025-07-24 15:21:57,168 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:13<02:26, 73.36s/it]
2025-07-24 15:22:06,428 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=45.79, std=65.41应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-24 15:22:06,429 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 15:22:06,430 - ERROR - [A
2025-07-24 15:22:13,891 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:07<02:29,  7.46s/it]
2025-07-24 15:22:13,891 - ERROR - [A
2025-07-24 15:22:22,917 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:16<02:39,  8.38s/it]
2025-07-24 15:22:22,918 - ERROR - [A
2025-07-24 15:22:31,741 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:25<02:34,  8.58s/it]
2025-07-24 15:22:31,741 - ERROR - [A
2025-07-24 15:22:32,165 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:25<01:31,  5.36s/it]
2025-07-24 15:22:32,168 - ERROR - [A
2025-07-24 15:22:35,547 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:29<01:14,  4.65s/it]
2025-07-24 15:22:35,548 - ERROR - [A
2025-07-24 15:22:38,239 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:31<00:59,  3.98s/it]
2025-07-24 15:22:38,240 - ERROR - [A
2025-07-24 15:22:39,687 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:33<00:44,  3.15s/it]
2025-07-24 15:22:39,689 - ERROR - [A
2025-07-24 15:22:40,733 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:34<00:32,  2.48s/it]
2025-07-24 15:22:40,733 - ERROR - [A
2025-07-24 15:22:43,368 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:36<00:30,  2.53s/it]
2025-07-24 15:22:43,370 - ERROR - [A
2025-07-24 15:22:46,027 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:39<00:28,  2.57s/it]
2025-07-24 15:22:46,027 - ERROR - [A
2025-07-24 15:22:48,249 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:41<00:24,  2.46s/it]
2025-07-24 15:22:48,250 - ERROR - [A
2025-07-24 15:22:51,224 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:44<00:23,  2.62s/it]
2025-07-24 15:22:51,224 - ERROR - [A
2025-07-24 15:22:54,136 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:47<00:21,  2.71s/it]
2025-07-24 15:22:54,137 - ERROR - [A
2025-07-24 15:22:54,303 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:47<00:13,  1.94s/it]
2025-07-24 15:22:54,304 - ERROR - [A
2025-07-24 15:22:54,457 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:48<00:08,  1.40s/it]
2025-07-24 15:22:54,457 - ERROR - [A
2025-07-24 15:22:54,622 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:48<00:05,  1.03s/it]
2025-07-24 15:22:54,623 - ERROR - [A
2025-07-24 15:23:04,974 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:58<00:15,  3.83s/it]
2025-07-24 15:23:04,974 - ERROR - [A
2025-07-24 15:23:26,615 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [01:20<00:27,  9.18s/it]
2025-07-24 15:23:26,616 - ERROR - [A
2025-07-24 15:23:35,100 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [01:28<00:17,  8.97s/it]
2025-07-24 15:23:35,100 - ERROR - [A
2025-07-24 15:23:44,751 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [01:38<00:09,  9.18s/it]
2025-07-24 15:23:44,751 - ERROR - [A
2025-07-24 15:23:44,757 - ERROR - [A
2025-07-24 15:25:20,733 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [04:36<02:29, 149.95s/it]
2025-07-24 15:25:28,297 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=35.76, std=51.77应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-24 15:25:28,298 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 15:25:28,298 - ERROR - [A
2025-07-24 15:25:28,953 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:13,  1.53it/s]
2025-07-24 15:25:28,954 - ERROR - [A
2025-07-24 15:25:29,202 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:07,  2.40it/s]
2025-07-24 15:25:29,203 - ERROR - [A
2025-07-24 15:25:30,283 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:12,  1.39it/s]
2025-07-24 15:25:30,283 - ERROR - [A
2025-07-24 15:25:31,747 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:03<00:17,  1.01s/it]
2025-07-24 15:25:31,748 - ERROR - [A
2025-07-24 15:25:31,849 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:03<00:05,  2.51it/s]
2025-07-24 15:25:31,849 - ERROR - [A
2025-07-24 15:25:31,960 - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 12/21 [00:03<00:01,  5.79it/s]
2025-07-24 15:25:31,960 - ERROR - [A
2025-07-24 15:25:32,063 - ERROR - 
波段 3/3 写入进度:  76%|################################################               | 16/21 [00:03<00:00,  8.86it/s]
2025-07-24 15:25:32,063 - ERROR - [A
2025-07-24 15:25:32,143 - ERROR - [A
2025-07-24 15:27:02,775 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [06:18<00:00, 128.08s/it]
2025-07-24 15:27:02,778 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [06:18<00:00, 126.32s/it]
2025-07-24 15:27:36,738 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif影像保存完成，耗时: 389.51 秒 (15:27:03)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp (15:27:03)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tifTIF文件信息: 宽=17007, 高=20364, 波段数=3开始读取影像数据，大小约: 3963.44 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif提取TIF文件信息...TIF文件信息: 宽=17007, 高=20364, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 218929698 个无效像素检查波段 2...波段 2 检测到 218929698 个无效像素检查波段 3...波段 3 检测到 218929698 个无效像素掩码统计: 总像素数 346330548, 有效像素数 127400850 (36.79%), 无效像素数 218929698 (63.21%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 1 个初始轮廓轮廓面积统计: 最小=127380512.50, 最大=127380512.50, 平均=127380512.50, 总数=1将 1 个轮廓转换为地理坐标...
2025-07-24 15:27:36,763 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-24 15:27:37,260 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.02it/s]
2025-07-24 15:27:37,261 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.01it/s]
2025-07-24 15:27:37,408 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-24 15:27:47,699 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-24 15:27:47,699 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-24 15:27:48,573 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:12,  2.29it/s]
2025-07-24 15:27:48,736 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:09,  3.10it/s]
2025-07-24 15:27:48,785 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 28.57it/s]
2025-07-24 15:27:49,508 - INFO - 处理了 82263 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp写入shapefile特征...处理MultiPolygon，包含 19 个多边形
2025-07-24 15:27:49,509 - ERROR - 
写入多边形:   0%|                                                                               | 0/19 [00:00<?, ?it/s]
2025-07-24 15:27:49,669 - ERROR - 
写入多边形:  26%|##################6                                                    | 5/19 [00:00<00:00, 31.49it/s]
2025-07-24 15:27:49,674 - ERROR - 
写入多边形: 100%|#####################################################################| 19/19 [00:00<00:00, 115.60it/s]
2025-07-24 15:27:49,842 - INFO - 处理完成，耗时: 520.48 秒 (8.67 分钟)
2025-07-24 15:27:49,843 - INFO - 处理结果: 成功
2025-07-24 15:27:49,844 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, 大小: 740.84 MB
2025-07-24 15:27:49,844 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp, 大小: 22.81 KB
2025-07-24 15:27:49,858 - INFO - TIF处理任务 7869ab0c-addd-47d1-b52b-5dcc70ba31fa 执行成功
2025-07-24 15:27:49,859 - INFO - 完成时间: 2025-07-24 15:27:49
2025-07-24 15:27:49,859 - INFO - 状态: 运行成功
2025-07-24 15:27:49,860 - INFO - ============ 任务执行结束 ============
