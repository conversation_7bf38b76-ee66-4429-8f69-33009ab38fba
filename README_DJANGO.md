# GeoServer Django API

本项目已成功从Flask框架迁移到Django框架，保持所有API功能不变。

## 项目结构

```
geoserverAPIDJ/
├── geoserver_django/           # Django项目配置
│   ├── settings.py            # Django设置文件
│   ├── urls.py               # 主URL配置
│   └── wsgi.py               # WSGI配置
├── geoserver_api/             # Django应用
│   ├── core/                 # 核心功能模块
│   │   ├── manager.py        # GeoServer管理器
│   │   ├── raster_query.py   # 栅格数据查询
│   │   ├── batch_executor.py # 批处理执行器
│   │   ├── tif_process.py    # TIF处理器
│   │   └── geo_publisher.py  # GeoServer发布器
│   ├── views.py              # Django视图
│   ├── urls.py               # 应用URL配置
│   └── models.py             # Django模型
├── geoserverRest/             # 原始Flask代码（保留作为参考）
├── manage.py                  # Django管理脚本
├── start_django_api.bat       # Django启动脚本
├── test_django_api.py         # API测试脚本
└── requirements.txt           # 依赖包列表
```

## 安装和配置

### 1. 环境准备

确保已激活conda环境：
```bash
conda activate geoserverapi
```

### 2. 安装依赖

```bash
# 使用conda安装Django相关包
conda install -c conda-forge django djangorestframework

# 或者使用pip（如果网络允许）
pip install django==4.2.16 djangorestframework==3.14.0
```

### 3. 配置检查

```bash
# 检查Django项目配置
python manage.py check

# 应用数据库迁移
python manage.py migrate
```

## 启动服务

### 方法1：使用批处理脚本
```bash
start_django_api.bat
```

### 方法2：手动启动
```bash
# 激活环境
conda activate geoserverapi

# 启动Django开发服务器
python manage.py runserver 127.0.0.1:5000
```

## API端点

Django版本保持了与Flask版本相同的API端点：

### 基础端点
- `GET /health/` - 健康检查

### 栅格查询API
- `GET /api/query/` - 查询坐标点处的栅格图层
- `GET /api/layers/` - 获取工作区中的所有图层
- `GET /api/test/` - 测试坐标点在特定图层中是否有有效数据
- `GET /api/pixel/` - 获取坐标点处的像素值

### 管理API
- `GET /api/management/workspaces/` - 获取所有工作区
- `GET /api/management/workspaces/create/` - 创建工作区
- `GET /api/management/workspaces/delete/` - 删除工作区
- `GET /api/management/layers/` - 获取图层列表
- `GET /api/management/datastores/` - 获取数据存储
- `GET /api/management/shapefiles/publish/` - 发布Shapefile

### 批处理API
- `GET /api/batch/execute/` - 执行批处理文件
- `GET /api/batch/status/<task_id>/` - 获取批处理任务状态
- `GET /api/batch/log/<task_id>/` - 获取批处理任务日志
- `GET /api/batch/all/` - 获取所有批处理任务状态

### TIF处理API
- `GET /api/tif/process/` - 处理TIF文件
- `GET /api/tif/execute/` - 异步执行TIF处理
- `GET /api/tif/status/<task_id>/` - 获取TIF处理任务状态

### GeoServer异步发布API
- `GET /api/geo/shapefile/execute/` - 异步发布Shapefile
- `GET /api/geo/geotiff/execute/` - 异步发布GeoTIFF
- `GET /api/geo/status/<task_id>/` - 获取发布任务状态

## 测试

运行测试脚本验证API功能：
```bash
python test_django_api.py
```

## 配置说明

### Django设置 (geoserver_django/settings.py)

主要配置项：
- `GEOSERVER_URL`: GeoServer服务地址
- `GEOSERVER_USER`: GeoServer用户名
- `GEOSERVER_PASSWORD`: GeoServer密码
- `DEFAULT_WORKSPACE`: 默认工作区
- `DEFAULT_DATASTORE`: 默认数据存储

### 环境变量支持

可以通过环境变量或.env文件配置：
```
GEOSERVER_URL=http://localhost:8083/geoserver
GEOSERVER_USER=admin
GEOSERVER_PASSWORD=geoserver
DEBUG=True
```

## 迁移说明

### 主要变更
1. **框架迁移**: 从Flask迁移到Django
2. **URL路由**: Flask蓝图转换为Django URL配置
3. **视图函数**: Flask视图函数适配为Django视图
4. **配置系统**: 集成到Django settings.py
5. **日志系统**: 使用Django日志配置

### 保持不变
1. **API端点**: 所有API路径和参数保持不变
2. **核心功能**: GeoServer管理、栅格查询等核心功能不变
3. **异步任务**: 批处理、TIF处理、发布任务的异步执行机制不变
4. **日志文件**: 保持原有的日志文件结构和位置

## 故障排除

### 常见问题

1. **模块导入错误**
   - 确保已安装所有依赖包
   - 检查conda环境是否正确激活

2. **GeoServer连接失败**
   - 检查GeoServer是否运行在配置的地址
   - 验证用户名和密码是否正确

3. **端口占用**
   - 默认使用5000端口，如有冲突可修改启动命令

4. **权限问题**
   - 确保对日志目录有写入权限
   - 检查数据文件的访问权限

## 性能优化

1. **生产环境部署**
   - 使用Gunicorn或uWSGI作为WSGI服务器
   - 配置Nginx作为反向代理
   - 设置DEBUG=False

2. **数据库优化**
   - 考虑使用PostgreSQL替代SQLite
   - 配置数据库连接池

3. **缓存配置**
   - 启用Django缓存框架
   - 配置Redis或Memcached

## 支持

如有问题，请检查：
1. Django服务器日志
2. GeoServer连接状态
3. 相关任务日志文件

项目成功从Flask迁移到Django，保持了所有原有功能的完整性和兼容性。
