2025-07-24 16:12:58,945 - INFO - ============ TIF处理任务 4acbd746-f075-45ba-b856-04ec2ebb15e0 开始执行 ============
2025-07-24 16:12:58,949 - INFO - 开始时间: 2025-07-24 16:12:58
2025-07-24 16:12:58,951 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 16:12:58,953 - INFO - 系统信息:
2025-07-24 16:12:58,954 - INFO -   操作系统: Windows 10.0.19045
2025-07-24 16:12:58,955 - INFO -   Python版本: 3.8.20
2025-07-24 16:12:58,955 - INFO -   GDAL版本: 3.9.2
2025-07-24 16:12:58,957 - INFO -   GPU可用: 否
2025-07-24 16:12:58,958 - INFO - 检查参数有效性...
2025-07-24 16:12:58,959 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:12:58,960 - INFO - 开始执行TIF处理流程...
2025-07-24 16:12:58,960 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:12:58,961 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif
2025-07-24 16:12:58,962 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp
2025-07-24 16:13:04,287 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (16:13:04)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-24 16:13:04,318 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-24 16:13:04,451 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:02,  7.78it/s]
2025-07-24 16:13:04,566 - ERROR - 
处理数据块:  29%|####################2                                                  | 6/21 [00:00<00:00, 27.79it/s]
2025-07-24 16:13:04,697 - ERROR - 
处理数据块:  43%|##############################4                                        | 9/21 [00:00<00:00, 25.45it/s]
2025-07-24 16:13:06,039 - ERROR - 
处理数据块:  57%|########################################                              | 12/21 [00:01<00:01,  5.14it/s]
2025-07-24 16:13:06,397 - ERROR - 
处理数据块:  67%|##############################################6                       | 14/21 [00:02<00:01,  5.26it/s]
2025-07-24 16:13:07,311 - ERROR - 
处理数据块:  81%|########################################################6             | 17/21 [00:02<00:00,  4.31it/s]
2025-07-24 16:13:08,063 - ERROR - 
处理数据块:  86%|############################################################          | 18/21 [00:03<00:00,  3.24it/s]
2025-07-24 16:13:08,107 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:03<00:00,  5.55it/s]
2025-07-24 16:13:08,108 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-24 16:13:08,109 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-24 16:13:08,146 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 583.43it/s]
2025-07-24 16:13:13,860 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 9.85 秒 (16:13:13)预计总处理时间: 约 29.55 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif (16:13:13)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-24 16:13:13,861 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-24 16:13:48,381 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-24 16:13:48,412 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:13:48,414 - ERROR - [A
2025-07-24 16:13:48,580 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:01, 12.08it/s]
2025-07-24 16:13:48,581 - ERROR - [A
2025-07-24 16:13:48,714 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:00, 17.55it/s]
2025-07-24 16:13:48,717 - ERROR - [A
2025-07-24 16:13:48,862 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:00<00:00, 18.78it/s]
2025-07-24 16:13:48,863 - ERROR - [A
2025-07-24 16:13:48,967 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 18.87it/s]
2025-07-24 16:13:48,968 - ERROR - [A
2025-07-24 16:13:49,074 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:00<00:00, 21.83it/s]
2025-07-24 16:13:49,074 - ERROR - [A
2025-07-24 16:13:49,174 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:00<00:00, 24.17it/s]
2025-07-24 16:13:49,175 - ERROR - [A
2025-07-24 16:13:49,288 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:00<00:00, 24.92it/s]
2025-07-24 16:13:49,289 - ERROR - [A
2025-07-24 16:13:49,349 - ERROR - [A
2025-07-24 16:14:38,486 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:24<02:49, 84.62s/it]
2025-07-24 16:14:43,242 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-24 16:14:43,245 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:14:43,245 - ERROR - [A
2025-07-24 16:14:43,601 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:07,  2.83it/s]
2025-07-24 16:14:43,601 - ERROR - [A
2025-07-24 16:14:43,705 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:03,  4.82it/s]
2025-07-24 16:14:43,708 - ERROR - [A
2025-07-24 16:14:43,812 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  6.19it/s]
2025-07-24 16:14:43,813 - ERROR - [A
2025-07-24 16:14:43,918 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  7.17it/s]
2025-07-24 16:14:43,918 - ERROR - [A
2025-07-24 16:14:44,024 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:02,  7.85it/s]
2025-07-24 16:14:44,026 - ERROR - [A
2025-07-24 16:14:44,154 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:01,  7.79it/s]
2025-07-24 16:14:44,155 - ERROR - [A
2025-07-24 16:14:44,357 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  6.53it/s]
2025-07-24 16:14:44,358 - ERROR - [A
2025-07-24 16:14:44,491 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:01,  6.81it/s]
2025-07-24 16:14:44,493 - ERROR - [A
2025-07-24 16:14:44,664 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  6.45it/s]
2025-07-24 16:14:44,665 - ERROR - [A
2025-07-24 16:14:44,803 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  6.66it/s]
2025-07-24 16:14:44,804 - ERROR - [A
2025-07-24 16:14:44,953 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:01,  6.66it/s]
2025-07-24 16:14:44,954 - ERROR - [A
2025-07-24 16:14:45,178 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:01,  7.55it/s]
2025-07-24 16:14:45,178 - ERROR - [A
2025-07-24 16:14:45,297 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:00,  7.74it/s]
2025-07-24 16:14:45,298 - ERROR - [A
2025-07-24 16:14:46,779 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:03<00:01,  2.60it/s]
2025-07-24 16:14:46,779 - ERROR - [A
2025-07-24 16:14:49,268 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:06<00:03,  1.15it/s]
2025-07-24 16:14:49,269 - ERROR - [A
2025-07-24 16:14:52,866 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:09<00:04,  1.54s/it]
2025-07-24 16:14:52,867 - ERROR - [A
2025-07-24 16:14:57,411 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:14<00:04,  2.32s/it]
2025-07-24 16:14:57,411 - ERROR - [A
2025-07-24 16:15:02,346 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:19<00:03,  3.03s/it]
2025-07-24 16:15:02,347 - ERROR - [A
2025-07-24 16:15:03,555 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:20<00:00,  2.52s/it]
2025-07-24 16:15:03,555 - ERROR - [A
2025-07-24 16:15:03,556 - ERROR - [A
2025-07-24 16:16:18,272 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [03:04<01:33, 93.54s/it]
2025-07-24 16:16:23,338 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-24 16:16:23,339 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:16:23,340 - ERROR - [A
2025-07-24 16:16:23,713 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:07,  2.69it/s]
2025-07-24 16:16:23,714 - ERROR - [A
2025-07-24 16:16:24,089 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:07,  2.67it/s]
2025-07-24 16:16:24,090 - ERROR - [A
2025-07-24 16:16:24,511 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:07,  2.53it/s]
2025-07-24 16:16:24,511 - ERROR - [A
2025-07-24 16:16:24,899 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:01<00:06,  2.54it/s]
2025-07-24 16:16:24,900 - ERROR - [A
2025-07-24 16:16:25,319 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:01<00:06,  2.48it/s]
2025-07-24 16:16:25,319 - ERROR - [A
2025-07-24 16:16:25,432 - ERROR - 
波段 3/3 写入进度:  38%|########################3                                       | 8/21 [00:02<00:02,  5.52it/s]
2025-07-24 16:16:25,433 - ERROR - [A
2025-07-24 16:16:25,545 - ERROR - 
波段 3/3 写入进度:  52%|#################################                              | 11/21 [00:02<00:01,  8.72it/s]
2025-07-24 16:16:25,546 - ERROR - [A
2025-07-24 16:16:25,652 - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:00, 12.06it/s]
2025-07-24 16:16:25,652 - ERROR - [A
2025-07-24 16:16:25,758 - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 18/21 [00:02<00:00, 17.07it/s]
2025-07-24 16:16:25,760 - ERROR - [A
2025-07-24 16:16:25,816 - ERROR - [A
2025-07-24 16:18:13,019 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [04:59<00:00, 103.22s/it]
2025-07-24 16:18:13,020 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:59<00:00, 99.72s/it]
2025-07-24 16:18:40,777 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif影像保存完成，耗时: 299.76 秒 (16:18:13)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp (16:18:13)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp使用NoData值: -9999, 简化容差: 0.1读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-24 16:18:40,803 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-24 16:18:41,211 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.45it/s]
2025-07-24 16:18:41,213 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.45it/s]
2025-07-24 16:18:41,237 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-24 16:18:53,208 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-24 16:18:53,209 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-24 16:18:54,596 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:01<00:20,  1.44it/s]
2025-07-24 16:18:54,756 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:13,  2.13it/s]
2025-07-24 16:18:54,813 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 19.34it/s]
2025-07-24 16:18:55,460 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-24 16:18:55,461 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-24 16:18:55,475 - ERROR - 
写入多边形: 100%|####################################################################| 35/35 [00:00<00:00, 2916.41it/s]
2025-07-24 16:18:55,607 - INFO - 处理完成，耗时: 356.64 秒 (5.94 分钟)
2025-07-24 16:18:55,607 - INFO - 处理结果: 成功
2025-07-24 16:18:55,608 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, 大小: 781.83 MB
2025-07-24 16:18:55,608 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp, 大小: 21.20 KB
2025-07-24 16:18:55,613 - INFO - TIF处理任务 4acbd746-f075-45ba-b856-04ec2ebb15e0 执行成功
2025-07-24 16:18:55,613 - INFO - 完成时间: 2025-07-24 16:18:55
2025-07-24 16:18:55,614 - INFO - 状态: 运行成功
2025-07-24 16:18:55,614 - INFO - ============ 任务执行结束 ============
