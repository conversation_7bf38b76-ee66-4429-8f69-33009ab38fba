执行命令: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
开始时间: 2025-07-17 11:37:30

[INFO]    Initializing ODM 3.5.5 - Thu Jul 17 11:38:30  2025
[INFO]    ==============
[INFO]    3d_tiles: False
[INFO]    align: None
[INFO]    auto_boundary: False
[INFO]    auto_boundary_distance: 0
[INFO]    bg_removal: False
[INFO]    boundary: {}
[INFO]    build_overviews: False
[INFO]    camera_lens: auto
[INFO]    cameras: {}
[INFO]    cog: False
[INFO]    copy_to: None
[INFO]    crop: 3
[INFO]    dem_decimation: 1
[INFO]    dem_euclidean_map: False
[INFO]    dem_gapfill_steps: 3
[INFO]    dem_resolution: 5
[INFO]    dsm: False
[INFO]    dtm: False
[INFO]    end_with: odm_postprocess
[INFO]    fast_orthophoto: False
[INFO]    feature_quality: high
[INFO]    feature_type: dspsift
[INFO]    force_gps: False
[INFO]    gcp: None
[INFO]    geo: None
[INFO]    gltf: False
[INFO]    gps_accuracy: 3
[INFO]    ignore_gsd: False
[INFO]    matcher_neighbors: 0
[INFO]    matcher_order: 0
[INFO]    matcher_type: flann
[INFO]    max_concurrency: 8
[INFO]    merge: all
[INFO]    mesh_octree_depth: 11
[INFO]    mesh_size: 200000
[INFO]    min_num_features: 10000
[INFO]    name: D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
[INFO]    no_gpu: False
[INFO]    optimize_disk_space: False
[INFO]    orthophoto_compression: DEFLATE
[INFO]    orthophoto_cutline: False
[INFO]    orthophoto_kmz: False
[INFO]    orthophoto_no_tiled: False
[INFO]    orthophoto_png: False
[INFO]    orthophoto_resolution: 5
[INFO]    pc_classify: False
[INFO]    pc_copc: False
[INFO]    pc_csv: False
[INFO]    pc_ept: False
[INFO]    pc_filter: 5
[INFO]    pc_las: False
[INFO]    pc_quality: medium
[INFO]    pc_rectify: False
[INFO]    pc_sample: 0
[INFO]    pc_skip_geometric: False
[INFO]    primary_band: auto
[INFO]    project_path: D:/Drone_Project/nginxData/ODM/Input/20250705171600/project
[INFO]    radiometric_calibration: none
[INFO]    rerun: None
[INFO]    rerun_all: False
[INFO]    rerun_from: None
[INFO]    rolling_shutter: False
[INFO]    rolling_shutter_readout: 0
[INFO]    sfm_algorithm: incremental
[INFO]    sfm_no_partial: False
[INFO]    skip_3dmodel: False
[INFO]    skip_band_alignment: False
[INFO]    skip_orthophoto: False
[INFO]    skip_report: False
[INFO]    sky_removal: False
[INFO]    sm_cluster: None
[INFO]    sm_no_align: False
[INFO]    smrf_scalar: 1.25
[INFO]    smrf_slope: 0.15
[INFO]    smrf_threshold: 0.5
[INFO]    smrf_window: 18.0
[INFO]    split: 999999
[INFO]    split_image_groups: None
[INFO]    split_overlap: 150
[INFO]    texturing_keep_unseen_faces: False
[INFO]    texturing_single_material: False
[INFO]    texturing_skip_global_seam_leveling: False
[INFO]    tiles: False
[INFO]    use_3dmesh: False
[INFO]    use_exif: False
[INFO]    use_fixed_camera_params: False
[INFO]    use_hybrid_bundle_adjustment: False
[INFO]    video_limit: 500
[INFO]    video_resolution: 4000
[INFO]    ==============
[INFO]    Running dataset stage
[INFO]    Loading dataset from: D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\images
[INFO]    Loading images database: D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\images.json
[INFO]    Found 98 usable images
[INFO]    Coordinates file already exist: D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\odm_georeferencing\coords.txt
[INFO]    Model geo file already exist: D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\odm_georeferencing\odm_georeferencing_model_geo.txt
[INFO]    Finished dataset stage
[INFO]    Running split stage
[INFO]    Normal dataset, will process all at once.
[INFO]    Finished split stage
[INFO]    Running merge stage
[INFO]    Normal dataset, nothing to merge.
[INFO]    Finished merge stage
[INFO]    Running opensfm stage
[WARNING] D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm\image_list.txt already exists, not rerunning OpenSfM setup
[WARNING] D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm\exif already exists, not rerunning photo to metadata
[WARNING] Detect features already done: D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm\features exists
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" match_features "D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm"
2025-07-17 11:39:02,289 INFO: Altitude for orientation based matching 1067.8139024740462
2025-07-17 11:39:08,762 INFO: Matching 1416 image pairs
2025-07-17 11:39:08,762 INFO: Computing pair matching with 1 processes
Traceback (most recent call last):
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 25, in <module>
    commands.command_runner(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command_runner.py", line 38, in command_runner
    command.run(data, args)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command.py", line 13, in run
    self.run_impl(data, args)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\match_features.py", line 13, in run_impl
    match_features.run_dataset(dataset)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\actions\match_features.py", line 14, in run_dataset
    pairs_matches, preport = matching.match_images(data, {}, images, images)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\matching.py", line 56, in match_images
    match_images_with_pairs(data, config_override, exifs, pairs),
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\matching.py", line 80, in match_images_with_pairs
    matches = context.parallel_map(match_unwrap_args, args, processes, jobs_per_process)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\context.py", line 52, in parallel_map
    res = list(map(func, args))
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\matching.py", line 210, in match_unwrap_args
    matches = match(im1, im2, camera1, camera2, data, config_override, pose)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\matching.py", line 571, in match
    p1, p2, matches, matcher_type = _match_descriptors_impl(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\matching.py", line 353, in _match_descriptors_impl
    features_data1 = feature_loader.instance.load_all_data(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\feature_loading.py", line 109, in load_all_data
    features_data = self._load_all_data_masked(data, image)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\feature_loading.py", line 160, in _load_all_data_masked
    features_data = self._load_all_data_unmasked(data, image)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\feature_loading.py", line 154, in _load_all_data_unmasked
    return self._load_features_nocache(data, image)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\feature_loading.py", line 201, in _load_features_nocache
    features_data = data.load_features(image)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 318, in load_features
    with self.io_handler.open(features_filepath, "rb") as f:
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\io.py", line 1486, in open
    return open(*args, **kwargs)
FileNotFoundError: [Errno 2] No such file or directory: 'D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171600\\project\\opensfm\\features\\DJI_20250705172445_0068_V.jpeg.features.npz'
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" create_tracks "D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm"
2025-07-17 11:40:24,700 INFO: reading features
Traceback (most recent call last):
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 25, in <module>
    commands.command_runner(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command_runner.py", line 38, in command_runner
    command.run(data, args)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command.py", line 13, in run
    self.run_impl(data, args)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\create_tracks.py", line 13, in run_impl
    create_tracks.run_dataset(dataset)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\actions\create_tracks.py", line 12, in run_dataset
    features, colors, segmentations, instances = tracking.load_features(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\tracking.py", line 29, in load_features
    features_data = dataset.load_features(im)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 318, in load_features
    with self.io_handler.open(features_filepath, "rb") as f:
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\io.py", line 1486, in open
    return open(*args, **kwargs)
FileNotFoundError: [Errno 2] No such file or directory: 'D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171600\\project\\opensfm\\features\\DJI_20250705171658_0009_V.jpeg.features.npz'
[INFO]    running "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\opensfm" reconstruct "D:\Drone_Project\nginxData\ODM\Input\20250705171600\project\opensfm"
Traceback (most recent call last):
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\bin\\opensfm_main.py", line 25, in <module>
    commands.command_runner(
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command_runner.py", line 38, in command_runner
    command.run(data, args)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\command.py", line 13, in run
    self.run_impl(data, args)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\commands\reconstruct.py", line 14, in run_impl
    reconstruct.run_dataset(dataset)
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\actions\reconstruct.py", line 9, in run_dataset
    tracks_manager = data.load_tracks_manager()
  File "D:\Drone_Project\ODM\ODM\SuperBuild\install\bin\opensfm\opensfm\dataset.py", line 406, in load_tracks_manager
    return pymap.TracksManager.instanciate_from_file(self._tracks_manager_file(filename))
RuntimeError: Can't read tracks manager file
[ERROR]   The program could not process this dataset using the current settings. Check that the images have enough overlap, that there are enough recognizable features and that the images are in focus. The program will now exit.

完成时间: 2025-07-17 11:40:43
返回代码: 0
状态: 运行成功
