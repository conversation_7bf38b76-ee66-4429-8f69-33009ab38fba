#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer REST API服务 - TIF处理API模块
提供TIF栅格影像处理的API端点，包括NoData设置、有效区域提取等功能

Author: 吴博文 <EMAIL>
Date: 2025-07-15 15:14:14
LastEditors: 吴博文 <EMAIL>
LastEditTime: 2025-07-24 10:53:09
"""

import os
import sys
import logging
from flask import Blueprint, request, jsonify
import uuid
import time
import datetime

# NumPy兼容性修复 - 处理numpy.bool弃用问题
try:
    import numpy

    if not hasattr(numpy, "bool"):
        numpy.bool = numpy.bool_
except:
    pass

# 检查GPU可用性
HAS_GPU = False
try:
    import torch

    HAS_GPU = torch.cuda.is_available()
except ImportError:
    pass

try:
    import cupy

    HAS_GPU = True
except ImportError:
    pass

# 导入核心模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from core.tif_process import (
    read_tif,
    set_nodata_and_save,
    create_edge_only_mask,
    create_shapefile_from_mask,
    create_shapefile_from_output_tif,
    process_uav_image,
    tif_executor,
)

# 创建日志记录器
tif_logger = logging.getLogger("tif_api")
if not tif_logger.handlers:
    tif_logger.setLevel(logging.INFO)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    tif_logger.addHandler(console_handler)

    # 文件处理器
    log_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../logs"))
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir)
        except Exception as e:
            print(f"创建日志目录失败: {log_dir}, 错误: {str(e)}")

    log_file = os.path.join(log_dir, f'tif_api_{time.strftime("%Y%m%d_%H%M%S")}.log')
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(formatter)
    tif_logger.addHandler(file_handler)

# 创建API蓝图
tif_api = Blueprint("tif_api", __name__)


@tif_api.route("/process", methods=["GET"])
def process_tif():
    """
    处理TIF栅格影像，设置NoData值，提取有效区域shapefile

    该API仍然保留同步处理模式，适用于简单任务或需要立即结果的场景。
    对于大型影像处理，推荐使用/execute异步处理API。

    查询参数:
        input_tif: 输入TIF文件路径（必需）
        output_tif: 输出TIF文件路径（可选，默认为input_tif同目录下的文件名_out.tif）
        output_shp: 输出shapefile文件路径（可选，默认为input_tif同目录下的文件名_out.shp）
        black_threshold: 黑色判定阈值，默认0
        white_threshold: 白色判定阈值，默认255
        nodata_value: NoData值，默认-9999
        simplify_tolerance: 多边形简化参数，默认0.1
        edge_width: 边缘检测宽度(像素)，默认100
        process_mode: 处理模式，'full'检测整个影像，'edge'仅检测边缘，默认'full'
        keep_alpha: 是否保留Alpha通道(如果存在)，默认false
        protect_interior: 是否保护有效区域内部像素，默认false
        use_gpu: 是否使用GPU加速，默认false
        num_gpus: 使用GPU数量，0表示使用所有可用，默认0

    返回:
        处理结果
    """
    try:
        tif_logger.info("收到TIF处理请求 (同步模式)")

        # 获取参数
        input_tif = request.args.get("input_tif")
        output_tif = request.args.get("output_tif")
        output_shp = request.args.get("output_shp")

        # 验证必要参数
        if not input_tif:
            error_msg = "缺少必要参数: input_tif"
            tif_logger.error(error_msg)
            return jsonify({"status": "error", "message": error_msg}), 400

        # 规范化路径
        input_tif = os.path.normpath(input_tif)
        tif_logger.info(f"处理输入文件: {input_tif}")

        # 如果未提供输出路径，则生成默认路径
        if not output_tif:
            input_dir = os.path.dirname(input_tif)
            input_filename = os.path.basename(input_tif)
            filename_without_ext = os.path.splitext(input_filename)[0]
            output_tif = os.path.join(input_dir, f"{filename_without_ext}_out.tif")

        if not output_shp:
            input_dir = os.path.dirname(input_tif)
            input_filename = os.path.basename(input_tif)
            filename_without_ext = os.path.splitext(input_filename)[0]
            output_shp = os.path.join(input_dir, f"{filename_without_ext}_out.shp")

        # 规范化输出路径
        output_tif = os.path.normpath(output_tif)
        output_shp = os.path.normpath(output_shp)

        tif_logger.info(f"输出TIF: {output_tif}")
        tif_logger.info(f"输出SHP: {output_shp}")

        # 获取可选参数
        black_threshold = int(request.args.get("black_threshold", 0))
        white_threshold = int(request.args.get("white_threshold", 255))
        nodata_value = float(request.args.get("nodata_value", -9999))
        simplify_tolerance = float(request.args.get("simplify_tolerance", 0.1))
        edge_width = int(request.args.get("edge_width", 100))
        process_mode = request.args.get("process_mode", "full")

        # 布尔参数处理
        keep_alpha = request.args.get("keep_alpha", "false").lower() == "true"
        protect_interior = (
            request.args.get("protect_interior", "false").lower() == "true"
        )
        use_gpu = request.args.get("use_gpu", "false").lower() == "true"

        # 检查GPU可用性
        if use_gpu and not HAS_GPU:
            tif_logger.warning("请求使用GPU但GPU不可用，将使用CPU模式")
            use_gpu = False

        # 其他可选参数
        num_gpus = int(request.args.get("num_gpus", 0))

        # 检查输入文件是否存在
        if not os.path.exists(input_tif):
            error_msg = f"输入文件不存在: {input_tif}"
            tif_logger.error(error_msg)
            return jsonify({"status": "error", "message": error_msg}), 404

        # 检查输入文件格式
        file_ext = os.path.splitext(input_tif)[1].lower()
        if file_ext not in [".tif", ".tiff"]:
            warning_msg = f"警告: 输入文件扩展名 '{file_ext}' 不是标准的GeoTIFF扩展名"
            tif_logger.warning(warning_msg)

        # 检查文件大小
        try:
            file_size_mb = os.path.getsize(input_tif) / (1024 * 1024)
            tif_logger.info(f"输入文件大小: {file_size_mb:.2f} MB")
            if file_size_mb > 1000:  # 如果文件大于1GB
                tif_logger.warning(
                    f"文件很大 ({file_size_mb:.2f} MB)，同步处理可能耗时较长，建议使用异步API"
                )
        except Exception as e:
            tif_logger.warning(f"无法获取文件大小: {str(e)}")

        # 确保输出目录存在
        output_tif_dir = os.path.dirname(output_tif)
        if output_tif_dir and not os.path.exists(output_tif_dir):
            try:
                tif_logger.info(f"创建输出TIF目录: {output_tif_dir}")
                os.makedirs(output_tif_dir)
            except Exception as e:
                error_msg = f"创建输出目录失败: {output_tif_dir}, 错误: {str(e)}"
                tif_logger.error(error_msg)
                return jsonify({"status": "error", "message": error_msg}), 500

        output_shp_dir = os.path.dirname(output_shp)
        if output_shp_dir and not os.path.exists(output_shp_dir):
            try:
                tif_logger.info(f"创建输出SHP目录: {output_shp_dir}")
                os.makedirs(output_shp_dir)
            except Exception as e:
                error_msg = f"创建输出目录失败: {output_shp_dir}, 错误: {str(e)}"
                tif_logger.error(error_msg)
                return jsonify({"status": "error", "message": error_msg}), 500

        # 检查输出文件是否已存在
        if os.path.exists(output_tif):
            tif_logger.warning(f"输出TIF文件已存在，将被覆盖: {output_tif}")
        if os.path.exists(output_shp):
            tif_logger.warning(f"输出SHP文件已存在，将被覆盖: {output_shp}")

        # 记录参数
        tif_logger.info(
            f"处理参数: 黑色阈值={black_threshold}, 白色阈值={white_threshold}, NoData值={nodata_value}"
        )
        tif_logger.info(
            f"简化参数={simplify_tolerance}, 处理模式={process_mode}, 保留Alpha={keep_alpha}"
        )
        tif_logger.info(
            f"保护内部区域={protect_interior}, 使用GPU={use_gpu}, GPU数量={num_gpus}"
        )

        # 执行处理
        start_time = time.time()
        tif_logger.info("开始执行TIF处理...")

        try:
            result = process_uav_image(
                input_tif=input_tif,
                output_tif=output_tif,
                output_shp=output_shp,
                black_threshold=black_threshold,
                white_threshold=white_threshold,
                nodata_value=nodata_value,
                simplify_tolerance=simplify_tolerance,
                edge_width=edge_width,
                process_mode=process_mode,
                keep_alpha=keep_alpha,
                protect_interior=protect_interior,
                use_gpu=use_gpu,
                num_gpus=num_gpus,
            )
        except Exception as proc_e:
            error_msg = f"TIF处理过程中发生错误: {str(proc_e)}"
            tif_logger.error(error_msg)
            import traceback

            tif_logger.error(traceback.format_exc())
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": error_msg,
                        "traceback": traceback.format_exc(),
                    }
                ),
                500,
            )

        end_time = time.time()
        processing_time = end_time - start_time
        tif_logger.info(f"处理完成，耗时: {processing_time:.2f}秒")

        # 验证输出文件
        output_files_status = {}
        if os.path.exists(output_tif):
            output_files_status["tif"] = {
                "exists": True,
                "size": os.path.getsize(output_tif),
                "size_mb": os.path.getsize(output_tif) / (1024 * 1024),
            }
            tif_logger.info(
                f"输出TIF文件已创建: {output_tif}, 大小: {output_files_status['tif']['size_mb']:.2f} MB"
            )
        else:
            output_files_status["tif"] = {"exists": False}
            tif_logger.warning(f"输出TIF文件未创建: {output_tif}")

        if os.path.exists(output_shp):
            output_files_status["shp"] = {
                "exists": True,
                "size": os.path.getsize(output_shp),
                "size_kb": os.path.getsize(output_shp) / 1024,
            }
            tif_logger.info(
                f"输出SHP文件已创建: {output_shp}, 大小: {output_files_status['shp']['size_kb']:.2f} KB"
            )

            # 检查SHP相关文件
            missing_files = []
            for ext in ["shx", "dbf", "prj"]:
                companion_file = output_shp.replace(".shp", f".{ext}")
                if not os.path.exists(companion_file):
                    missing_files.append(ext)

            if missing_files:
                output_files_status["shp"]["missing_components"] = missing_files
                tif_logger.warning(f"SHP文件缺少组件文件: {', '.join(missing_files)}")
        else:
            output_files_status["shp"] = {"exists": False}
            tif_logger.warning(f"输出SHP文件未创建: {output_shp}")

        if result:
            tif_logger.info("TIF处理成功")
            return jsonify(
                {
                    "status": "success",
                    "message": "TIF处理成功",
                    "output_tif": output_tif,
                    "output_shp": output_shp,
                    "processing_time": f"{processing_time:.2f}秒",
                    "output_files_status": output_files_status,
                }
            )
        else:
            error_msg = "TIF处理失败"
            tif_logger.error(error_msg)
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": error_msg,
                        "output_files_status": output_files_status,
                    }
                ),
                500,
            )

    except Exception as e:
        error_msg = f"API错误: {str(e)}"
        tif_logger.error(error_msg)
        import traceback

        stack_trace = traceback.format_exc()
        tif_logger.error(stack_trace)
        return (
            jsonify(
                {
                    "status": "error",
                    "message": f"服务器内部错误: {str(e)}",
                    "error_details": {
                        "type": type(e).__name__,
                        "traceback": stack_trace,
                    },
                }
            ),
            500,
        )


@tif_api.route("/execute", methods=["GET"])
def execute_tif_process():
    """
    异步执行TIF处理任务

    查询参数:
        input_tif: 输入TIF文件路径（必需）
        output_tif: 输出TIF文件路径（可选，默认为input_tif同目录下的文件名_out.tif）
        output_shp: 输出shapefile文件路径（可选，默认为input_tif同目录下的文件名_out.shp）
        black_threshold: 黑色判定阈值，默认0
        white_threshold: 白色判定阈值，默认255
        nodata_value: NoData值，默认-9999
        simplify_tolerance: 多边形简化参数，默认0.1
        edge_width: 边缘检测宽度(像素)，默认100
        process_mode: 处理模式，'full'检测整个影像，'edge'仅检测边缘，默认'full'
        keep_alpha: 是否保留Alpha通道(如果存在)，默认false
        protect_interior: 是否保护有效区域内部像素，默认false
        use_gpu: 是否使用GPU加速，默认false
        num_gpus: 使用GPU数量，0表示使用所有可用，默认0

    返回:
        包含任务ID的响应
    """
    try:
        tif_logger.info("收到TIF处理请求 (异步模式)")

        # 获取参数
        input_tif = request.args.get("input_tif")

        # 验证必要参数
        if not input_tif:
            error_msg = "缺少必要参数: input_tif"
            tif_logger.error(error_msg)
            return jsonify({"status": "error", "message": error_msg}), 400

        # 规范化路径
        input_tif = os.path.normpath(input_tif)
        tif_logger.info(f"处理输入文件: {input_tif}")

        # 检查输入文件是否存在
        if not os.path.exists(input_tif):
            error_msg = f"输入文件不存在: {input_tif}"
            tif_logger.error(error_msg)
            return jsonify({"status": "error", "message": error_msg}), 404

        # 检查输入文件格式
        file_ext = os.path.splitext(input_tif)[1].lower()
        if file_ext not in [".tif", ".tiff"]:
            warning_msg = f"警告: 输入文件扩展名 '{file_ext}' 不是标准的GeoTIFF扩展名"
            tif_logger.warning(warning_msg)

        # 检查文件大小
        try:
            file_size_mb = os.path.getsize(input_tif) / (1024 * 1024)
            tif_logger.info(f"输入文件大小: {file_size_mb:.2f} MB")
        except Exception as e:
            tif_logger.warning(f"无法获取文件大小: {str(e)}")

        # 构建参数字典
        process_args = {
            "input_tif": input_tif,
        }

        # 设置输出路径
        output_tif = request.args.get("output_tif")
        if output_tif:
            process_args["output_tif"] = output_tif
            tif_logger.info(f"输出TIF: {output_tif}")

            # 检查输出目录是否存在
            output_tif_dir = os.path.dirname(output_tif)
            if output_tif_dir and not os.path.exists(output_tif_dir):
                try:
                    tif_logger.info(f"创建输出TIF目录: {output_tif_dir}")
                    os.makedirs(output_tif_dir, exist_ok=True)
                except Exception as e:
                    error_msg = f"创建输出目录失败: {output_tif_dir}, 错误: {str(e)}"
                    tif_logger.error(error_msg)
                    return jsonify({"status": "error", "message": error_msg}), 500

        output_shp = request.args.get("output_shp")
        if output_shp:
            process_args["output_shp"] = output_shp
            tif_logger.info(f"输出SHP: {output_shp}")

            # 检查输出目录是否存在
            output_shp_dir = os.path.dirname(output_shp)
            if output_shp_dir and not os.path.exists(output_shp_dir):
                try:
                    tif_logger.info(f"创建输出SHP目录: {output_shp_dir}")
                    os.makedirs(output_shp_dir, exist_ok=True)
                except Exception as e:
                    error_msg = f"创建输出目录失败: {output_shp_dir}, 错误: {str(e)}"
                    tif_logger.error(error_msg)
                    return jsonify({"status": "error", "message": error_msg}), 500

        # 添加其他可选参数
        if "black_threshold" in request.args:
            black_threshold = int(request.args.get("black_threshold"))
            process_args["black_threshold"] = black_threshold
            tif_logger.info(f"黑色阈值: {black_threshold}")

        if "white_threshold" in request.args:
            white_threshold = int(request.args.get("white_threshold"))
            process_args["white_threshold"] = white_threshold
            tif_logger.info(f"白色阈值: {white_threshold}")

        if "nodata_value" in request.args:
            nodata_value = float(request.args.get("nodata_value"))
            process_args["nodata_value"] = nodata_value
            tif_logger.info(f"NoData值: {nodata_value}")

        if "simplify_tolerance" in request.args:
            simplify_tolerance = float(request.args.get("simplify_tolerance"))
            process_args["simplify_tolerance"] = simplify_tolerance
            tif_logger.info(f"简化容差: {simplify_tolerance}")

        if "edge_width" in request.args:
            edge_width = int(request.args.get("edge_width"))
            process_args["edge_width"] = edge_width
            tif_logger.info(f"边缘宽度: {edge_width}")

        if "process_mode" in request.args:
            process_mode = request.args.get("process_mode")
            process_args["process_mode"] = process_mode
            tif_logger.info(f"处理模式: {process_mode}")

        if "keep_alpha" in request.args:
            keep_alpha = request.args.get("keep_alpha").lower() == "true"
            process_args["keep_alpha"] = keep_alpha
            tif_logger.info(f"保留Alpha通道: {keep_alpha}")

        if "protect_interior" in request.args:
            protect_interior = request.args.get("protect_interior").lower() == "true"
            process_args["protect_interior"] = protect_interior
            tif_logger.info(f"保护内部区域: {protect_interior}")

        if "use_gpu" in request.args:
            use_gpu = request.args.get("use_gpu").lower() == "true"
            # 检查GPU可用性
            if use_gpu and not HAS_GPU:
                tif_logger.warning("请求使用GPU但GPU不可用，将使用CPU模式")
                process_args["use_gpu"] = False
            else:
                process_args["use_gpu"] = use_gpu
                tif_logger.info(f"使用GPU: {use_gpu}")

        if "num_gpus" in request.args:
            num_gpus = int(request.args.get("num_gpus"))
            process_args["num_gpus"] = num_gpus
            tif_logger.info(f"使用GPU数量: {num_gpus}")

        # 执行任务
        tif_logger.info("启动异步TIF处理任务...")
        task_id = tif_executor.execute_tif_process(**process_args)

        if task_id:
            tif_logger.info(f"异步任务启动成功，任务ID: {task_id}")
            return jsonify(
                {
                    "status": "success",
                    "message": "TIF处理任务已启动",
                    "task_id": task_id,
                    "process_args": process_args,
                }
            )
        else:
            error_msg = "启动TIF处理任务失败"
            tif_logger.error(error_msg)
            return jsonify({"status": "error", "message": error_msg}), 500

    except Exception as e:
        error_msg = f"API错误: {str(e)}"
        tif_logger.error(error_msg)
        import traceback

        stack_trace = traceback.format_exc()
        tif_logger.error(stack_trace)
        return (
            jsonify(
                {
                    "status": "error",
                    "message": f"服务器内部错误: {str(e)}",
                    "error_details": {
                        "type": type(e).__name__,
                        "traceback": stack_trace,
                    },
                }
            ),
            500,
        )


@tif_api.route("/status", methods=["GET"])
def get_tif_process_status():
    """
    获取TIF处理任务状态

    查询参数:
        task_id: 任务ID

    返回:
        任务状态信息
    """
    try:
        tif_logger.info("收到任务状态查询请求")

        # 获取任务ID
        task_id = request.args.get("task_id")

        # 检查任务ID是否提供
        if not task_id:
            error_msg = "缺少必要参数: task_id"
            tif_logger.error(error_msg)
            return jsonify({"status": "error", "message": error_msg}), 400

        tif_logger.info(f"查询任务: {task_id}")

        # 获取任务状态
        task_status = tif_executor.get_task_status(task_id)

        if not task_status:
            error_msg = f"未找到任务: {task_id}"
            tif_logger.warning(error_msg)
            return jsonify({"status": "error", "message": error_msg}), 404

        # 读取任务日志
        log_file = task_status.get("log_file")
        recent_logs = []

        if log_file and os.path.exists(log_file):
            try:
                # 读取日志文件的最后30行
                with open(log_file, "r", encoding="utf-8") as f:
                    # 逐行读取整个文件（可能会更好地处理大文件）
                    all_lines = f.readlines()
                    # 获取最后30行
                    recent_logs = all_lines[-30:] if len(all_lines) > 30 else all_lines
                    # 清除行尾的换行符
                    recent_logs = [line.rstrip() for line in recent_logs]
            except Exception as e:
                tif_logger.warning(f"读取任务日志文件时出错: {str(e)}")

        # 检查任务是否已完成
        is_completed = task_status.get("status") != "running"
        is_success = task_status.get("status") == "running_success"

        # 添加文件信息
        file_info = {}
        if is_completed:
            # 检查输出文件是否存在
            output_tif = task_status.get("parameters", {}).get("output_tif")
            if output_tif and os.path.exists(output_tif):
                try:
                    file_size = os.path.getsize(output_tif)
                    file_info["output_tif"] = {
                        "exists": True,
                        "size": file_size,
                        "size_mb": file_size / (1024 * 1024),
                    }
                except Exception as e:
                    tif_logger.warning(f"获取输出TIF文件信息时出错: {str(e)}")
                    file_info["output_tif"] = {"exists": True, "error": str(e)}
            elif output_tif:
                file_info["output_tif"] = {"exists": False}

            output_shp = task_status.get("parameters", {}).get("output_shp")
            if output_shp and os.path.exists(output_shp):
                try:
                    file_size = os.path.getsize(output_shp)
                    file_info["output_shp"] = {
                        "exists": True,
                        "size": file_size,
                        "size_kb": file_size / 1024,
                    }

                    # 检查SHP相关文件
                    missing_files = []
                    for ext in ["shx", "dbf", "prj"]:
                        companion_file = output_shp.replace(".shp", f".{ext}")
                        if not os.path.exists(companion_file):
                            missing_files.append(ext)

                    if missing_files:
                        file_info["output_shp"]["missing_components"] = missing_files
                except Exception as e:
                    tif_logger.warning(f"获取输出SHP文件信息时出错: {str(e)}")
                    file_info["output_shp"] = {"exists": True, "error": str(e)}
            elif output_shp:
                file_info["output_shp"] = {"exists": False}

        # 增强响应信息
        response = {
            "status": "success",
            "task": task_status,
            "recent_logs": recent_logs,
            "file_info": file_info,
            "query_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }

        # 如果任务失败，添加更多错误细节
        if task_status.get("status") == "running_failed":
            error_details = task_status.get("error_details", {})
            if not error_details and task_status.get("error"):
                error_details = {
                    "error_message": task_status.get("error"),
                    "error_type": "Unknown",
                }
            response["error_details"] = error_details

        tif_logger.info(
            f"任务 {task_id} 状态查询成功，当前状态: {task_status.get('status')}"
        )
        return jsonify(response)

    except Exception as e:
        error_msg = f"查询任务状态时出错: {str(e)}"
        tif_logger.error(error_msg)
        import traceback

        stack_trace = traceback.format_exc()
        tif_logger.error(stack_trace)
        return (
            jsonify(
                {
                    "status": "error",
                    "message": error_msg,
                    "error_details": {
                        "type": type(e).__name__,
                        "traceback": stack_trace,
                    },
                }
            ),
            500,
        )


@tif_api.route("/all", methods=["GET"])
def get_all_tif_status():
    """
    获取所有TIF处理任务状态

    返回:
        所有任务状态信息
    """
    try:
        # 获取所有任务状态
        tasks = tif_executor.get_all_tasks()

        return jsonify({"status": "success", "count": len(tasks), "tasks": tasks})

    except Exception as e:
        tif_logger.error(f"API错误: {str(e)}")
        import traceback

        tif_logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@tif_api.route("/set-nodata", methods=["GET"])
def set_nodata():
    """
    为TIF栅格影像设置NoData值，根据掩码区域

    查询参数:
        input_tif: 输入TIF文件路径（必需）
        output_tif: 输出TIF文件路径（可选，默认为input_tif同目录下的文件名_nodata.tif）
        black_threshold: 黑色判定阈值，默认0
        white_threshold: 白色判定阈值，默认255
        nodata_value: NoData值，默认-9999
        edge_width: 边缘检测宽度(像素)，默认100

    返回:
        处理结果
    """
    try:
        # 获取参数
        input_tif = request.args.get("input_tif")
        output_tif = request.args.get("output_tif")

        # 验证必要参数
        if not input_tif:
            return (
                jsonify({"status": "error", "message": "缺少必要参数: input_tif"}),
                400,
            )

        # 规范化路径
        input_tif = os.path.normpath(input_tif)

        # 如果未提供输出路径，则生成默认路径
        if not output_tif:
            input_dir = os.path.dirname(input_tif)
            input_filename = os.path.basename(input_tif)
            filename_without_ext = os.path.splitext(input_filename)[0]
            output_tif = os.path.join(input_dir, f"{filename_without_ext}_nodata.tif")

        # 规范化输出路径
        output_tif = os.path.normpath(output_tif)

        # 获取可选参数
        black_threshold = int(request.args.get("black_threshold", 0))
        white_threshold = int(request.args.get("white_threshold", 255))
        nodata_value = float(request.args.get("nodata_value", -9999))
        edge_width = int(request.args.get("edge_width", 100))

        # 检查输入文件是否存在
        if not os.path.exists(input_tif):
            return (
                jsonify({"status": "error", "message": f"输入文件不存在: {input_tif}"}),
                404,
            )

        # 确保输出目录存在
        output_dir = os.path.dirname(output_tif)
        if output_dir and not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
            except Exception as e:
                return (
                    jsonify(
                        {
                            "status": "error",
                            "message": f"创建输出目录失败: {output_dir}, 错误: {str(e)}",
                        }
                    ),
                    500,
                )

        # 读取输入影像
        tif_logger.info(f"读取输入影像: {input_tif}")
        info = read_tif(input_tif)
        if info is None:
            return (
                jsonify(
                    {"status": "error", "message": f"无法读取输入文件: {input_tif}"}
                ),
                400,
            )

        # 创建掩码
        tif_logger.info(f"创建边缘掩码，边缘宽度: {edge_width}像素")
        mask = create_edge_only_mask(
            info["data"],
            edge_width=edge_width,
            black_threshold=black_threshold,
            white_threshold=white_threshold,
        )

        # 设置NoData值并保存
        tif_logger.info(f"设置NoData值为: {nodata_value}")
        result = set_nodata_and_save(
            input_tif, output_tif, mask, nodata_value, processed_info=info
        )

        if result:
            return jsonify(
                {
                    "status": "success",
                    "message": "NoData设置成功",
                    "output_tif": output_tif,
                }
            )
        else:
            return jsonify({"status": "error", "message": "NoData设置失败"}), 500

    except Exception as e:
        tif_logger.error(f"API错误: {str(e)}")
        import traceback

        tif_logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@tif_api.route("/create-shapefile", methods=["GET"])
def create_shapefile():
    """
    从TIF文件生成有效区域的shapefile

    查询参数:
        input_tif: 输入TIF文件路径（必需）
        output_shp: 输出shapefile文件路径（可选，默认为input_tif同目录下的文件名_boundary.shp）
        nodata_value: NoData值，默认-9999
        simplify_tolerance: 多边形简化参数，默认0.1

    返回:
        处理结果
    """
    try:
        # 获取参数
        input_tif = request.args.get("input_tif")
        output_shp = request.args.get("output_shp")

        # 验证必要参数
        if not input_tif:
            return (
                jsonify({"status": "error", "message": "缺少必要参数: input_tif"}),
                400,
            )

        # 规范化路径
        input_tif = os.path.normpath(input_tif)

        # 如果未提供输出路径，则生成默认路径
        if not output_shp:
            input_dir = os.path.dirname(input_tif)
            input_filename = os.path.basename(input_tif)
            filename_without_ext = os.path.splitext(input_filename)[0]
            output_shp = os.path.join(input_dir, f"{filename_without_ext}_boundary.shp")

        # 规范化输出路径
        output_shp = os.path.normpath(output_shp)

        # 获取可选参数
        nodata_value = float(request.args.get("nodata_value", -9999))
        simplify_tolerance = float(request.args.get("simplify_tolerance", 0.1))

        # 检查输入文件是否存在
        if not os.path.exists(input_tif):
            return (
                jsonify({"status": "error", "message": f"输入文件不存在: {input_tif}"}),
                404,
            )

        # 确保输出目录存在
        output_dir = os.path.dirname(output_shp)
        if output_dir and not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
            except Exception as e:
                return (
                    jsonify(
                        {
                            "status": "error",
                            "message": f"创建输出目录失败: {output_dir}, 错误: {str(e)}",
                        }
                    ),
                    500,
                )

        # 执行shapefile创建
        tif_logger.info(f"从TIF文件创建shapefile: {input_tif} -> {output_shp}")
        result = create_shapefile_from_output_tif(
            input_tif,
            output_shp,
            nodata_value=nodata_value,
            simplify_tolerance=simplify_tolerance,
        )

        if result:
            return jsonify(
                {
                    "status": "success",
                    "message": "Shapefile创建成功",
                    "output_shp": output_shp,
                }
            )
        else:
            return jsonify({"status": "error", "message": "Shapefile创建失败"}), 500

    except Exception as e:
        tif_logger.error(f"API错误: {str(e)}")
        import traceback

        tif_logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500


@tif_api.route("/info", methods=["GET"])
def get_tif_info():
    """
    获取TIF文件的基本信息

    查询参数:
        path: TIF文件路径

    返回:
        TIF文件信息
    """
    try:
        # 获取参数
        tif_path = request.args.get("path")

        # 验证必要参数
        if not tif_path:
            return jsonify({"status": "error", "message": "缺少必要参数: path"}), 400

        # 规范化路径
        tif_path = os.path.normpath(tif_path)

        # 检查文件是否存在
        if not os.path.exists(tif_path):
            return (
                jsonify({"status": "error", "message": f"文件不存在: {tif_path}"}),
                404,
            )

        # 读取TIF信息
        info = read_tif(tif_path)
        if info is None:
            return (
                jsonify({"status": "error", "message": f"无法读取文件: {tif_path}"}),
                400,
            )

        # 提取基本信息
        tif_info = {
            "width": info["width"],
            "height": info["height"],
            "bands": info["bands"],
            "geotrans": info["geotrans"],
            "projection": info["proj"],
            "file_size": os.path.getsize(tif_path),
            "file_path": tif_path,
        }

        # 移除数据集和像素数据以减少响应大小
        if "data" in info:
            del info["data"]
        if "dataset" in info:
            info["dataset"] = None

        return jsonify({"status": "success", "tif_info": tif_info})

    except Exception as e:
        tif_logger.error(f"API错误: {str(e)}")
        import traceback

        tif_logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"服务器内部错误: {str(e)}"}), 500
