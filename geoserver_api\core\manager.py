#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer管理器 - 用于管理GeoServer服务的核心功能 (Django版本)
"""

import os
import logging
import glob
import datetime
from geo.Geoserver import Geoserver
import requests
from requests.auth import HTTPBasicAuth
from django.conf import settings

# 获取Django配置的日志记录器
logger = logging.getLogger('geoserver_api')


class GeoServerManager:
    """用于管理GeoServer操作的类，如发布图层、创建工作区等。"""

    def __init__(
        self, 
        url=None, 
        username=None, 
        password=None
    ):
        """初始化GeoServer连接。"""
        # 使用Django settings中的配置
        self.url = url or settings.GEOSERVER_URL
        self.username = username or settings.GEOSERVER_USER
        self.password = password or settings.GEOSERVER_PASSWORD
        
        self.geo = Geoserver(self.url, username=self.username, password=self.password)
        logger.info(f"已连接到GeoServer：{self.url}")
        self.check_connection()

    def check_connection(self):
        """检查GeoServer连接是否正常，并获取GeoServer信息。"""
        try:
            # 构建REST API URL
            base_url = self.geo.service_url
            if not base_url.endswith("/rest"):
                base_url = f"{base_url}/rest"

            # 尝试访问about/version.json获取GeoServer版本信息
            url = f"{base_url}/about/version.json"
            auth = HTTPBasicAuth(self.geo.username, self.geo.password)
            headers = {"Accept": "application/json"}

            response = requests.get(url, auth=auth, headers=headers, timeout=10)
            
            if response.status_code == 200:
                version_info = response.json()
                logger.info(f"GeoServer连接成功！版本信息：{version_info}")
                return True
            else:
                logger.warning(f"GeoServer连接检查失败，状态码：{response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"GeoServer连接检查失败：{str(e)}")
            return False
        except Exception as e:
            logger.error(f"GeoServer连接检查出现未知错误：{str(e)}")
            return False

    def get_workspaces(self):
        """获取所有工作区列表。"""
        try:
            workspaces = self.geo.get_workspaces()
            logger.info(f"获取到 {len(workspaces)} 个工作区")
            return workspaces
        except Exception as e:
            logger.error(f"获取工作区列表失败：{str(e)}")
            return []

    def check_workspace_exists(self, workspace_name):
        """检查工作区是否存在。"""
        try:
            workspace = self.geo.get_workspace(workspace_name)
            exists = workspace is not None
            logger.info(f"工作区 '{workspace_name}' {'存在' if exists else '不存在'}")
            return exists
        except Exception as e:
            logger.error(f"检查工作区 '{workspace_name}' 时出错：{str(e)}")
            return False

    def create_workspace(self, workspace_name):
        """创建新的工作区。"""
        try:
            if self.check_workspace_exists(workspace_name):
                logger.info(f"工作区 '{workspace_name}' 已存在，跳过创建")
                return True
                
            result = self.geo.create_workspace(workspace_name)
            if result:
                logger.info(f"成功创建工作区：{workspace_name}")
                return True
            else:
                logger.error(f"创建工作区失败：{workspace_name}")
                return False
        except Exception as e:
            logger.error(f"创建工作区 '{workspace_name}' 时出错：{str(e)}")
            return False

    def delete_workspace(self, workspace_name, recurse=True):
        """删除工作区。"""
        try:
            if not self.check_workspace_exists(workspace_name):
                logger.warning(f"工作区 '{workspace_name}' 不存在，无法删除")
                return False
                
            result = self.geo.delete_workspace(workspace_name, recurse=recurse)
            if result:
                logger.info(f"成功删除工作区：{workspace_name}")
                return True
            else:
                logger.error(f"删除工作区失败：{workspace_name}")
                return False
        except Exception as e:
            logger.error(f"删除工作区 '{workspace_name}' 时出错：{str(e)}")
            return False

    def get_layers(self, workspace=None):
        """获取图层列表。"""
        try:
            if workspace:
                layers = self.geo.get_layers(workspace)
                logger.info(f"获取到工作区 '{workspace}' 中的 {len(layers)} 个图层")
            else:
                layers = self.geo.get_layers()
                logger.info(f"获取到 {len(layers)} 个图层")
            return layers
        except Exception as e:
            logger.error(f"获取图层列表失败：{str(e)}")
            return []

    def get_datastores(self, workspace):
        """获取指定工作区中的数据存储。"""
        try:
            datastores = self.geo.get_datastores(workspace)
            logger.info(f"获取到工作区 '{workspace}' 中的 {len(datastores)} 个数据存储")
            return datastores
        except Exception as e:
            logger.error(f"获取工作区 '{workspace}' 的数据存储失败：{str(e)}")
            return []

    def create_datastore(self, name, workspace, host, port, database, username, password, schema="public"):
        """创建PostGIS数据存储。"""
        try:
            # 确保工作区存在
            if not self.check_workspace_exists(workspace):
                logger.info(f"工作区 '{workspace}' 不存在，正在创建...")
                if not self.create_workspace(workspace):
                    logger.error(f"无法创建工作区 {workspace}")
                    return False
                logger.info(f"成功创建工作区 '{workspace}'")

            # 创建数据存储
            result = self.geo.create_datastore(
                name=name,
                workspace=workspace,
                host=host,
                port=port,
                database=database,
                username=username,
                password=password,
                schema=schema
            )

            if result:
                logger.info(f"成功创建PostGIS数据存储：{workspace}:{name}")
                return True
            else:
                logger.error(f"创建PostGIS数据存储失败：{workspace}:{name}")
                return False
        except Exception as e:
            logger.error(f"创建PostGIS数据存储 '{workspace}:{name}' 时出错：{str(e)}")
            return False

    def publish_shapefile(self, file_path, workspace, store=None, layer=None, charset="UTF-8"):
        """发布Shapefile到GeoServer。"""
        try:
            if not os.path.exists(file_path):
                logger.error(f"Shapefile文件不存在：{file_path}")
                return False

            # 确保工作区存在
            if not self.check_workspace_exists(workspace):
                logger.info(f"工作区 '{workspace}' 不存在，正在创建...")
                if not self.create_workspace(workspace):
                    logger.error(f"无法创建工作区 {workspace}")
                    return False
                logger.info(f"成功创建工作区 '{workspace}'")

            # 如果没有指定存储名称，使用文件名
            if not store:
                store = os.path.splitext(os.path.basename(file_path))[0]

            # 如果没有指定图层名称，使用存储名称
            if not layer:
                layer = store

            # 发布Shapefile
            result = self.geo.create_shp_datastore(
                path=file_path,
                store_name=store,
                workspace=workspace,
                charset=charset
            )

            if result:
                logger.info(f"成功发布Shapefile：{workspace}:{layer}")
                return True
            else:
                logger.error(f"发布Shapefile失败：{file_path}")
                return False
        except Exception as e:
            logger.error(f"发布Shapefile '{file_path}' 时出错：{str(e)}")
            return False

    def publish_geotiff(self, file_path, workspace, store=None, layer=None):
        """发布GeoTIFF到GeoServer。"""
        try:
            if not os.path.exists(file_path):
                logger.error(f"GeoTIFF文件不存在：{file_path}")
                return False

            # 确保工作区存在
            if not self.check_workspace_exists(workspace):
                logger.info(f"工作区 '{workspace}' 不存在，正在创建...")
                if not self.create_workspace(workspace):
                    logger.error(f"无法创建工作区 {workspace}")
                    return False
                logger.info(f"成功创建工作区 '{workspace}'")

            # 如果没有指定存储名称，使用文件名
            if not store:
                store = os.path.splitext(os.path.basename(file_path))[0]

            # 发布GeoTIFF
            result = self.geo.create_coveragestore(
                path=file_path,
                workspace=workspace,
                layer_name=layer or store,
                store_name=store
            )

            if result:
                logger.info(f"成功发布GeoTIFF：{workspace}:{layer or store}")
                return True
            else:
                logger.error(f"发布GeoTIFF失败：{file_path}")
                return False
        except Exception as e:
            logger.error(f"发布GeoTIFF '{file_path}' 时出错：{str(e)}")
            return False
