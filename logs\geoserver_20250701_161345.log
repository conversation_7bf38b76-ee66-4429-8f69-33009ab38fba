2025-07-01 16:13:45,101 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250701_161345.log
2025-07-01 16:13:45,119 - root - INFO - 开始执行命令: publish-shapefile-directory
2025-07-01 16:13:45,120 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-01 16:13:45,120 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-01 16:13:45,140 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-01 16:13:45,141 - root - INFO - 在目录 'data/20250701/testshp' 中找到 5 个Shapefile文件
2025-07-01 16:13:45,142 - root - INFO - 使用字符集: UTF-8
2025-07-01 16:13:45,143 - root - INFO - 未提供存储名称，使用目录名称作为前缀: testshp
2025-07-01 16:13:45,158 - root - INFO - 工作区 'test_workspace2' 不存在，正在创建...
2025-07-01 16:13:45,215 - root - INFO - 成功创建工作区 'test_workspace2'
2025-07-01 16:13:45,235 - root - INFO - 在工作区 'test_workspace2' 中找到 0 个图层
2025-07-01 16:13:45,235 - root - INFO - 发布前工作区 'test_workspace2' 中有 0 个图层
2025-07-01 16:13:45,236 - root - INFO - 正在发布Shapefile: 招商引资片区.shp, 存储名: testshp
2025-07-01 16:13:45,254 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区.shp
2025-07-01 16:13:45,255 - root - INFO - 添加文件到ZIP: 招商引资片区.shp
2025-07-01 16:13:45,380 - root - INFO - 添加文件到ZIP: 招商引资片区.dbf
2025-07-01 16:13:45,383 - root - INFO - 添加文件到ZIP: 招商引资片区.shx
2025-07-01 16:13:45,384 - root - INFO - 添加文件到ZIP: 招商引资片区.prj
2025-07-01 16:13:45,385 - root - INFO - 添加文件到ZIP: 招商引资片区.qix
2025-07-01 16:13:45,385 - root - INFO - 添加文件到ZIP: 招商引资片区.cpg
2025-07-01 16:13:45,386 - root - INFO - 添加文件到ZIP: 招商引资片区.qmd
2025-07-01 16:13:45,387 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace2/datastores/testshp/file.shp
2025-07-01 16:13:45,388 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:13:45,529 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:13:45,551 - root - INFO - 图层 '招商引资片区' 已存在
2025-07-01 16:13:45,574 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace2/testshp/招商引资片区
2025-07-01 16:13:45,575 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区.shp' 成功发布为图层 'test_workspace2:招商引资片区'
2025-07-01 16:13:45,576 - root - INFO - 正在发布Shapefile: 招商引资片区交种植区.shp, 存储名: testshp
2025-07-01 16:13:45,592 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区交种植区.shp
2025-07-01 16:13:45,593 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shp
2025-07-01 16:13:46,298 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.dbf
2025-07-01 16:13:46,455 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shx
2025-07-01 16:13:46,469 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.prj
2025-07-01 16:13:46,470 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qix
2025-07-01 16:13:46,483 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.cpg
2025-07-01 16:13:46,485 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qmd
2025-07-01 16:13:46,485 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace2/datastores/testshp/file.shp
2025-07-01 16:13:46,486 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:13:46,695 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:13:46,707 - root - INFO - 图层 '招商引资片区交种植区' 已存在
2025-07-01 16:13:46,725 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace2/testshp/招商引资片区交种植区
2025-07-01 16:13:46,727 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区交种植区.shp' 成功发布为图层 'test_workspace2:招商引资片区交种植区'
2025-07-01 16:13:46,728 - root - INFO - 正在发布Shapefile: 招商引资片区交设施农用地.shp, 存储名: testshp
2025-07-01 16:13:46,743 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区交设施农用地.shp
2025-07-01 16:13:46,744 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shp
2025-07-01 16:13:46,882 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.dbf
2025-07-01 16:13:46,895 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shx
2025-07-01 16:13:46,896 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.prj
2025-07-01 16:13:46,897 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qix
2025-07-01 16:13:46,900 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.cpg
2025-07-01 16:13:46,901 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qmd
2025-07-01 16:13:46,901 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace2/datastores/testshp/file.shp
2025-07-01 16:13:46,902 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:13:47,007 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:13:47,073 - root - INFO - 成功创建图层 '招商引资片区交设施农用地'
2025-07-01 16:13:47,092 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace2/testshp/招商引资片区交设施农用地
2025-07-01 16:13:47,092 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区交设施农用地.shp' 成功发布为图层 'test_workspace2:招商引资片区交设施农用地'
2025-07-01 16:13:47,094 - root - INFO - 正在发布Shapefile: 种植区.shp, 存储名: testshp
2025-07-01 16:13:47,109 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\种植区.shp
2025-07-01 16:13:47,110 - root - INFO - 添加文件到ZIP: 种植区.shp
2025-07-01 16:13:48,408 - root - INFO - 添加文件到ZIP: 种植区.dbf
2025-07-01 16:13:48,617 - root - INFO - 添加文件到ZIP: 种植区.shx
2025-07-01 16:13:48,640 - root - INFO - 添加文件到ZIP: 种植区.prj
2025-07-01 16:13:48,641 - root - INFO - 添加文件到ZIP: 种植区.qix
2025-07-01 16:13:48,666 - root - INFO - 添加文件到ZIP: 种植区.cpg
2025-07-01 16:13:48,668 - root - INFO - 添加文件到ZIP: 种植区.qmd
2025-07-01 16:13:48,668 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace2/datastores/testshp/file.shp
2025-07-01 16:13:48,669 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:13:48,950 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:13:49,011 - root - INFO - 成功创建图层 '种植区'
2025-07-01 16:13:49,035 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace2/testshp/种植区
2025-07-01 16:13:49,036 - root - INFO - Shapefile 'data/20250701/testshp\种植区.shp' 成功发布为图层 'test_workspace2:种植区'
2025-07-01 16:13:49,038 - root - INFO - 正在发布Shapefile: 设施农用地潜力.shp, 存储名: testshp
2025-07-01 16:13:49,053 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\设施农用地潜力.shp
2025-07-01 16:13:49,054 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shp
2025-07-01 16:13:50,149 - root - INFO - 添加文件到ZIP: 设施农用地潜力.dbf
2025-07-01 16:13:50,213 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shx
2025-07-01 16:13:50,222 - root - INFO - 添加文件到ZIP: 设施农用地潜力.prj
2025-07-01 16:13:50,223 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qix
2025-07-01 16:13:50,236 - root - INFO - 添加文件到ZIP: 设施农用地潜力.cpg
2025-07-01 16:13:50,237 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qmd
2025-07-01 16:13:50,238 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace2/datastores/testshp/file.shp
2025-07-01 16:13:50,238 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:13:50,441 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:13:50,507 - root - INFO - 成功创建图层 '设施农用地潜力'
2025-07-01 16:13:50,521 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace2/testshp/设施农用地潜力
2025-07-01 16:13:50,521 - root - INFO - Shapefile 'data/20250701/testshp\设施农用地潜力.shp' 成功发布为图层 'test_workspace2:设施农用地潜力'
2025-07-01 16:13:50,536 - root - INFO - 在工作区 'test_workspace2' 中找到 5 个图层
2025-07-01 16:13:50,537 - root - INFO - 发布后工作区 'test_workspace2' 中有 5 个图层
2025-07-01 16:13:50,537 - root - INFO - 新增了 5 个图层: 招商引资片区交设施农用地, 设施农用地潜力, 招商引资片区交种植区, 招商引资片区, 种植区
2025-07-01 16:13:50,538 - root - INFO - 成功发布了 5 个Shapefile文件
2025-07-01 16:13:50,538 - root - INFO - 成功发布: 5, 失败: 0
2025-07-01 16:13:50,539 - root - INFO - 命令执行完成: publish-shapefile-directory
