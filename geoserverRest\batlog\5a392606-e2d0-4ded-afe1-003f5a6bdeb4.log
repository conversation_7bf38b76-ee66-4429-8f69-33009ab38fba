执行命令: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
开始时间: 2025-07-24 16:11:55

[INFO]    Initializing ODM 3.5.5 - Thu Jul 24 16:12:15  2025
[INFO]    ==============
[INFO]    3d_tiles: False
[INFO]    align: None
[INFO]    auto_boundary: False
[INFO]    auto_boundary_distance: 0
[INFO]    bg_removal: False
[INFO]    boundary: {}
[INFO]    build_overviews: False
[INFO]    camera_lens: auto
[INFO]    cameras: {}
[INFO]    cog: False
[INFO]    copy_to: None
[INFO]    crop: 3
[INFO]    dem_decimation: 1
[INFO]    dem_euclidean_map: False
[INFO]    dem_gapfill_steps: 3
[INFO]    dem_resolution: 5
[INFO]    dsm: False
[INFO]    dtm: False
[INFO]    end_with: odm_postprocess
[INFO]    fast_orthophoto: False
[INFO]    feature_quality: high
[INFO]    feature_type: dspsift
[INFO]    force_gps: False
[INFO]    gcp: None
[INFO]    geo: None
[INFO]    gltf: False
[INFO]    gps_accuracy: 3
[INFO]    ignore_gsd: False
[INFO]    matcher_neighbors: 0
[INFO]    matcher_order: 0
[INFO]    matcher_type: flann
[INFO]    max_concurrency: 8
[INFO]    merge: all
[INFO]    mesh_octree_depth: 11
[INFO]    mesh_size: 200000
[INFO]    min_num_features: 10000
[INFO]    name: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
[INFO]    no_gpu: False
[INFO]    optimize_disk_space: False
[INFO]    orthophoto_compression: DEFLATE
[INFO]    orthophoto_cutline: False
[INFO]    orthophoto_kmz: False
[INFO]    orthophoto_no_tiled: False
[INFO]    orthophoto_png: False
[INFO]    orthophoto_resolution: 5
[INFO]    pc_classify: False
[INFO]    pc_copc: False
[INFO]    pc_csv: False
[INFO]    pc_ept: False
[INFO]    pc_filter: 5
[INFO]    pc_las: False
[INFO]    pc_quality: medium
[INFO]    pc_rectify: False
[INFO]    pc_sample: 0
[INFO]    pc_skip_geometric: False
[INFO]    primary_band: auto
[INFO]    project_path: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
[INFO]    radiometric_calibration: none
[INFO]    rerun: None
[INFO]    rerun_all: False
[INFO]    rerun_from: None
[INFO]    rolling_shutter: False
[INFO]    rolling_shutter_readout: 0
[INFO]    sfm_algorithm: incremental
[INFO]    sfm_no_partial: False
[INFO]    skip_3dmodel: False
[INFO]    skip_band_alignment: False
[INFO]    skip_orthophoto: False
[INFO]    skip_report: False
[INFO]    sky_removal: False
[INFO]    sm_cluster: None
[INFO]    sm_no_align: False
[INFO]    smrf_scalar: 1.25
[INFO]    smrf_slope: 0.15
[INFO]    smrf_threshold: 0.5
[INFO]    smrf_window: 18.0
[INFO]    split: 999999
[INFO]    split_image_groups: None
[INFO]    split_overlap: 150
[INFO]    texturing_keep_unseen_faces: False
[INFO]    texturing_single_material: False
[INFO]    texturing_skip_global_seam_leveling: False
[INFO]    tiles: False
[INFO]    use_3dmesh: False
[INFO]    use_exif: False
[INFO]    use_fixed_camera_params: False
[INFO]    use_hybrid_bundle_adjustment: False
[INFO]    video_limit: 500
[INFO]    video_resolution: 4000
[INFO]    ==============
[INFO]    Running dataset stage
[INFO]    Loading dataset from: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\images
[INFO]    Loading images database: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\images.json
[INFO]    Found 20 usable images
[INFO]    Coordinates file already exist: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_georeferencing\coords.txt
[INFO]    Model geo file already exist: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_georeferencing\odm_georeferencing_model_geo.txt
[INFO]    Finished dataset stage
[INFO]    Running split stage
[INFO]    Normal dataset, will process all at once.
[INFO]    Finished split stage
[INFO]    Running merge stage
[INFO]    Normal dataset, nothing to merge.
[INFO]    Finished merge stage
[INFO]    Running opensfm stage
[WARNING] D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\opensfm\image_list.txt already exists, not rerunning OpenSfM setup
[WARNING] D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\opensfm\exif already exists, not rerunning photo to metadata
[WARNING] Detect features already done: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\opensfm\features exists
[WARNING] Match features already done: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\opensfm\matches exists
[WARNING] Found a valid OpenSfM tracks file in: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\opensfm\tracks.csv
[WARNING] Found a valid OpenSfM reconstruction file in: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\opensfm\reconstruction.json
[INFO]    Already extracted cameras
[INFO]    Export reconstruction stats
[WARNING] Found existing reconstruction stats D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\opensfm\stats\stats.json
[WARNING] Will skip exporting D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\opensfm\reconstruction.geocoords.json
[INFO]    Undistorting D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\opensfm ...
[WARNING] Already undistorted (nominal)
[WARNING] Found a valid OpenSfM NVM reconstruction file in: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\opensfm\undistorted/reconstruction.nvm
[INFO]    Finished opensfm stage
[INFO]    Running openmvs stage
[WARNING] Found a valid OpenMVS reconstruction file in: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\opensfm\undistorted\openmvs\scene_dense_dense_filtered.ply
[INFO]    Finished openmvs stage
[INFO]    Running odm_filterpoints stage
[WARNING] Found a valid point cloud file in: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_filterpoints\point_cloud.ply
[INFO]    Finished odm_filterpoints stage
[INFO]    Running odm_meshing stage
[WARNING] Found a valid ODM Mesh file in: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_meshing\odm_mesh.ply
[WARNING] Found a valid ODM 2.5D Mesh file in: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_meshing\odm_25dmesh.ply
[INFO]    Finished odm_meshing stage
[INFO]    Running mvs_texturing stage
[WARNING] Found a valid ODM Texture file in: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_texturing\odm_textured_model_geo.obj
[WARNING] Found a valid ODM Texture file in: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_texturing_25d\odm_textured_model_geo.obj
[INFO]    Finished mvs_texturing stage
[INFO]    Running odm_georeferencing stage
[WARNING] Found a valid georeferenced model in: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_georeferencing\odm_georeferenced_model.laz
[INFO]    Finished odm_georeferencing stage
[INFO]    Running odm_dem stage
[WARNING] Maximum resolution set to 1.0 * (GSD - 10.0%) (7.27 cm / pixel, requested resolution was 5.00 cm / pixel)
[INFO]    Create DSM: False
[INFO]    Create DTM: False
[INFO]    DEM input file D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_georeferencing\odm_georeferenced_model.laz found: True
[WARNING] DEM will not be generated
[INFO]    Finished odm_dem stage
[INFO]    Running odm_orthophoto stage
[WARNING] Found a valid orthophoto in: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
[INFO]    Finished odm_orthophoto stage
[INFO]    Running odm_report stage
[INFO]    Exporting shots.geojson
[WARNING] Found a valid shots file in: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_report\shots.geojson
[WARNING] Found a valid camera mappings file in: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_report\camera_mappings.npz
[WARNING] Reading existing stats D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_report\stats.json
[WARNING] Cannot generate overlap diagram, point cloud stats missing
[INFO]    Exporting report to D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_report\report.pdf
[WARNING] Report D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_report\report.pdf already exported
[INFO]    Finished odm_report stage
[INFO]    Running odm_postprocess stage
[INFO]    Post Processing
[INFO]    Adding TIFFTAGs to D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
[INFO]    Finished odm_postprocess stage
[INFO]    No more stages to run
[INFO]    MMMMMMMMMMMNNNMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMNNNMMMMMMMMMMM
[INFO]    MMMMMMdo:..---../sNMMMMMMMMMMMMMMMMMMMMMMMMMMNs/..---..:odMMMMMM
[INFO]    MMMMy-.odNMMMMMNy/`/mMMMMMMMMMMMMMMMMMMMMMMm/`/hNMMMMMNdo.-yMMMM
[INFO]    MMN/`sMMMMMMMMMNNMm/`yMMMMMMMMMMMMMMMMMMMMy`/mMNNMMMMMMMMNs`/MMM
[INFO]    MM/ hMMMMMMMMNs.+MMM/ dMMMMMMMMMMMMMMMMMMh +MMM+.sNMMMMMMMMh +MM
[INFO]    MN /MMMMMMNo/./mMMMMN :MMMMMMMMMMMMMMMMMM: NMMMMm/./oNMMMMMM: NM
[INFO]    Mm +MMMMMN+ `/MMMMMMM`-MMMMMMMMMMMMMMMMMM-`MMMMMMM:` oNMMMMM+ mM
[INFO]    MM..NMMNs./mNMMMMMMMy sMMMMMMMMMMMMMMMMMMo hMMMMMMMNm/.sNMMN`-MM
[INFO]    MMd`:mMNomMMMMMMMMMy`:MMMMMMMNmmmmNMMMMMMN:`hMMMMMMMMMdoNMm-`dMM
[INFO]    MMMm:.omMMMMMMMMNh/  sdmmho/.`..`-``-/sddh+  /hNMMMMMMMMdo.:mMMM
[INFO]    MMMMMd+--/osss+:-:/`  ```:- .ym+ hmo``:-`   `+:-:ossso/-:+dMMMMM
[INFO]    MMMMMMMNmhysosydmNMo   /ds`/NMM+ hMMd..dh.  sMNmdysosyhmNMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMMMs .:-:``hmmN+ yNmds -:.:`-NMMMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMMN.-mNm- //:::. -:://: +mMd`-NMMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMM+ dMMN -MMNNN+ yNNNMN :MMMs sMMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMM`.mmmy /mmmmm/ smmmmm``mmmh :MMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMM``:::- ./////. -:::::` :::: -MMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMM:`mNNd /NNNNN+ hNNNNN .NNNy +MMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMMd`/MMM.`ys+//. -/+oso +MMN.`mMMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMMMy /o:- `oyhd/ shys+ `-:s-`hMMMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMNmdhhhdmNMMM`  +d+ sMMM+ hMMN:`hh-  sMMNmdhhhdmNMMMMMMMM
[INFO]    MMMMMms:::/++//::+ho    .+- /dM+ hNh- +/`   -h+:://++/::/smMMMMM
[INFO]    MMMN+./hmMMMMMMNds-  ./oso:.``:. :-``.:os+-  -sdNMMMMMMmy:.oNMMM
[INFO]    MMm-.hMNhNMMMMMMMMNo`/MMMMMNdhyyyyhhdNMMMM+`oNMMMMMMMMNhNMh.-mMM
[INFO]    MM:`mMMN/-sNNMMMMMMMo yMMMMMMMMMMMMMMMMMMy sMMMMMMMNNs-/NMMm`:MM
[INFO]    Mm /MMMMMd/.-oMMMMMMN :MMMMMMMMMMMMMMMMMM-`MMMMMMMo-./dMMMMM/ NM
[INFO]    Mm /MMMMMMm:-`sNMMMMN :MMMMMMMMMMMMMMMMMM-`MMMMMNs`-/NMMMMMM/ NM
[INFO]    MM:`mMMMMMMMMd/-sMMMo yMMMMMMMMMMMMMMMMMMy sMMMs-/dMMMMMMMMd`:MM
[INFO]    MMm-.hMMMMMMMMMdhMNo`+MMMMMMMMMMMMMMMMMMMM+`oNMhdMMMMMMMMMh.-mMM
[INFO]    MMMNo./hmNMMMMMNms--yMMMMMMMMMMMMMMMMMMMMMMy--smNMMMMMNmy/.oNMMM
[INFO]    MMMMMms:-:/+++/:-+hMMMMMMMMMMMMMMMMMMMMMMMMMNh+-:/+++/:-:smMMMMM
[INFO]    MMMMMMMMNdhhyhdmMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMmdhyhhmNMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMNNNNNMMMMMMNNNNNNMMMMMMMMNNMMMMMMMNNMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMh/-...-+dMMMm......:+hMMMMs../MMMMMo..sMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMM/  /yhy-  sMMm  -hhy/  :NMM+   oMMMy   /MMMMMMMMMMMM
[INFO]    MMMMMMMMMMMy  /MMMMN`  NMm  /MMMMo  +MM: .` yMd``` :MMMMMMMMMMMM
[INFO]    MMMMMMMMMMM+  sMMMMM:  hMm  /MMMMd  -MM- /s `h.`d- -MMMMMMMMMMMM
[INFO]    MMMMMMMMMMMs  +MMMMM.  mMm  /MMMMy  /MM. +M/   yM: `MMMMMMMMMMMM
[INFO]    MMMMMMMMMMMN-  smNm/  +MMm  :NNdo` .mMM` oMM+/yMM/  MMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMNo-    `:yMMMm      `:sNMMM` sMMMMMMM+  NMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMNmmNMMMMMMMNmmmmNMMMMMMMNNMMMMMMMMMNNMMMMMMMMMMMM
[INFO]    ODM app finished - Thu Jul 24 16:12:18  2025

完成时间: 2025-07-24 16:12:19
返回代码: 0
状态: 运行成功
