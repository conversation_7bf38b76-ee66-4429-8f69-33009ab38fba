import numpy as np
from osgeo import gdal,ogr,osr
import os
from skimage.measure import find_contours
import shapely.geometry as geometry
from shapely.ops import unary_union
import multiprocessing
from functools import partial
import cv2
import argparse
import time
import sys
import uuid
import json
import datetime
import threading
import logging
from logging.handlers import RotatingFileHandler
from shapely.geometry import Polygon, MultiPolygon

# NumPy兼容性修复 - 处理numpy.bool弃用问题
try:
    import numpy
    if not hasattr(numpy, 'bool'):
        numpy.bool = numpy.bool_
except:
    pass

try:
    from tqdm import tqdm
except ImportError:
    print("警告: 未安装tqdm库，将不显示进度条。可通过 pip install tqdm 安装。")
    # 定义一个简单的替代类
    class tqdm:
        def __init__(self, iterable=None, total=None, desc=None, **kwargs):
            self.iterable = iterable
            self.total = total
            self.desc = desc
            self.count = 0
        
        def __iter__(self):
            for obj in self.iterable:
                self.count += 1
                if self.count % 10 == 0 and self.desc:
                    print(f"{self.desc}: {self.count}/{self.total}")
                yield obj
        
        def update(self, n=1):
            self.count += n

# 设置GPU支持标志为False，不再动态检测
HAS_CUPY = False
HAS_TORCH = False
NUM_GPUS = 0
HAS_GPU = False


def read_tif(filepath):
    """
    读取TIF格式的栅格影像文件并返回相关信息
    
    参数:
        filepath (str): 需要读取的TIF文件的路径
    
    返回:
        dict 或 None: 如果读取成功，返回包含以下键的字典:
            - width: 影像宽度(像素)
            - height: 影像高度(像素)
            - bands: 影像波段数量
            - data: 栅格数据数组，多波段时形状为 (bands, height, width)
            - geotrans: GDAL地理变换参数(6个元素的数组)
            - proj: 投影信息(WKT字符串)
            - dataset: GDAL数据集对象
        如果读取失败，返回None
    """
    # 检查文件是否存在
    if not os.path.exists(filepath):
        print(f"错误：文件不存在: {filepath}")
        return None
        
    # 检查文件权限
    try:
        with open(filepath, 'rb') as f:
            pass
    except PermissionError:
        print(f"错误：没有文件读取权限: {filepath}")
        return None
    except Exception as e:
        print(f"错误：检查文件权限时出错: {filepath}, 错误: {str(e)}")
        return None
        
    try:
        print(f"尝试打开TIF文件: {filepath}")
        dataset = gdal.Open(filepath)
        if dataset is None:
            print(f"无法打开文件: {filepath}")
            gdal_error = gdal.GetLastErrorMsg() if hasattr(gdal, 'GetLastErrorMsg') else '未知错误'
            print(f"GDAL错误信息: {gdal_error}")
            return None
        
        width = dataset.RasterXSize
        height = dataset.RasterYSize
        bands = dataset.RasterCount
        
        print(f"TIF文件信息: 宽={width}, 高={height}, 波段数={bands}")
        
        # 检查影像大小
        if width == 0 or height == 0 or bands == 0:
            print(f"无效的影像尺寸: 宽={width}, 高={height}, 波段数={bands}")
            return None
        
        try:
            print(f"开始读取影像数据，大小约: {(width * height * bands * 4) / (1024*1024):.2f} MB")
            data = dataset.ReadAsArray(0, 0, width, height)
            
            print("获取地理参照信息...")
            geotrans = dataset.GetGeoTransform()
            proj = dataset.GetProjection()
            
            if proj:
                print(f"投影信息: {proj[:100]}...")  # 只打印前100个字符
            else:
                print("警告: 未找到投影信息")
                
            if geotrans:
                print(f"地理变换参数: {geotrans}")
            else:
                print("警告: 未找到地理变换参数")
                
        except Exception as e:
            print(f"读取影像数据失败: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return None
            
        print(f"成功读取TIF文件: {filepath}")
        return {
            'width': width,
            'height': height,
            'bands': bands,
            'data': data,
            'geotrans': geotrans,
            'proj': proj,
            'dataset': dataset
        }
    except Exception as e:
        print(f"读取影像时发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return None


def create_edge_only_mask(rgb_data, edge_width=100, black_threshold=0, white_threshold=255):
    """
    创建掩码，仅识别图像边缘区域中的无效像素(黑色或白色)
    
    这个函数主要用于处理无人机影像，通常在影像边缘区域会有黑色或白色的无效区域。
    函数会检查图像边缘指定宽度范围内的像素，识别出全黑或全白的区域标记为无效区域。
    
    参数:
        rgb_data (numpy.ndarray): 输入的RGB图像数据，形状为(3, height, width)
        edge_width (int, 可选): 检查边缘的宽度(像素)，默认为100
        black_threshold (int, 可选): 黑色判定阈值，RGB值均小于等于此值的像素视为黑色，默认为0
        white_threshold (int, 可选): 白色判定阈值，RGB值均大于等于此值的像素视为白色，默认为255
        
    返回:
        numpy.ndarray: 掩码数组，形状为(height, width)，有效数据区域为1，无效区域(边缘的黑/白区域)为0
    """
    height, width = rgb_data.shape[1], rgb_data.shape[2]
    
    # 创建初始全1掩码（所有像素默认都是有效的）
    mask = np.ones((height, width), dtype=np.uint8)
    
    # 创建边缘区域掩码
    edge_mask = np.zeros((height, width), dtype=bool)
    
    # 标记边缘区域
    edge_mask[:edge_width, :] = True  # 上边缘
    edge_mask[-edge_width:, :] = True  # 下边缘
    edge_mask[:, :edge_width] = True  # 左边缘
    edge_mask[:, -edge_width:] = True  # 右边缘
    
    # 仅在边缘区域内检查黑色像素
    if black_threshold == 0:
        black_pixels = (rgb_data[0] == 0) & (rgb_data[1] == 0) & (rgb_data[2] == 0) & edge_mask
    else:
        black_pixels = (rgb_data[0] <= black_threshold) & (rgb_data[1] <= black_threshold) & (rgb_data[2] <= black_threshold) & edge_mask
    
    # 仅在边缘区域内检查白色像素
    if white_threshold == 255:
        white_pixels = (rgb_data[0] == 255) & (rgb_data[1] == 255) & (rgb_data[2] == 255) & edge_mask
    else:
        white_pixels = (rgb_data[0] >= white_threshold) & (rgb_data[1] >= white_threshold) & (rgb_data[2] >= white_threshold) & edge_mask
    
    # 合并掩码
    invalid_pixels = black_pixels | white_pixels
    mask[invalid_pixels] = 0
    
    return mask


def set_nodata_and_save(input_file, output_file, mask, nodata_value=-9999, processed_info=None):
    """
    根据掩码在影像中设置NoData值并将结果保存为新的TIF文件
    
    该函数读取输入TIF文件，根据提供的掩码将标记为无效区域的像素设置为NoData值，
    并将处理后的影像保存为新的TIF文件。
    
    参数:
        input_file (str): 输入TIF文件路径
        output_file (str): 输出TIF文件路径
        mask (numpy.ndarray): 掩码数组，形状为(height, width)，值为0的区域将被设置为NoData
        nodata_value (float, 可选): 设置的NoData值，默认为-9999
        processed_info (dict, 可选): 如果已经读取过输入文件，可以传入之前获取的信息，
                                    避免重复读取，提高效率
    
    返回:
        bool: 操作成功返回True，失败返回False
    """
    print(f"开始设置NoData值并保存输出文件: {output_file}")
    
    # 使用已处理过的信息，或者重新读取文件
    if processed_info is not None:
        print("使用已处理过的影像信息")
        info = processed_info
    else:
        print(f"重新读取输入文件: {input_file}")
        info = read_tif(input_file)
    
    if info is None:
        print("获取影像信息失败，无法继续处理")
        return False
    
    # 获取原始数据和信息
    data = info['data']
    geotrans = info['geotrans']
    proj = info['proj']
    bands = info['bands']
    width = info['width']
    height = info['height']
    
    print(f"影像信息: 宽={width}, 高={height}, 波段数={bands}")
    print(f"掩码形状: {mask.shape}, 数据类型: {mask.dtype}")
    
    # 检查掩码尺寸是否匹配
    if mask.shape != (height, width):
        print(f"错误: 掩码形状 {mask.shape} 与影像形状 ({height}, {width}) 不匹配")
        return False
    
    # 检查输出目录是否存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            print(f"创建输出目录: {output_dir}")
        except Exception as e:
            print(f"创建输出目录失败: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return False
    
    # 规范化输出路径
    output_file = output_file.replace('\\', '/')
    
    # 检查输出文件是否已存在
    if os.path.exists(output_file):
        print(f"警告: 输出文件已存在，将被覆盖: {output_file}")
    
    # 创建输出文件
    try:
        print("获取GDAL GTiff驱动...")
        driver = gdal.GetDriverByName("GTiff")
        if driver is None:
            print("错误: 无法获取GDAL GTiff驱动")
            gdal_error = gdal.GetLastErrorMsg() if hasattr(gdal, 'GetLastErrorMsg') else '未知错误'
            print(f"GDAL错误信息: {gdal_error}")
            return False
            
        # 安全创建
        try:
            print(f"创建输出文件: {output_file}, 尺寸: {width}x{height}x{bands}")
            creation_options = ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']
            print(f"使用GDAL创建选项: {creation_options}")
            
            output_dataset = driver.Create(
                output_file, 
                width, 
                height, 
                bands, 
                gdal.GDT_Float32,
                creation_options
            )
            
            if output_dataset is None:
                print("创建输出文件失败")
                gdal_error = gdal.GetLastErrorMsg() if hasattr(gdal, 'GetLastErrorMsg') else '未知错误'
                print(f"GDAL错误信息: {gdal_error}")
                
                # 检查目录写入权限
                try:
                    test_file = os.path.join(output_dir, "test_write_permission.txt")
                    with open(test_file, 'w') as f:
                        f.write("test")
                    os.remove(test_file)
                    print(f"目录 {output_dir} 有写入权限")
                except Exception as e:
                    print(f"目录 {output_dir} 可能没有写入权限: {str(e)}")
                
                return False
                
            # 设置地理信息
            print("设置地理信息...")
            output_dataset.SetGeoTransform(geotrans)
            output_dataset.SetProjection(proj)
            
            # 处理每个波段，使用进度条
            print("写入带有nodata值的影像...")
            for i in tqdm(range(bands), desc="处理波段"):
                try:
                    # 转换数据类型并应用掩码
                    band_data = data[i].astype(np.float32)
                    
                    # 检查并记录原始数据的统计信息
                    if np.isfinite(band_data).all():
                        print(f"波段 {i+1} 所有像素值有效")
                    else:
                        non_finite = np.logical_not(np.isfinite(band_data))
                        non_finite_count = np.sum(non_finite)
                        print(f"警告: 波段 {i+1} 有 {non_finite_count} 个无效像素值 (NaN或Inf)")
                    
                    valid_data = band_data[np.isfinite(band_data)]
                    if len(valid_data) > 0:
                        print(f"波段 {i+1} 统计信息: min={np.min(valid_data)}, max={np.max(valid_data)}, "
                              f"mean={np.mean(valid_data):.2f}, std={np.std(valid_data):.2f}")
                    
                    # 应用掩码
                    invalid_count = np.sum(mask == 0)
                    print(f"应用掩码: {invalid_count} 个像素将被设置为NoData值 {nodata_value}")
                    band_data[mask == 0] = nodata_value
                
                    # 写入数据
                    print(f"获取输出波段 {i+1}...")
                    output_band = output_dataset.GetRasterBand(i+1)
                    
                    # 分块写入数据以显示更精细的进度
                    chunk_size = 1000  # 每个块的行数
                    height, width = band_data.shape
                    total_chunks = (height + chunk_size - 1) // chunk_size
                    
                    print(f"开始分块写入波段 {i+1}, 总共 {total_chunks} 个数据块")
                    for chunk_idx in tqdm(range(total_chunks), desc=f"波段 {i+1}/{bands} 写入进度", leave=False):
                        start_y = chunk_idx * chunk_size
                        end_y = min(start_y + chunk_size, height)
                        
                        # 写入当前块
                        if end_y > start_y:
                            chunk_data = band_data[start_y:end_y, :]
                            output_band.WriteArray(chunk_data, 0, start_y)
                    
                    print(f"设置波段 {i+1} 的NoData值为 {nodata_value}")
                    output_band.SetNoDataValue(nodata_value)
                    output_band.FlushCache()
                    
                    print(f"波段 {i+1}/{bands} 处理完成")
                except Exception as e:
                    print(f"处理波段 {i+1} 时发生错误: {str(e)}")
                    import traceback
                    print(traceback.format_exc())
                    return False
            
            # 关闭数据集并释放资源
            print("刷新缓存并关闭输出文件...")
            output_dataset.FlushCache()
            output_dataset = None
            
            print(f"成功保存输出文件: {output_file}")
            return True
        except Exception as e:
            print(f"保存文件时发生错误: {str(e)}")
            import traceback
            print(traceback.format_exc())
            
            # 尝试获取更多信息
            if 'output_dataset' in locals() and output_dataset is not None:
                try:
                    output_dataset = None
                    print("尝试释放数据集资源")
                except:
                    pass
            
            # 检查输出文件是否创建
            if os.path.exists(output_file):
                print(f"输出文件已创建但可能不完整: {output_file}")
                file_size = os.path.getsize(output_file)
                print(f"文件大小: {file_size / (1024*1024):.2f} MB")
            
            return False
            
    except Exception as e:
        print(f"初始化GDAL驱动时发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False


def pixel_to_geo(x, y, geotrans):
    """
    将栅格影像的像素坐标转换为地理坐标(投影坐标)
    
    该函数使用GDAL地理变换参数将像素行列号转换为对应的地理坐标。
    转换时考虑了像素中心点的偏移，提高精度。
    
    参数:
        x (int/float): 像素的列号(x方向)
        y (int/float): 像素的行号(y方向)
        geotrans (tuple): GDAL地理变换参数，包含6个元素:
                          (左上角x坐标, x方向分辨率, 行旋转, 左上角y坐标, 列旋转, y方向分辨率)
    
    返回:
        tuple: 包含两个元素的元组 (geo_x, geo_y)，分别为转换后的x和y地理坐标
    """
    # 像素中心点的地理坐标
    geo_x = geotrans[0] + (x + 0.5) * geotrans[1] + (y + 0.5) * geotrans[2]
    geo_y = geotrans[3] + (x + 0.5) * geotrans[4] + (y + 0.5) * geotrans[5]
    return geo_x, geo_y


def create_shapefile_from_mask(mask, geotrans, proj, output_shp, simplify_tolerance=0.1):
    """
    从掩码数组创建shapefile文件，提取有效数据区域的边界多边形
    
    该函数将掩码中值为1的区域(有效区域)提取为多边形，并保存为ESRI Shapefile格式。
    流程包括:
    1. 找出掩码中有效区域的轮廓
    2. 将像素坐标转换为地理坐标
    3. 生成多边形几何体
    4. 合并并简化多边形
    5. 保存为shapefile文件
    
    参数:
        mask (numpy.ndarray): 掩码数组，形状为(height, width)，值为1的区域是有效区域
        geotrans (tuple): GDAL地理变换参数，用于坐标转换
        proj (str): 投影定义(WKT格式)
        output_shp (str): 输出shapefile文件路径
        simplify_tolerance (float, 可选): 多边形简化的容差参数，值越大简化程度越高，默认为0.1
        
    返回:
        bool: 操作成功返回True，失败返回False
    """
    # 确保输出路径使用正斜杠，解决Windows路径问题        
    output_shp = output_shp.replace('\\', '/')
    
    # 检查输出目录是否存在
    output_dir = os.path.dirname(output_shp)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            print(f"创建输出目录: {output_dir}")
        except Exception as e:
            print(f"创建输出目录失败: {str(e)}")
            return False
            
    if not mask.any():
        print("警告: 掩码全为0，无有效区域可提取")
        return False
    
    # 由于WinError 87通常与文件路径或API调用有关，这里添加更严格的检查
    try:
        shapefile_ext = os.path.splitext(output_shp)[1].lower()
        if shapefile_ext != '.shp':
            print(f"警告: 输出文件扩展名不是.shp: {shapefile_ext}")
            # 添加.shp扩展名
            if not output_shp.lower().endswith('.shp'):
                output_shp = output_shp + '.shp'
                print(f"已修正输出路径: {output_shp}")
        
        # 预处理掩码 - 使用形态学操作去除噪点并连接相近区域
        print("预处理掩码以提高轮廓查找质量...")
        kernel = np.ones((3, 3), np.uint8)
        mask_processed = mask.copy()
        try:
            mask_processed = cv2.morphologyEx(mask_processed, cv2.MORPH_CLOSE, kernel)
            mask_processed = cv2.morphologyEx(mask_processed, cv2.MORPH_OPEN, kernel)
        except Exception as e:
            print(f"掩码预处理失败，使用原始掩码: {str(e)}")
        
        # 找到轮廓，使用多种方法尝试
        print("寻找有效区域轮廓...")
        contours = find_contours(mask_processed, 0.5)
        
        # 检查是否找到轮廓，如果没找到，尝试其他阈值
        if not contours:
            print("使用默认阈值未找到轮廓，尝试其他阈值...")
            contours = find_contours(mask_processed, 0.1)
        
        # 如果仍然没找到，使用OpenCV的轮廓查找算法
        if not contours:
            print("使用skimage未找到轮廓，切换到OpenCV方法...")
            # 确保掩码是8位无符号整数类型
            mask_cv = mask_processed.astype(np.uint8)
            
            try:
                if cv2.__version__[0] >= '4':
                    cv_contours, _ = cv2.findContours(mask_cv, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                else:
                    _, cv_contours, _ = cv2.findContours(mask_cv, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                if cv_contours and len(cv_contours) > 0:
                    # 将OpenCV轮廓转换为skimage轮廓格式
                    contours = []
                    for cv_cont in cv_contours:
                        if len(cv_cont) > 2:  # 确保有足够的点
                            # 转换为skimage轮廓格式 [[y,x], [y,x], ...]
                            contour = np.array([[p[0][1], p[0][0]] for p in cv_cont])
                            contours.append(contour)
                    print(f"使用OpenCV找到 {len(contours)} 个轮廓")
            except Exception as e:
                print(f"OpenCV轮廓查找失败: {str(e)}")
                # 如果OpenCV也失败，尝试反转掩码
                try:
                    print("尝试反转掩码并重新查找轮廓...")
                    inverted_mask = (1 - mask_processed).astype(np.uint8)
                    contours = find_contours(inverted_mask, 0.5)
                    if contours:
                        print(f"使用反转掩码找到 {len(contours)} 个轮廓")
                except Exception as e2:
                    print(f"反转掩码后轮廓查找也失败: {str(e2)}")
        
        if not contours:
            print("所有轮廓查找方法都失败，无法生成shapefile")
            return False
        
        print(f"找到 {len(contours)} 个轮廓，转换为地理坐标...")
        
        # 将轮廓转换为地理坐标
        polygons = []
        valid_points_count = 0
        
        for contour in tqdm(contours, desc="处理轮廓"):
            # 轮廓点转为地理坐标
            geo_points = [pixel_to_geo(point[1], point[0], geotrans) for point in contour]
            valid_points_count += len(geo_points)
            
            # 创建多边形
            if len(geo_points) > 2:  # 至少需要3个点来形成多边形
                try:
                    poly = geometry.Polygon(geo_points)
                    if poly.is_valid:
                        polygons.append(poly)
                    else:
                        # 尝试修复无效多边形
                        poly = poly.buffer(0)
                        if poly.is_valid:
                            polygons.append(poly)
                except Exception as e:
                    print(f"创建多边形失败: {str(e)}")
        
        print(f"处理了 {valid_points_count} 个坐标点")
        
        # 合并所有多边形
        if not polygons:
            print("未找到有效多边形")
            return False
            
        # 合并并简化多边形
        print(f"合并并简化 {len(polygons)} 个多边形...")
        try:
            if len(polygons) > 1:
                merged_polygon = unary_union(polygons)
            else:
                merged_polygon = polygons[0]
                
            if simplify_tolerance > 0:
                merged_polygon = merged_polygon.simplify(simplify_tolerance)
                
            print(f"多边形简化完成，类型: {type(merged_polygon)}")
        except Exception as e:
            print(f"多边形处理失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        # 尝试先删除旧文件
        try:
            if os.path.exists(output_shp):
                # 使用os删除文件
                try:
                    os.unlink(output_shp)
                    print(f"已删除现有文件: {output_shp}")
                except Exception as e:
                    print(f"无法删除现有文件: {str(e)}")
                    
                # 删除相关文件 (.dbf, .prj, .shx)
                base_path = os.path.splitext(output_shp)[0]
                for ext in ['.dbf', '.prj', '.shx']:
                    aux_file = base_path + ext
                    if os.path.exists(aux_file):
                        try:
                            os.unlink(aux_file)
                            print(f"已删除辅助文件: {aux_file}")
                        except:
                            pass
        except Exception as e:
            print(f"清理旧文件时出错: {str(e)}")
        
        # 使用ogr创建shapefile
        print(f"创建新shapefile: {output_shp}")
        try:
            # 创建临时目录以确保路径有效
            temp_dir = os.path.dirname(output_shp)
            if temp_dir and not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
                
            # 创建shapefile
            driver = ogr.GetDriverByName('ESRI Shapefile')
            if driver is None:
                print("无法获取ESRI Shapefile驱动")
                return False
                
            # 使用CreateDataSource创建数据源
            data_source = driver.CreateDataSource(output_shp)
            if data_source is None:
                print(f"创建shapefile数据源失败: {output_shp}")
                return False
            
            # 设置空间参考
            srs = osr.SpatialReference()
            try:
                # 尝试从WKT导入
                result = srs.ImportFromWkt(proj)
                if result != 0:
                    print(f"警告: 无法从WKT导入空间参考，错误码: {result}")
                    # 尝试使用WGS84作为默认
                    srs.ImportFromEPSG(4326)
                    print("使用WGS84 (EPSG:4326) 作为默认空间参考")
            except Exception as e:
                print(f"设置空间参考时出错: {str(e)}")
                srs.ImportFromEPSG(4326)
                print("使用WGS84 (EPSG:4326) 作为默认空间参考")
                
            # 创建图层
            layer = data_source.CreateLayer('boundary', srs, ogr.wkbPolygon)
            if layer is None:
                print("创建图层失败")
                return False
            
            # 添加字段
            field_name = ogr.FieldDefn('ID', ogr.OFTInteger)
            layer.CreateField(field_name)
            field_area = ogr.FieldDefn('AREA', ogr.OFTReal)
            layer.CreateField(field_area)
            field_perim = ogr.FieldDefn('PERIMETER', ogr.OFTReal)
            layer.CreateField(field_perim)
            
            # 添加特征
            print("写入shapefile特征...")
            
            # 处理MultiPolygon和Polygon两种情况
            feature_id = 1
            polygons_to_process = []
            
            if isinstance(merged_polygon, geometry.MultiPolygon):
                polygons_to_process = list(merged_polygon.geoms)
                print(f"处理MultiPolygon，包含 {len(polygons_to_process)} 个多边形")
            else:
                polygons_to_process = [merged_polygon]
                print("处理单个Polygon")
            
            for single_poly in tqdm(polygons_to_process, desc="写入多边形"):
                try:
                    # 计算面积和周长
                    area = single_poly.area
                    perimeter = single_poly.length
                    
                    # 获取WKT
                    wkt = single_poly.wkt
                    
                    # 创建OGR几何体
                    ogr_geom = ogr.CreateGeometryFromWkt(wkt)
                    if ogr_geom is None:
                        print(f"无法从WKT创建几何体: {wkt[:100]}...")
                        continue
                    
                    # 创建要素
                    feature_defn = layer.GetLayerDefn()
                    feature = ogr.Feature(feature_defn)
                    feature.SetField('ID', feature_id)
                    feature.SetField('AREA', float(area))
                    feature.SetField('PERIMETER', float(perimeter))
                    
                    # 设置几何体
                    feature.SetGeometry(ogr_geom)
                    
                    # 将要素添加到图层
                    if layer.CreateFeature(feature) != 0:
                        print(f"添加要素 {feature_id} 失败")
                    
                    # 递增ID
                    feature_id += 1
                    
                    # 清理资源
                    feature = None
                    ogr_geom = None
                except Exception as e:
                    print(f"处理多边形 {feature_id} 时出错: {str(e)}")
            
            # 同步到磁盘
            data_source.SyncToDisk()
            
            # 清理资源
            data_source = None
            
            # 验证文件是否创建成功
            if os.path.exists(output_shp) and os.path.getsize(output_shp) > 0:
                print(f"Shapefile创建成功: {output_shp}")
                return True
            else:
                print(f"Shapefile创建失败，文件不存在或大小为0: {output_shp}")
                return False
                
        except Exception as e:
            print(f"创建shapefile时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"处理轮廓时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def process_chunk(chunk_info, rgb_data, edge_width, black_threshold, white_threshold, use_gpu=False, gpu_id=0):
    """
    并行处理图像数据的一个块，返回该块的掩码
    
    该函数用于处理大型影像时的分块并行处理，支持GPU加速。
    函数从完整影像中提取指定范围的块，并根据参数检测边缘区域的无效像素。
    
    参数:
        chunk_info (tuple): 包含两个整数的元组 (start_y, end_y)，表示要处理的行范围
        rgb_data (numpy.ndarray): 完整的RGB影像数据，形状为(3, height, width)
        edge_width (int): 边缘检测宽度(像素)
        black_threshold (int): 黑色判定阈值，RGB值均小于等于此值的像素视为黑色
        white_threshold (int): 白色判定阈值，RGB值均大于等于此值的像素视为白色
        use_gpu (bool, 可选): 是否使用GPU加速，默认为False
        gpu_id (int, 可选): 使用的GPU设备ID，仅在use_gpu=True时有效，默认为0
        
    返回:
        tuple: 包含三个元素的元组 (start_y, end_y, mask_chunk)
               - start_y, end_y: 处理块的行范围
               - mask_chunk: 生成的掩码块，形状为(end_y-start_y, width)，
                            值为1的区域是有效区域，值为0的区域是无效区域
    """
    start_y, end_y = chunk_info
    
    # 提取当前块的数据
    try:
        chunk_data = rgb_data[:, start_y:end_y, :]
    except Exception as e:
        print(f"提取块数据出错: {str(e)}，可能是维度不匹配")
        # 返回一个全1的默认掩码
        if len(rgb_data.shape) == 3:
            height = end_y - start_y
            width = rgb_data.shape[2]
            return (start_y, end_y, np.ones((height, width), dtype=np.uint8))
        else:
            print("无法确定块的正确维度，处理失败")
            raise
    
    if use_gpu and HAS_CUPY:
        # GPU处理
        try:
            # 选择GPU设备
            with cp.cuda.Device(gpu_id):
                chunk_data_gpu = cp.asarray(chunk_data)
                height, width = chunk_data.shape[1], chunk_data.shape[2]
                
                # 创建掩码
                mask_gpu = cp.ones((height, width), dtype=cp.uint8)
                
                # 创建边缘区域掩码
                edge_width_y = min(edge_width, height)
                
                # 仅处理块中的边缘区域
                if start_y < edge_width or end_y > rgb_data.shape[1] - edge_width:
                    # 上边界（仅对于第一个块）
                    if start_y < edge_width:
                        edge_y_top = edge_width - start_y
                        
                        # 检查黑色像素
                        if black_threshold == 0:
                            black_pixels = (chunk_data_gpu[0, :edge_y_top, :] == 0) & (chunk_data_gpu[1, :edge_y_top, :] == 0) & (chunk_data_gpu[2, :edge_y_top, :] == 0)
                        else:
                            black_pixels = (chunk_data_gpu[0, :edge_y_top, :] <= black_threshold) & (chunk_data_gpu[1, :edge_y_top, :] <= black_threshold) & (chunk_data_gpu[2, :edge_y_top, :] <= black_threshold)
                        
                        # 检查白色像素
                        if white_threshold == 255:
                            white_pixels = (chunk_data_gpu[0, :edge_y_top, :] == 255) & (chunk_data_gpu[1, :edge_y_top, :] == 255) & (chunk_data_gpu[2, :edge_y_top, :] == 255)
                        else:
                            white_pixels = (chunk_data_gpu[0, :edge_y_top, :] >= white_threshold) & (chunk_data_gpu[1, :edge_y_top, :] >= white_threshold) & (chunk_data_gpu[2, :edge_y_top, :] >= white_threshold)
                        
                        # 合并掩码
                        invalid_pixels = black_pixels | white_pixels
                        mask_gpu[:edge_y_top, :][invalid_pixels] = 0
                    
                    # 下边界（仅对于最后一个块）
                    if end_y > rgb_data.shape[1] - edge_width:
                        edge_y_bottom = edge_width - (rgb_data.shape[1] - end_y)
                        if edge_y_bottom > 0:
                            # 检查黑色像素
                            if black_threshold == 0:
                                black_pixels = (chunk_data_gpu[0, -edge_y_bottom:, :] == 0) & (chunk_data_gpu[1, -edge_y_bottom:, :] == 0) & (chunk_data_gpu[2, -edge_y_bottom:, :] == 0)
                            else:
                                black_pixels = (chunk_data_gpu[0, -edge_y_bottom:, :] <= black_threshold) & (chunk_data_gpu[1, -edge_y_bottom:, :] <= black_threshold) & (chunk_data_gpu[2, -edge_y_bottom:, :] <= black_threshold)
                            
                            # 检查白色像素
                            if white_threshold == 255:
                                white_pixels = (chunk_data_gpu[0, -edge_y_bottom:, :] == 255) & (chunk_data_gpu[1, -edge_y_bottom:, :] == 255) & (chunk_data_gpu[2, -edge_y_bottom:, :] == 255)
                            else:
                                white_pixels = (chunk_data_gpu[0, -edge_y_bottom:, :] >= white_threshold) & (chunk_data_gpu[1, -edge_y_bottom:, :] >= white_threshold) & (chunk_data_gpu[2, -edge_y_bottom:, :] >= white_threshold)
                            
                            # 合并掩码
                            invalid_pixels = black_pixels | white_pixels
                            mask_gpu[-edge_y_bottom:, :][invalid_pixels] = 0
                    
                    # 左边界和右边界（对所有块）
                    # 左边界
                    if black_threshold == 0:
                        black_pixels = (chunk_data_gpu[0, :, :edge_width] == 0) & (chunk_data_gpu[1, :, :edge_width] == 0) & (chunk_data_gpu[2, :, :edge_width] == 0)
                    else:
                        black_pixels = (chunk_data_gpu[0, :, :edge_width] <= black_threshold) & (chunk_data_gpu[1, :, :edge_width] <= black_threshold) & (chunk_data_gpu[2, :, :edge_width] <= black_threshold)
                    
                    if white_threshold == 255:
                        white_pixels = (chunk_data_gpu[0, :, :edge_width] == 255) & (chunk_data_gpu[1, :, :edge_width] == 255) & (chunk_data_gpu[2, :, :edge_width] == 255)
                    else:
                        white_pixels = (chunk_data_gpu[0, :, :edge_width] >= white_threshold) & (chunk_data_gpu[1, :, :edge_width] >= white_threshold) & (chunk_data_gpu[2, :, :edge_width] >= white_threshold)
                    
                    invalid_pixels = black_pixels | white_pixels
                    mask_gpu[:, :edge_width][invalid_pixels] = 0
                    
                    # 右边界
                    if black_threshold == 0:
                        black_pixels = (chunk_data_gpu[0, :, -edge_width:] == 0) & (chunk_data_gpu[1, :, -edge_width:] == 0) & (chunk_data_gpu[2, :, -edge_width:] == 0)
                    else:
                        black_pixels = (chunk_data_gpu[0, :, -edge_width:] <= black_threshold) & (chunk_data_gpu[1, :, -edge_width:] <= black_threshold) & (chunk_data_gpu[2, :, -edge_width:] <= black_threshold)
                    
                    if white_threshold == 255:
                        white_pixels = (chunk_data_gpu[0, :, -edge_width:] == 255) & (chunk_data_gpu[1, :, -edge_width:] == 255) & (chunk_data_gpu[2, :, -edge_width:] == 255)
                    else:
                        white_pixels = (chunk_data_gpu[0, :, -edge_width:] >= white_threshold) & (chunk_data_gpu[1, :, -edge_width:] >= white_threshold) & (chunk_data_gpu[2, :, -edge_width:] >= white_threshold)
                    
                    invalid_pixels = black_pixels | white_pixels
                    mask_gpu[:, -edge_width:][invalid_pixels] = 0
                
                # 将结果从GPU转回CPU
                mask_chunk = cp.asnumpy(mask_gpu)
                
        except Exception as e:
            print(f"GPU {gpu_id} 处理错误: {str(e)}，回退到CPU处理")
            # 如果GPU处理失败，回退到CPU处理
            mask_chunk = process_chunk_cpu(chunk_data, edge_width, black_threshold, white_threshold, start_y, rgb_data.shape[1])
    else:
        # CPU处理
        mask_chunk = process_chunk_cpu(chunk_data, edge_width, black_threshold, white_threshold, start_y, rgb_data.shape[1])
    
    return (start_y, end_y, mask_chunk)


def process_chunk_cpu(chunk_data, edge_width, black_threshold, white_threshold, start_y, total_height):
    """
    使用CPU处理图像块数据，识别边缘区域的无效像素
    
    该函数是process_chunk的CPU实现部分，用于在没有GPU或GPU处理失败时使用。
    函数会在图像块的边缘区域内检测黑色或白色像素，标记为无效区域。
    
    参数:
        chunk_data (numpy.ndarray): 要处理的图像块数据，形状为(3, height, width)
        edge_width (int): 边缘检测宽度(像素)
        black_threshold (int): 黑色判定阈值，RGB值均小于等于此值的像素视为黑色
        white_threshold (int): 白色判定阈值，RGB值均大于等于此值的像素视为白色
        start_y (int): 该块在原始图像中的起始行位置
        total_height (int): 原始图像的总行数
        
    返回:
        numpy.ndarray: 生成的掩码块，形状为(height, width)
                      值为1的区域是有效区域，值为0的区域是无效区域
    """
    height, width = chunk_data.shape[1], chunk_data.shape[2]
    mask_chunk = np.ones((height, width), dtype=np.uint8)
    
    # 创建边缘区域掩码
    edge_mask = np.zeros((height, width), dtype=bool)
    
    # 仅处理块中的边缘区域
    if start_y < edge_width:
        edge_mask[:edge_width-start_y, :] = True  # 上边缘
    
    if start_y + height > total_height - edge_width:
        bottom_edge = min(height, edge_width - (total_height - (start_y + height)))
        if bottom_edge > 0:
            edge_mask[-bottom_edge:, :] = True  # 下边缘
    
    # 所有块都处理左右边缘
    edge_mask[:, :edge_width] = True  # 左边缘
    edge_mask[:, -edge_width:] = True  # 右边缘
    
    # 仅在边缘区域内检查黑色和白色像素
    if edge_mask.any():  # 只有当边缘掩码有True值时才进行处理
        # 检查黑色像素
        if black_threshold == 0:
            black_pixels = (chunk_data[0] == 0) & (chunk_data[1] == 0) & (chunk_data[2] == 0) & edge_mask
        else:
            black_pixels = (chunk_data[0] <= black_threshold) & (chunk_data[1] <= black_threshold) & (chunk_data[2] <= black_threshold) & edge_mask
        
        # 检查白色像素
        if white_threshold == 255:
            white_pixels = (chunk_data[0] == 255) & (chunk_data[1] == 255) & (chunk_data[2] == 255) & edge_mask
        else:
            white_pixels = (chunk_data[0] >= white_threshold) & (chunk_data[1] >= white_threshold) & (chunk_data[2] >= white_threshold) & edge_mask
        
        # 合并掩码
        invalid_pixels = black_pixels | white_pixels
        mask_chunk[invalid_pixels] = 0
        
    return mask_chunk


def create_hybrid_mask_parallel(rgb_data, edge_width=100, black_threshold=0, white_threshold=255, 
                              num_processes=None, use_gpu=False, num_gpus=0, chunk_size=1000):
    """
    使用混合处理模式(多GPU+多CPU线程)创建掩码，检测边缘区域的无效像素
    
    该函数采用分块并行处理的策略，可以同时利用CPU和GPU资源处理大型影像。
    主要用于"edge"模式，只检测图像边缘区域的黑色或白色无效像素。
    
    实现细节:
    1. 将影像分成多个块(chunks)
    2. 根据可用硬件资源分配任务到GPU或CPU
    3. 并行处理各个块，检测边缘区域的无效像素
    4. 合并处理结果，生成完整掩码
    
    参数:
        rgb_data (numpy.ndarray): 输入的RGB图像数据，形状为(3, height, width)
        edge_width (int, 可选): 检查边缘的宽度(像素)，默认为100
        black_threshold (int, 可选): 黑色判定阈值，RGB值均小于等于此值的像素视为黑色，默认为0
        white_threshold (int, 可选): 白色判定阈值，RGB值均大于等于此值的像素视为白色，默认为255
        num_processes (int, 可选): CPU线程数，None表示自动选择
        use_gpu (bool, 可选): 是否使用GPU加速，默认为False
        num_gpus (int, 可选): 使用的GPU数量，0表示使用所有可用的GPU
        chunk_size (int, 可选): 分块处理的块大小(像素)，默认为1000
        
    返回:
        numpy.ndarray: 掩码数组，形状为(height, width)，有效数据区域为1，无效区域为0
    """
    # 检查rgb_data的形状
    if len(rgb_data.shape) != 3:
        print(f"警告: 输入数据形状不符合预期 RGB 格式，当前形状: {rgb_data.shape}")
        # 如果是单波段数据
        if len(rgb_data.shape) == 2:
            height, width = rgb_data.shape
            # 创建一个简单的边缘掩码
            mask = np.ones((height, width), dtype=np.uint8)
            
            # 标记边缘区域
            edge_mask = np.zeros((height, width), dtype=bool)
            edge_mask[:edge_width, :] = True  # 上边缘
            edge_mask[-edge_width:, :] = True  # 下边缘
            edge_mask[:, :edge_width] = True  # 左边缘
            edge_mask[:, -edge_width:] = True  # 右边缘
            
            # 检测黑色区域
            black_pixels = (rgb_data <= black_threshold) & edge_mask
            # 检测白色区域
            white_pixels = (rgb_data >= white_threshold) & edge_mask
            # 合并掩码
            invalid_pixels = black_pixels | white_pixels
            mask[invalid_pixels] = 0
            
            return mask
        else:
            raise ValueError(f"不支持的数据维度: {rgb_data.shape}")

    height, width = rgb_data.shape[1], rgb_data.shape[2]
    
    # 确定可用的处理资源
    available_gpus = 0
    if use_gpu:
        if HAS_CUPY:
            available_gpus = min(NUM_GPUS, num_gpus) if num_gpus > 0 else NUM_GPUS
            if available_gpus == 0:
                print("警告: 请求使用GPU但没有可用GPU，将使用CPU处理")
        else:
            print("警告: 请求使用GPU但未安装CuPy库，将使用CPU处理")
    
    # 确定CPU处理器数量
    if num_processes is None:
        # 如果使用GPU，则给GPU线程预留一些核心
        if available_gpus > 0:
            num_processes = max(1, multiprocessing.cpu_count() - available_gpus)
        else:
            num_processes = max(1, multiprocessing.cpu_count() - 1)
    
    # 将数据分成多个块
    chunks = []
    for start_y in range(0, height, chunk_size):
        end_y = min(start_y + chunk_size, height)
        chunks.append((start_y, end_y))
    
    total_chunks = len(chunks)
    
    # 解决Windows上的多进程问题：使用替代方法
    # 在Windows上，大数据无法通过管道传递给子进程，导致WinError 87
    try:
        # 创建结果掩码并初始化
        mask = np.ones((height, width), dtype=np.uint8)
        
        # 计算总块数
        total_chunks = len(chunks)
        print(f"将处理 {total_chunks} 个数据块...")
        
        # 如果没有可用GPU或不使用GPU，就只使用CPU
        if available_gpus == 0:
            print(f"使用多线程CPU处理 ({num_processes} 线程)...")
            
            # 避免Windows上的多进程问题，使用替代方案
            if os.name == 'nt':  # Windows系统
                print("检测到Windows系统，使用替代并行方法...")
                
                try:
                    # 尝试导入线程池以代替进程池
                    from concurrent.futures import ThreadPoolExecutor
                    print("使用ThreadPoolExecutor...")
                    
                    with ThreadPoolExecutor(max_workers=num_processes) as executor:
                        futures = []
                        for chunk in chunks:
                            future = executor.submit(
                                process_chunk,
                                chunk,
                                rgb_data, 
                                edge_width,
                                black_threshold, 
                                white_threshold,
                                False,
                                0
                            )
                            futures.append(future)
                        
                        # 使用tqdm显示进度
                        results = []
                        for i, future in enumerate(tqdm(futures, desc="处理数据块")):
                            result = future.result()
                            results.append(result)
                            # 显示更详细的进度信息
                            if (i+1) % 10 == 0 or i == len(futures) - 1:
                                completed = i + 1
                                total = len(futures)
                                percent = (completed / total) * 100
                                print(f"已完成: {completed}/{total} 块 ({percent:.1f}%)")
                
                except Exception as e:
                    print(f"线程池处理失败: {str(e)}")
                    print("回退到顺序处理...")
                    
                    # 如果线程池也失败，回退到顺序处理
                    results = []
                    for chunk in tqdm(chunks, desc="顺序处理数据块"):
                        result = process_chunk(
                            chunk,
                            rgb_data, 
                            edge_width,
                            black_threshold, 
                            white_threshold,
                            False,
                            0
                        )
                        results.append(result)
            
            else:  # 非Windows系统，使用原有多进程
                # 使用多进程池处理各个块
                with multiprocessing.Pool(processes=num_processes) as pool:
                    process_chunk_partial = partial(
                        process_chunk, 
                        rgb_data=rgb_data, 
                        edge_width=edge_width,
                        black_threshold=black_threshold, 
                        white_threshold=white_threshold,
                        use_gpu=False
                    )
                    
                    # 使用tqdm显示进度
                    print(f"使用 {num_processes} 个CPU线程处理 {total_chunks} 个数据块...")
                    try:
                        results = list(tqdm(
                            pool.imap(process_chunk_partial, chunks),
                            total=total_chunks,
                            desc="处理数据块"
                        ))
                    except Exception as e:
                        print(f"多进程处理出错: {str(e)}")
                        import traceback
                        traceback.print_exc()
                        # 回退到单进程处理
                        print("回退到单进程处理")
                        results = []
                        for chunk in tqdm(chunks, desc="单进程处理块"):
                            results.append(process_chunk_partial(chunk))
        
        else:  # GPU处理
            # GPU处理在Windows上同样有多进程问题，使用不同策略
            print(f"在 {available_gpus} 个GPU和CPU上处理数据块...")
            
            if os.name == 'nt':  # Windows系统
                print("检测到Windows系统，GPU处理使用顺序执行...")
                
                # 为不同GPU分配块
                gpu_assignments = []
                for i in range(total_chunks):
                    gpu_id = i % (available_gpus + 1)
                    use_gpu_for_chunk = gpu_id < available_gpus
                    if use_gpu_for_chunk:
                        gpu_assignments.append((i, True, gpu_id))
                    else:
                        gpu_assignments.append((i, False, 0))
                
                # 顺序处理所有块
                results = []
                for i, use_gpu_for_chunk, gpu_id in tqdm(gpu_assignments, desc="处理数据块"):
                    if i < len(chunks):
                        chunk = chunks[i]
                        result = process_chunk(
                            chunk,
                            rgb_data,
                            edge_width,
                            black_threshold,
                            white_threshold,
                            use_gpu_for_chunk,
                            gpu_id
                        )
                        results.append(result)
            
            else:  # 非Windows系统，使用原有多进程方法
                # 创建进程池
                pool = multiprocessing.Pool(processes=num_processes + available_gpus)
                
                # 分配任务给GPU和CPU
                results = []
                
                # 计算每个GPU设备处理的块数
                gpu_chunks_per_device = total_chunks // (available_gpus + 1)
                
                # GPU处理的块
                gpu_tasks_count = 0
                
                for gpu_id in range(available_gpus):
                    # 计算当前GPU应处理的块范围
                    start_idx = gpu_id * gpu_chunks_per_device
                    end_idx = (gpu_id + 1) * gpu_chunks_per_device
                    
                    # 为当前GPU分配任务
                    for chunk_idx in range(start_idx, end_idx):
                        if chunk_idx < total_chunks:
                            chunk = chunks[chunk_idx]
                            result = pool.apply_async(
                                process_chunk, 
                                args=(chunk, rgb_data, edge_width, black_threshold, white_threshold, True, gpu_id % available_gpus)
                            )
                            results.append(result)
                            gpu_tasks_count += 1
                
                # CPU处理剩余的块
                cpu_tasks_count = total_chunks - gpu_tasks_count
                for chunk_idx in range(gpu_tasks_count, total_chunks):
                    if chunk_idx < total_chunks:
                        chunk = chunks[chunk_idx]
                        result = pool.apply_async(
                            process_chunk, 
                            args=(chunk, rgb_data, edge_width, black_threshold, white_threshold, False, 0)
                        )
                        results.append(result)
                
                # 关闭进程池并等待所有任务完成
                pool.close()
                
                # 使用tqdm显示进度
                print(f"处理 {total_chunks} 个数据块（GPU: {gpu_tasks_count}, CPU: {cpu_tasks_count}）...")
                results_with_progress = []
                try:
                    for r in tqdm(results, total=len(results), desc="等待任务完成"):
                        results_with_progress.append(r.get())
                except Exception as e:
                    print(f"混合模式处理出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    # 尝试收集已完成的结果
                    print("尝试收集已完成的结果...")
                    results_with_progress = []
                    for i, r in enumerate(results):
                        try:
                            if r.ready():
                                results_with_progress.append(r.get(timeout=0.1))
                            else:
                                print(f"任务 {i} 未完成，跳过")
                        except Exception as e2:
                            print(f"获取结果 {i} 失败: {str(e2)}")
                
                results = results_with_progress
                pool.join()
        
        if not results:
            print("警告: 没有生成任何处理结果，返回默认掩码")
            # 返回一个全1的默认掩码
            return np.ones((height, width), dtype=np.uint8)
        
        # 创建最终掩码并整合结果
        print("合并处理结果...")
        try:
            for i, (start_y, end_y, mask_chunk) in enumerate(tqdm(results, desc="合并结果")):
                if mask_chunk.shape[0] == end_y - start_y and mask_chunk.shape[1] == width:
                    mask[start_y:end_y, :] = mask_chunk
                else:
                    print(f"警告: 块形状不匹配 - 预期 {end_y-start_y}x{width}, 实际 {mask_chunk.shape}")
                    # 尝试调整大小
                    if mask_chunk.size > 0:
                        resized_chunk = cv2.resize(mask_chunk, (width, end_y-start_y), interpolation=cv2.INTER_NEAREST)
                        mask[start_y:end_y, :] = resized_chunk
                    
                # 显示更详细的进度信息
                if (i+1) % 20 == 0 or i == len(results) - 1:
                    completed = i + 1
                    total = len(results)
                    percent = (completed / total) * 100
                    print(f"合并进度: {completed}/{total} 块 ({percent:.1f}%)")
        except Exception as e:
            print(f"合并结果时出错: {str(e)}")
            import traceback
            traceback.print_exc()
        
        return mask
        
    except Exception as e:
        print(f"掩码创建过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # 返回默认掩码
        print("返回默认掩码(所有数据有效)")
        return np.ones((height, width), dtype=np.uint8)


def estimate_memory_usage(width, height, bands, chunk_size=1000):
    """
    估计处理栅格影像所需的内存用量
    
    该函数计算处理特定尺寸影像所需的内存量，包括原始数据、掩码和输出数据。
    结果以易读的形式返回(KB, MB或GB)。
    
    参数:
        width (int): 影像宽度(像素)
        height (int): 影像高度(像素)
        bands (int): 波段数
        chunk_size (int, 可选): 分块处理的块大小，默认为1000
        
    返回:
        tuple: 包含两个字符串的元组 (total_memory_str, chunk_memory_str)
               - total_memory_str: 处理整个影像所需的内存，如"1.5 GB"
               - chunk_memory_str: 处理一个数据块所需的内存，如"10.5 MB"
    """
    # 单个像素占用内存 (3种数据类型: 原始数据uint8, 掩码uint8, 输出float32)
    pixel_size_bytes = bands + 1 + bands * 4
    
    # 总像素数
    total_pixels = width * height
    
    # 总内存估计 (字节)
    total_memory_bytes = total_pixels * pixel_size_bytes
    
    # 转换为更易读的单位
    if total_memory_bytes < 1024**2:
        memory_str = f"{total_memory_bytes / 1024:.2f} KB"
    elif total_memory_bytes < 1024**3:
        memory_str = f"{total_memory_bytes / (1024**2):.2f} MB"
    else:
        memory_str = f"{total_memory_bytes / (1024**3):.2f} GB"
    
    # 估计块处理内存
    chunk_memory_bytes = width * chunk_size * pixel_size_bytes
    if chunk_memory_bytes < 1024**2:
        chunk_memory_str = f"{chunk_memory_bytes / 1024:.2f} KB"
    elif chunk_memory_bytes < 1024**3:
        chunk_memory_str = f"{chunk_memory_bytes / (1024**2):.2f} MB"
    else:
        chunk_memory_str = f"{chunk_memory_bytes / (1024**3):.2f} GB"
    
    return memory_str, chunk_memory_str


def create_full_image_mask(rgb_data, invalid_threshold_low=0, invalid_threshold_high=255, protect_interior=False, tolerance=2.0):
    """
    创建掩码，检测整个影像中的无效像素
    参数:
        rgb_data: 影像数据
        invalid_threshold_low: 判断为无效数据的低阈值(默认0)
        invalid_threshold_high: 判断为无效数据的高阈值(默认255)
        protect_interior: 是否保护有效区域内部的像素(不标记为无效)
        tolerance: 轮廓简化参数
    返回：
        掩码数组，有效数据为1，无效数据为0
    """
    print("创建全影像掩码，寻找无效区域...")
    
    # 检查输入数据的形状
    if len(rgb_data.shape) == 2:  # 单波段
        height, width = rgb_data.shape
        # 创建初始全1掩码（所有像素默认都是有效的）
        mask = np.ones((height, width), dtype=np.uint8)
        
        # 检测无效像素 - 对于单波段，检测值为阈值的像素
        invalid_pixels_low = (rgb_data <= invalid_threshold_low)
        invalid_pixels_high = (rgb_data >= invalid_threshold_high)
        invalid_pixels = invalid_pixels_low | invalid_pixels_high
        
        if protect_interior:
            # 保护内部区域 - 先找到大致的有效区域轮廓
            # 创建一个临时掩码，只标记边缘的无效像素
            edge_width = min(100, min(height, width) // 10)  # 自适应边缘宽度
            edge_mask = np.zeros((height, width), dtype=bool)
            edge_mask[:edge_width, :] = True  # 上边缘
            edge_mask[-edge_width:, :] = True  # 下边缘
            edge_mask[:, :edge_width] = True  # 左边缘
            edge_mask[:, -edge_width:] = True  # 右边缘
            
            # 先检测边缘的无效区域
            edge_invalid = invalid_pixels & edge_mask
            temp_mask = np.ones((height, width), dtype=np.uint8)
            temp_mask[edge_invalid] = 0
            
            # 使用形态学操作来清理掩码
            if cv2.__version__[0] >= '3':
                # OpenCV 3.x 或更高版本
                kernel = np.ones((3, 3), np.uint8)
                temp_mask = cv2.morphologyEx(temp_mask, cv2.MORPH_OPEN, kernel)
                temp_mask = cv2.morphologyEx(temp_mask, cv2.MORPH_CLOSE, kernel)
            
            # 找到初步的有效区域轮廓
            if cv2.__version__[0] >= '4':
                # OpenCV 4.x
                contours, _ = cv2.findContours(temp_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            else:
                # OpenCV 3.x 或更早
                _, contours, _ = cv2.findContours(temp_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 创建新的掩码，只将轮廓外的区域标记为无效
            protector_mask = np.zeros((height, width), dtype=np.uint8)
            cv2.drawContours(protector_mask, contours, -1, 1, -1)  # 填充轮廓内部
            
            # 只在保护区域外应用无效像素检测
            mask[invalid_pixels & (protector_mask == 0)] = 0
            print(f"保护模式: 保留了 {np.sum(invalid_pixels & (protector_mask == 1))} 个内部特殊像素")
        else:
            # 不保护内部区域，直接标记所有无效像素
            mask[invalid_pixels] = 0
        
        return mask
    
    if len(rgb_data.shape) == 3:  # 多波段
        bands, height, width = rgb_data.shape
        # 创建初始全1掩码（所有像素默认都是有效的）
        mask = np.ones((height, width), dtype=np.uint8)
        
        # 检测无效像素 - 可能是全黑(0)或全白(255)
        if bands >= 3:
            # 检测低值无效区域(黑色)
            invalid_low = (rgb_data[0] <= invalid_threshold_low) & (rgb_data[1] <= invalid_threshold_low) & (rgb_data[2] <= invalid_threshold_low)
            # 检测高值无效区域(白色)
            invalid_high = (rgb_data[0] >= invalid_threshold_high) & (rgb_data[1] >= invalid_threshold_high) & (rgb_data[2] >= invalid_threshold_high)
            # 合并无效区域
            invalid_pixels = invalid_low | invalid_high
            
            if protect_interior:
                # 保护内部区域 - 先找到大致的有效区域轮廓
                # 创建一个临时掩码，只标记边缘的无效像素
                edge_width = min(100, min(height, width) // 10)  # 自适应边缘宽度
                edge_mask = np.zeros((height, width), dtype=bool)
                edge_mask[:edge_width, :] = True  # 上边缘
                edge_mask[-edge_width:, :] = True  # 下边缘
                edge_mask[:, :edge_width] = True  # 左边缘
                edge_mask[:, -edge_width:] = True  # 右边缘
                
                # 先检测边缘的无效区域
                edge_invalid = invalid_pixels & edge_mask
                temp_mask = np.ones((height, width), dtype=np.uint8)
                temp_mask[edge_invalid] = 0
                
                # 使用形态学操作来清理掩码
                if cv2.__version__[0] >= '3':
                    # OpenCV 3.x 或更高版本
                    kernel = np.ones((5, 5), np.uint8)
                    temp_mask = cv2.morphologyEx(temp_mask, cv2.MORPH_OPEN, kernel)
                    temp_mask = cv2.morphologyEx(temp_mask, cv2.MORPH_CLOSE, kernel)
                
                # 找到初步的有效区域轮廓
                if cv2.__version__[0] >= '4':
                    # OpenCV 4.x
                    contours, _ = cv2.findContours(temp_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                else:
                    # OpenCV 3.x 或更早
                    _, contours, _ = cv2.findContours(temp_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # 简化轮廓
                simplified_contours = []
                for contour in contours:
                    epsilon = tolerance * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)
                    simplified_contours.append(approx)
                
                # 创建新的掩码，只将轮廓外的区域标记为无效
                protector_mask = np.zeros((height, width), dtype=np.uint8)
                cv2.drawContours(protector_mask, simplified_contours, -1, 1, -1)  # 填充轮廓内部
                
                # 只在保护区域外应用无效像素检测
                mask[invalid_pixels & (protector_mask == 0)] = 0
                print(f"保护模式: 保留了 {np.sum(invalid_pixels & (protector_mask == 1))} 个内部特殊像素")
            else:
                # 不保护内部区域，直接标记所有无效像素
                mask[invalid_pixels] = 0
                
            # 输出统计信息
            invalid_low_count = np.sum(invalid_low)
            invalid_high_count = np.sum(invalid_high)
            print(f"找到 {invalid_low_count} 个低值无效像素 (RGB值均 <= {invalid_threshold_low})")
            print(f"找到 {invalid_high_count} 个高值无效像素 (RGB值均 >= {invalid_threshold_high})")
            print(f"总计 {np.sum(invalid_pixels)} 个无效像素")
        else:
            # 单波段或双波段，检测每个波段的值
            invalid_low = np.ones((height, width), dtype=bool)
            invalid_high = np.ones((height, width), dtype=bool)
            for b in range(bands):
                invalid_low &= (rgb_data[b] <= invalid_threshold_low)
                invalid_high &= (rgb_data[b] >= invalid_threshold_high)
            
            invalid_pixels = invalid_low | invalid_high
            
            if protect_interior:
                # 与RGB处理类似，实现保护内部区域的逻辑
                edge_width = min(100, min(height, width) // 10)
                edge_mask = np.zeros((height, width), dtype=bool)
                edge_mask[:edge_width, :] = True
                edge_mask[-edge_width:, :] = True
                edge_mask[:, :edge_width] = True
                edge_mask[:, -edge_width:] = True
                
                edge_invalid = invalid_pixels & edge_mask
                temp_mask = np.ones((height, width), dtype=np.uint8)
                temp_mask[edge_invalid] = 0
                
                # 清理和找轮廓
                kernel = np.ones((5, 5), np.uint8)
                temp_mask = cv2.morphologyEx(temp_mask, cv2.MORPH_OPEN, kernel)
                temp_mask = cv2.morphologyEx(temp_mask, cv2.MORPH_CLOSE, kernel)
                
                if cv2.__version__[0] >= '4':
                    contours, _ = cv2.findContours(temp_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                else:
                    _, contours, _ = cv2.findContours(temp_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                protector_mask = np.zeros((height, width), dtype=np.uint8)
                cv2.drawContours(protector_mask, contours, -1, 1, -1)
                
                mask[invalid_pixels & (protector_mask == 0)] = 0
            else:
                mask[invalid_pixels] = 0
            
            print(f"找到 {np.sum(invalid_low)} 个低值无效像素 (所有波段值均 <= {invalid_threshold_low})")
            print(f"找到 {np.sum(invalid_high)} 个高值无效像素 (所有波段值均 >= {invalid_threshold_high})")
    
        return mask
    
    else:
        raise ValueError(f"不支持的数据维度: {rgb_data.shape}")


def process_chunk_for_full_mask(chunk_info, rgb_data, invalid_threshold_low=0, invalid_threshold_high=255,
                               protect_interior=False, protector_mask=None):
    """
    处理图像数据的一个块，返回该块的掩码 - 检测整个区域的无效数据
    参数:
        chunk_info: 起始和结束行
        rgb_data: 影像数据
        invalid_threshold_low: 低阈值，默认0
        invalid_threshold_high: 高阈值，默认255
        protect_interior: 是否保护内部区域
        protector_mask: 保护区域掩码，用于多线程处理
    """
    start_y, end_y = chunk_info
    
    # 提取当前块的数据
    try:
        chunk_data = rgb_data[:, start_y:end_y, :]
    except Exception as e:
        print(f"提取块数据出错: {str(e)}，可能是维度不匹配")
        # 返回一个全1的默认掩码
        if len(rgb_data.shape) == 3:
            height = end_y - start_y
            width = rgb_data.shape[2]
            return (start_y, end_y, np.ones((height, width), dtype=np.uint8))
        else:
            print("无法确定块的正确维度，处理失败")
            raise
    
    height, width = chunk_data.shape[1], chunk_data.shape[2]
    mask_chunk = np.ones((height, width), dtype=np.uint8)
    
    # 检测无效像素 - 三通道都小于等于低阈值或大于等于高阈值
    invalid_low = (chunk_data[0] <= invalid_threshold_low) & (chunk_data[1] <= invalid_threshold_low) & (chunk_data[2] <= invalid_threshold_low)
    invalid_high = (chunk_data[0] >= invalid_threshold_high) & (chunk_data[1] >= invalid_threshold_high) & (chunk_data[2] >= invalid_threshold_high)
    invalid_pixels = invalid_low | invalid_high
    
    if protect_interior and protector_mask is not None:
        # 获取该块对应的protector_mask
        chunk_protector = protector_mask[start_y:end_y, :]
        # 只在保护区域外应用无效像素检测
        mask_chunk[invalid_pixels & (chunk_protector == 0)] = 0
    else:
        # 不保护或没有提供保护掩码，直接应用无效像素检测
        mask_chunk[invalid_pixels] = 0
        
    return (start_y, end_y, mask_chunk)


def create_full_mask_parallel(rgb_data, invalid_threshold_low=0, invalid_threshold_high=255, 
                           num_processes=None, use_gpu=False, num_gpus=0, chunk_size=1000,
                           protect_interior=False):
    """
    使用并行处理创建整个影像的掩码，识别无效区域
    参数:
        rgb_data: 影像数据
        invalid_threshold_low: 判断为无效数据的低阈值，默认为0
        invalid_threshold_high: 判断为无效数据的高阈值，默认为255
        num_processes: CPU线程数，None表示自动
        use_gpu: 是否使用GPU
        num_gpus: 使用GPU数量，0表示使用所有可用
        chunk_size: 分块大小
        protect_interior: 是否保护有效区域内部的像素
    """
    # 检查rgb_data的形状
    if len(rgb_data.shape) != 3:
        print(f"警告: 输入数据形状不符合预期 RGB 格式，当前形状: {rgb_data.shape}")
        # 如果是单波段数据
        if len(rgb_data.shape) == 2:
            height, width = rgb_data.shape
            # 创建一个掩码
            mask = np.ones((height, width), dtype=np.uint8)
            
            # 检测无效区域 - 低于低阈值或高于高阈值的像素
            invalid_low = (rgb_data <= invalid_threshold_low)
            invalid_high = (rgb_data >= invalid_threshold_high)
            invalid_pixels = invalid_low | invalid_high
            
            mask[invalid_pixels] = 0
            
            return mask
        else:
            raise ValueError(f"不支持的数据维度: {rgb_data.shape}")

    height, width = rgb_data.shape[1], rgb_data.shape[2]
    
    # 保护内部区域处理
    protector_mask = None
    if protect_interior:
        print("启用内部区域保护模式，先识别有效区域...")
        # 创建一个临时掩码用于识别边缘的无效区域
        edge_width = min(100, min(height, width) // 10)  # 自适应边缘宽度
        
        # 创建边缘区域掩码
        edge_mask = np.zeros((height, width), dtype=bool)
        edge_mask[:edge_width, :] = True  # 上边缘
        edge_mask[-edge_width:, :] = True  # 下边缘
        edge_mask[:, :edge_width] = True  # 左边缘
        edge_mask[:, -edge_width:] = True  # 右边缘
        
        # 检测边缘的低值和高值无效区域
        edge_invalid_low = (rgb_data[0] <= invalid_threshold_low) & (rgb_data[1] <= invalid_threshold_low) & (rgb_data[2] <= invalid_threshold_low) & edge_mask
        edge_invalid_high = (rgb_data[0] >= invalid_threshold_high) & (rgb_data[1] >= invalid_threshold_high) & (rgb_data[2] >= invalid_threshold_high) & edge_mask
        edge_invalid = edge_invalid_low | edge_invalid_high
        
        # 创建初步掩码，标记边缘有效区域
        temp_mask = np.ones((height, width), dtype=np.uint8)
        temp_mask[edge_invalid] = 0
        
        # 使用形态学操作清理掩码
        if cv2.__version__[0] >= '3':
            kernel = np.ones((5, 5), np.uint8)
            temp_mask = cv2.morphologyEx(temp_mask, cv2.MORPH_OPEN, kernel)
            temp_mask = cv2.morphologyEx(temp_mask, cv2.MORPH_CLOSE, kernel)
        
        # 找到有效区域轮廓
        if cv2.__version__[0] >= '4':
            contours, _ = cv2.findContours(temp_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        else:
            _, contours, _ = cv2.findContours(temp_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 简化轮廓
        simplified_contours = []
        for contour in contours:
            epsilon = 2.0 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            simplified_contours.append(approx)
        
        # 创建保护掩码 - 轮廓内部区域为1
        protector_mask = np.zeros((height, width), dtype=np.uint8)
        cv2.drawContours(protector_mask, simplified_contours, -1, 1, -1)  # 填充轮廓内部
        
        print(f"找到 {len(simplified_contours)} 个有效区域，这些区域内部将被保留")
    
    # 将数据分成多个块
    chunks = []
    for start_y in range(0, height, chunk_size):
        end_y = min(start_y + chunk_size, height)
        chunks.append((start_y, end_y))
    
    total_chunks = len(chunks)
    print(f"将处理 {total_chunks} 个数据块...")
    
    # 创建结果掩码并初始化
    mask = np.ones((height, width), dtype=np.uint8)
    
    # 使用ThreadPoolExecutor处理 - 避免Windows上的多进程问题
    try:
        from concurrent.futures import ThreadPoolExecutor
        
        # 确定CPU处理器数量
        if num_processes is None:
            num_processes = max(1, multiprocessing.cpu_count() - 1)
        
        print(f"使用多线程处理 ({num_processes} 线程)...")
        
        with ThreadPoolExecutor(max_workers=num_processes) as executor:
            futures = []
            for chunk in chunks:
                future = executor.submit(
                    process_chunk_for_full_mask,
                    chunk,
                    rgb_data, 
                    invalid_threshold_low,
                    invalid_threshold_high,
                    protect_interior,
                    protector_mask
                )
                futures.append(future)
            
            # 使用tqdm显示进度
            results = []
            for i, future in enumerate(tqdm(futures, desc="处理数据块")):
                result = future.result()
                results.append(result)
                # 显示更详细的进度信息
                if (i+1) % 10 == 0 or i == len(futures) - 1:
                    completed = i + 1
                    total = len(futures)
                    percent = (completed / total) * 100
                    print(f"已完成: {completed}/{total} 块 ({percent:.1f}%)")
        
    except Exception as e:
        print(f"线程池处理失败: {str(e)}")
        print("回退到顺序处理...")
        
        # 如果线程池失败，回退到顺序处理
        results = []
        for chunk in tqdm(chunks, desc="顺序处理数据块"):
            result = process_chunk_for_full_mask(
                chunk,
                rgb_data, 
                invalid_threshold_low,
                invalid_threshold_high,
                protect_interior,
                protector_mask
            )
            results.append(result)
    
    # 合并结果
    print("合并处理结果...")
    try:
        for i, (start_y, end_y, mask_chunk) in enumerate(tqdm(results, desc="合并结果")):
            if mask_chunk.shape[0] == end_y - start_y and mask_chunk.shape[1] == width:
                mask[start_y:end_y, :] = mask_chunk
            else:
                print(f"警告: 块形状不匹配 - 预期 {end_y-start_y}x{width}, 实际 {mask_chunk.shape}")
                # 尝试调整大小
                if mask_chunk.size > 0:
                    resized_chunk = cv2.resize(mask_chunk, (width, end_y-start_y), interpolation=cv2.INTER_NEAREST)
                    mask[start_y:end_y, :] = resized_chunk
                    
            # 显示更详细的进度信息
            if (i+1) % 20 == 0 or i == len(results) - 1:
                completed = i + 1
                total = len(results)
                percent = (completed / total) * 100
                print(f"合并进度: {completed}/{total} 块 ({percent:.1f}%)")
    except Exception as e:
        print(f"合并结果时出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 统计无效像素数量
    invalid_count = np.sum(mask == 0)
    valid_count = np.sum(mask == 1)
    total_pixels = height * width
    print(f"统计结果: 总像素 {total_pixels}, 有效像素 {valid_count} ({valid_count/total_pixels*100:.2f}%), "
          f"无效像素 {invalid_count} ({invalid_count/total_pixels*100:.2f}%)")
    
    return mask


def create_shapefile_from_output_tif(output_tif, output_shp, nodata_value=-9999, simplify_tolerance=0.1):
    """
    从处理后的TIF文件直接提取有效区域并生成shapefile
    
    该函数会读取已处理的TIF文件，识别其中的NoData区域(无效区域)，
    然后提取有效区域的边界多边形，并保存为shapefile文件。
    相比于直接从内存中的掩码创建shapefile，该方法能更准确地处理大型影像。
    
    处理流程:
    1. 读取TIF文件，获取栅格数据和地理信息
    2. 创建掩码，标记有效和无效区域
    3. 使用形态学操作对掩码进行平滑处理
    4. 提取有效区域轮廓
    5. 将像素坐标转换为地理坐标
    6. 创建、合并、简化多边形
    7. 保存为shapefile文件
    
    参数:
        output_tif (str): 输入的处理后TIF文件路径
        output_shp (str): 输出的shapefile文件路径
        nodata_value (float, 可选): TIF文件中的NoData值，默认为-9999
        simplify_tolerance (float, 可选): 多边形简化的容差参数，值越大简化程度越高，默认为0.1
        
    返回:
        bool: 操作成功返回True，失败返回False
    """
    print(f"从处理后的TIF文件提取有效区域: {output_tif}")
    print(f"输出Shapefile: {output_shp}")
    print(f"使用NoData值: {nodata_value}, 简化容差: {simplify_tolerance}")
    
    # 确保输出路径使用正斜杠，解决Windows路径问题        
    output_shp = output_shp.replace('\\', '/')
    
    # 检查输出目录是否存在
    output_dir = os.path.dirname(output_shp)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            print(f"创建输出目录: {output_dir}")
        except Exception as e:
            print(f"创建输出目录失败: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return False
            
    # 检查输入TIF文件是否存在
    if not os.path.exists(output_tif):
        print(f"错误: 输入TIF文件不存在: {output_tif}")
        return False
    
    # 检查输出SHP文件是否已存在
    shp_files = [output_shp]
    for ext in ['shx', 'dbf', 'prj']:
        shp_files.append(output_shp.replace('.shp', f'.{ext}'))
    
    existing_files = [f for f in shp_files if os.path.exists(f)]
    if existing_files:
        print(f"警告: 以下Shapefile相关文件已存在并将被覆盖:")
        for f in existing_files:
            print(f"  - {f}")
    
    # 读取处理后的TIF文件
    print("读取TIF文件...")
    info = read_tif(output_tif)
    if info is None:
        print(f"错误: 无法读取处理后的TIF文件: {output_tif}")
        return False
    
    try:
        print("提取TIF文件信息...")
        data = info['data']
        geotrans = info['geotrans']
        proj = info['proj']
        bands = info['bands']
        width = info['width']
        height = info['height']
        
        print(f"TIF文件信息: 宽={width}, 高={height}, 波段数={bands}")
        
        # 创建掩码 - 有效区域为1，无效区域为0
        print("创建掩码，识别有效区域...")
        mask = np.ones((height, width), dtype=np.uint8)
        
        # 根据波段数处理
        if bands == 1:
            # 单波段情况
            print("处理单波段数据...")
            invalid_pixels = np.isclose(data, nodata_value, rtol=1e-5, atol=1e-8)
            mask[invalid_pixels] = 0
            invalid_count = np.sum(invalid_pixels)
            print(f"使用NoData值 {nodata_value} 检测到 {invalid_count} 个无效像素")
        else:
            # 多波段情况 - 如果任一波段是nodata，则该像素无效
            print(f"处理多波段数据 ({bands} 个波段)...")
            any_invalid = False
            for b in range(min(3, bands)):  # 只检查前三个波段(RGB)
                print(f"检查波段 {b+1}...")
                invalid_pixels = np.isclose(data[b], nodata_value, rtol=1e-5, atol=1e-8)
                invalid_count = np.sum(invalid_pixels)
                print(f"波段 {b+1} 检测到 {invalid_count} 个无效像素")
                mask[invalid_pixels] = 0
                any_invalid = any_invalid or invalid_count > 0
            
            if not any_invalid:
                print("警告: 未检测到任何无效像素，结果可能不准确")
        
        # 检查掩码的有效像素占比
        valid_pixel_count = np.sum(mask == 1)
        invalid_pixel_count = np.sum(mask == 0)
        total_pixels = width * height
        valid_percent = valid_pixel_count / total_pixels * 100
        print(f"掩码统计: 总像素数 {total_pixels}, 有效像素数 {valid_pixel_count} ({valid_percent:.2f}%), 无效像素数 {invalid_pixel_count} ({100-valid_percent:.2f}%)")
        
        # 如果有效像素太少，可能是检测有问题
        if valid_percent < 1:
            print(f"警告: 有效像素比例过低 ({valid_percent:.2f}%)，结果可能不准确")
        elif valid_percent > 99:
            print(f"警告: 有效像素比例过高 ({valid_percent:.2f}%)，可能未正确识别无效区域")
        
        # 清理掩码 - 轻微平滑处理，减少边缘锯齿但保持精确套合
        print("轻微平滑处理掩码，减少边缘锯齿...")
        try:
            # 使用小核心进行形态学操作，保持边缘细节
            kernel_size = 3  # 使用3×3核心保持细节
            kernel = np.ones((kernel_size, kernel_size), np.uint8)
            
            # 闭操作填充小孔洞
            print(f"执行形态学闭操作 (kernel={kernel_size}x{kernel_size})...")
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            
            # 开操作去除小噪点
            print(f"执行形态学开操作 (kernel={kernel_size}x{kernel_size})...")
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            
        except Exception as e:
            print(f"掩码形态学处理失败: {str(e)}")
            import traceback
            print(traceback.format_exc())
            print("继续使用原始掩码...")
        
        # 找到轮廓
        print("寻找有效区域轮廓...")
        contours = []
        try:
            # 使用OpenCV查找轮廓 - 使用CHAIN_APPROX_SIMPLE适度简化轮廓
            print("尝试使用OpenCV查找轮廓...")
            if cv2.__version__[0] >= '4':
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            else:
                _, contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            print(f"OpenCV找到 {len(contours)} 个初始轮廓")
            
            # 检查轮廓有效性
            if not contours:
                print("警告: OpenCV未找到任何轮廓")
            else:
                # 分析轮廓尺寸分布
                contour_areas = [cv2.contourArea(cnt) for cnt in contours]
                if contour_areas:
                    min_area = min(contour_areas)
                    max_area = max(contour_areas)
                    mean_area = sum(contour_areas) / len(contour_areas)
                    print(f"轮廓面积统计: 最小={min_area:.2f}, 最大={max_area:.2f}, 平均={mean_area:.2f}, 总数={len(contour_areas)}")
            
            # 尝试过滤小轮廓
            min_contour_area = (width * height) * 0.0001  # 最小面积为总面积的0.01%
            filtered_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_contour_area]
            
            if len(filtered_contours) < len(contours):
                print(f"过滤后剩余 {len(filtered_contours)}/{len(contours)} 个轮廓 (最小面积阈值: {min_contour_area:.2f})")
            
            if not filtered_contours and contours:
                print("警告: 过滤后没有足够大的轮廓，使用原始轮廓")
                filtered_contours = contours
            
            contours = filtered_contours  # 使用过滤后的轮廓
            
            # 再次检查轮廓
            if not contours:
                print("OpenCV未找到有效轮廓，尝试使用skimage...")
                # 尝试使用skimage的find_contours作为备选，使用更低的阈值以捕获更多细节
                sk_contours = find_contours(mask, 0.5, fully_connected='high')
                if sk_contours:
                    print(f"skimage找到 {len(sk_contours)} 个轮廓")
                    # 将skimage轮廓转换为OpenCV格式
                    for sk_cnt in sk_contours:
                        # 转换为整数坐标
                        cv_cnt = np.array([[[int(p[1]), int(p[0])]] for p in sk_cnt], dtype=np.int32)
                        if len(cv_cnt) > 3:  # 至少需要3个点
                            contours.append(cv_cnt)
                    print(f"转换后获得 {len(contours)} 个有效轮廓")
        except Exception as e:
            print(f"轮廓查找失败: {str(e)}")
            import traceback
            print(traceback.format_exc())
            
            # 尝试使用skimage作为备选方案
            try:
                print("尝试使用skimage作为备选方案查找轮廓...")
                sk_contours = find_contours(mask, 0.5, fully_connected='high')
                if sk_contours:
                    print(f"skimage找到 {len(sk_contours)} 个轮廓")
                    # 将skimage轮廓转换为OpenCV格式
                    for sk_cnt in sk_contours:
                        # 转换为整数坐标
                        cv_cnt = np.array([[[int(p[1]), int(p[0])]] for p in sk_cnt], dtype=np.int32)
                        if len(cv_cnt) > 3:  # 至少需要3个点
                            contours.append(cv_cnt)
                    print(f"转换后获得 {len(contours)} 个有效轮廓")
            except Exception as sk_e:
                print(f"skimage轮廓查找也失败: {str(sk_e)}")
                import traceback
                print(traceback.format_exc())
        
        if not contours:
            print("错误: 未找到有效轮廓，无法生成shapefile")
            return False
        
        # 将轮廓转换为地理坐标的多边形
        print(f"将 {len(contours)} 个轮廓转换为地理坐标...")
        polygons = []
        for i, contour in enumerate(tqdm(contours, desc="处理轮廓")):
            try:
                # 提取轮廓点 - 使用亚像素精度
                points = []
                for point in contour:
                    x, y = point[0][0], point[0][1]
                    geo_x, geo_y = pixel_to_geo(x, y, geotrans)
                    points.append((geo_x, geo_y))
                
                # 确保多边形有足够的点
                if len(points) >= 3:
                    # 创建多边形
                    poly = Polygon(points)
                    # 检查多边形的有效性
                    if poly.is_valid:
                        polygons.append(poly)
                    else:
                        print(f"警告: 轮廓 {i} 创建的多边形无效，尝试修复...")
                        # 尝试修复无效多边形
                        fixed_poly = poly.buffer(0)
                        if fixed_poly.is_valid:
                            print(f"成功修复轮廓 {i}")
                            polygons.append(fixed_poly)
                        else:
                            print(f"无法修复轮廓 {i}，跳过")
                else:
                    print(f"警告: 轮廓 {i} 仅有 {len(points)} 个点，至少需要3个点才能创建多边形")
            except Exception as e:
                print(f"处理轮廓 {i} 时出错: {str(e)}")
                import traceback
                print(traceback.format_exc())
        
        print(f"成功创建 {len(polygons)} 个有效多边形")
        
        if not polygons:
            print("错误: 没有有效的多边形，无法创建shapefile")
            return False
        
        # 合并多边形（如果有多个）
        print("合并和简化多边形...")
        try:
            if len(polygons) > 1:
                print(f"将 {len(polygons)} 个多边形合并为一个...")
                multi_polygon = MultiPolygon(polygons)
                # 尝试使用级联联合来合并相交多边形
                try:
                    unified_polygon = unary_union(multi_polygon)
                    print(f"多边形联合后的类型: {type(unified_polygon).__name__}")
                except Exception as e:
                    print(f"多边形联合失败: {str(e)}")
                    unified_polygon = multi_polygon
            else:
                print("只有一个多边形，无需合并")
                unified_polygon = polygons[0]
                
            # 应用简化以减少点数
            print(f"使用简化容差 {simplify_tolerance} 简化多边形...")
            if simplify_tolerance > 0:
                original_points = count_points(unified_polygon)
                simplified_polygon = unified_polygon.simplify(simplify_tolerance)
                simplified_points = count_points(simplified_polygon)
                print(f"简化多边形: 从 {original_points} 个点减少到 {simplified_points} 个点")
                final_polygon = simplified_polygon
            else:
                print("跳过简化过程")
                final_polygon = unified_polygon
        
            # 获取最终多边形信息
            try:
                if isinstance(final_polygon, Polygon):
                    exterior_points = len(final_polygon.exterior.coords)
                    interior_rings = len(final_polygon.interiors)
                    print(f"最终多边形: {exterior_points} 个外环点, {interior_rings} 个内环")
                elif isinstance(final_polygon, MultiPolygon):
                    num_parts = len(final_polygon.geoms)
                    print(f"最终多边形: 多部分多边形，共 {num_parts} 个部分")
            except:
                print("无法获取多边形详细信息")
        except Exception as e:
            print(f"处理多边形时发生错误: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return False
        
        # 创建shapefile
        print(f"创建shapefile: {output_shp}")
        try:
            # 删除可能已存在的文件
            if os.path.exists(output_shp):
                print(f"删除已存在的shapefile: {output_shp}")
                try:
                    for ext in ['shp', 'shx', 'dbf', 'prj']:
                        f = output_shp.replace('.shp', f'.{ext}')
                        if os.path.exists(f):
                            os.remove(f)
                except Exception as rm_e:
                    print(f"删除旧文件时出错: {str(rm_e)}")
            
            # 使用OGR创建shapefile
            print("使用OGR创建Shapefile...")
            driver = ogr.GetDriverByName("ESRI Shapefile")
            if driver is None:
                print("OGR驱动不可用")
                return False
            
            # 创建数据源
            data_source = driver.CreateDataSource(output_shp)
            if data_source is None:
                print(f"无法创建数据源: {output_shp}")
                return False
            
            # 创建空间参考
            srs = osr.SpatialReference()
            if proj:
                try:
                    srs.ImportFromWkt(proj)
                    print(f"设置shapefile投影: {proj[:100]}...")
                except Exception as crs_e:
                    print(f"设置投影信息失败: {str(crs_e)}")
                    # 尝试从WKT中提取EPSG码
                    try:
                        epsg = srs.GetAuthorityCode(None)
                        if epsg:
                            print(f"从WKT中提取EPSG码: {epsg}")
                            srs.ImportFromEPSG(int(epsg))
                        else:
                            print("无法从WKT中提取EPSG码")
                    except Exception as osr_e:
                        print(f"备用投影设置方法也失败: {str(osr_e)}")
            
            # 创建图层
            layer = data_source.CreateLayer("boundary", srs, ogr.wkbPolygon)
            if layer is None:
                print("无法创建图层")
                return False
            
            # 创建要素
            feature_defn = layer.GetLayerDefn()
            feature = ogr.Feature(feature_defn)
            
            # 转换Shapely几何对象到OGR几何对象
            wkt = final_polygon.wkt
            geom = ogr.CreateGeometryFromWkt(wkt)
            if geom is None:
                print("无法创建OGR几何对象")
                return False
            
            # 设置几何对象
            feature.SetGeometry(geom)
            
            # 添加要素到图层
            if layer.CreateFeature(feature) != 0:
                print("添加要素失败")
                return False
            
            # 释放资源
            feature = None
            data_source = None
            
            # 验证结果
            if os.path.exists(output_shp):
                print(f"成功创建shapefile: {output_shp}")
                shp_size = os.path.getsize(output_shp)
                print(f"文件大小: {shp_size / 1024:.2f} KB")
                
                # 检查其他必要文件
                required_exts = ['shx', 'dbf', 'prj']
                missing_files = []
                for ext in required_exts:
                    companion_file = output_shp.replace('.shp', f'.{ext}')
                    if not os.path.exists(companion_file):
                        missing_files.append(companion_file)
                
                if missing_files:
                    print(f"警告: 以下Shapefile组件文件缺失:")
                    for f in missing_files:
                        print(f"  - {f}")
                else:
                    print("所有Shapefile组件文件都已创建")
                
                return True
            else:
                print(f"错误: 未能创建shapefile文件: {output_shp}")
                return False
                
        except Exception as e:
            print(f"创建shapefile时发生错误: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return False
            
    except Exception as e:
        print(f"处理TIF文件时发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False


def process_uav_image(input_tif, output_tif, output_shp, 
                     black_threshold=0, white_threshold=255,
                     nodata_value=-9999, simplify_tolerance=0.1,
                     edge_width=100, num_processes=None, 
                     use_gpu=False, num_gpus=0, chunk_size=1000,
                     process_mode="full", keep_alpha=False, protect_interior=False):
    """无人机遥感影像处理的主函数，用于处理影像无效区域并生成有效区域矢量图
    
    这是整个处理流程的主入口，完成从输入TIF到输出TIF和shapefile的全过程。
    该函数可处理两种模式:
    - "full"模式: 检测整个影像中的无效像素(黑色或白色)
    - "edge"模式: 仅检测影像边缘区域的无效像素
    
    处理流程:
    1. 读取输入TIF影像
    2. 可选地处理Alpha通道
    3. 创建掩码，标记有效和无效区域
    4. 根据掩码设置NoData值，并保存为新的TIF文件
    5. 从处理后的TIF文件提取有效区域的边界多边形
    6. 保存为shapefile文件
    
    参数:
        input_tif (str): 输入影像路径
        output_tif (str): 输出影像路径
        output_shp (str): 输出shapefile路径
        black_threshold (int, 可选): 黑色/无效像素低阈值，像素值小于等于此值将被视为无效，默认为0
        white_threshold (int, 可选): 白色/无效像素高阈值，像素值大于等于此值将被视为无效，默认为255
        nodata_value (float, 可选): 设置的NoData值，默认为-9999
        simplify_tolerance (float, 可选): 多边形简化参数，默认为0.1
        edge_width (int, 可选): 边缘检测宽度(像素)，默认为100，仅在edge模式有效
        num_processes (int, 可选): CPU处理线程数，None表示自动选择
        use_gpu (bool, 可选): 是否使用GPU加速，默认为False
        num_gpus (int, 可选): 使用的GPU数量，0表示使用所有可用的GPU
        chunk_size (int, 可选): 分块处理的块大小(像素)，默认为1000
        process_mode (str, 可选): 处理模式，'full'检测整个影像，'edge'仅检测边缘，默认为'full'
        keep_alpha (bool, 可选): 是否保留Alpha通道(如果存在)，默认为False
        protect_interior (bool, 可选): 是否保护有效区域内部像素，避免内部特殊像素被标记为无效，默认为False
        
    返回:
        bool: 处理成功返回True，失败返回False
    """
    
    start_time = time.time()
    
    # 规范化路径
    input_tif = input_tif.replace('\\', '/')
    output_tif = output_tif.replace('\\', '/')
    output_shp = output_shp.replace('\\', '/')
    
    print(f"开始处理影像: {input_tif}")
    print(f"可用GPU数量: {NUM_GPUS}, 使用GPU: {use_gpu}")
    print(f"处理模式: {'全图检测无效区域' if process_mode == 'full' else '仅边缘检测'}")
    print(f"保护内部区域: {protect_interior}")
    print(f"保留Alpha通道: {keep_alpha}")
    
    # 读取输入影像
    info = read_tif(input_tif)
    if info is None:
        print(f"无法读取输入影像: {input_tif}")
        return False
    
    try:
        # 处理Alpha通道
        has_alpha = info['bands'] == 4
        if has_alpha:
            print(f"检测到4波段影像(可能包含Alpha通道)")
            if not keep_alpha:
                print("将移除Alpha通道，只保留RGB三波段")
                # 只取前3个波段（RGB），去掉Alpha通道
                info['data'] = info['data'][:3]
                info['bands'] = 3
                print(f"处理后波段数: {info['bands']}")
            else:
                print("保留所有4个波段")
        
        # 估计内存使用
        mem_total, mem_chunk = estimate_memory_usage(
            info['width'], info['height'], info['bands'], chunk_size
        )
        print(f"影像尺寸: {info['width']}x{info['height']}x{info['bands']}")
        print(f"估计内存使用: 总计 {mem_total}, 单个处理块 {mem_chunk}")
        
        # 创建掩码，识别无效区域
        mask_start = time.time()
        print(f"开始创建掩码... ({time.strftime('%H:%M:%S')})")
        
        # 根据处理模式选择不同的掩码创建方法
        if process_mode == "full":
            print("使用全图检测无效区域模式...")
            if info['bands'] >= 3:
                # 多波段影像 - 检测整个影像中RGB三通道都为0或255的区域
                mask = create_full_mask_parallel(
                    info['data'],
                    invalid_threshold_low=black_threshold,
                    invalid_threshold_high=white_threshold,
                    num_processes=num_processes,
                    use_gpu=use_gpu,
                    num_gpus=num_gpus,
                    chunk_size=chunk_size,
                    protect_interior=protect_interior
                )
            else:
                # 单波段或双波段影像
                print(f"检测到{info['bands']}波段影像，使用全图检测模式")
                mask = create_full_image_mask(
                    info['data'], 
                    invalid_threshold_low=black_threshold,
                    invalid_threshold_high=white_threshold,
                    protect_interior=protect_interior
                )
        else:
            # 使用原来的边缘检测模式
            print("使用边缘检测模式...")
            if info['bands'] >= 3:
                # 对于RGB影像
                mask = create_hybrid_mask_parallel(
                    info['data'], 
                    edge_width=edge_width,
                    black_threshold=black_threshold, 
                    white_threshold=white_threshold,
                    num_processes=num_processes,
                    use_gpu=use_gpu,
                    num_gpus=num_gpus,
                    chunk_size=chunk_size
                )
            else:
                # 对于单波段影像，使用特殊处理
                print("检测到单波段影像，使用特殊处理方式")
                # 创建一个简单的边缘掩码
                height, width = info['data'].shape if info['bands'] == 1 else info['data'].shape[1:]
                mask = np.ones((height, width), dtype=np.uint8)
                
                # 标记边缘区域
                edge_mask = np.zeros((height, width), dtype=bool)
                edge_mask[:edge_width, :] = True  # 上边缘
                edge_mask[-edge_width:, :] = True  # 下边缘
                edge_mask[:, :edge_width] = True  # 左边缘
                edge_mask[:, -edge_width:] = True  # 右边缘
                
                # 单波段情况下处理
                if info['bands'] == 1:
                    band_data = info['data']
                    # 检测黑色区域
                    black_pixels = (band_data <= black_threshold) & edge_mask
                    # 检测白色区域
                    white_pixels = (band_data >= white_threshold) & edge_mask
                    # 合并掩码
                    invalid_pixels = black_pixels | white_pixels
                    mask[invalid_pixels] = 0
        
        mask_end = time.time()
        mask_duration = mask_end - mask_start
        print(f"掩码创建完成，耗时: {mask_duration:.2f} 秒 ({time.strftime('%H:%M:%S')})")
        print(f"预计总处理时间: 约 {mask_duration * 3:.2f} 秒")
    
        # 设置nodata并保存新的tif
        tif_start = time.time()
        print(f"开始设置nodata值并保存到: {output_tif} ({time.strftime('%H:%M:%S')})")
        if not set_nodata_and_save(input_tif, output_tif, mask, nodata_value, processed_info=info):
            print("保存处理后的影像失败")
            return False
        tif_end = time.time()
        tif_duration = tif_end - tif_start
        print(f"影像保存完成，耗时: {tif_duration:.2f} 秒 ({time.strftime('%H:%M:%S')})")
        
        # 创建shapefile - 使用新方法，直接从输出的TIF文件提取
        shp_start = time.time()
        print(f"开始从处理后的影像提取有效区域shapefile: {output_shp} ({time.strftime('%H:%M:%S')})")
        if not create_shapefile_from_output_tif(output_tif, output_shp, nodata_value, simplify_tolerance):
            print("从输出TIF生成shapefile失败，尝试使用内存中的掩码生成")
            # 回退到原来的方法
            if not create_shapefile_from_mask(mask, info['geotrans'], info['proj'], 
                                           output_shp, simplify_tolerance):
                print("生成shapefile失败")
                return False
        shp_end = time.time()
        shp_duration = shp_end - shp_start
        print(f"Shapefile生成完成，耗时: {shp_duration:.2f} 秒 ({time.strftime('%H:%M:%S')})")
        
    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 释放大型数据
        if 'data' in info:
            del info['data']
        if 'dataset' in info:
            info['dataset'] = None
            
    total_time = time.time() - start_time
    print(f"处理完成! 总耗时: {total_time:.2f} 秒 ({time.strftime('%H:%M:%S')})")
    print(f"各阶段耗时:")
    print(f"  - 掩码创建: {mask_duration:.2f} 秒 ({mask_duration/total_time*100:.1f}%)")
    print(f"  - 影像保存: {tif_duration:.2f} 秒 ({tif_duration/total_time*100:.1f}%)")
    print(f"  - Shapefile生成: {shp_duration:.2f} 秒 ({shp_duration/total_time*100:.1f}%)")
    return True


def main():
    """命令行入口函数"""
    print("开始解析命令行参数...")
    
    # 打印当前工作目录和命令行参数
    print(f"当前工作目录: {os.getcwd()}")
    print(f"命令行参数: {sys.argv}")
    
    parser = argparse.ArgumentParser(description="处理遥感影像，设置nodata值并创建有效区域shapefile")
    parser.add_argument("--input", "-i", required=True, help="输入影像路径")
    parser.add_argument("--output", "-o", required=True, help="输出影像路径")
    parser.add_argument("--shp", "-s", required=True, help="输出shapefile路径")
    parser.add_argument("--black", type=int, default=0, help="黑色/无效像素低阈值，默认0")
    parser.add_argument("--white", type=int, default=255, help="白色/无效像素高阈值，默认255")
    parser.add_argument("--nodata", type=float, default=-9999, help="NoData值，默认-9999")
    parser.add_argument("--tolerance", type=float, default=0.1, help="多边形简化参数，默认0.1")
    parser.add_argument("--edge", type=int, default=100, help="边缘检测宽度(像素)，默认100，仅在edge模式有效")
    parser.add_argument("--cpu", type=int, default=None, help="CPU线程数，默认自动")
    parser.add_argument("--no-gpu", action="store_true", help="不使用GPU")
    parser.add_argument("--gpus", type=int, default=0, help="使用GPU数量，0表示使用所有可用")
    parser.add_argument("--chunk", type=int, default=2000, help="处理块大小，默认2000")
    parser.add_argument("--mode", choices=["full", "edge"], default="full", 
                        help="处理模式: 'full'检测整个影像中无效区域, 'edge'仅检测边缘")
    parser.add_argument("--protect-interior", action="store_true", 
                       help="保护有效区域内部像素，避免内部黑白像素被标记为无效")
    parser.add_argument("--keep-alpha", action="store_true", 
                       help="保留Alpha通道(如果存在)，默认删除Alpha通道只保留RGB")
    parser.add_argument("--version", "-v", action="store_true", help="显示版本信息")
    parser.add_argument("--test", action="store_true", help="测试模式，检查参数和环境但不执行处理")
    
    try:
        args = parser.parse_args()
        
        # 显示版本信息
        if args.version:
            print("nodata_shp.py 版本 1.2.0")
            print(f"GDAL 版本: {gdal.__version__ if hasattr(gdal, '__version__') else '未知'}")
            print(f"NumPy 版本: {np.__version__}")
            print(f"可用GPU数量: {NUM_GPUS}")
            print(f"CuPy 可用: {HAS_CUPY}")
            print(f"PyTorch CUDA 可用: {HAS_TORCH}")
            return
            
        print("参数解析成功:")
        print(f"  输入影像: {args.input}")
        print(f"  输出影像: {args.output}")
        print(f"  Shapefile: {args.shp}")
        print(f"  黑色/无效像素低阈值: {args.black}")
        print(f"  白色/无效像素高阈值: {args.white}")
        print(f"  NoData值: {args.nodata}")
        print(f"  简化参数: {args.tolerance}")
        print(f"  边缘宽度: {args.edge}")
        print(f"  处理模式: {args.mode}")
        print(f"  保护内部区域: {args.protect_interior}")
        print(f"  保留Alpha通道: {args.keep_alpha}")
        print(f"  CPU线程数: {args.cpu if args.cpu else '自动'}")
        print(f"  使用GPU: {not args.no_gpu}")
        print(f"  GPU数量: {args.gpus if args.gpus > 0 else '全部可用'}")
        print(f"  块大小: {args.chunk}")
        print(f"  测试模式: {args.test}")
    
        # 规范化输入路径 - 使用正斜杠避免Windows路径问题
        input_path = os.path.abspath(os.path.normpath(args.input)).replace('\\', '/')
        output_path = os.path.abspath(os.path.normpath(args.output)).replace('\\', '/')
        shp_path = os.path.abspath(os.path.normpath(args.shp)).replace('\\', '/')
        
        print(f"规范化路径:")
        print(f"  输入: {input_path}")
        print(f"  输出: {output_path}")
        print(f"  SHP: {shp_path}")
    
        # 检查输入文件是否存在
        if not os.path.exists(input_path):
            print(f"错误：输入文件不存在: {input_path}")
            return
            
        # 测试GDAL是否能打开输入文件
        try:
            test_dataset = gdal.Open(input_path)
            if test_dataset is None:
                print(f"错误: GDAL无法打开输入文件: {input_path}")
                print("可能是文件损坏或格式不支持")
                return
            test_dataset = None  # 释放资源
        except Exception as e:
            print(f"测试GDAL打开文件时发生错误: {str(e)}")
            return
    
        # 检查输出目录
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
                print(f"创建输出目录: {output_dir}")
            except Exception as e:
                print(f"创建输出目录失败: {str(e)}")
                return
    
        shp_dir = os.path.dirname(shp_path)
        if shp_dir and not os.path.exists(shp_dir):
            try:
                os.makedirs(shp_dir)
                print(f"创建shapefile目录: {shp_dir}")
            except Exception as e:
                print(f"创建shapefile目录失败: {str(e)}")
                return
        
        # 测试模式
        if args.test:
            print("测试模式 - 参数和环境检查通过，未执行实际处理")
            return
    
        # 处理影像
        process_uav_image(
            input_tif=input_path,  # 使用规范化路径
            output_tif=output_path,
            output_shp=shp_path,
            black_threshold=args.black,
            white_threshold=args.white,
            nodata_value=args.nodata,
            simplify_tolerance=args.tolerance,
            edge_width=args.edge,
            num_processes=args.cpu,
            use_gpu=not args.no_gpu,
            num_gpus=args.gpus,
            chunk_size=args.chunk,
            process_mode=args.mode,
            keep_alpha=args.keep_alpha,
            protect_interior=args.protect_interior
        )
    except Exception as e:
        print(f"执行过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

    
if __name__ == "__main__":
    print("程序启动...")
    main()
    print("程序结束。")


"""
==================== 使用说明 ====================

本脚本用于处理遥感影像，识别并处理无效区域，设置NoData值，并生成有效区域的Shapefile。
支持两种处理模式：检测整个影像的无效区域或仅检测边缘区域。

基本用法示例:
-------------

1. 检测整个影像中的无效区域(默认模式):
   python tif_process.py -i "input.tif" -o "output.tif" -s "valid_area.shp" --black 0 --white 255

2. 保护有效区域内部的特殊像素:
   python tif_process.py -i "input.tif" -o "output.tif" -s "valid_area.shp" --protect-interior

3. 处理4波段影像，自动删除Alpha通道:
   python tif_process.py -i "input.tif" -o "output.tif" -s "valid_area.shp"

4. 保留Alpha通道:
   python tif_process.py -i "input.tif" -o "output.tif" -s "valid_area.shp" --keep-alpha

5. 仅检测边缘区域的无效数据:
   python tif_process.py -i "input.tif" -o "output.tif" -s "valid_area.shp" --mode edge --edge 100

6. 使用GPU加速:
   python tif_process.py -i "input.tif" -o "output.tif" -s "valid_area.shp" --gpus 2

参数说明:
---------
--input, -i       : 输入影像路径 (必需)
--output, -o      : 输出影像路径 (必需)
--shp, -s         : 输出shapefile路径 (必需)
--black           : 黑色/无效像素低阈值，默认0。RGB三通道值均小于等于此阈值的像素将被视为无效数据
--white           : 白色/无效像素高阈值，默认255。RGB三通道值均大于等于此阈值的像素将被视为无效数据
--nodata          : NoData值，默认-9999
--tolerance       : 多边形简化参数，默认0.5
--edge            : 边缘检测宽度(像素)，默认100 (仅在边缘检测模式有效)
--mode            : 处理模式，'full'检测整个影像中无效区域，'edge'仅检测边缘，默认'full'
--protect-interior: 保护有效区域内部像素，避免内部黑白像素被标记为无效
--keep-alpha      : 保留Alpha通道(如果存在)，默认删除Alpha通道只保留RGB
--cpu             : CPU线程数，默认自动
--no-gpu          : 不使用GPU
--gpus            : 使用GPU数量，0表示使用所有可用
--chunk           : 处理块大小，默认2000
--version, -v     : 显示版本信息
--test            : 测试模式，检查参数和环境但不执行处理

注意事项:
--------
1. 'full'模式会检测整个影像中的无效数据(如RGB三通道均为0或255的像素)
2. 'edge'模式只检测边缘区域的黑色和白色像素
3. 使用--protect-interior选项可以保护有效区域内部的特殊像素
4. 默认会删除Alpha通道(第4波段)，使用--keep-alpha选项可以保留
5. 确保已安装所需的库: GDAL, NumPy, Shapely, scikit-image, OpenCV

版本: 1.2.0
==================== 使用说明结束 ====================
"""

# 添加TIF处理执行器
import os
import sys
import uuid
import time
import json
import datetime
import threading
import logging
from logging.handlers import RotatingFileHandler

# 设置日志配置
TIFLOG_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'tiflog'))

# 确保日志目录存在
if not os.path.exists(TIFLOG_DIR):
    os.makedirs(TIFLOG_DIR)

# 创建日志处理器
tif_logger = logging.getLogger('tif_executor')
tif_logger.setLevel(logging.INFO)
log_handler = RotatingFileHandler(
    os.path.join(TIFLOG_DIR, 'tif_executor.log'),
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
log_handler.setFormatter(formatter)
tif_logger.addHandler(log_handler)

# 处理日志
process_logger = logging.getLogger('tif_process')
process_logger.setLevel(logging.INFO)
process_log_handler = RotatingFileHandler(
    os.path.join(TIFLOG_DIR, 'tif_process.log'),
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
process_log_handler.setFormatter(formatter)
process_logger.addHandler(process_log_handler)

class TifStatus:
    """TIF处理任务状态常量"""
    RUNNING = "正在运行"
    SUCCESS = "运行成功"
    FAILED = "运行失败"

class TifExecutor:
    """TIF处理任务执行器"""
    
    def __init__(self):
        """初始化TIF处理执行器"""
        self.tif_tasks = {}  # 存储任务ID和状态
        self.tasks_lock = threading.Lock()  # 用于线程安全的操作
        self.status_file = os.path.join(TIFLOG_DIR, 'tif_status.json')
        self._load_tasks()
    
    def _load_tasks(self):
        """从文件加载任务状态"""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    self.tif_tasks = json.load(f)
                    tif_logger.info(f"加载了 {len(self.tif_tasks)} 个任务状态")
        except Exception as e:
            tif_logger.error(f"加载任务状态失败: {str(e)}")
    
    def _save_tasks(self):
        """保存任务状态到文件"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.tif_tasks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            tif_logger.error(f"保存任务状态失败: {str(e)}")
    
    def execute_tif_process(self, **kwargs):
        """
        执行TIF处理任务
        
        参数:
            **kwargs: 传递给process_uav_image函数的参数
            
        返回:
            task_id: 任务ID
        """
        # 生成唯一任务ID
        task_id = str(uuid.uuid4())
        
        # 必需参数检查
        if 'input_tif' not in kwargs:
            tif_logger.error("缺少必要参数input_tif")
            return None
        
        # 如果没有提供输出路径，则生成默认路径
        if 'output_tif' not in kwargs:
            input_dir = os.path.dirname(kwargs['input_tif'])
            input_filename = os.path.basename(kwargs['input_tif'])
            filename_without_ext = os.path.splitext(input_filename)[0]
            kwargs['output_tif'] = os.path.join(input_dir, f"{filename_without_ext}_out.tif")
            
        if 'output_shp' not in kwargs:
            input_dir = os.path.dirname(kwargs['input_tif'])
            input_filename = os.path.basename(kwargs['input_tif'])
            filename_without_ext = os.path.splitext(input_filename)[0]
            kwargs['output_shp'] = os.path.join(input_dir, f"{filename_without_ext}_out.shp")
            
        # 创建任务日志文件
        log_file_path = os.path.join(TIFLOG_DIR, f"{task_id}.log")
        
        # 记录任务信息
        task_info = {
            'task_id': task_id,
            'parameters': kwargs,
            'status': TifStatus.RUNNING,
            'start_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': None,
            'log_file': log_file_path
        }
        
        with self.tasks_lock:
            self.tif_tasks[task_id] = task_info
            self._save_tasks()
        
        # 启动执行线程
        thread = threading.Thread(
            target=self._run_tif_process,
            args=(task_id, kwargs, log_file_path),
            daemon=True
        )
        thread.start()
        
        tif_logger.info(f"启动TIF处理任务 {task_id}: {kwargs['input_tif']} -> {kwargs['output_tif']}, {kwargs['output_shp']}")
        return task_id
    
    def _run_tif_process(self, task_id, kwargs, log_file_path):
        """
        在单独的线程中执行TIF处理
        
        参数:
            task_id: 任务ID
            kwargs: 处理参数
            log_file_path: 日志文件路径
        """
        # 定义本地任务日志记录器
        task_logger = None
        
        try:
            # 配置日志记录到文件
            task_logger = logging.getLogger(f'tif_task_{task_id}')
            task_logger.setLevel(logging.INFO)
            
            # 清除任何现有处理器
            if task_logger.handlers:
                for handler in task_logger.handlers:
                    task_logger.removeHandler(handler)
                    
            # 添加文件处理器
            file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
            file_handler.setFormatter(formatter)
            task_logger.addHandler(file_handler)
            
            # 将输出重定向到日志
            original_stdout = sys.stdout
            original_stderr = sys.stderr
            
            class LoggerWriter:
                def __init__(self, logger, level):
                    self.logger = logger
                    self.level = level
                    self.buf = ""
                
                def write(self, message):
                    if message and not message.isspace():
                        self.buf += message
                        if self.buf.endswith('\n'):
                            self.logger.log(self.level, self.buf.rstrip())
                            self.buf = ""
                
                def flush(self):
                    if self.buf:
                        self.logger.log(self.level, self.buf)
                        self.buf = ""
            
            sys.stdout = LoggerWriter(task_logger, logging.INFO)
            sys.stderr = LoggerWriter(task_logger, logging.ERROR)
            
            # 记录启动信息和系统状态
            task_logger.info(f"============ TIF处理任务 {task_id} 开始执行 ============")
            task_logger.info(f"开始时间: {self.tif_tasks[task_id]['start_time']}")
            task_logger.info(f"处理参数: {json.dumps(kwargs, ensure_ascii=False, indent=2)}")
            
            # 记录系统信息
            import platform
            
            try:
                task_logger.info(f"系统信息:")
                task_logger.info(f"  操作系统: {platform.system()} {platform.version()}")
                task_logger.info(f"  Python版本: {platform.python_version()}")
                
                # GDAL版本信息
                import osgeo
                task_logger.info(f"  GDAL版本: {osgeo.__version__}")
                
                # GPU状态始终为不可用
                task_logger.info(f"  GPU可用: 否")
            except Exception as e:
                task_logger.warning(f"获取系统信息时出错: {str(e)}")
            
            # 检查参数有效性
            task_logger.info("检查参数有效性...")
            if 'input_tif' not in kwargs:
                task_logger.error("缺少必要参数: input_tif")
                raise ValueError("缺少必要参数: input_tif")
                
            input_tif = kwargs['input_tif']
            if not os.path.exists(input_tif):
                task_logger.error(f"输入文件不存在: {input_tif}")
                raise FileNotFoundError(f"输入文件不存在: {input_tif}")
                
            task_logger.info(f"输入文件存在: {input_tif}")
            
            # 检查输出路径
            output_tif = kwargs.get('output_tif')
            output_shp = kwargs.get('output_shp')
            
            if output_tif:
                output_tif_dir = os.path.dirname(output_tif)
                if output_tif_dir and not os.path.exists(output_tif_dir):
                    task_logger.info(f"输出TIF目录不存在，将创建: {output_tif_dir}")
                    try:
                        os.makedirs(output_tif_dir, exist_ok=True)
                        task_logger.info(f"成功创建目录: {output_tif_dir}")
                    except Exception as e:
                        task_logger.error(f"创建输出目录失败: {output_tif_dir}, 错误: {str(e)}")
                        raise
                        
            if output_shp:
                output_shp_dir = os.path.dirname(output_shp)
                if output_shp_dir and not os.path.exists(output_shp_dir):
                    task_logger.info(f"输出SHP目录不存在，将创建: {output_shp_dir}")
                    try:
                        os.makedirs(output_shp_dir, exist_ok=True)
                        task_logger.info(f"成功创建目录: {output_shp_dir}")
                    except Exception as e:
                        task_logger.error(f"创建输出目录失败: {output_shp_dir}, 错误: {str(e)}")
                        raise
            
            # 强制使用CPU模式
            kwargs['use_gpu'] = False
            
            # 记录开始处理消息
            task_logger.info(f"开始执行TIF处理流程...")
            task_logger.info(f"输入: {input_tif}")
            task_logger.info(f"输出TIF: {output_tif or '(自动生成)'}")
            task_logger.info(f"输出SHP: {output_shp or '(自动生成)'}")
            
            # 执行TIF处理
            start_time = time.time()
            result = process_uav_image(**kwargs)
            end_time = time.time()
            
            # 记录处理时间
            processing_time = end_time - start_time
            task_logger.info(f"处理完成，耗时: {processing_time:.2f} 秒 ({processing_time/60:.2f} 分钟)")
            
            # 验证输出文件
            if result:
                task_logger.info("处理结果: 成功")
                
                # 验证输出文件
                final_output_tif = kwargs.get('output_tif')
                final_output_shp = kwargs.get('output_shp')
                
                if final_output_tif and os.path.exists(final_output_tif):
                    file_size = os.path.getsize(final_output_tif)
                    task_logger.info(f"输出TIF文件: {final_output_tif}, 大小: {file_size / (1024*1024):.2f} MB")
                else:
                    task_logger.warning(f"输出TIF文件不存在或未指定: {final_output_tif}")
                    
                if final_output_shp and os.path.exists(final_output_shp):
                    file_size = os.path.getsize(final_output_shp)
                    task_logger.info(f"输出SHP文件: {final_output_shp}, 大小: {file_size / 1024:.2f} KB")
                    
                    # 检查SHP相关文件
                    missing_files = []
                    for ext in ['shx', 'dbf', 'prj']:
                        companion_file = final_output_shp.replace('.shp', f'.{ext}')
                        if not os.path.exists(companion_file):
                            missing_files.append(ext)
                    
                    if missing_files:
                        task_logger.warning(f"SHP文件缺少关联组件文件: {', '.join(missing_files)}")
                else:
                    task_logger.warning(f"输出SHP文件不存在或未指定: {final_output_shp}")
            else:
                task_logger.error("处理结果: 失败")
            
            # 更新状态
            with self.tasks_lock:
                self.tif_tasks[task_id]['end_time'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self.tif_tasks[task_id]['status'] = TifStatus.SUCCESS if result else TifStatus.FAILED
                self.tif_tasks[task_id]['result'] = result
                self.tif_tasks[task_id]['processing_time'] = f"{processing_time:.2f}秒"
                self._save_tasks()
            
            # 记录完成信息
            status_str = "成功" if result else "失败"
            task_logger.info(f"TIF处理任务 {task_id} 执行{status_str}")
            task_logger.info(f"完成时间: {self.tif_tasks[task_id]['end_time']}")
            task_logger.info(f"状态: {self.tif_tasks[task_id]['status']}")
            task_logger.info(f"============ 任务执行结束 ============")
            
            # 恢复原始输出
            sys.stdout = original_stdout
            sys.stderr = original_stderr
            
        except Exception as e:
            tif_logger.error(f"执行任务 {task_id} 时出错: {str(e)}")
            import traceback
            stack_trace = traceback.format_exc()
            tif_logger.error(stack_trace)
            
            # 获取更详细的错误信息
            error_type = type(e).__name__
            error_message = str(e)
            error_details = {
                'error_type': error_type,
                'error_message': error_message,
                'traceback': stack_trace
            }
            
            # 更新状态为失败
            with self.tasks_lock:
                self.tif_tasks[task_id]['end_time'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self.tif_tasks[task_id]['status'] = TifStatus.FAILED
                self.tif_tasks[task_id]['error'] = error_message
                self.tif_tasks[task_id]['error_details'] = error_details
                self._save_tasks()
            
            # 记录错误到日志文件
            try:
                # 确保任务日志记录器存在
                if task_logger is None or not task_logger.handlers:
                    task_logger = logging.getLogger(f'tif_task_{task_id}')
                    task_logger.setLevel(logging.INFO)
                    file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
                    file_handler.setFormatter(formatter)
                    task_logger.addHandler(file_handler)
                
                task_logger.error(f"============ 处理失败 ============")
                task_logger.error(f"错误类型: {error_type}")
                task_logger.error(f"错误信息: {error_message}")
                task_logger.error("详细堆栈跟踪:")
                for line in stack_trace.splitlines():
                    task_logger.error(line)
                
                # 尝试收集额外诊断信息
                task_logger.error("系统诊断信息:")
                
                # 检查磁盘空间
                try:
                    import shutil
                    if kwargs.get('output_tif'):
                        output_dir = os.path.dirname(kwargs['output_tif'])
                        if not output_dir:
                            output_dir = os.getcwd()
                        disk_usage = shutil.disk_usage(output_dir)
                        free_space_gb = disk_usage.free / (1024**3)
                        task_logger.error(f"输出目录磁盘空间: 总计 {disk_usage.total/(1024**3):.2f} GB, "
                                         f"可用 {free_space_gb:.2f} GB, "
                                         f"使用率 {(disk_usage.used/disk_usage.total)*100:.1f}%")
                        
                        if free_space_gb < 1.0:
                            task_logger.error("警告: 磁盘空间不足，可能导致处理失败")
                except Exception as disk_e:
                    task_logger.error(f"检查磁盘空间时出错: {str(disk_e)}")
                
                # 检查输入文件
                try:
                    if kwargs.get('input_tif') and os.path.exists(kwargs['input_tif']):
                        input_size = os.path.getsize(kwargs['input_tif']) / (1024**3)
                        task_logger.error(f"输入文件大小: {input_size:.2f} GB")
                        
                        if input_size > 2.0:
                            task_logger.error("警告: 输入文件较大，可能需要更多内存")
                except Exception as file_e:
                    task_logger.error(f"检查输入文件时出错: {str(file_e)}")
                
                # 内存使用信息已移除
                
                task_logger.error(f"完成时间: {self.tif_tasks[task_id]['end_time']}")
                task_logger.error(f"状态: {TifStatus.FAILED}")
                task_logger.error(f"============ 任务异常结束 ============")
                
                # 恢复原始输出
                if 'original_stdout' in locals():
                    sys.stdout = original_stdout
                    sys.stderr = original_stderr
            except Exception as log_error:
                tif_logger.error(f"记录错误信息时发生异常: {str(log_error)}")
                tif_logger.error(traceback.format_exc())
    
    def get_task_status(self, task_id):
        """
        获取任务状态
        
        参数:
            task_id: 任务ID
            
        返回:
            任务状态信息字典，如果任务不存在则返回None
        """
        with self.tasks_lock:
            return self.tif_tasks.get(task_id)
    
    def get_all_tasks(self):
        """
        获取所有任务状态
        
        返回:
            任务状态信息字典列表
        """
        with self.tasks_lock:
            return list(self.tif_tasks.values())
    
    def clear_completed_tasks(self, hours=24):
        """
        清理指定时间前完成的任务
        
        参数:
            hours: 清理多少小时前完成的任务
        """
        cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=hours)
        cutoff_str = cutoff_time.strftime('%Y-%m-%d %H:%M:%S')
        
        with self.tasks_lock:
            to_delete = []
            for task_id, task in self.tif_tasks.items():
                if task['status'] != TifStatus.RUNNING and task['end_time'] < cutoff_str:
                    to_delete.append(task_id)
            
            for task_id in to_delete:
                del self.tif_tasks[task_id]
            
            if to_delete:
                self._save_tasks()
                tif_logger.info(f"清理了 {len(to_delete)} 个已完成的任务")

# 创建全局执行器实例
tif_executor = TifExecutor()