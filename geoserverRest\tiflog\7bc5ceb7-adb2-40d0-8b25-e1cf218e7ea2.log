2025-07-30 15:40:29,013 - INFO - ============ TIF处理任务 7bc5ceb7-adb2-40d0-8b25-e1cf218e7ea2 开始执行 ============
2025-07-30 15:40:29,021 - INFO - 开始时间: 2025-07-30 15:40:28
2025-07-30 15:40:29,023 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-30 15:40:29,025 - INFO - 系统信息:
2025-07-30 15:40:29,026 - INFO -   操作系统: Windows 10.0.19045
2025-07-30 15:40:29,030 - INFO -   Python版本: 3.8.20
2025-07-30 15:40:29,031 - INFO -   GDAL版本: 3.9.2
2025-07-30 15:40:29,045 - INFO -   GPU可用: 否
2025-07-30 15:40:29,047 - INFO - 检查参数有效性...
2025-07-30 15:40:29,049 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-30 15:40:29,050 - INFO - 开始执行TIF处理流程...
2025-07-30 15:40:29,051 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171601\project\odm_orthophoto\odm_orthophoto.tif
2025-07-30 15:40:29,052 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-07-30 15:40:29,055 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp
2025-07-30 15:40:29,063 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-30 15:41:03,239 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17007, 高=20364, 波段数=4开始读取影像数据，大小约: 5284.58 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171601/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17007x20364x3估计内存使用: 总计 5.16 GB, 单个处理块 259.51 MB开始创建掩码... (15:41:02)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-30 15:41:03,249 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-30 15:41:05,010 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:01<00:34,  1.72s/it]
2025-07-30 15:41:05,422 - ERROR - 
处理数据块:  48%|#################################3                                    | 10/21 [00:02<00:01,  5.97it/s]
2025-07-30 15:41:05,559 - ERROR - 
处理数据块:  62%|###########################################3                          | 13/21 [00:02<00:01,  7.65it/s]
2025-07-30 15:41:05,561 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:02<00:00,  9.18it/s]
2025-07-30 15:41:05,568 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-30 15:41:05,569 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-30 15:41:05,684 - ERROR - 
合并结果:  52%|#####################################7                                  | 11/21 [00:00<00:00, 97.44it/s]
2025-07-30 15:41:05,752 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 115.72it/s]
2025-07-30 15:41:08,968 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 346330548, 有效像素 127400850 (36.79%), 无效像素 218929698 (63.21%)掩码创建完成，耗时: 5.05 秒 (15:41:07)预计总处理时间: 约 15.15 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif (15:41:07)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif使用已处理过的影像信息影像信息: 宽=17007, 高=20364, 波段数=3掩码形状: (20364, 17007), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 尺寸: 17007x20364x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-30 15:41:08,971 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-30 15:41:20,125 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=42.58, std=63.34应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-30 15:41:20,152 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-30 15:41:20,153 - ERROR - [A
2025-07-30 15:41:20,710 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:11,  1.80it/s]
2025-07-30 15:41:20,711 - ERROR - [A
2025-07-30 15:41:20,841 - ERROR - 
波段 1/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  7.11it/s]
2025-07-30 15:41:20,842 - ERROR - [A
2025-07-30 15:41:21,064 - ERROR - 
波段 1/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:01,  7.82it/s]
2025-07-30 15:41:21,066 - ERROR - [A
2025-07-30 15:41:21,302 - ERROR - 
波段 1/3 写入进度:  38%|########################3                                       | 8/21 [00:01<00:01,  8.04it/s]
2025-07-30 15:41:21,303 - ERROR - [A
2025-07-30 15:41:22,276 - ERROR - 
波段 1/3 写入进度:  48%|##############################                                 | 10/21 [00:02<00:02,  3.95it/s]
2025-07-30 15:41:22,277 - ERROR - [A
2025-07-30 15:41:22,475 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:02<00:01,  4.97it/s]
2025-07-30 15:41:22,477 - ERROR - [A
2025-07-30 15:41:22,603 - ERROR - 
波段 1/3 写入进度:  62%|#######################################                        | 13/21 [00:02<00:01,  5.34it/s]
2025-07-30 15:41:22,604 - ERROR - [A
2025-07-30 15:41:22,741 - ERROR - 
波段 1/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:01,  5.67it/s]
2025-07-30 15:41:22,743 - ERROR - [A
2025-07-30 15:41:26,097 - ERROR - 
波段 1/3 写入进度:  71%|#############################################                  | 15/21 [00:05<00:05,  1.08it/s]
2025-07-30 15:41:26,098 - ERROR - [A
2025-07-30 15:41:27,085 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:06<00:04,  1.06it/s]
2025-07-30 15:41:27,086 - ERROR - [A
2025-07-30 15:41:27,255 - ERROR - 
波段 1/3 写入进度:  81%|###################################################            | 17/21 [00:07<00:02,  1.35it/s]
2025-07-30 15:41:27,256 - ERROR - [A
2025-07-30 15:41:28,204 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:08<00:02,  1.26it/s]
2025-07-30 15:41:28,206 - ERROR - [A
2025-07-30 15:41:28,375 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:08<00:01,  1.61it/s]
2025-07-30 15:41:28,376 - ERROR - [A
2025-07-30 15:41:28,533 - ERROR - 
波段 1/3 写入进度:  95%|############################################################   | 20/21 [00:08<00:00,  2.05it/s]
2025-07-30 15:41:28,534 - ERROR - [A
2025-07-30 15:41:28,574 - ERROR - [A
2025-07-30 15:42:15,384 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:06<02:12, 66.40s/it]
2025-07-30 15:42:37,821 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=45.79, std=65.41应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-30 15:42:37,822 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-30 15:42:37,826 - ERROR - [A
2025-07-30 15:42:38,287 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:09,  2.17it/s]
2025-07-30 15:42:38,288 - ERROR - [A
2025-07-30 15:42:38,624 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:07,  2.58it/s]
2025-07-30 15:42:38,625 - ERROR - [A
2025-07-30 15:42:39,025 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:07,  2.56it/s]
2025-07-30 15:42:39,026 - ERROR - [A
2025-07-30 15:42:39,347 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:01<00:06,  2.73it/s]
2025-07-30 15:42:39,348 - ERROR - [A
2025-07-30 15:42:39,682 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:01<00:05,  2.82it/s]
2025-07-30 15:42:39,682 - ERROR - [A
2025-07-30 15:42:40,104 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:05,  2.65it/s]
2025-07-30 15:42:40,105 - ERROR - [A
2025-07-30 15:42:40,662 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:02<00:06,  2.29it/s]
2025-07-30 15:42:40,663 - ERROR - [A
2025-07-30 15:42:40,956 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:03<00:05,  2.56it/s]
2025-07-30 15:42:40,956 - ERROR - [A
2025-07-30 15:42:41,214 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:03<00:04,  2.86it/s]
2025-07-30 15:42:41,215 - ERROR - [A
2025-07-30 15:42:41,548 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:03<00:03,  2.90it/s]
2025-07-30 15:42:41,549 - ERROR - [A
2025-07-30 15:42:41,897 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:04<00:03,  2.89it/s]
2025-07-30 15:42:41,897 - ERROR - [A
2025-07-30 15:42:42,464 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:04<00:03,  2.42it/s]
2025-07-30 15:42:42,465 - ERROR - [A
2025-07-30 15:42:42,981 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:05<00:03,  2.25it/s]
2025-07-30 15:42:42,982 - ERROR - [A
2025-07-30 15:42:43,405 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:05<00:03,  2.28it/s]
2025-07-30 15:42:43,405 - ERROR - [A
2025-07-30 15:42:43,979 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:06<00:02,  2.09it/s]
2025-07-30 15:42:43,980 - ERROR - [A
2025-07-30 15:42:44,263 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:06<00:02,  2.38it/s]
2025-07-30 15:42:44,263 - ERROR - [A
2025-07-30 15:42:46,607 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:08<00:03,  1.00it/s]
2025-07-30 15:42:46,609 - ERROR - [A
2025-07-30 15:42:51,861 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:14<00:06,  2.28s/it]
2025-07-30 15:42:51,863 - ERROR - [A
2025-07-30 15:42:57,426 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:19<00:06,  3.26s/it]
2025-07-30 15:42:57,427 - ERROR - [A
2025-07-30 15:43:05,197 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:27<00:04,  4.62s/it]
2025-07-30 15:43:05,197 - ERROR - [A
2025-07-30 15:43:05,204 - ERROR - [A
2025-07-30 15:44:37,120 - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [03:28<01:50, 110.72s/it]
2025-07-30 15:45:01,431 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=35.76, std=51.77应用掩码: 218929698 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-30 15:45:01,436 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-30 15:45:01,438 - ERROR - [A
2025-07-30 15:45:02,568 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:01<00:22,  1.13s/it]
2025-07-30 15:45:02,569 - ERROR - [A
2025-07-30 15:45:03,236 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:01<00:16,  1.17it/s]
2025-07-30 15:45:03,238 - ERROR - [A
2025-07-30 15:45:03,910 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:02<00:13,  1.29it/s]
2025-07-30 15:45:03,910 - ERROR - [A
2025-07-30 15:45:04,513 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:03<00:12,  1.42it/s]
2025-07-30 15:45:04,514 - ERROR - [A
2025-07-30 15:45:04,621 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:03<00:03,  3.51it/s]
2025-07-30 15:45:04,623 - ERROR - [A
2025-07-30 15:45:04,737 - ERROR - 
波段 3/3 写入进度:  48%|##############################                                 | 10/21 [00:03<00:01,  5.94it/s]
2025-07-30 15:45:04,738 - ERROR - [A
2025-07-30 15:45:04,860 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:03<00:00,  8.54it/s]
2025-07-30 15:45:04,861 - ERROR - [A
2025-07-30 15:45:04,975 - ERROR - 
波段 3/3 写入进度:  76%|################################################               | 16/21 [00:03<00:00, 11.33it/s]
2025-07-30 15:45:04,979 - ERROR - [A
2025-07-30 15:45:05,087 - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [00:03<00:00, 15.73it/s]
2025-07-30 15:45:05,088 - ERROR - [A
2025-07-30 15:45:05,097 - ERROR - [A
2025-07-30 15:48:27,365 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [07:18<00:00, 165.30s/it]
2025-07-30 15:48:27,367 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [07:18<00:00, 146.13s/it]
2025-07-30 15:48:59,406 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif影像保存完成，耗时: 440.91 秒 (15:48:28)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp (15:48:28)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tifTIF文件信息: 宽=17007, 高=20364, 波段数=3开始读取影像数据，大小约: 3963.44 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif提取TIF文件信息...TIF文件信息: 宽=17007, 高=20364, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 218929698 个无效像素检查波段 2...波段 2 检测到 218929698 个无效像素检查波段 3...波段 3 检测到 218929698 个无效像素掩码统计: 总像素数 346330548, 有效像素数 127400850 (36.79%), 无效像素数 218929698 (63.21%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 1 个初始轮廓轮廓面积统计: 最小=127380512.50, 最大=127380512.50, 平均=127380512.50, 总数=1将 1 个轮廓转换为地理坐标...
2025-07-30 15:48:59,415 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-30 15:48:59,816 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.63it/s]
2025-07-30 15:48:59,817 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  2.61it/s]
2025-07-30 15:49:01,795 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-30 15:49:12,090 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-30 15:49:12,091 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-30 15:49:13,015 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:13,  2.17it/s]
2025-07-30 15:49:13,156 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:09,  3.06it/s]
2025-07-30 15:49:13,229 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 27.31it/s]
2025-07-30 15:49:14,673 - INFO - 处理了 82263 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp写入shapefile特征...处理MultiPolygon，包含 19 个多边形
2025-07-30 15:49:14,674 - ERROR - 
写入多边形:   0%|                                                                               | 0/19 [00:00<?, ?it/s]
2025-07-30 15:49:15,752 - ERROR - 
写入多边形:  26%|##################6                                                    | 5/19 [00:01<00:02,  4.68it/s]
2025-07-30 15:49:15,879 - ERROR - 
写入多边形:  58%|########################################5                             | 11/19 [00:01<00:00, 10.85it/s]
2025-07-30 15:49:15,882 - ERROR - 
写入多边形: 100%|######################################################################| 19/19 [00:01<00:00, 15.86it/s]
2025-07-30 15:49:16,045 - INFO - 处理完成，耗时: 526.99 秒 (8.78 分钟)
2025-07-30 15:49:16,046 - INFO - 处理结果: 成功
2025-07-30 15:49:16,050 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif, 大小: 740.84 MB
2025-07-30 15:49:16,052 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp, 大小: 22.81 KB
2025-07-30 15:49:16,348 - INFO - TIF处理任务 7bc5ceb7-adb2-40d0-8b25-e1cf218e7ea2 执行成功
2025-07-30 15:49:16,351 - INFO - 完成时间: 2025-07-30 15:49:16
2025-07-30 15:49:16,360 - INFO - 状态: 运行成功
2025-07-30 15:49:16,361 - INFO - ============ 任务执行结束 ============
