2025-07-10 16:20:52,529 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250710_162052.log
2025-07-10 16:20:52,559 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-10 16:20:52,562 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-10 16:20:53,377 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-10 16:20:53,400 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-10 16:20:53,401 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-10 16:20:53,427 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-10 16:20:53,444 - root - INFO - === GeoServer REST API服务 ===
2025-07-10 16:20:53,445 - root - INFO - 主机: 0.0.0.0
2025-07-10 16:20:53,446 - root - INFO - 端口: 5083
2025-07-10 16:20:53,446 - root - INFO - 调试模式: 禁用
2025-07-10 16:20:53,446 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-10 16:20:53,480 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-10 16:20:53,482 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-10 16:21:32,596 - werkzeug - INFO - ************** - - [10/Jul/2025 16:21:32] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:21:32,771 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 16:21:33,060 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 16:21:33,114 - werkzeug - INFO - ************** - - [10/Jul/2025 16:21:33] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:21:39,678 - werkzeug - INFO - ************** - - [10/Jul/2025 16:21:39] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:21:39,717 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 16:21:39,804 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 16:21:39,807 - werkzeug - INFO - ************** - - [10/Jul/2025 16:21:39] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:22:38,692 - werkzeug - INFO - ************** - - [10/Jul/2025 16:22:38] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:22:38,746 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 16:22:38,852 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 16:22:38,872 - werkzeug - INFO - ************** - - [10/Jul/2025 16:22:38] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:23:10,429 - werkzeug - INFO - ************** - - [10/Jul/2025 16:23:10] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:23:10,525 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 16:23:10,585 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 16:23:10,619 - werkzeug - INFO - ************** - - [10/Jul/2025 16:23:10] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:23:24,290 - werkzeug - INFO - ************** - - [10/Jul/2025 16:23:24] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:23:24,301 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 16:23:24,367 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 16:23:24,373 - werkzeug - INFO - ************** - - [10/Jul/2025 16:23:24] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:23:33,587 - root - INFO - 开始查询坐标点 (22.916649263322512, 108.47062166267862) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-10 16:23:34,428 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-10 16:23:34,868 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-10 16:23:35,271 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-10 16:23:35,297 - root - INFO - 在坐标点 (22.916649263322512, 108.47062166267862) 处找到 2 个有有效数据的栅格图层
2025-07-10 16:23:35,303 - werkzeug - INFO - ************** - - [10/Jul/2025 16:23:35] "GET /api/query_values?lat=22.916649263322512&lon=108.47062166267862&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-10 16:23:54,785 - werkzeug - INFO - ************** - - [10/Jul/2025 16:23:54] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:23:54,854 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 16:23:54,924 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 16:23:54,934 - werkzeug - INFO - ************** - - [10/Jul/2025 16:23:54] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:24:29,585 - werkzeug - INFO - ************** - - [10/Jul/2025 16:24:29] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 16:24:29,607 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 16:24:29,654 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 16:24:29,659 - werkzeug - INFO - ************** - - [10/Jul/2025 16:24:29] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 17:03:40,155 - werkzeug - INFO - ************** - - [10/Jul/2025 17:03:40] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 17:03:40,217 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 17:03:40,400 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 17:03:40,429 - werkzeug - INFO - ************** - - [10/Jul/2025 17:03:40] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 17:10:46,478 - werkzeug - INFO - ************** - - [10/Jul/2025 17:10:46] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 17:10:46,490 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 17:10:46,594 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 17:10:46,598 - werkzeug - INFO - ************** - - [10/Jul/2025 17:10:46] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 17:10:49,544 - werkzeug - INFO - ************** - - [10/Jul/2025 17:10:49] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-10 17:10:49,708 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-10 17:10:49,845 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-10 17:10:49,998 - werkzeug - INFO - ************** - - [10/Jul/2025 17:10:49] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-10 17:41:46,322 - werkzeug - INFO - ************** - - [10/Jul/2025 17:41:46] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-10 17:41:46,537 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-10 17:41:46,708 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-10 17:41:46,715 - werkzeug - INFO - ************** - - [10/Jul/2025 17:41:46] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
