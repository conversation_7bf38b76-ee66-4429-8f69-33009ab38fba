2025-07-11 16:51:35,112 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250711_165135.log
2025-07-11 16:51:35,114 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-11 16:51:35,114 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-11 16:51:35,130 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-11 16:51:35,139 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-11 16:51:35,140 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-11 16:51:35,152 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-11 16:51:35,156 - root - INFO - === GeoServer REST API服务 ===
2025-07-11 16:51:35,157 - root - INFO - 主机: 0.0.0.0
2025-07-11 16:51:35,157 - root - INFO - 端口: 5083
2025-07-11 16:51:35,157 - root - INFO - 调试模式: 禁用
2025-07-11 16:51:35,158 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-11 16:51:35,172 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-11 16:51:35,175 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-11 16:51:42,580 - root - INFO - 成功创建工作区: tttt
2025-07-11 16:51:42,581 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 16:51:42] "GET /api/management/workspaces/create?name=tttt HTTP/1.1" 200 -
2025-07-11 16:56:16,348 - root - INFO - 成功删除工作区: tttt
2025-07-11 16:56:16,349 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 16:56:16] "GET /api/management/workspaces/delete?name=tttt HTTP/1.1" 200 -
2025-07-11 17:16:19,319 - root - ERROR - Shapefile文件不存在: D:\Drone_Project\geoserverapi\data\20250701\nanning.tif文件路径
2025-07-11 17:16:19,320 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:16:19] "[35m[1mGET /api/management/shapefiles/publish?file=D:\\Drone_Project\\geoserverapi\\data\\20250701\\nanning.tif文件路径&workspace=tttt&store=nanning&layer=nanning&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-11 17:16:42,337 - root - ERROR - Shapefile文件不存在: D:/Drone_Project/geoserverapi/data/20250701/nanning.tif文件路径
2025-07-11 17:16:42,338 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:16:42] "[35m[1mGET /api/management/shapefiles/publish?file=D:/Drone_Project/geoserverapi/data/20250701/nanning.tif文件路径&workspace=tttt&store=nanning&layer=nanning&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-11 17:19:26,722 - root - INFO - 工作区 tttt 不存在，正在创建
2025-07-11 17:19:26,772 - root - INFO - 成功创建工作区: tttt
2025-07-11 17:19:26,773 - root - ERROR - 发布GeoTIFF时出错: create_coveragestore() got an unexpected keyword argument 'store_name'
2025-07-11 17:19:26,775 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:19:26] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/geoserverapi/data/20250701/nanning.tif&workspace=tttt&store=nanning&layer=nanning HTTP/1.1[0m" 500 -
2025-07-11 17:20:31,702 - root - ERROR - 发布Shapefile时出错: create_shp_datastore() got an unexpected keyword argument 'file_name'
2025-07-11 17:20:31,705 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:20:31] "[35m[1mGET /api/management/shapefiles/publish?file=D:\\Drone_Project\\geoserverapi\\data\\20250701\\testshp\\设施农用地潜力.shp&workspace=tttt&store=nanning&layer=nanning&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-11 17:21:02,528 - root - ERROR - Shapefile文件不存在: D:\Drone_Project\geoserverapi\data\20250701\testshp\设施农用地潜力.
2025-07-11 17:21:02,530 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:21:02] "[35m[1mGET /api/management/shapefiles/publish?file=D:\\Drone_Project\\geoserverapi\\data\\20250701\\testshp\\设施农用地潜力.&workspace=tttt&store=nanning&layer=nanning&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-11 17:21:05,299 - root - ERROR - Shapefile文件不存在: D:\Drone_Project\geoserverapi\data\20250701\testshp\设施农用地潜力
2025-07-11 17:21:05,301 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 17:21:05] "[35m[1mGET /api/management/shapefiles/publish?file=D:\\Drone_Project\\geoserverapi\\data\\20250701\\testshp\\设施农用地潜力&workspace=tttt&store=nanning&layer=nanning&charset=UTF-8 HTTP/1.1[0m" 500 -
