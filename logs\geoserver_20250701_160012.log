2025-07-01 16:00:12,239 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250701_160012.log
2025-07-01 16:00:12,259 - root - INFO - 开始执行命令: diagnose
2025-07-01 16:00:12,259 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-01 16:00:12,259 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-01 16:00:12,277 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-01 16:00:12,278 - root - INFO - 开始诊断GeoServer...
2025-07-01 16:00:12,278 - root - INFO - 开始GeoServer诊断...
2025-07-01 16:00:12,279 - root - INFO - 检查GeoServer连接: http://localhost:8083/geoserver/rest
2025-07-01 16:00:12,291 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-01 16:00:12,304 - root - INFO - 找到 13 个工作区: cite, division_data_mangement, drone, gislearn, it.geosolutions, nurc, sde, sf, test, testworkspace, tiger, topp, yz
2025-07-01 16:00:12,317 - root - INFO - 在工作区 'testworkspace' 中找到 5 个图层
2025-07-01 16:00:12,329 - root - INFO - 在工作区 'testworkspace' 中找到 15 个数据存储
2025-07-01 16:00:12,330 - root - INFO - 工作区 'testworkspace' 中有 5 个图层，15 个数据存储
2025-07-01 16:00:12,330 - root - INFO - 图层: shp_0063, shp_1325, shp_1634, shp_6866, shp_8653
2025-07-01 16:00:12,331 - root - INFO - 数据存储: shp_0063, shp_1325, shp_1634, shp_2541, shp_5427, shp_5790, shp_6798, shp_6866, shp_8653, shp_9444, shp____, shp_______, shp________, shp___________, shp_____________
2025-07-01 16:00:12,331 - root - INFO - GeoServer中共有 5 个图层
2025-07-01 16:00:12,332 - root - INFO - GeoServer连接状态: 正常, 版本: 2.26.1
2025-07-01 16:00:12,332 - root - INFO - 工作区: 13, 图层: 5
2025-07-01 16:00:12,333 - root - INFO - 工作区 'testworkspace' 详情:
2025-07-01 16:00:12,333 - root - INFO -   - 数据存储: 15
2025-07-01 16:00:12,333 - root - INFO -   - 图层: 5
2025-07-01 16:00:12,334 - root - INFO - 命令执行完成: diagnose
