2025-07-24 11:40:15,663 - INFO - ============ TIF处理任务 31e7e2b3-a47d-4a3d-ae6f-4ed4cbd966b5 开始执行 ============
2025-07-24 11:40:15,671 - INFO - 开始时间: 2025-07-24 11:40:15
2025-07-24 11:40:15,671 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171601\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 11:40:15,685 - ERROR - ============ 处理失败 ============
2025-07-24 11:40:15,693 - ERROR - 错误类型: ModuleNotFoundError
2025-07-24 11:40:15,694 - ERROR - 错误信息: No module named 'psutil'
2025-07-24 11:40:15,702 - ERROR - 详细堆栈跟踪:
2025-07-24 11:40:15,702 - ERROR - Traceback (most recent call last):
2025-07-24 11:40:15,705 - ERROR -   File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 2746, in _run_tif_process
2025-07-24 11:40:15,706 - ERROR -     import psutil
2025-07-24 11:40:15,706 - ERROR - ModuleNotFoundError: No module named 'psutil'
2025-07-24 11:40:15,707 - ERROR - 系统诊断信息:
2025-07-24 11:40:15,709 - ERROR - 输出目录磁盘空间: 总计 1050.91 GB, 可用 47.19 GB, 使用率 95.5%
2025-07-24 11:40:15,710 - ERROR - 输入文件大小: 0.22 GB
2025-07-24 11:40:15,712 - ERROR - 检查内存使用时出错: No module named 'psutil'
2025-07-24 11:40:15,714 - ERROR - 完成时间: 2025-07-24 11:40:15
2025-07-24 11:40:15,724 - ERROR - 状态: 运行失败
2025-07-24 11:40:15,725 - ERROR - ============ 任务异常结束 ============
