# TIF处理API使用说明

## 概述

TIF处理API提供了对GeoTIFF影像的处理功能，包括：

1. 设置NoData值，识别和处理无效区域
2. 提取有效区域shapefile
3. 获取TIF文件基本信息

API支持同步处理和异步处理两种模式：
- **同步处理**：适用于小型影像或简单处理任务，API会等待处理完成后返回结果
- **异步处理**：适用于大型影像或复杂处理任务，API会立即返回任务ID，然后在后台执行处理

## API端点

### 1. 同步处理TIF影像

```
GET /api/tif/process?input_tif=TIF文件路径&其他参数...
```

同步处理完整的TIF影像处理流程，包括设置NoData值和生成有效区域shapefile。

**必选参数:**
- `input_tif`: 输入TIF文件路径

**可选参数:**
- `output_tif`: 输出TIF文件路径 (默认为input_tif同目录下的文件名_out.tif)
- `output_shp`: 输出shapefile文件路径 (默认为input_tif同目录下的文件名_out.shp)
- `black_threshold`: 黑色判定阈值，默认0
- `white_threshold`: 白色判定阈值，默认255
- `nodata_value`: NoData值，默认-9999
- `simplify_tolerance`: 多边形简化参数，默认0.1
- `edge_width`: 边缘检测宽度(像素)，默认100
- `process_mode`: 处理模式，'full'检测整个影像，'edge'仅检测边缘，默认'full'
- `keep_alpha`: 是否保留Alpha通道(如果存在)，默认false
- `protect_interior`: 是否保护有效区域内部像素，默认false
- `use_gpu`: 是否使用GPU加速，默认false
- `num_gpus`: 使用GPU数量，0表示使用所有可用，默认0

**示例:**
```
GET /api/tif/process?input_tif=D:/data/ortho.tif&black_threshold=10&white_threshold=245
```

**响应:**
```json
{
  "status": "success",
  "message": "TIF处理成功",
  "output_tif": "D:/data/ortho_out.tif",
  "output_shp": "D:/data/ortho_out.shp",
  "processing_time": "35.21秒"
}
```

### 2. 异步执行TIF处理任务

```
GET /api/tif/execute?input_tif=TIF文件路径&其他参数...
```

异步处理TIF影像，立即返回任务ID，然后在后台执行处理。这种方式适用于大型影像处理，避免HTTP请求超时。

**参数:**
与同步处理相同，支持所有相同的参数。

**示例:**
```
GET /api/tif/execute?input_tif=D:/data/large_ortho.tif&process_mode=full&use_gpu=true
```

**响应:**
```json
{
  "status": "success",
  "message": "TIF处理任务已启动",
  "task_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### 3. 查询TIF处理任务状态

```
GET /api/tif/status?task_id=任务ID
```

查询特定任务ID对应的TIF处理任务状态。可用于跟踪异步任务的进度。

**必选参数:**
- `task_id`: 执行TIF处理任务时返回的任务ID

**返回:**
包含任务状态信息的JSON对象，包括执行状态（正在运行、运行成功、运行失败）、开始时间、结束时间等。

**示例:**
```
GET /api/tif/status?task_id=550e8400-e29b-41d4-a716-446655440000
```

**响应:**
```json
{
  "status": "success",
  "task": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "parameters": {
      "input_tif": "D:/data/large_ortho.tif",
      "output_tif": "D:/data/large_ortho_out.tif",
      "output_shp": "D:/data/large_ortho_out.shp",
      "process_mode": "full",
      "use_gpu": true
    },
    "status": "运行成功",
    "start_time": "2025-07-12 15:30:00",
    "end_time": "2025-07-12 15:45:20",
    "log_file": "D:/Drone_Project/geoserverapi/geoserverRest/tiflog/550e8400-e29b-41d4-a716-446655440000.log"
  }
}
```

### 4. 查询所有TIF处理任务状态

```
GET /api/tif/all
```

查询所有TIF处理任务的状态。可用于监控所有任务的执行情况。

**返回:**
包含所有任务状态信息的JSON对象列表。

**示例:**
```
GET /api/tif/all
```

**响应:**
```json
{
  "status": "success",
  "count": 2,
  "tasks": [
    {
      "task_id": "550e8400-e29b-41d4-a716-446655440000",
      "parameters": {
        "input_tif": "D:/data/large_ortho.tif",
        "output_tif": "D:/data/large_ortho_out.tif",
        "output_shp": "D:/data/large_ortho_out.shp",
        "process_mode": "full",
        "use_gpu": true
      },
      "status": "运行成功",
      "start_time": "2025-07-12 15:30:00",
      "end_time": "2025-07-12 15:45:20",
      "log_file": "D:/Drone_Project/geoserverapi/geoserverRest/tiflog/550e8400-e29b-41d4-a716-446655440000.log"
    },
    {
      "task_id": "660e8400-e29b-41d4-a716-446655440000",
      "parameters": {
        "input_tif": "D:/data/ortho2.tif",
        "process_mode": "edge"
      },
      "status": "正在运行",
      "start_time": "2025-07-12 15:50:00",
      "end_time": null,
      "log_file": "D:/Drone_Project/geoserverapi/geoserverRest/tiflog/660e8400-e29b-41d4-a716-446655440000.log"
    }
  ]
}
```

### 5. 设置NoData值

```
GET /api/tif/set-nodata?input_tif=TIF文件路径&output_tif=输出TIF路径&其他参数...
```

设置TIF文件的NoData值，处理无效区域，并生成新的TIF文件。

**必选参数:**
- `input_tif`: 输入TIF文件路径

**可选参数:**
- `output_tif`: 输出TIF文件路径 (默认为input_tif同目录下的文件名_nodata.tif)
- `black_threshold`: 黑色判定阈值，默认0
- `white_threshold`: 白色判定阈值，默认255
- `nodata_value`: NoData值，默认-9999
- `edge_width`: 边缘检测宽度(像素)，默认100

**示例:**
```
GET /api/tif/set-nodata?input_tif=D:/data/ortho.tif&output_tif=D:/data/ortho_nodata.tif&nodata_value=-9999
```

**响应:**
```json
{
  "status": "success",
  "message": "NoData设置成功",
  "output_tif": "D:/data/ortho_nodata.tif"
}
```

### 6. 创建Shapefile

```
GET /api/tif/create-shapefile?input_tif=TIF文件路径&output_shp=输出Shapefile路径&其他参数...
```

从设置了NoData值的TIF文件中提取有效区域的边界，生成shapefile文件。

**必选参数:**
- `input_tif`: 输入TIF文件路径（应该是已经设置了NoData值的TIF）

**可选参数:**
- `output_shp`: 输出shapefile文件路径 (默认为input_tif同目录下的文件名_boundary.shp)
- `nodata_value`: NoData值，默认-9999
- `simplify_tolerance`: 多边形简化参数，默认0.1

**示例:**
```
GET /api/tif/create-shapefile?input_tif=D:/data/ortho_nodata.tif&output_shp=D:/data/ortho_boundary.shp
```

**响应:**
```json
{
  "status": "success",
  "message": "Shapefile创建成功",
  "output_shp": "D:/data/ortho_boundary.shp"
}
```

### 7. 获取TIF文件信息

```
GET /api/tif/info?path=TIF文件路径
```

获取TIF文件的基本信息，包括尺寸、波段数、地理变换参数等。

**必选参数:**
- `path`: TIF文件路径

**示例:**
```
GET /api/tif/info?path=D:/data/ortho.tif
```

**响应:**
```json
{
  "status": "success",
  "tif_info": {
    "width": 10000,
    "height": 8000,
    "bands": 3,
    "geotrans": [456378.5, 0.1, 0, 4567832.5, 0, -0.1],
    "projection": "PROJCS[\"WGS 84 / UTM zone 50N\",GEOGCS[\"WGS 84\",...",
    "file_size": 240000000,
    "file_path": "D:/data/ortho.tif"
  }
}
```

## 参数说明

### 处理模式

- `process_mode=full`：检测整个影像中的无效区域（如黑色或白色区域）
- `process_mode=edge`：仅检测影像边缘区域的无效区域

### 保护内部区域

- `protect_interior=true`：避免将有效区域内部的特殊像素（如黑色或白色）标记为无效区域
- `protect_interior=false`：所有符合无效条件的像素都将被标记为无效区域

### GPU加速

- `use_gpu=true`：使用GPU加速处理（需要支持CUDA的显卡和相关库）
- `use_gpu=false`：使用CPU处理

### Alpha通道处理

- `keep_alpha=true`：保留输入影像的Alpha通道（如果存在）
- `keep_alpha=false`：移除Alpha通道，只保留RGB波段

## 异步处理工作流程示例

1. **提交任务**：
   ```
   GET /api/tif/execute?input_tif=D:/data/large_ortho.tif&process_mode=full&use_gpu=true

   http://localhost:5083/api/tif/execute?input_tif=D:/Drone_Project/geoserverapi/data/20250701/nanning.tif&output_tif=D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.tif&output_shp=D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.shp&black_threshold=0&white_threshold=255

   http://localhost:5083/api/tif/execute?input_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif&output_shp=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.shp&black_threshold=0&white_threshold=255
   ```
   返回任务ID：`550e8400-e29b-41d4-a716-446655440000`

2. **轮询任务状态**：
   ```
   GET /api/tif/status?task_id=550e8400-e29b-41d4-a716-446655440000
   ```
   检查任务是否完成

3. **获取任务结果**：
   当任务状态变为"运行成功"时，结果文件将保存在指定的输出路径
   
4. **查看任务日志**：
   任务日志保存在响应中提供的`log_file`路径，可用于调试或了解处理详情

## 常见问题

1. **任务超时**：对于大型影像，请使用异步处理API（/execute）避免HTTP请求超时

2. **内存不足**：处理大型影像时可能会遇到内存不足问题，可以尝试：
   - 使用更小的块大小（减少一次性加载的数据量）
   - 使用边缘检测模式（`process_mode=edge`）而不是全图处理
   - 使用GPU加速（如果有支持CUDA的显卡）

3. **找不到任务**：确保使用正确的task_id查询任务状态

4. **任务状态显示失败**：查看任务日志了解具体错误原因 