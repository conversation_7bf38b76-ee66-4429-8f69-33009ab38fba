#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer栅格数据坐标查询模块 (Django版本)
基于Flask版本完全重写，确保功能和返回数据一致
"""

import os
import sys
import logging
import requests
from urllib.parse import urljoin
import json
import numpy as np
from osgeo import gdal
import xml.etree.ElementTree as ET
from django.conf import settings

from .manager import GeoServerManager

# 获取Django配置的日志记录器
logger = logging.getLogger('geoserver_api')


class GeoServerRasterQuery:
    """用于在GeoServer上查询栅格数据的类 - 与Flask版本完全一致"""

    def __init__(self, geoserver_manager=None):
        """
        初始化查询类

        Args:
            geoserver_manager: GeoServerManager实例，如果不提供则创建新的实例
        """
        self.manager = geoserver_manager if geoserver_manager else GeoServerManager()
        self.logger = logger

    def get_raster_layers(self, workspace):
        """
        获取指定工作区中的所有栅格图层
        与Flask版本完全一致的实现

        Args:
            workspace: 工作区名称

        Returns:
            栅格图层列表
        """
        try:
            # 检查工作区是否存在
            if not self.manager.check_workspace_exists(workspace):
                self.logger.error(f"工作区 '{workspace}' 不存在")
                return []

            # 使用GeoServer REST API获取图层列表
            base_url = self.manager.geo.service_url
            if not base_url.endswith("/rest"):
                base_url = f"{base_url}/rest"

            auth = (self.manager.geo.username, self.manager.geo.password)
            url = f"{base_url}/workspaces/{workspace}/coveragestores.json"

            self.logger.debug(f"正在请求覆盖层存储列表: {url}")
            response = requests.get(url, auth=auth)

            if response.status_code != 200:
                self.logger.error(
                    f"获取栅格存储列表失败，状态码: {response.status_code}"
                )
                return []

            data = response.json()
            self.logger.debug(f"覆盖层存储响应: {json.dumps(data, ensure_ascii=False)}")

            raster_layers = []

            # 处理覆盖层存储列表
            if "coverageStores" in data and "coverageStore" in data["coverageStores"]:
                for store in data["coverageStores"]["coverageStore"]:
                    store_name = store["name"]
                    self.logger.debug(f"找到覆盖层存储: {store_name}")

                    # 获取存储下的图层列表
                    store_url = f"{base_url}/workspaces/{workspace}/coveragestores/{store_name}/coverages.json"
                    self.logger.debug(f"正在请求覆盖层列表: {store_url}")
                    store_response = requests.get(store_url, auth=auth)

                    if store_response.status_code == 200:
                        store_data = store_response.json()
                        self.logger.debug(
                            f"覆盖层响应: {json.dumps(store_data, ensure_ascii=False)}"
                        )

                        if (
                            "coverages" in store_data
                            and "coverage" in store_data["coverages"]
                        ):
                            for coverage in store_data["coverages"]["coverage"]:
                                layer_info = {
                                    "name": coverage["name"],
                                    "store": store_name,
                                    "workspace": workspace,
                                    "id": f"{workspace}:{coverage['name']}",
                                    "bbox": coverage.get("latLonBoundingBox", {}),
                                }
                                self.logger.debug(
                                    f"添加图层: {layer_info['name']}, 边界框: {layer_info['bbox']}"
                                )
                                raster_layers.append(layer_info)
                    else:
                        self.logger.warning(
                            f"获取存储 '{store_name}' 的覆盖层失败，状态码: {store_response.status_code}"
                        )
            else:
                self.logger.warning(f"在工作区 '{workspace}' 中未找到覆盖层存储")

            # 如果有任何图层没有边界框，尝试从WMS GetCapabilities获取
            self._fetch_missing_bboxes(raster_layers, workspace)

            self.logger.info(
                f"在工作区 '{workspace}' 中找到 {len(raster_layers)} 个栅格图层"
            )
            return raster_layers

        except Exception as e:
            self.logger.error(f"获取栅格图层列表时出错: {str(e)}")
            return []

    def get_all_layers(self, workspace):
        """
        获取工作区中的所有图层（栅格和矢量）并按照前端需要的格式返回 - 与Flask版本完全一致

        Args:
            workspace: 工作区名称

        Returns:
            格式化后的图层信息列表
        """
        try:
            # 获取栅格和矢量图层
            raster_layers = self.get_raster_layers(workspace)
            vector_layers = self.get_vector_layers(workspace)

            # 格式化图层信息
            formatted_layers = []

            # 处理栅格图层
            for layer in raster_layers:
                formatted_layer = {
                    "id": layer.get("id", ""),
                    "name": layer.get("name", ""),
                    "type": "raster",
                    "initialLoad": False,
                    "protocol": "WMTS",
                    "workspace": workspace,
                    "layerName": layer.get("name", ""),
                    "theme": "",
                }
                formatted_layers.append(formatted_layer)

            # 处理矢量图层
            for layer in vector_layers:
                formatted_layer = {
                    "id": layer.get("id", ""),
                    "name": layer.get("name", ""),
                    "type": "vector",
                    "protocol": "WFS",
                    "workspace": workspace,
                    "layerName": layer.get("name", ""),
                    "initialLoad": False,
                    "theme": "",
                }

                # 添加默认样式信息（如果有）
                if "default_style" in layer:
                    formatted_layer["defaultStyle"] = layer.get("default_style")
                else:
                    formatted_layer["defaultStyle"] = "default_polygon"

                # 添加几何类型信息（如果有）并规范化类型名称
                if "geometry_type" in layer:
                    # 获取几何类型，此时应该已经是规范化的
                    formatted_layer["geometryType"] = layer.get("geometry_type")
                else:
                    # 默认为MultiPolygon类型
                    formatted_layer["geometryType"] = "MultiPolygon"

                formatted_layers.append(formatted_layer)

            self.logger.info(
                f"在工作区 '{workspace}' 中找到 {len(formatted_layers)} 个图层（{len(raster_layers)} 个栅格，{len(vector_layers)} 个矢量）"
            )
            return formatted_layers

        except Exception as e:
            self.logger.error(f"获取所有图层时出错: {str(e)}")
            return []

    def get_vector_layers(self, workspace):
        """
        获取指定工作区中的所有矢量图层
        与Flask版本完全一致的实现

        Args:
            workspace: 工作区名称

        Returns:
            矢量图层列表
        """
        try:
            # 检查工作区是否存在
            if not self.manager.check_workspace_exists(workspace):
                self.logger.error(f"工作区 '{workspace}' 不存在")
                return []

            # 使用GeoServer REST API获取数据存储列表
            base_url = self.manager.geo.service_url
            if not base_url.endswith("/rest"):
                base_url = f"{base_url}/rest"

            auth = (self.manager.geo.username, self.manager.geo.password)
            url = f"{base_url}/workspaces/{workspace}/datastores.json"

            self.logger.debug(f"正在请求数据存储列表: {url}")
            response = requests.get(url, auth=auth)

            if response.status_code != 200:
                self.logger.debug(
                    f"获取数据存储列表失败，状态码: {response.status_code}，可能没有矢量数据"
                )
                return []

            data = response.json()
            self.logger.debug(f"数据存储响应: {json.dumps(data, ensure_ascii=False)}")

            vector_layers = []

            # 处理数据存储列表
            if "dataStores" in data and "dataStore" in data["dataStores"]:
                for store in data["dataStores"]["dataStore"]:
                    store_name = store["name"]
                    self.logger.debug(f"找到数据存储: {store_name}")

                    # 获取存储下的要素类型列表
                    store_url = f"{base_url}/workspaces/{workspace}/datastores/{store_name}/featuretypes.json"
                    self.logger.debug(f"正在请求要素类型列表: {store_url}")
                    store_response = requests.get(store_url, auth=auth)

                    if store_response.status_code == 200:
                        store_data = store_response.json()
                        self.logger.debug(
                            f"要素类型响应: {json.dumps(store_data, ensure_ascii=False)}"
                        )

                        if (
                            "featureTypes" in store_data
                            and "featureType" in store_data["featureTypes"]
                        ):
                            for feature_type in store_data["featureTypes"]["featureType"]:
                                layer_info = {
                                    "name": feature_type["name"],
                                    "store": store_name,
                                    "workspace": workspace,
                                    "id": f"{workspace}:{feature_type['name']}",
                                }

                                # 获取图层的详细信息，包括边界框
                                detail_url = f"{base_url}/workspaces/{workspace}/datastores/{store_name}/featuretypes/{feature_type['name']}.json"
                                detail_response = requests.get(detail_url, auth=auth)

                                if detail_response.status_code == 200:
                                    detail_data = detail_response.json()
                                    if "featureType" in detail_data:
                                        feature_detail = detail_data["featureType"]

                                        # 提取边界框信息
                                        if "nativeBoundingBox" in feature_detail:
                                            bbox = feature_detail["nativeBoundingBox"]
                                            layer_info["bbox"] = {
                                                "minx": bbox.get("minx", 0),
                                                "miny": bbox.get("miny", 0),
                                                "maxx": bbox.get("maxx", 0),
                                                "maxy": bbox.get("maxy", 0),
                                                "crs": bbox.get("crs", "EPSG:4326"),
                                            }

                                vector_layers.append(layer_info)
                                self.logger.debug(f"添加矢量图层: {layer_info}")

            self.logger.info(f"在工作区 '{workspace}' 中找到 {len(vector_layers)} 个矢量图层")
            return vector_layers

        except Exception as e:
            self.logger.error(f"获取矢量图层列表时出错: {str(e)}")
            return []

    def _fetch_missing_bboxes(self, layers, workspace):
        """
        尝试获取缺失的边界框信息 - 与Flask版本完全一致

        Args:
            layers: 图层列表
            workspace: 工作区名称
        """
        # 首先检查是否有任何图层缺少边界框
        missing_bbox_layers = [layer for layer in layers if not layer.get("bbox")]

        if not missing_bbox_layers:
            self.logger.debug("所有图层都有边界框信息")
            return

        self.logger.debug(
            f"有 {len(missing_bbox_layers)} 个图层缺少边界框信息，尝试获取"
        )

        try:
            # 尝试从REST API获取
            self._fetch_bboxes_from_rest_api(missing_bbox_layers, workspace)
        except Exception as e:
            self.logger.error(f"获取边界框信息时出错: {str(e)}")

    def _fetch_bboxes_from_rest_api(self, layers, workspace):
        """
        从REST API获取边界框信息 - 与Flask版本完全一致

        Args:
            layers: 图层列表
            workspace: 工作区名称
        """
        try:
            base_url = self.manager.geo.service_url
            if not base_url.endswith("/rest"):
                base_url = f"{base_url}/rest"

            auth = (self.manager.geo.username, self.manager.geo.password)

            # 处理每个缺少边界框的图层
            for layer in layers:
                if not layer.get("bbox"):
                    layer_name = layer["name"]
                    store_name = layer["store"]

                    # 获取覆盖层详细信息
                    coverage_url = f"{base_url}/workspaces/{workspace}/coveragestores/{store_name}/coverages/{layer_name}.json"

                    self.logger.debug(f"请求覆盖层详细信息: {coverage_url}")
                    response = requests.get(coverage_url, auth=auth)

                    if response.status_code == 200:
                        data = response.json()

                        if "coverage" in data:
                            bbox = data["coverage"].get("latLonBoundingBox")
                            if bbox:
                                layer["bbox"] = bbox
                                self.logger.debug(
                                    f"为图层 {layer_name} 设置边界框: {bbox}"
                                )
                    else:
                        self.logger.warning(
                            f"获取覆盖层 '{layer_name}' 详细信息失败，状态码: {response.status_code}"
                        )

        except Exception as e:
            self.logger.error(f"从REST API获取边界框信息时出错: {str(e)}")

    def query_point(self, lat, lon, workspace, layer_name=None):
        """
        查询指定坐标点处的栅格值 - 与Flask版本完全一致
        这是主要的查询方法，被Django视图调用

        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称
            layer_name: 图层名称（可选）

        Returns:
            查询结果字典，格式与Flask版本完全一致
        """
        try:
            self.logger.info(f"开始查询坐标点 ({lat}, {lon}) 在工作区 '{workspace}' 中的栅格值")

            # 获取工作区中的所有栅格图层
            layers = self.get_raster_layers(workspace)

            if not layers:
                return {
                    'status': 'error',
                    'message': f'工作区 {workspace} 中没有找到栅格图层'
                }

            results = []

            # 如果指定了特定图层，只查询该图层
            if layer_name:
                target_layers = [layer for layer in layers if layer["name"] == layer_name]
                if not target_layers:
                    return {
                        'status': 'error',
                        'message': f'在工作区 {workspace} 中未找到图层 {layer_name}'
                    }
            else:
                target_layers = layers

            # 查询每个图层
            for layer in target_layers:
                layer_name = layer["name"]
                self.logger.debug(f"正在查询图层: {layer_name}")

                try:
                    # 使用GetFeatureInfo查询像素值
                    pixel_value = self.get_pixel_value(lat, lon, workspace, layer_name)

                    if pixel_value is not None:
                        results.append({
                            "layer": f"{workspace}:{layer_name}",
                            "value": pixel_value,
                            "coordinates": {"lat": lat, "lon": lon}
                        })
                        self.logger.debug(f"图层 {layer_name} 在坐标 ({lat}, {lon}) 处的值: {pixel_value}")
                    else:
                        self.logger.debug(f"图层 {layer_name} 在坐标 ({lat}, {lon}) 处没有有效值")

                except Exception as e:
                    self.logger.warning(f"查询图层 {layer_name} 时出错: {str(e)}")

            self.logger.info(f"查询完成，找到 {len(results)} 个有效结果")

            return {
                'status': 'success',
                'results': results,
                'coordinates': {'lat': lat, 'lon': lon},
                'workspace': workspace,
                'total_layers': len(target_layers),
                'layers_with_data': len(results)
            }

        except Exception as e:
            self.logger.error(f"查询栅格值时出错: {str(e)}")
            return {
                'status': 'error',
                'message': f'服务器内部错误: {str(e)}'
            }

    def get_pixel_value(self, lat, lon, workspace, layer_name):
        """
        获取栅格图层在指定坐标点的像素值 - 与Flask版本完全一致

        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称
            layer_name: 图层名称

        Returns:
            像素值字典，包含波段及其对应的值
        """
        try:
            # 验证参数
            if not all([lat, lon, workspace, layer_name]):
                self.logger.error("获取像素值需要提供纬度、经度、工作区和图层名")
                return None

            # 构建WMS GetFeatureInfo请求
            base_url = self.manager.geo.service_url
            if base_url.endswith("/rest"):
                base_url = base_url[:-5]  # 移除'/rest'以获取GeoServer基础URL

            # 确保URL正确
            if not base_url.endswith("/"):
                base_url += "/"

            wms_url = f"{base_url}wms"

            # 使用较小的BBOX以提高精度
            bbox_width = 0.0001  # 约10米
            bbox_height = 0.0001  # 约10米

            params = {
                "SERVICE": "WMS",
                "VERSION": "1.1.1",
                "REQUEST": "GetFeatureInfo",
                "LAYERS": f"{workspace}:{layer_name}",
                "QUERY_LAYERS": f"{workspace}:{layer_name}",
                "STYLES": "",
                "SRS": "EPSG:4326",
                "BBOX": f"{lon-bbox_width/2},{lat-bbox_height/2},{lon+bbox_width/2},{lat+bbox_height/2}",
                "WIDTH": 1,
                "HEIGHT": 1,
                "FORMAT": "image/png",
                "INFO_FORMAT": "application/json",
                "X": 0,
                "Y": 0,
                "FEATURE_COUNT": 1,
            }

            self.logger.debug(f"发送GetFeatureInfo请求获取像素值: {wms_url}")

            auth = (self.manager.geo.username, self.manager.geo.password)
            response = requests.get(wms_url, params=params, auth=auth)

            if response.status_code != 200:
                self.logger.error(f"获取像素值失败，状态码: {response.status_code}")
                return None

            try:
                # 解析JSON响应
                data = response.json()
                self.logger.debug(f"GetFeatureInfo响应: {data}")

                if "features" in data and len(data["features"]) > 0:
                    feature = data["features"][0]
                    if "properties" in feature:
                        # 提取所有属性作为像素值
                        pixel_values = feature["properties"]

                        # 过滤掉非数值属性
                        filtered_values = {}
                        for key, value in pixel_values.items():
                            # 尝试将值转换为数字
                            try:
                                if value is not None:
                                    if isinstance(value, (int, float)):
                                        filtered_values[key] = value
                                    else:
                                        try:
                                            filtered_values[key] = float(value)
                                        except (ValueError, TypeError):
                                            # 保留非数值属性
                                            filtered_values[key] = value
                            except Exception:
                                filtered_values[key] = value

                        return {
                            "coordinates": {"lat": lat, "lon": lon},
                            "layer": f"{workspace}:{layer_name}",
                            "values": filtered_values,
                        }

                # 如果无法获取像素值，尝试使用text/plain格式
                return self._get_pixel_value_text_format(
                    lat, lon, workspace, layer_name
                )

            except json.JSONDecodeError:
                # JSON解析失败，尝试使用text/plain格式
                return self._get_pixel_value_text_format(
                    lat, lon, workspace, layer_name
                )

        except Exception as e:
            self.logger.error(f"获取像素值时出错: {str(e)}")
            return None

    def _get_pixel_value_text_format(self, lat, lon, workspace, layer_name):
        """
        使用text/plain格式获取栅格图层在指定坐标点的像素值 - 与Flask版本完全一致

        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称
            layer_name: 图层名称

        Returns:
            像素值字典
        """
        try:
            # 构建WMS GetFeatureInfo请求
            base_url = self.manager.geo.service_url
            if base_url.endswith("/rest"):
                base_url = base_url[:-5]

            if not base_url.endswith("/"):
                base_url += "/"

            wms_url = f"{base_url}wms"

            bbox_width = 0.0001
            bbox_height = 0.0001

            params = {
                "SERVICE": "WMS",
                "VERSION": "1.1.1",
                "REQUEST": "GetFeatureInfo",
                "LAYERS": f"{workspace}:{layer_name}",
                "QUERY_LAYERS": f"{workspace}:{layer_name}",
                "STYLES": "",
                "SRS": "EPSG:4326",
                "BBOX": f"{lon-bbox_width/2},{lat-bbox_height/2},{lon+bbox_width/2},{lat+bbox_height/2}",
                "WIDTH": 1,
                "HEIGHT": 1,
                "FORMAT": "image/png",
                "INFO_FORMAT": "text/plain",
                "X": 0,
                "Y": 0,
                "FEATURE_COUNT": 1,
            }

            self.logger.debug("使用text/plain格式尝试获取像素值")

            auth = (self.manager.geo.username, self.manager.geo.password)
            response = requests.get(wms_url, params=params, auth=auth)

            if response.status_code != 200:
                self.logger.error(f"获取像素值失败，状态码: {response.status_code}")
                return None

            # 解析文本响应
            text_content = response.text.strip()
            self.logger.debug(f"GetFeatureInfo文本响应: {text_content}")

            if (
                not text_content
                or "no feature" in text_content.lower()
                or "no results" in text_content.lower()
            ):
                self.logger.warning("未找到像素值")
                return {
                    "coordinates": {"lat": lat, "lon": lon},
                    "layer": f"{workspace}:{layer_name}",
                    "values": {},
                    "message": "该坐标点无有效数据",
                }

            # 尝试从文本响应中提取像素值
            import re

            values = {}

            # 查找形如 "band1 = 123.45" 的模式
            band_pattern = re.compile(r"(?:band|Band|BAND)(\d+)\s*=\s*([0-9.-]+)")
            band_matches = band_pattern.findall(text_content)

            if band_matches:
                for band, value in band_matches:
                    try:
                        values[f"band{band}"] = float(value)
                    except ValueError:
                        values[f"band{band}"] = value

            # 查找形如 "Red = 123" 的模式
            color_pattern = re.compile(
                r"(?:Red|GREEN|Blue|Gray|R|G|B)\s*=\s*([0-9.-]+)"
            )
            color_matches = color_pattern.findall(text_content)

            if color_matches:
                colors = ["Red", "Green", "Blue", "Gray"]
                for i, value in enumerate(color_matches):
                    if i < len(colors):
                        try:
                            values[colors[i]] = float(value)
                        except ValueError:
                            values[colors[i]] = value

            # 通用键值对模式
            kv_pattern = re.compile(r"([a-zA-Z0-9_]+)\s*[:=]\s*([0-9.-]+)")
            kv_matches = kv_pattern.findall(text_content)

            if kv_matches:
                for key, value in kv_matches:
                    if key.lower() not in [k.lower() for k in values.keys()]:
                        try:
                            values[key] = float(value)
                        except ValueError:
                            values[key] = value

            # 如果没有找到任何匹配，但有内容，则返回原始文本
            if not values and text_content:
                values = {"raw_text": text_content}

            return {
                "coordinates": {"lat": lat, "lon": lon},
                "layer": f"{workspace}:{layer_name}",
                "values": values,
            }

        except Exception as e:
            self.logger.error(f"解析文本格式像素值时出错: {str(e)}")
            return None

    def query_point(self, lat, lon, workspace):
        """
        查询指定坐标点处的图层 - 与Flask版本完全一致
        这是Flask版本中被调用的主要方法

        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称

        Returns:
            包含该坐标点的图层列表
        """
        try:
            # 获取工作区中的所有栅格图层
            layers = self.get_raster_layers(workspace)

            if not layers:
                self.logger.warning(f"在工作区 '{workspace}' 中未找到栅格图层")
                return []

            # 筛选出包含该坐标点的图层
            result_layers = []

            for layer in layers:
                bbox = layer.get("bbox", {})

                # 检查图层是否有边界框信息
                if not bbox:
                    self.logger.warning(
                        f"图层 '{layer['name']}' 没有边界框信息，无法确定是否包含坐标点"
                    )
                    continue

                # 检查坐标点是否在边界框内
                try:
                    minx = float(bbox.get("minx", 0))
                    miny = float(bbox.get("miny", 0))
                    maxx = float(bbox.get("maxx", 0))
                    maxy = float(bbox.get("maxy", 0))

                    if minx <= lon <= maxx and miny <= lat <= maxy:
                        self.logger.debug(
                            f"坐标点 ({lat}, {lon}) 在图层 '{layer['name']}' 的边界框内"
                        )

                        # 检查图层在该点是否有有效数据
                        if self._check_point_data(lat, lon, workspace, layer["name"]):
                            self.logger.info(
                                f"图层 '{layer['name']}' 在坐标点 ({lat}, {lon}) 有有效数据"
                            )
                            result_layers.append(layer)
                    else:
                        self.logger.debug(
                            f"坐标点 ({lat}, {lon}) 不在图层 '{layer['name']}' 的边界框内"
                        )
                except (ValueError, TypeError) as e:
                    self.logger.warning(
                        f"处理图层 '{layer['name']}' 的边界框时出错: {str(e)}"
                    )

            self.logger.info(
                f"在坐标点 ({lat}, {lon}) 找到 {len(result_layers)} 个有效图层"
            )
            return result_layers

        except Exception as e:
            self.logger.error(f"查询坐标点处的图层时出错: {str(e)}")
            return []

    def _check_point_data(self, lat, lon, workspace, layer_name):
        """
        检查指定坐标点在特定图层中是否有有效数据 - 与Flask版本完全一致

        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称
            layer_name: 图层名称

        Returns:
            布尔值，表示该点是否有有效数据
        """
        try:
            # 构建WMS GetFeatureInfo请求URL
            base_url = self.manager.geo.service_url
            if base_url.endswith("/rest"):
                base_url = base_url[:-5]  # 去掉/rest后缀

            wms_url = f"{base_url}/wms"

            # 获取图层的边界框信息，用于计算宽度和高度
            layer_info = None
            layers = self.get_raster_layers(workspace)

            for layer in layers:
                if layer["name"] == layer_name:
                    layer_info = layer
                    break

            if not layer_info:
                self.logger.warning(f"未找到图层: {workspace}:{layer_name}")
                return False

            bbox = layer_info.get("bbox", {})
            if not bbox:
                self.logger.warning(f"图层 {workspace}:{layer_name} 没有边界框信息")
                return False

            # 计算请求的边界框（以查询点为中心的小区域）
            buffer = 0.001  # 约100米的缓冲区
            request_bbox = f"{lon-buffer},{lat-buffer},{lon+buffer},{lat+buffer}"

            # GetFeatureInfo请求参数
            params = {
                'SERVICE': 'WMS',
                'VERSION': '1.1.1',
                'REQUEST': 'GetFeatureInfo',
                'LAYERS': f'{workspace}:{layer_name}',
                'QUERY_LAYERS': f'{workspace}:{layer_name}',
                'STYLES': '',
                'BBOX': request_bbox,
                'WIDTH': '3',
                'HEIGHT': '3',
                'FORMAT': 'image/png',
                'INFO_FORMAT': 'application/json',
                'SRS': 'EPSG:4326',
                'X': '1',
                'Y': '1'
            }

            self.logger.debug(f"检查点数据请求URL: {wms_url}")
            self.logger.debug(f"请求参数: {params}")

            # 发送请求
            auth = (self.manager.geo.username, self.manager.geo.password)
            response = requests.get(wms_url, params=params, auth=auth, timeout=10)

            if response.status_code != 200:
                self.logger.warning(f"GetFeatureInfo请求失败，状态码: {response.status_code}")
                return False

            # 解析响应
            try:
                data = response.json()

                # 检查是否有有效的特征数据
                if 'features' in data and len(data['features']) > 0:
                    feature = data['features'][0]
                    if 'properties' in feature:
                        properties = feature['properties']

                        # 检查是否有非空的属性值
                        for key, value in properties.items():
                            if value is not None and str(value).strip() != '':
                                self.logger.debug(f"在图层 {layer_name} 找到有效数据: {key}={value}")
                                return True

                return False

            except json.JSONDecodeError:
                self.logger.warning(f"无法解析GetFeatureInfo响应为JSON")
                return False

        except Exception as e:
            self.logger.warning(f"检查点数据时出错: {str(e)}")
            return False

    def query_point_with_values(self, lat, lon, workspace):
        """
        查询给定坐标点处的栅格图层，并返回有效像素值的图层 - 与Flask版本完全一致

        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称

        Returns:
            包含该坐标点有效数据的图层信息列表，每个图层包含像素值
        """
        try:
            self.logger.info(
                f"开始查询坐标点 ({lat}, {lon}) 在工作区 '{workspace}' 中的图层及像素值"
            )

            # 获取工作区中的所有栅格图层
            raster_layers = self.get_raster_layers(workspace)
            if not raster_layers:
                self.logger.warning(f"工作区 '{workspace}' 中没有找到栅格图层")
                return []

            result_layers = []

            # 对每个栅格图层进行检查
            for layer in raster_layers:
                # 首先检查坐标点是否在图层的边界框内
                bbox = layer.get("bbox", {})
                if not bbox:
                    self.logger.warning(f"图层 '{layer['name']}' 没有边界框信息，跳过")
                    continue

                try:
                    minx = float(bbox.get("minx", 0))
                    miny = float(bbox.get("miny", 0))
                    maxx = float(bbox.get("maxx", 0))
                    maxy = float(bbox.get("maxy", 0))

                    self.logger.debug(
                        f"检查图层 '{layer['name']}' 的边界框: ({minx}, {miny}) - ({maxx}, {maxy})"
                    )

                    # 如果点不在边界框内，则跳过
                    if not (minx <= lon <= maxx and miny <= lat <= maxy):
                        self.logger.debug(
                            f"坐标点 ({lat}, {lon}) 不在图层 '{layer['name']}' 的边界框内，跳过"
                        )
                        continue

                    self.logger.debug(
                        f"坐标点 ({lat}, {lon}) 在图层 '{layer['name']}' 的边界框内，获取像素值"
                    )

                    # 获取该点的像素值
                    pixel_data = self.get_pixel_value(
                        lat, lon, workspace, layer["name"]
                    )

                    # 如果获取像素值失败或没有像素值，跳过该图层
                    if not pixel_data or not pixel_data.get("values"):
                        self.logger.debug(
                            f"图层 '{layer['name']}' 在该坐标点无有效像素值，跳过"
                        )
                        continue

                    pixel_values = pixel_data.get("values", {})

                    # 检查是否为全黑(0,0,0)或全白(255,255,255)
                    is_black = False
                    is_white = False
                    has_transparent_alpha = False

                    # 检查是否有透明alpha通道
                    if "ALPHA_BAND" in pixel_values and pixel_values["ALPHA_BAND"] == 0:
                        has_transparent_alpha = True
                        self.logger.debug(
                            f"图层 '{layer['name']}' 在该坐标点ALPHA_BAND为0（完全透明），跳过"
                        )

                    # 检查是否有RGB值
                    if all(key in pixel_values for key in ["Red", "Green", "Blue"]):
                        rgb_values = [
                            pixel_values.get("Red", 0),
                            pixel_values.get("Green", 0),
                            pixel_values.get("Blue", 0),
                        ]
                        is_black = all(v == 0 for v in rgb_values)
                        is_white = all(v == 255 for v in rgb_values)

                    # 检查是否有band值（多波段栅格）
                    elif any(key.lower().startswith("band") for key in pixel_values):
                        band_values = [
                            v
                            for k, v in pixel_values.items()
                            if k.lower().startswith("band")
                        ]
                        is_black = all(v == 0 for v in band_values)
                        is_white = all(v == 255 for v in band_values)

                    # 检查其他可能的像素值格式
                    elif len(pixel_values) == 1:
                        single_value = list(pixel_values.values())[0]
                        is_black = single_value == 0
                        is_white = single_value == 255

                    # 如果是全黑或全白或透明，跳过
                    if is_black:
                        self.logger.debug(
                            f"图层 '{layer['name']}' 在该坐标点像素值为全黑，跳过"
                        )
                        continue
                    elif is_white:
                        self.logger.debug(
                            f"图层 '{layer['name']}' 在该坐标点像素值为全白，跳过"
                        )
                        continue
                    elif has_transparent_alpha:
                        continue

                    # 构建结果对象
                    layer_result = {
                        "name": layer["name"],
                        "store": layer["store"],
                        "workspace": layer["workspace"],
                        "id": layer["id"],
                        "bbox": layer["bbox"],
                        "pixel_values": pixel_values,
                    }

                    result_layers.append(layer_result)
                    self.logger.info(
                        f"图层 '{layer['name']}' 在该坐标点有有效数据，添加到结果中"
                    )

                except (ValueError, TypeError) as e:
                    self.logger.warning(f"处理图层 '{layer['name']}' 时出错: {str(e)}")

            self.logger.info(
                f"在坐标点 ({lat}, {lon}) 处找到 {len(result_layers)} 个有有效数据的栅格图层"
            )
            return result_layers

        except Exception as e:
            self.logger.error(f"查询坐标点 ({lat}, {lon}) 时出错: {str(e)}")
            return []
