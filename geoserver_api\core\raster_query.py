#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer栅格数据坐标查询模块 (Django版本)
"""

import os
import sys
import logging
import requests
from urllib.parse import urljoin
import json
import numpy as np
from osgeo import gdal
import xml.etree.ElementTree as ET
from django.conf import settings

from .manager import GeoServerManager

# 获取Django配置的日志记录器
logger = logging.getLogger('geoserver_api')


class GeoServerRasterQuery:
    """用于在GeoServer上查询栅格数据的类"""

    def __init__(self, geoserver_manager=None):
        """
        初始化查询类

        Args:
            geoserver_manager: GeoServerManager实例，如果不提供则创建新的实例
        """
        self.manager = geoserver_manager if geoserver_manager else GeoServerManager()
        self.logger = logger

    def get_raster_layers(self, workspace):
        """
        获取指定工作区中的所有栅格图层

        Args:
            workspace: 工作区名称

        Returns:
            栅格图层列表
        """
        try:
            # 检查工作区是否存在
            if not self.manager.check_workspace_exists(workspace):
                self.logger.error(f"工作区 '{workspace}' 不存在")
                return []

            # 使用GeoServer REST API获取图层列表
            base_url = self.manager.geo.service_url
            if not base_url.endswith("/rest"):
                base_url = f"{base_url}/rest"

            auth = (self.manager.geo.username, self.manager.geo.password)
            url = f"{base_url}/workspaces/{workspace}/coveragestores.json"

            self.logger.debug(f"正在请求覆盖层存储列表: {url}")
            response = requests.get(url, auth=auth)

            if response.status_code != 200:
                self.logger.error(
                    f"获取栅格存储列表失败，状态码: {response.status_code}"
                )
                return []

            data = response.json()
            self.logger.debug(f"覆盖层存储响应: {json.dumps(data, ensure_ascii=False)}")

            raster_layers = []

            # 处理覆盖层存储列表
            if "coverageStores" in data and "coverageStore" in data["coverageStores"]:
                for store in data["coverageStores"]["coverageStore"]:
                    store_name = store["name"]
                    self.logger.debug(f"找到覆盖层存储: {store_name}")

                    # 获取存储下的图层列表
                    store_url = f"{base_url}/workspaces/{workspace}/coveragestores/{store_name}/coverages.json"
                    self.logger.debug(f"正在请求覆盖层列表: {store_url}")
                    store_response = requests.get(store_url, auth=auth)

                    if store_response.status_code == 200:
                        store_data = store_response.json()
                        self.logger.debug(
                            f"覆盖层响应: {json.dumps(store_data, ensure_ascii=False)}"
                        )

                        if (
                            "coverages" in store_data
                            and "coverage" in store_data["coverages"]
                        ):
                            for coverage in store_data["coverages"]["coverage"]:
                                layer_info = {
                                    "name": coverage["name"],
                                    "store": store_name,
                                    "workspace": workspace,
                                    "id": f"{workspace}:{coverage['name']}",
                                    "bbox": coverage.get("latLonBoundingBox", {}),
                                }
                                self.logger.debug(
                                    f"找到栅格图层: {layer_info['id']}"
                                )
                                raster_layers.append(layer_info)

            self.logger.info(f"在工作区 '{workspace}' 中找到 {len(raster_layers)} 个栅格图层")
            return raster_layers

        except Exception as e:
            self.logger.error(f"获取栅格图层列表时出错: {str(e)}")
            return []

    def query_raster_value(self, lat, lon, workspace, layer_name=None):
        """
        查询指定坐标点的栅格值

        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称
            layer_name: 图层名称，如果不指定则查询所有栅格图层

        Returns:
            查询结果字典
        """
        try:
            self.logger.info(f"查询坐标 ({lat}, {lon}) 在工作区 '{workspace}' 中的栅格值")

            # 如果指定了图层名称，只查询该图层
            if layer_name:
                layers_to_query = [{"name": layer_name, "workspace": workspace, "id": f"{workspace}:{layer_name}"}]
            else:
                # 获取工作区中的所有栅格图层
                layers_to_query = self.get_raster_layers(workspace)

            if not layers_to_query:
                self.logger.warning(f"在工作区 '{workspace}' 中没有找到栅格图层")
                return {
                    "status": "error",
                    "message": f"在工作区 '{workspace}' 中没有找到栅格图层",
                    "results": []
                }

            results = []
            
            for layer in layers_to_query:
                try:
                    # 使用WMS GetFeatureInfo查询栅格值
                    value = self._query_single_layer(lat, lon, layer)
                    if value is not None:
                        results.append({
                            "layer": layer["id"],
                            "value": value,
                            "coordinates": {"lat": lat, "lon": lon}
                        })
                        self.logger.debug(f"图层 {layer['id']} 在坐标 ({lat}, {lon}) 的值: {value}")
                    else:
                        self.logger.debug(f"图层 {layer['id']} 在坐标 ({lat}, {lon}) 没有有效值")
                        
                except Exception as e:
                    self.logger.error(f"查询图层 {layer['id']} 时出错: {str(e)}")
                    continue

            return {
                "status": "success",
                "coordinates": {"lat": lat, "lon": lon},
                "workspace": workspace,
                "results": results,
                "total_layers": len(layers_to_query),
                "layers_with_data": len(results)
            }

        except Exception as e:
            self.logger.error(f"查询栅格值时出错: {str(e)}")
            return {
                "status": "error",
                "message": f"查询栅格值时出错: {str(e)}",
                "results": []
            }

    def _query_single_layer(self, lat, lon, layer):
        """
        查询单个图层的栅格值

        Args:
            lat: 纬度
            lon: 经度
            layer: 图层信息字典

        Returns:
            栅格值或None
        """
        try:
            # 构建WMS GetFeatureInfo请求
            base_url = self.manager.geo.service_url
            if base_url.endswith("/rest"):
                base_url = base_url[:-5]  # 移除 /rest
            
            wms_url = f"{base_url}/wms"
            
            params = {
                'SERVICE': 'WMS',
                'VERSION': '1.1.1',
                'REQUEST': 'GetFeatureInfo',
                'LAYERS': layer["id"],
                'QUERY_LAYERS': layer["id"],
                'STYLES': '',
                'BBOX': f'{lon-0.01},{lat-0.01},{lon+0.01},{lat+0.01}',
                'WIDTH': '101',
                'HEIGHT': '101',
                'FORMAT': 'image/png',
                'INFO_FORMAT': 'application/json',
                'SRS': 'EPSG:4326',
                'X': '50',
                'Y': '50'
            }

            auth = (self.manager.geo.username, self.manager.geo.password)
            response = requests.get(wms_url, params=params, auth=auth, timeout=30)

            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'features' in data and len(data['features']) > 0:
                        feature = data['features'][0]
                        if 'properties' in feature:
                            # 尝试获取栅格值
                            for key, value in feature['properties'].items():
                                if key.lower() not in ['x', 'y', 'lon', 'lat', 'longitude', 'latitude']:
                                    return value
                    return None
                except json.JSONDecodeError:
                    # 如果不是JSON格式，尝试解析为文本
                    text = response.text.strip()
                    if text and text != 'null' and 'no data' not in text.lower():
                        return text
                    return None
            else:
                self.logger.warning(f"WMS查询失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"查询单个图层时出错: {str(e)}")
            return None
