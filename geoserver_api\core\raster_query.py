#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer栅格数据坐标查询模块 (Django版本)
基于Flask版本完全重写，确保功能和返回数据一致
"""

import os
import sys
import logging
import requests
from urllib.parse import urljoin
import json
import numpy as np
from osgeo import gdal
import xml.etree.ElementTree as ET
from django.conf import settings

from .manager import GeoServerManager

# 获取Django配置的日志记录器
logger = logging.getLogger('geoserver_api')


class GeoServerRasterQuery:
    """用于在GeoServer上查询栅格数据的类 - 与Flask版本完全一致"""

    def __init__(self, geoserver_manager=None):
        """
        初始化查询类

        Args:
            geoserver_manager: GeoServerManager实例，如果不提供则创建新的实例
        """
        self.manager = geoserver_manager if geoserver_manager else GeoServerManager()
        self.logger = logger

    def get_raster_layers(self, workspace):
        """
        获取指定工作区中的所有栅格图层
        与Flask版本完全一致的实现

        Args:
            workspace: 工作区名称

        Returns:
            栅格图层列表
        """
        try:
            # 检查工作区是否存在
            if not self.manager.check_workspace_exists(workspace):
                self.logger.error(f"工作区 '{workspace}' 不存在")
                return []

            # 使用GeoServer REST API获取图层列表
            base_url = self.manager.geo.service_url
            if not base_url.endswith("/rest"):
                base_url = f"{base_url}/rest"

            auth = (self.manager.geo.username, self.manager.geo.password)
            url = f"{base_url}/workspaces/{workspace}/coveragestores.json"

            self.logger.debug(f"正在请求覆盖层存储列表: {url}")
            response = requests.get(url, auth=auth)

            if response.status_code != 200:
                self.logger.error(
                    f"获取栅格存储列表失败，状态码: {response.status_code}"
                )
                return []

            data = response.json()
            self.logger.debug(f"覆盖层存储响应: {json.dumps(data, ensure_ascii=False)}")

            raster_layers = []

            # 处理覆盖层存储列表
            if "coverageStores" in data and "coverageStore" in data["coverageStores"]:
                for store in data["coverageStores"]["coverageStore"]:
                    store_name = store["name"]
                    self.logger.debug(f"找到覆盖层存储: {store_name}")

                    # 获取存储下的图层列表
                    store_url = f"{base_url}/workspaces/{workspace}/coveragestores/{store_name}/coverages.json"
                    self.logger.debug(f"正在请求覆盖层列表: {store_url}")
                    store_response = requests.get(store_url, auth=auth)

                    if store_response.status_code == 200:
                        store_data = store_response.json()
                        self.logger.debug(
                            f"覆盖层响应: {json.dumps(store_data, ensure_ascii=False)}"
                        )

                        if (
                            "coverages" in store_data
                            and "coverage" in store_data["coverages"]
                        ):
                            for coverage in store_data["coverages"]["coverage"]:
                                layer_info = {
                                    "name": coverage["name"],
                                    "store": store_name,
                                    "workspace": workspace,
                                    "id": f"{workspace}:{coverage['name']}",
                                }

                                # 获取图层的详细信息，包括边界框
                                detail_url = f"{base_url}/workspaces/{workspace}/coveragestores/{store_name}/coverages/{coverage['name']}.json"
                                detail_response = requests.get(detail_url, auth=auth)

                                if detail_response.status_code == 200:
                                    detail_data = detail_response.json()
                                    if "coverage" in detail_data:
                                        coverage_detail = detail_data["coverage"]

                                        # 提取边界框信息
                                        if "nativeBoundingBox" in coverage_detail:
                                            bbox = coverage_detail["nativeBoundingBox"]
                                            layer_info["bbox"] = {
                                                "minx": bbox.get("minx", 0),
                                                "miny": bbox.get("miny", 0),
                                                "maxx": bbox.get("maxx", 0),
                                                "maxy": bbox.get("maxy", 0),
                                                "crs": bbox.get("crs", "EPSG:4326"),
                                            }

                                        # 提取其他有用信息
                                        if "dimensions" in coverage_detail:
                                            layer_info["dimensions"] = coverage_detail[
                                                "dimensions"
                                            ]

                                raster_layers.append(layer_info)
                                self.logger.debug(f"添加栅格图层: {layer_info}")

            self.logger.info(f"在工作区 '{workspace}' 中找到 {len(raster_layers)} 个栅格图层")
            return raster_layers

        except Exception as e:
            self.logger.error(f"获取栅格图层列表时出错: {str(e)}")
            return []

    def get_all_layers(self, workspace):
        """
        获取指定工作区中的所有图层（包括栅格和矢量）
        与Flask版本完全一致的实现

        Args:
            workspace: 工作区名称

        Returns:
            所有图层列表
        """
        try:
            all_layers = []

            # 获取栅格图层
            raster_layers = self.get_raster_layers(workspace)
            for layer in raster_layers:
                layer["type"] = "raster"
                all_layers.append(layer)

            # 获取矢量图层
            vector_layers = self.get_vector_layers(workspace)
            for layer in vector_layers:
                layer["type"] = "vector"
                all_layers.append(layer)

            self.logger.info(f"在工作区 '{workspace}' 中找到总共 {len(all_layers)} 个图层")
            return all_layers

        except Exception as e:
            self.logger.error(f"获取所有图层列表时出错: {str(e)}")
            return []

    def get_vector_layers(self, workspace):
        """
        获取指定工作区中的所有矢量图层
        与Flask版本完全一致的实现

        Args:
            workspace: 工作区名称

        Returns:
            矢量图层列表
        """
        try:
            # 检查工作区是否存在
            if not self.manager.check_workspace_exists(workspace):
                self.logger.error(f"工作区 '{workspace}' 不存在")
                return []

            # 使用GeoServer REST API获取数据存储列表
            base_url = self.manager.geo.service_url
            if not base_url.endswith("/rest"):
                base_url = f"{base_url}/rest"

            auth = (self.manager.geo.username, self.manager.geo.password)
            url = f"{base_url}/workspaces/{workspace}/datastores.json"

            self.logger.debug(f"正在请求数据存储列表: {url}")
            response = requests.get(url, auth=auth)

            if response.status_code != 200:
                self.logger.debug(
                    f"获取数据存储列表失败，状态码: {response.status_code}，可能没有矢量数据"
                )
                return []

            data = response.json()
            self.logger.debug(f"数据存储响应: {json.dumps(data, ensure_ascii=False)}")

            vector_layers = []

            # 处理数据存储列表
            if "dataStores" in data and "dataStore" in data["dataStores"]:
                for store in data["dataStores"]["dataStore"]:
                    store_name = store["name"]
                    self.logger.debug(f"找到数据存储: {store_name}")

                    # 获取存储下的要素类型列表
                    store_url = f"{base_url}/workspaces/{workspace}/datastores/{store_name}/featuretypes.json"
                    self.logger.debug(f"正在请求要素类型列表: {store_url}")
                    store_response = requests.get(store_url, auth=auth)

                    if store_response.status_code == 200:
                        store_data = store_response.json()
                        self.logger.debug(
                            f"要素类型响应: {json.dumps(store_data, ensure_ascii=False)}"
                        )

                        if (
                            "featureTypes" in store_data
                            and "featureType" in store_data["featureTypes"]
                        ):
                            for feature_type in store_data["featureTypes"]["featureType"]:
                                layer_info = {
                                    "name": feature_type["name"],
                                    "store": store_name,
                                    "workspace": workspace,
                                    "id": f"{workspace}:{feature_type['name']}",
                                }

                                # 获取图层的详细信息，包括边界框
                                detail_url = f"{base_url}/workspaces/{workspace}/datastores/{store_name}/featuretypes/{feature_type['name']}.json"
                                detail_response = requests.get(detail_url, auth=auth)

                                if detail_response.status_code == 200:
                                    detail_data = detail_response.json()
                                    if "featureType" in detail_data:
                                        feature_detail = detail_data["featureType"]

                                        # 提取边界框信息
                                        if "nativeBoundingBox" in feature_detail:
                                            bbox = feature_detail["nativeBoundingBox"]
                                            layer_info["bbox"] = {
                                                "minx": bbox.get("minx", 0),
                                                "miny": bbox.get("miny", 0),
                                                "maxx": bbox.get("maxx", 0),
                                                "maxy": bbox.get("maxy", 0),
                                                "crs": bbox.get("crs", "EPSG:4326"),
                                            }

                                vector_layers.append(layer_info)
                                self.logger.debug(f"添加矢量图层: {layer_info}")

            self.logger.info(f"在工作区 '{workspace}' 中找到 {len(vector_layers)} 个矢量图层")
            return vector_layers

        except Exception as e:
            self.logger.error(f"获取矢量图层列表时出错: {str(e)}")
            return []

    def query_point(self, lat, lon, workspace, layer_name=None):
        """
        查询指定坐标点处的栅格值 - 与Flask版本完全一致
        这是主要的查询方法，被Django视图调用

        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称
            layer_name: 图层名称（可选）

        Returns:
            查询结果字典，格式与Flask版本完全一致
        """
        try:
            self.logger.info(f"开始查询坐标点 ({lat}, {lon}) 在工作区 '{workspace}' 中的栅格值")

            # 获取工作区中的所有栅格图层
            layers = self.get_raster_layers(workspace)

            if not layers:
                return {
                    'status': 'error',
                    'message': f'工作区 {workspace} 中没有找到栅格图层'
                }

            results = []

            # 如果指定了特定图层，只查询该图层
            if layer_name:
                target_layers = [layer for layer in layers if layer["name"] == layer_name]
                if not target_layers:
                    return {
                        'status': 'error',
                        'message': f'在工作区 {workspace} 中未找到图层 {layer_name}'
                    }
            else:
                target_layers = layers

            # 查询每个图层
            for layer in target_layers:
                layer_name = layer["name"]
                self.logger.debug(f"正在查询图层: {layer_name}")

                try:
                    # 使用GetFeatureInfo查询像素值
                    pixel_value = self.get_pixel_value(lat, lon, workspace, layer_name)

                    if pixel_value is not None:
                        results.append({
                            "layer": f"{workspace}:{layer_name}",
                            "value": pixel_value,
                            "coordinates": {"lat": lat, "lon": lon}
                        })
                        self.logger.debug(f"图层 {layer_name} 在坐标 ({lat}, {lon}) 处的值: {pixel_value}")
                    else:
                        self.logger.debug(f"图层 {layer_name} 在坐标 ({lat}, {lon}) 处没有有效值")

                except Exception as e:
                    self.logger.warning(f"查询图层 {layer_name} 时出错: {str(e)}")

            self.logger.info(f"查询完成，找到 {len(results)} 个有效结果")

            return {
                'status': 'success',
                'results': results,
                'coordinates': {'lat': lat, 'lon': lon},
                'workspace': workspace,
                'total_layers': len(target_layers),
                'layers_with_data': len(results)
            }

        except Exception as e:
            self.logger.error(f"查询栅格值时出错: {str(e)}")
            return {
                'status': 'error',
                'message': f'服务器内部错误: {str(e)}'
            }

    def get_pixel_value(self, lat, lon, workspace, layer_name):
        """
        获取指定坐标点的像素值 - 与Flask版本完全一致
        使用GeoServer的GetFeatureInfo请求

        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称
            layer_name: 图层名称

        Returns:
            像素值或None
        """
        try:
            # 构建GetFeatureInfo请求URL
            base_url = self.manager.geo.service_url
            if base_url.endswith("/rest"):
                base_url = base_url[:-5]  # 移除/rest后缀

            wms_url = f"{base_url}/wms"

            # GetFeatureInfo请求参数
            params = {
                'SERVICE': 'WMS',
                'VERSION': '1.1.1',
                'REQUEST': 'GetFeatureInfo',
                'LAYERS': f'{workspace}:{layer_name}',
                'QUERY_LAYERS': f'{workspace}:{layer_name}',
                'STYLES': '',
                'BBOX': f'{lon-0.01},{lat-0.01},{lon+0.01},{lat+0.01}',
                'WIDTH': '101',
                'HEIGHT': '101',
                'FORMAT': 'image/png',
                'INFO_FORMAT': 'application/json',
                'SRS': 'EPSG:4326',
                'X': '50',
                'Y': '50'
            }

            self.logger.debug(f"GetFeatureInfo请求URL: {wms_url}")
            self.logger.debug(f"请求参数: {params}")

            # 发送请求
            auth = (self.manager.geo.username, self.manager.geo.password)
            response = requests.get(wms_url, params=params, auth=auth, timeout=30)

            if response.status_code != 200:
                self.logger.error(f"GetFeatureInfo请求失败，状态码: {response.status_code}")
                self.logger.error(f"响应内容: {response.text}")
                return None

            # 解析JSON响应
            try:
                data = response.json()
                self.logger.debug(f"GetFeatureInfo响应: {json.dumps(data, ensure_ascii=False)}")

                # 提取像素值
                if 'features' in data and len(data['features']) > 0:
                    feature = data['features'][0]
                    if 'properties' in feature:
                        properties = feature['properties']

                        # 查找第一个非空的属性值作为像素值
                        for key, value in properties.items():
                            if value is not None and key.lower() != 'id':
                                try:
                                    # 尝试转换为数字
                                    if isinstance(value, (int, float)):
                                        return value
                                    elif isinstance(value, str):
                                        # 尝试转换字符串为数字
                                        if value.replace('.', '').replace('-', '').isdigit():
                                            return float(value) if '.' in value else int(value)
                                        else:
                                            return value
                                    else:
                                        return value
                                except (ValueError, TypeError):
                                    return value

                return None

            except json.JSONDecodeError as e:
                self.logger.error(f"解析GetFeatureInfo响应JSON失败: {str(e)}")
                self.logger.error(f"响应内容: {response.text}")
                return None

        except Exception as e:
            self.logger.error(f"获取像素值时出错: {str(e)}")
            return None

    def query_point(self, lat, lon, workspace):
        """
        查询指定坐标点处的图层 - 与Flask版本完全一致
        这是Flask版本中被调用的主要方法

        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称

        Returns:
            包含该坐标点的图层列表
        """
        try:
            # 获取工作区中的所有栅格图层
            layers = self.get_raster_layers(workspace)

            if not layers:
                self.logger.warning(f"在工作区 '{workspace}' 中未找到栅格图层")
                return []

            # 筛选出包含该坐标点的图层
            result_layers = []

            for layer in layers:
                bbox = layer.get("bbox", {})

                # 检查图层是否有边界框信息
                if not bbox:
                    self.logger.warning(
                        f"图层 '{layer['name']}' 没有边界框信息，无法确定是否包含坐标点"
                    )
                    continue

                # 检查坐标点是否在边界框内
                try:
                    minx = float(bbox.get("minx", 0))
                    miny = float(bbox.get("miny", 0))
                    maxx = float(bbox.get("maxx", 0))
                    maxy = float(bbox.get("maxy", 0))

                    if minx <= lon <= maxx and miny <= lat <= maxy:
                        self.logger.debug(
                            f"坐标点 ({lat}, {lon}) 在图层 '{layer['name']}' 的边界框内"
                        )

                        # 检查图层在该点是否有有效数据
                        if self._check_point_data(lat, lon, workspace, layer["name"]):
                            self.logger.info(
                                f"图层 '{layer['name']}' 在坐标点 ({lat}, {lon}) 有有效数据"
                            )
                            result_layers.append(layer)
                    else:
                        self.logger.debug(
                            f"坐标点 ({lat}, {lon}) 不在图层 '{layer['name']}' 的边界框内"
                        )
                except (ValueError, TypeError) as e:
                    self.logger.warning(
                        f"处理图层 '{layer['name']}' 的边界框时出错: {str(e)}"
                    )

            self.logger.info(
                f"在坐标点 ({lat}, {lon}) 找到 {len(result_layers)} 个有效图层"
            )
            return result_layers

        except Exception as e:
            self.logger.error(f"查询坐标点处的图层时出错: {str(e)}")
            return []

    def _check_point_data(self, lat, lon, workspace, layer_name):
        """
        检查指定坐标点在特定图层中是否有有效数据 - 与Flask版本完全一致

        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称
            layer_name: 图层名称

        Returns:
            布尔值，表示该点是否有有效数据
        """
        try:
            # 构建WMS GetFeatureInfo请求URL
            base_url = self.manager.geo.service_url
            if base_url.endswith("/rest"):
                base_url = base_url[:-5]  # 去掉/rest后缀

            wms_url = f"{base_url}/wms"

            # 获取图层的边界框信息，用于计算宽度和高度
            layer_info = None
            layers = self.get_raster_layers(workspace)

            for layer in layers:
                if layer["name"] == layer_name:
                    layer_info = layer
                    break

            if not layer_info:
                self.logger.warning(f"未找到图层: {workspace}:{layer_name}")
                return False

            bbox = layer_info.get("bbox", {})
            if not bbox:
                self.logger.warning(f"图层 {workspace}:{layer_name} 没有边界框信息")
                return False

            # 计算请求的边界框（以查询点为中心的小区域）
            buffer = 0.001  # 约100米的缓冲区
            request_bbox = f"{lon-buffer},{lat-buffer},{lon+buffer},{lat+buffer}"

            # GetFeatureInfo请求参数
            params = {
                'SERVICE': 'WMS',
                'VERSION': '1.1.1',
                'REQUEST': 'GetFeatureInfo',
                'LAYERS': f'{workspace}:{layer_name}',
                'QUERY_LAYERS': f'{workspace}:{layer_name}',
                'STYLES': '',
                'BBOX': request_bbox,
                'WIDTH': '3',
                'HEIGHT': '3',
                'FORMAT': 'image/png',
                'INFO_FORMAT': 'application/json',
                'SRS': 'EPSG:4326',
                'X': '1',
                'Y': '1'
            }

            self.logger.debug(f"检查点数据请求URL: {wms_url}")
            self.logger.debug(f"请求参数: {params}")

            # 发送请求
            auth = (self.manager.geo.username, self.manager.geo.password)
            response = requests.get(wms_url, params=params, auth=auth, timeout=10)

            if response.status_code != 200:
                self.logger.warning(f"GetFeatureInfo请求失败，状态码: {response.status_code}")
                return False

            # 解析响应
            try:
                data = response.json()

                # 检查是否有有效的特征数据
                if 'features' in data and len(data['features']) > 0:
                    feature = data['features'][0]
                    if 'properties' in feature:
                        properties = feature['properties']

                        # 检查是否有非空的属性值
                        for key, value in properties.items():
                            if value is not None and str(value).strip() != '':
                                self.logger.debug(f"在图层 {layer_name} 找到有效数据: {key}={value}")
                                return True

                return False

            except json.JSONDecodeError:
                self.logger.warning(f"无法解析GetFeatureInfo响应为JSON")
                return False

        except Exception as e:
            self.logger.warning(f"检查点数据时出错: {str(e)}")
            return False

    def query_point_with_values(self, lat, lon, workspace):
        """
        查询指定坐标点处的所有栅格图层并获取有效像素值 - 与Flask版本完全一致

        Args:
            lat: 纬度
            lon: 经度
            workspace: 工作区名称

        Returns:
            包含有效数据的图层及其像素值列表
        """
        try:
            # 获取工作区中的所有栅格图层
            layers = self.get_raster_layers(workspace)

            if not layers:
                self.logger.warning(f"在工作区 '{workspace}' 中未找到栅格图层")
                return []

            result_layers = []

            for layer in layers:
                layer_name = layer["name"]
                self.logger.debug(f"正在查询图层: {layer_name}")

                try:
                    # 检查坐标点是否在图层边界框内
                    bbox = layer.get("bbox", {})
                    if bbox:
                        minx = float(bbox.get("minx", 0))
                        miny = float(bbox.get("miny", 0))
                        maxx = float(bbox.get("maxx", 0))
                        maxy = float(bbox.get("maxy", 0))

                        if minx <= lon <= maxx and miny <= lat <= maxy:
                            # 获取像素值
                            pixel_value = self.get_pixel_value(lat, lon, workspace, layer_name)

                            if pixel_value is not None:
                                layer_result = {
                                    "name": layer["name"],
                                    "store": layer["store"],
                                    "workspace": layer["workspace"],
                                    "id": layer["id"],
                                    "pixel_value": pixel_value,
                                    "coordinates": {"lat": lat, "lon": lon},
                                    "bbox": bbox
                                }
                                result_layers.append(layer_result)
                                self.logger.debug(f"图层 {layer_name} 在坐标 ({lat}, {lon}) 处的值: {pixel_value}")
                            else:
                                self.logger.debug(f"图层 {layer_name} 在坐标 ({lat}, {lon}) 处没有有效值")
                        else:
                            self.logger.debug(f"坐标点 ({lat}, {lon}) 不在图层 {layer_name} 的边界框内")
                    else:
                        self.logger.warning(f"图层 {layer_name} 没有边界框信息")

                except Exception as e:
                    self.logger.warning(f"查询图层 {layer_name} 时出错: {str(e)}")

            self.logger.info(f"在坐标点 ({lat}, {lon}) 找到 {len(result_layers)} 个有有效数据的图层")
            return result_layers

        except Exception as e:
            self.logger.error(f"查询坐标点有效像素值时出错: {str(e)}")
            return []
