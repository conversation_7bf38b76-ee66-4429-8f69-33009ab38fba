'''
Author: 吴博文 <EMAIL>
Date: 2025-07-02 09:01:33
LastEditors: 吴博文 <EMAIL>
LastEditTime: 2025-07-02 09:03:51
FilePath: \geoserverapi\coordinate_query\run_server.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
# -*- coding: utf-8 -*-
'''
Description: 启动GeoServer栅格数据坐标查询API服务
'''

import os
import sys
import argparse
import logging

# 确保可以导入coordinate_query包
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# 现在导入geoserver_query_api模块
from coordinate_query.geoserver_query_api import start_server, logger

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='启动GeoServer栅格数据坐标查询API服务'
    )
    parser.add_argument(
        '--host', 
        type=str, 
        default='0.0.0.0',
        help='服务器主机地址 (默认: 0.0.0.0)'
    )
    parser.add_argument(
        '--port', 
        type=int, 
        default=5000,
        help='服务器端口 (默认: 5000)'
    )
    parser.add_argument(
        '--debug', 
        action='store_true',
        help='启用调试模式'
    )
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 打印启动信息
    logger.info("=== GeoServer栅格数据坐标查询API服务 ===")
    logger.info(f"主机: {args.host}")
    logger.info(f"端口: {args.port}")
    logger.info(f"调试模式: {'启用' if args.debug else '禁用'}")
    
    # 启动服务器
    try:
        start_server(
            host=args.host, 
            port=args.port, 
            debug=args.debug
        )
    except Exception as e:
        logger.error(f"启动服务器时出错: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main() 