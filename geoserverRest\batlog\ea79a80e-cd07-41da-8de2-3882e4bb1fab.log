执行命令: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
开始时间: 2025-08-04 11:39:22

[INFO]    Initializing ODM 3.5.5 - Mon Aug 04 11:39:26  2025
[INFO]    ==============
[INFO]    3d_tiles: False
[INFO]    align: None
[INFO]    auto_boundary: False
[INFO]    auto_boundary_distance: 0
[INFO]    bg_removal: False
[INFO]    boundary: {}
[INFO]    build_overviews: False
[INFO]    camera_lens: auto
[INFO]    cameras: {}
[INFO]    cog: False
[INFO]    copy_to: None
[INFO]    crop: 3
[INFO]    dem_decimation: 1
[INFO]    dem_euclidean_map: False
[INFO]    dem_gapfill_steps: 3
[INFO]    dem_resolution: 5
[INFO]    dsm: False
[INFO]    dtm: False
[INFO]    end_with: odm_postprocess
[INFO]    fast_orthophoto: False
[INFO]    feature_quality: high
[INFO]    feature_type: dspsift
[INFO]    force_gps: False
[INFO]    gcp: None
[INFO]    geo: None
[INFO]    gltf: False
[INFO]    gps_accuracy: 3
[INFO]    ignore_gsd: False
[INFO]    matcher_neighbors: 0
[INFO]    matcher_order: 0
[INFO]    matcher_type: flann
[INFO]    max_concurrency: 8
[INFO]    merge: all
[INFO]    mesh_octree_depth: 11
[INFO]    mesh_size: 200000
[INFO]    min_num_features: 10000
[INFO]    name: D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
[INFO]    no_gpu: False
[INFO]    optimize_disk_space: False
[INFO]    orthophoto_compression: DEFLATE
[INFO]    orthophoto_cutline: False
[INFO]    orthophoto_kmz: False
[INFO]    orthophoto_no_tiled: False
[INFO]    orthophoto_png: False
[INFO]    orthophoto_resolution: 5
[INFO]    pc_classify: False
[INFO]    pc_copc: False
[INFO]    pc_csv: False
[INFO]    pc_ept: False
[INFO]    pc_filter: 5
[INFO]    pc_las: False
[INFO]    pc_quality: medium
[INFO]    pc_rectify: False
[INFO]    pc_sample: 0
[INFO]    pc_skip_geometric: False
[INFO]    primary_band: auto
[INFO]    project_path: D:/Drone_Project/nginxData/ODM/Input/20250705171599/project
[INFO]    radiometric_calibration: none
[INFO]    rerun: None
[INFO]    rerun_all: False
[INFO]    rerun_from: None
[INFO]    rolling_shutter: False
[INFO]    rolling_shutter_readout: 0
[INFO]    sfm_algorithm: incremental
[INFO]    sfm_no_partial: False
[INFO]    skip_3dmodel: False
[INFO]    skip_band_alignment: False
[INFO]    skip_orthophoto: False
[INFO]    skip_report: False
[INFO]    sky_removal: False
[INFO]    sm_cluster: None
[INFO]    sm_no_align: False
[INFO]    smrf_scalar: 1.25
[INFO]    smrf_slope: 0.15
[INFO]    smrf_threshold: 0.5
[INFO]    smrf_window: 18.0
[INFO]    split: 999999
[INFO]    split_image_groups: None
[INFO]    split_overlap: 150
[INFO]    texturing_keep_unseen_faces: False
[INFO]    texturing_single_material: False
[INFO]    texturing_skip_global_seam_leveling: False
[INFO]    tiles: False
[INFO]    use_3dmesh: False
[INFO]    use_exif: False
[INFO]    use_fixed_camera_params: False
[INFO]    use_hybrid_bundle_adjustment: False
[INFO]    video_limit: 500
[INFO]    video_resolution: 4000
[INFO]    ==============
[INFO]    Running dataset stage
[INFO]    Loading dataset from: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\images
[INFO]    Loading images database: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\images.json
[INFO]    Found 2 usable images
[INFO]    Coordinates file already exist: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\odm_georeferencing\coords.txt
[INFO]    Model geo file already exist: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\odm_georeferencing\odm_georeferencing_model_geo.txt
[INFO]    Finished dataset stage
[INFO]    Running split stage
[INFO]    Normal dataset, will process all at once.
[INFO]    Finished split stage
[INFO]    Running merge stage
[INFO]    Normal dataset, nothing to merge.
[INFO]    Finished merge stage
[INFO]    Running opensfm stage
[WARNING] D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\opensfm\image_list.txt already exists, not rerunning OpenSfM setup
[WARNING] D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\opensfm\exif already exists, not rerunning photo to metadata
[WARNING] Detect features already done: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\opensfm\features exists
[WARNING] Match features already done: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\opensfm\matches exists
[WARNING] Found a valid OpenSfM tracks file in: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\opensfm\tracks.csv
[WARNING] Found a valid OpenSfM reconstruction file in: D:\Drone_Project\nginxData\ODM\Input\20250705171599\project\opensfm\reconstruction.json
[ERROR]   The program could not process this dataset using the current settings. Check that the images have enough overlap, that there are enough recognizable features and that the images are in focus. The program will now exit.

完成时间: 2025-08-04 11:39:27
返回代码: 0
状态: 运行成功
