# 批处理执行API使用说明

本文档介绍批处理执行API的使用方法，包括批处理文件的执行和状态监控功能。

## API端点

### 1. 执行批处理文件

```
GET /api/batch/execute?batch_path=批处理文件路径&project_path=项目路径&fast-orthophoto&其他参数...
```

执行指定的批处理文件并返回任务ID。

**可选参数:**
- batch_path: 批处理文件路径 (默认: D:\Drone_Project\ODM\ODM\run.bat)
- project_path: 项目路径，作为批处理的第一个参数传递 (默认: c://user/20250714140500/project)
- 其他参数: 其他传递给批处理文件的参数

**参数传递规则:**
- 有值参数格式: `--参数名 参数值`
- 无值标志参数格式: `--参数名`
- project_path参数会被特殊处理作为第一个位置参数，不加前缀

**示例1 (使用默认批处理文件):**
```
GET /api/batch/execute?project_path=D://Drone_Project//dataset//DJITESTIMAGE//20250705171600//project&fast-orthophoto
```

**示例2 (指定批处理文件):**
```
GET /api/batch/execute?batch_path=D://custom_scripts//process.bat&project_path=D://Drone_Project//dataset//project2&mesh-size=100000
```

**响应:**
```json
{
  "status": "success",
  "message": "批处理文件执行已启动",
  "task_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### 2. 查询批处理执行状态

```
GET /api/batch/status?task_id=任务ID
```

查询特定任务ID对应的批处理文件执行状态。

**必选参数:**
- task_id: 执行批处理文件时返回的任务ID

**示例:**
```
GET /api/batch/status?task_id=550e8400-e29b-41d4-a716-446655440000
```

**响应:**
```json
{
  "status": "success",
  "task": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat",
    "args": {
      "project_path": "D://Drone_Project//dataset//DJITESTIMAGE//20250705171600//project",
      "fast-orthophoto": true
    },
    "status": "运行成功",
    "start_time": "2025-07-12 15:30:00",
    "end_time": "2025-07-12 15:30:20",
    "return_code": 0,
    "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\550e8400-e29b-41d4-a716-446655440000.log"
  }
}
```

### 3. 查询所有批处理执行状态

```
GET /api/batch/all
```

查询所有批处理文件的执行状态。

**示例:**
```
GET /api/batch/all
```

**响应:**
```json
{
  "status": "success",
  "count": 2,
  "tasks": [
    {
      "task_id": "550e8400-e29b-41d4-a716-446655440000",
      "batch_path": "d:\\Drone_Project\\testdata\\test_script.bat",
      "args": {
        "project_path": "D://Drone_Project//dataset//DJITESTIMAGE//20250705171600//project",
        "fast-orthophoto": true
      },
      "status": "运行成功",
      "start_time": "2025-07-12 15:30:00",
      "end_time": "2025-07-12 15:30:20",
      "return_code": 0,
      "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\550e8400-e29b-41d4-a716-446655440000.log"
    },
    {
      "task_id": "660e8400-e29b-41d4-a716-446655440000",
      "batch_path": "d:\\Drone_Project\\ODM\\ODM\\run.bat",
      "args": {
        "project_path": "D://Drone_Project//dataset//project2",
        "mesh-size": "100000"
      },
      "status": "正在运行",
      "start_time": "2025-07-12 15:35:00",
      "end_time": null,
      "log_file": "D:\\Drone_Project\\geoserverapi\\geoserverRest\\batlog\\660e8400-e29b-41d4-a716-446655440000.log"
    }
  ]
}
```

## 批处理文件格式

批处理执行API可以执行任何Windows批处理文件(.bat)。API会将参数按照以下规则传递给批处理文件：

1. project_path参数作为第一个位置参数传递，不添加任何前缀
2. 有值参数以 `--参数名 参数值` 的格式传递
3. 无值标志参数以 `--参数名` 的格式传递

例如，对于以下API请求：

```
GET /api/batch/execute?batch_path=D://custom_scripts//process.bat&project_path=D://Drone_Project//dataset//project2&mesh-size=100000&fast-orthophoto
```

最终执行的命令类似于：

```
D://custom_scripts//process.bat D://Drone_Project//dataset//project2 --mesh-size 100000 --fast-orthophoto
```

## 日志和状态

- 每个批处理执行任务都有一个唯一的任务ID
- 任务日志保存在 `batlog` 目录中，文件名为 `{task_id}.log`
- 任务状态包括：`正在运行`、`运行成功`、`运行失败`
- 任务完成后，可以通过status API查看执行结果和返回码 