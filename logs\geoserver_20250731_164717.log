2025-07-31 16:47:17,324 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250731_164717.log
2025-07-31 16:47:17,327 - geo_publisher - INFO - 加载了 32 个任务状态
2025-07-31 16:47:17,452 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-31 16:47:17,453 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-31 16:47:17,464 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-31 16:47:17,468 - root - INFO - === GeoServer REST API服务 ===
2025-07-31 16:47:17,468 - root - INFO - 主机: 0.0.0.0
2025-07-31 16:47:17,468 - root - INFO - 端口: 5083
2025-07-31 16:47:17,469 - root - INFO - 调试模式: 禁用
2025-07-31 16:47:17,470 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-31 16:47:17,487 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-31 16:47:17,488 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 16:47:19,305 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:47:19,310 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:47:19,321 - map_api - INFO - 找到 3 个目录
2025-07-31 16:47:19,323 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:47:19,334 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:47:19,335 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:47:19,341 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:47:19,346 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:47:19,352 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:47:19,354 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:47:19,363 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:47:19,365 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:47:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-31 16:47:26,851 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:47:26,855 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:47:26,878 - map_api - INFO - 找到 3 个目录
2025-07-31 16:47:26,879 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:47:26,884 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:47:26,886 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:47:26,893 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:47:26,896 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:47:26,900 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:47:26,902 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:47:26,904 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:47:26,910 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:47:26] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-31 16:47:43,990 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:47:43,994 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:47:44,008 - map_api - INFO - 找到 3 个目录
2025-07-31 16:47:44,009 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:47:44,014 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:47:44,016 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:47:44,029 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:47:44,033 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:47:44,044 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:47:44,046 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:47:44,052 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:47:44,057 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:47:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-31 16:47:48,137 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:47:48,141 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:47:48,150 - map_api - INFO - 找到 3 个目录
2025-07-31 16:47:48,152 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:47:48,164 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:47:48,166 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:47:48,172 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:47:48,180 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:47:48,186 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:47:48,189 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:47:48,193 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:47:48,196 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:47:48] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-31 16:47:48,339 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:47:48,345 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:47:48,351 - map_api - INFO - 找到 3 个目录
2025-07-31 16:47:48,355 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:47:48,362 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:47:48,365 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:47:48,373 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:47:48,380 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:47:48,386 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:47:48,387 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:47:48,395 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:47:48,401 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:47:48] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-31 16:47:48,566 - map_api - INFO - 请求文件: baseMap.json
2025-07-31 16:47:48,622 - map_api - INFO - 成功获取文件 baseMap.json, 内容大小: 3253 字节
2025-07-31 16:47:48,629 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:47:48] "GET /api/map/base-map HTTP/1.1" 200 -
2025-07-31 16:47:49,188 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:47:49,190 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:47:49,201 - map_api - INFO - 找到 3 个目录
2025-07-31 16:47:49,202 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:47:49,235 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:47:49,238 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:47:49,267 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:47:49,271 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:47:49,298 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:47:49,301 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:47:49,304 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:47:49,309 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:47:49] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-31 16:48:10,546 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:48:10,547 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:48:10,581 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:48:10,585 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:10] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:48:10,599 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:48:10,602 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:10] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:48:10,629 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-31 16:48:10,632 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:48:10,643 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-31 16:48:10,644 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:48:10,648 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:10] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-31 16:48:10,649 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:10] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:48:10,703 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-31 16:48:10,707 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:48:10,713 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-31 16:48:10,714 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:48:10,718 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:10] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-31 16:48:10,719 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:10] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:48:10,959 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:10] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:48:10,986 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:48:11,006 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:48:11,007 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:11] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:48:36,852 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:48:36,856 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:48:36,863 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:48:36,865 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:36] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:48:36,867 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:48:36,869 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:36] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:48:36,906 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:48:36,909 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-31 16:48:36,916 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:48:36,918 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:36] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:48:36,919 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-31 16:48:36,928 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:36] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-31 16:48:36,967 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:48:36,968 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-31 16:48:36,978 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:48:36,980 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:36] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:48:36,999 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-31 16:48:37,006 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:37] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-31 16:48:37,345 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:37] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:48:37,382 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:48:37,413 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:48:37,414 - werkzeug - INFO - 192.168.43.150 - - [31/Jul/2025 16:48:37] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:48:44,010 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:48:44,014 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:48:44,027 - map_api - INFO - 找到 3 个目录
2025-07-31 16:48:44,029 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:48:44,034 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:48:44,044 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:48:44,051 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:48:44,052 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:48:44,056 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:48:44,058 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:48:44,063 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:48:44,065 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:48:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-07-31 16:48:49,664 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:48:49,708 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:48:49,781 - map_api - INFO - 请求文件: baseStyle2.json
2025-07-31 16:48:49,789 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:48:49,792 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:48:49,805 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:48:49] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:48:49,813 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:48:49] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:48:49,808 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-07-31 16:48:49,824 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:48:49] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-07-31 16:48:49,840 - map_api - INFO - 请求文件: baseMap2.json
2025-07-31 16:48:49,849 - map_api - INFO - 请求文件: baseMap3.json
2025-07-31 16:48:49,854 - map_api - INFO - 请求文件: baseStyle3.json
2025-07-31 16:48:49,857 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6330 字节
2025-07-31 16:48:49,866 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:48:49] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-07-31 16:48:49,867 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 4478 字节
2025-07-31 16:48:49,869 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-07-31 16:48:49,913 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:48:49] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-07-31 16:48:49,925 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 16:48:49] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-07-31 16:48:51,101 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:48:51] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:48:51,193 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:48:51,330 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:48:51,369 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:48:51] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:48:51,984 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:48:51] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-31 16:48:52,049 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-07-31 16:48:52,097 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-07-31 16:48:52,132 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:48:52] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-07-31 16:48:56,011 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:48:56,046 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:48:56,047 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:48:56] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:49:02,427 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:49:02] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:49:02,433 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-07-31 16:49:02,458 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-07-31 16:49:02,459 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:49:02] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-07-31 16:49:54,034 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:49:54,038 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:49:54,051 - map_api - INFO - 找到 3 个目录
2025-07-31 16:49:54,053 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:49:54,064 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:49:54,067 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:49:54,073 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:49:54,076 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:49:54,085 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:49:54,086 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:49:54,091 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:49:54,096 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:49:54] "GET /api/map/odm/tasks HTTP/1.1" 200 -
