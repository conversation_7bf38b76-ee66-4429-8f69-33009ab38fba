2025-07-12 16:06:31,649 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250712_160631.log
2025-07-12 16:06:31,681 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 16:06:31,681 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 16:06:33,346 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 16:06:33,384 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 16:06:33,384 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 16:06:33,404 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 16:06:33,409 - root - INFO - === GeoServer REST API服务 ===
2025-07-12 16:06:33,409 - root - INFO - 主机: 0.0.0.0
2025-07-12 16:06:33,409 - root - INFO - 端口: 5083
2025-07-12 16:06:33,410 - root - INFO - 调试模式: 禁用
2025-07-12 16:06:33,410 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-12 16:06:33,423 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://************:5083
2025-07-12 16:06:33,428 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 16:08:03,626 - batch_executor - INFO - 启动任务 e97919ef-7e28-4cef-9ba9-bfa1a842a46c: d:\Drone_Project\testdata\test_script.bat --arg1 参数1 --arg2 参数2
2025-07-12 16:08:03,628 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:08:03] "GET /api/batch/execute?arg1=参数1&arg2=参数2 HTTP/1.1" 200 -
2025-07-12 16:08:03,687 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:08:03] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-12 16:08:03,722 - batch_executor - ERROR - 执行任务 e97919ef-7e28-4cef-9ba9-bfa1a842a46c 时出错: 'utf-8' codec can't decode byte 0xb2 in position 18: invalid start byte
2025-07-12 16:09:15,978 - batch_executor - INFO - 启动任务 b826b26a-16e0-4986-980a-350ce23d546f: d:\Drone_Project\testdata\test_script.bat --arg1 参数1 --arg2 参数2
2025-07-12 16:09:15,979 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:09:15] "GET /api/batch/execute?arg1=参数1&arg2=参数2 HTTP/1.1" 200 -
2025-07-12 16:09:16,030 - batch_executor - ERROR - 执行任务 b826b26a-16e0-4986-980a-350ce23d546f 时出错: 'utf-8' codec can't decode byte 0xb2 in position 18: invalid start byte
2025-07-12 16:09:41,662 - batch_executor - INFO - 启动任务 f2964afb-63a6-4dc8-b390-a77c2474e500: d:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-12 16:09:41,664 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:09:41] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
2025-07-12 16:09:41,717 - batch_executor - ERROR - 执行任务 f2964afb-63a6-4dc8-b390-a77c2474e500 时出错: 'utf-8' codec can't decode bytes in position 8-9: invalid continuation byte
2025-07-12 16:09:42,744 - batch_executor - INFO - 启动任务 d335d619-139f-458f-942d-9832d84bfe15: d:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-12 16:09:42,746 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:09:42] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
2025-07-12 16:09:42,790 - batch_executor - ERROR - 执行任务 d335d619-139f-458f-942d-9832d84bfe15 时出错: 'utf-8' codec can't decode bytes in position 8-9: invalid continuation byte
2025-07-12 16:13:08,263 - batch_executor - INFO - 启动任务 f354168f-7601-4812-bed8-da015777e697: d:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-12 16:13:08,264 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:13:08] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
2025-07-12 16:13:08,437 - batch_executor - ERROR - 执行任务 f354168f-7601-4812-bed8-da015777e697 时出错: 'utf-8' codec can't decode bytes in position 8-9: invalid continuation byte
2025-07-12 16:13:34,015 - batch_executor - INFO - 启动任务 36031c47-27af-413d-920f-0e05d9e51d67: d:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-12 16:13:34,018 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:13:34] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
2025-07-12 16:13:34,064 - batch_executor - ERROR - 执行任务 36031c47-27af-413d-920f-0e05d9e51d67 时出错: 'utf-8' codec can't decode bytes in position 8-9: invalid continuation byte
