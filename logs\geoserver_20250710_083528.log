2025-07-10 08:35:28,782 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250710_083528.log
2025-07-10 08:35:28,785 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-10 08:35:28,785 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-10 08:35:29,273 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-10 08:35:29,280 - root - INFO - === GeoServer REST API服务 ===
2025-07-10 08:35:29,280 - root - INFO - 主机: 0.0.0.0
2025-07-10 08:35:29,280 - root - INFO - 端口: 5083
2025-07-10 08:35:29,280 - root - INFO - 调试模式: 禁用
2025-07-10 08:35:29,281 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-10 08:35:29,291 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-10 08:35:29,291 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-10 08:41:18,587 - werkzeug - INFO - ************** - - [10/Jul/2025 08:41:18] "[33mOPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1[0m" 404 -
2025-07-10 08:41:23,979 - werkzeug - INFO - ************** - - [10/Jul/2025 08:41:23] "[33mOPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1[0m" 404 -
2025-07-10 08:41:33,754 - werkzeug - INFO - ************** - - [10/Jul/2025 08:41:33] "[33mOPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1[0m" 404 -
2025-07-10 08:41:39,098 - werkzeug - INFO - ************** - - [10/Jul/2025 08:41:39] "[33mOPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1[0m" 404 -
2025-07-10 08:41:58,813 - root - INFO - 开始查询坐标点 (22.889209283041012, 108.3507302313987) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-10 08:41:59,425 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-10 08:41:59,814 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-10 08:41:59,920 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-10 08:41:59,921 - root - INFO - 在坐标点 (22.889209283041012, 108.3507302313987) 处找到 2 个有有效数据的栅格图层
2025-07-10 08:41:59,925 - werkzeug - INFO - ************** - - [10/Jul/2025 08:41:59] "GET /api/query_values?lat=22.889209283041012&lon=108.3507302313987&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-10 08:42:05,990 - root - INFO - 开始查询坐标点 (22.88131665242736, 108.43159448567349) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-10 08:42:06,362 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-10 08:42:06,387 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-10 08:42:06,489 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-10 08:42:06,490 - root - INFO - 在坐标点 (22.88131665242736, 108.43159448567349) 处找到 2 个有有效数据的栅格图层
2025-07-10 08:42:06,493 - werkzeug - INFO - ************** - - [10/Jul/2025 08:42:06] "GET /api/query_values?lat=22.88131665242736&lon=108.43159448567349&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-10 08:42:10,692 - werkzeug - INFO - ************** - - [10/Jul/2025 08:42:10] "[33mOPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=id4 HTTP/1.1[0m" 404 -
2025-07-10 08:42:35,575 - werkzeug - INFO - ************** - - [10/Jul/2025 08:42:35] "[33mOPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1[0m" 404 -
2025-07-10 08:42:44,742 - werkzeug - INFO - ************** - - [10/Jul/2025 08:42:44] "[33mGET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1[0m" 404 -
2025-07-10 08:42:44,821 - werkzeug - INFO - ************** - - [10/Jul/2025 08:42:44] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-10 08:48:33,141 - werkzeug - INFO - ************** - - [10/Jul/2025 08:48:33] "[33mGET /api//bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1[0m" 404 -
2025-07-10 08:48:37,139 - werkzeug - INFO - ************** - - [10/Jul/2025 08:48:37] "[33mGET /api/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1[0m" 404 -
2025-07-10 08:49:16,132 - werkzeug - INFO - ************** - - [10/Jul/2025 08:49:16] "[33mGET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1[0m" 404 -
2025-07-10 08:49:17,852 - werkzeug - INFO - ************** - - [10/Jul/2025 08:49:17] "[33mGET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1[0m" 404 -
