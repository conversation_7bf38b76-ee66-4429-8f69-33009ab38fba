#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
Description: 测试特定坐标点的栅格数据查询
'''

import os
import sys
import json
import logging

# 添加父目录到系统路径，以便导入模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 设置日志级别为DEBUG以查看详细信息
from config import LOG_LEVEL
import logging
logging.basicConfig(level=logging.DEBUG)

# 导入查询类
from coordinate_query.geoserver_query import GeoServerRasterQuery

def test_specific_point():
    """测试特定坐标点"""
    # 创建查询实例
    query = GeoServerRasterQuery()
    
    # 测试坐标点
    lat = 22.77912
    lon = 108.36502
    workspace = "test_myworkspace"
    
    print(f"===== 测试坐标点 ({lat}, {lon}) 在工作区 '{workspace}' 中的图层 =====")
    
    # 首先获取工作区中的所有栅格图层
    print("\n1. 获取工作区中的所有栅格图层...")
    layers = query.get_raster_layers(workspace)
    
    if not layers:
        print(f"工作区 '{workspace}' 中没有找到栅格图层")
        return
    
    print(f"找到 {len(layers)} 个栅格图层:")
    for i, layer in enumerate(layers):
        print(f"  {i+1}. {layer['name']} (存储: {layer['store']})")
        bbox = layer.get('bbox', {})
        if bbox:
            print(f"     边界框: ({bbox.get('minx', 'N/A')}, {bbox.get('miny', 'N/A')}) - "
                  f"({bbox.get('maxx', 'N/A')}, {bbox.get('maxy', 'N/A')})")
    
    # 检查坐标点是否在每个图层的边界框内
    print("\n2. 检查坐标点是否在各图层边界框内...")
    for i, layer in enumerate(layers):
        bbox = layer.get('bbox', {})
        if bbox:
            try:
                minx = float(bbox.get('minx', 0))
                miny = float(bbox.get('miny', 0))
                maxx = float(bbox.get('maxx', 0))
                maxy = float(bbox.get('maxy', 0))
                
                is_in_bbox = (minx <= lon <= maxx and miny <= lat <= maxy)
                print(f"  {i+1}. {layer['name']}: 坐标点{'' if is_in_bbox else '不'}在边界框内")
                
                if is_in_bbox:
                    # 如果在边界框内，测试GetFeatureInfo请求
                    print(f"     正在检查图层 '{layer['name']}' 在坐标点处是否有有效数据...")
                    has_data = query._check_point_data(lat, lon, workspace, layer['name'])
                    print(f"     结果: {'有' if has_data else '无'}有效数据")
            except (ValueError, TypeError) as e:
                print(f"  {i+1}. {layer['name']}: 处理边界框时出错: {str(e)}")
        else:
            print(f"  {i+1}. {layer['name']}: 没有边界框信息")
    
    # 执行完整查询
    print("\n3. 执行完整坐标点查询...")
    result_layers = query.query_point(lat, lon, workspace)
    
    print(f"查询结果: 找到 {len(result_layers)} 个有效图层")
    for i, layer in enumerate(result_layers):
        print(f"  {i+1}. {layer['name']} (存储: {layer['store']})")

if __name__ == "__main__":
    test_specific_point() 