2025-07-15 18:55:25,086 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250715_185525.log
2025-07-15 18:55:25,086 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-15 18:55:25,087 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-15 18:55:26,114 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-15 18:55:26,120 - batch_executor - INFO - 加载了 35 个任务状态
2025-07-15 18:55:29,823 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-15 18:55:29,823 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-15 18:55:29,921 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-15 18:55:29,926 - root - INFO - === GeoServer REST API服务 ===
2025-07-15 18:55:29,926 - root - INFO - 主机: 0.0.0.0
2025-07-15 18:55:29,928 - root - INFO - 端口: 5083
2025-07-15 18:55:29,929 - root - INFO - 调试模式: 禁用
2025-07-15 18:55:29,930 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-15 18:55:29,948 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-15 18:55:29,949 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-15 18:57:28,762 - tif_api - INFO - 处理请求 - 输入: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto.tif, 输出TIF: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto_out2.tif, 输出SHP: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto_out2.shp
2025-07-15 18:57:29,036 - tif_api - INFO - 参数: 黑色阈值=0, 白色阈值=255, NoData值=-9999.0
2025-07-15 18:57:29,064 - tif_api - INFO - 简化参数=0.1, 处理模式=full, 保留Alpha=False
2025-07-15 18:57:29,158 - tif_api - INFO - 保护内部区域=False, 使用GPU=False, GPU数量=0
