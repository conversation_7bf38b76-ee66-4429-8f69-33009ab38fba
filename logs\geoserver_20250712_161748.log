2025-07-12 16:17:48,409 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250712_161748.log
2025-07-12 16:17:48,412 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 16:17:48,413 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 16:17:48,433 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 16:17:48,455 - batch_executor - INFO - 加载了 7 个任务状态
2025-07-12 16:17:48,466 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 16:17:48,466 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 16:17:48,483 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 16:17:48,492 - root - INFO - === GeoServer REST API服务 ===
2025-07-12 16:17:48,492 - root - INFO - 主机: 0.0.0.0
2025-07-12 16:17:48,492 - root - INFO - 端口: 5083
2025-07-12 16:17:48,493 - root - INFO - 调试模式: 禁用
2025-07-12 16:17:48,494 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-12 16:17:48,507 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://************:5083
2025-07-12 16:17:48,508 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 16:18:01,714 - batch_executor - INFO - 启动任务 b5f51677-2b03-42fe-a760-fab98935ac3c: d:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-12 16:18:01,715 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:18:01] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
2025-07-12 16:18:01,747 - batch_executor - ERROR - 执行任务 b5f51677-2b03-42fe-a760-fab98935ac3c 时出错: 'gbk' codec can't decode byte 0x80 in position 11: illegal multibyte sequence
2025-07-12 16:18:20,560 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:18:20] "GET /api/batch/status?task_id=b5f51677-2b03-42fe-a760-fab98935ac3c HTTP/1.1" 200 -
2025-07-12 16:23:23,908 - batch_executor - INFO - 启动任务 ae525ce7-0792-46d0-a11c-16646f082d00: d:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-12 16:23:23,910 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:23:23] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
2025-07-12 16:24:43,266 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:24:43] "GET /api/batch/status?task_id=ae525ce7-0792-46d0-a11c-16646f082d00 HTTP/1.1" 200 -
2025-07-12 16:25:16,541 - batch_executor - INFO - 启动任务 c245a134-6280-4221-8a6b-9ef11adc9738: d:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-12 16:25:16,542 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:25:16] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
2025-07-12 16:25:24,650 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:25:24] "GET /api/batch/status?task_id=c245a134-6280-4221-8a6b-9ef11adc9738 HTTP/1.1" 200 -
2025-07-12 16:25:30,572 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:25:30] "GET /api/batch/status?task_id=c245a134-6280-4221-8a6b-9ef11adc9738 HTTP/1.1" 200 -
2025-07-12 16:27:24,581 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:27:24] "GET /api/batch/status?task_id=c245a134-6280-4221-8a6b-9ef11adc9738 HTTP/1.1" 200 -
2025-07-12 16:45:15,983 - batch_executor - INFO - 启动任务 99160467-19f0-432e-bcb1-71550a346208: d:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-12 16:45:15,985 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:45:15] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
2025-07-12 16:45:15,999 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:45:15] "GET /api/batch/status?task_id=99160467-19f0-432e-bcb1-71550a346208 HTTP/1.1" 200 -
2025-07-12 16:45:16,605 - batch_executor - INFO - 启动任务 3881ee54-1b17-46be-a1ae-de07d0caeb06: d:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-12 16:45:16,607 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:45:16] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
2025-07-12 16:45:16,619 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:45:16] "GET /api/batch/status?task_id=3881ee54-1b17-46be-a1ae-de07d0caeb06 HTTP/1.1" 200 -
2025-07-12 16:45:25,982 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:45:25] "GET /api/batch/status?task_id=99160467-19f0-432e-bcb1-71550a346208 HTTP/1.1" 200 -
2025-07-12 16:45:26,596 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:45:26] "GET /api/batch/status?task_id=3881ee54-1b17-46be-a1ae-de07d0caeb06 HTTP/1.1" 200 -
2025-07-12 16:45:36,012 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:45:36] "GET /api/batch/status?task_id=99160467-19f0-432e-bcb1-71550a346208 HTTP/1.1" 200 -
2025-07-12 16:45:36,628 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:45:36] "GET /api/batch/status?task_id=3881ee54-1b17-46be-a1ae-de07d0caeb06 HTTP/1.1" 200 -
2025-07-12 16:45:46,064 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:45:46] "GET /api/batch/status?task_id=99160467-19f0-432e-bcb1-71550a346208 HTTP/1.1" 200 -
2025-07-12 16:45:46,682 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:45:46] "GET /api/batch/status?task_id=3881ee54-1b17-46be-a1ae-de07d0caeb06 HTTP/1.1" 200 -
2025-07-12 16:45:48,007 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:45:48] "GET /api/batch/status?task_id=3881ee54-1b17-46be-a1ae-de07d0caeb06 HTTP/1.1" 200 -
2025-07-12 16:45:56,050 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:45:56] "GET /api/batch/status?task_id=99160467-19f0-432e-bcb1-71550a346208 HTTP/1.1" 200 -
2025-07-12 16:45:57,672 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:45:57] "GET /api/batch/status?task_id=99160467-19f0-432e-bcb1-71550a346208 HTTP/1.1" 200 -
2025-07-12 16:46:00,793 - batch_executor - INFO - 启动任务 185e90d6-dd66-4134-a06d-d58a50ab1a63: d:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-12 16:46:00,794 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:46:00] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
2025-07-12 16:46:00,807 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:46:00] "GET /api/batch/status?task_id=185e90d6-dd66-4134-a06d-d58a50ab1a63 HTTP/1.1" 200 -
2025-07-12 16:46:10,854 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:46:10] "GET /api/batch/status?task_id=185e90d6-dd66-4134-a06d-d58a50ab1a63 HTTP/1.1" 200 -
2025-07-12 16:46:20,875 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:46:20] "GET /api/batch/status?task_id=185e90d6-dd66-4134-a06d-d58a50ab1a63 HTTP/1.1" 200 -
2025-07-12 16:46:30,878 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:46:30] "GET /api/batch/status?task_id=185e90d6-dd66-4134-a06d-d58a50ab1a63 HTTP/1.1" 200 -
