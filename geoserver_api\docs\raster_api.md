# 栅格查询API文档

栅格查询API模块提供强大的栅格数据查询功能，支持坐标点查询、图层管理和相交分析等功能。

## 模块概述

栅格查询模块是GeoServer Django API的核心功能之一，提供：
- 指定坐标点的栅格值查询
- 工作区图层列表获取
- 图层相交情况分析
- 像素值提取和分析
- 多图层批量查询

## API端点列表

### 1. 查询坐标点栅格值

**端点**: `GET /api/query/`

**描述**: 查询指定坐标点处的栅格图层数据

**参数**:
- `lat` (必选): 纬度，浮点数
- `lon` (必选): 经度，浮点数  
- `workspace` (必选): 工作区名称

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/query/?lat=39.9042&lon=116.4074&workspace=test"
```

**成功响应**:
```json
{
  "status": "success",
  "coordinates": {"lat": 39.9042, "lon": 116.4074},
  "workspace": "test",
  "results": [
    {
      "layer": "test:elevation",
      "value": 45.2,
      "coordinates": {"lat": 39.9042, "lon": 116.4074}
    }
  ],
  "total_layers": 5,
  "layers_with_data": 1
}
```

### 2. 获取栅格图层列表

**端点**: `GET /api/layers/`

**描述**: 获取指定工作区中的所有栅格图层

**参数**:
- `workspace` (必选): 工作区名称

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/layers/?workspace=test"
```

**成功响应**:
```json
{
  "status": "success",
  "workspace": "test",
  "count": 3,
  "layers": [
    {
      "name": "elevation",
      "store": "elevation_store",
      "workspace": "test",
      "id": "test:elevation",
      "bbox": {
        "minx": 116.0,
        "miny": 39.0,
        "maxx": 117.0,
        "maxy": 40.0
      }
    }
  ]
}
```

### 3. 获取所有图层列表

**端点**: `GET /api/all_layers/`

**描述**: 获取指定工作区中的所有图层（包括栅格和矢量）

**参数**:
- `workspace` (必选): 工作区名称

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/all_layers/?workspace=test"
```

**成功响应**:
```json
{
  "status": "success",
  "workspace": "test",
  "count": 5,
  "layers": [
    {
      "name": "elevation",
      "workspace": "test",
      "id": "test:elevation",
      "type": "raster"
    },
    {
      "name": "boundaries",
      "workspace": "test", 
      "id": "test:boundaries",
      "type": "vector"
    }
  ]
}
```

### 4. 测试坐标点数据

**端点**: `GET /api/test/` 或 `GET /api/test_point/`

**描述**: 测试指定坐标点在特定图层中是否有有效数据

**参数**:
- `lat` (必选): 纬度，浮点数
- `lon` (必选): 经度，浮点数
- `workspace` (必选): 工作区名称
- `layer` (可选): 图层名称，不指定则查询所有图层

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/test/?lat=39.9042&lon=116.4074&workspace=test&layer=elevation"
```

**成功响应**:
```json
{
  "status": "success",
  "coordinates": {"lat": 39.9042, "lon": 116.4074},
  "workspace": "test",
  "layer": "elevation",
  "has_data": true,
  "result": {
    "status": "success",
    "results": [
      {
        "layer": "test:elevation",
        "value": 45.2,
        "coordinates": {"lat": 39.9042, "lon": 116.4074}
      }
    ]
  }
}
```

### 5. 获取像素值

**端点**: `GET /api/pixel/` 或 `GET /api/pixel_value/`

**描述**: 获取指定坐标点处的像素值

**参数**:
- `lat` (必选): 纬度，浮点数
- `lon` (必选): 经度，浮点数
- `workspace` (必选): 工作区名称
- `layer` (必选): 图层名称

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/pixel/?lat=39.9042&lon=116.4074&workspace=test&layer=elevation"
```

**成功响应**:
```json
{
  "status": "success",
  "coordinates": {"lat": 39.9042, "lon": 116.4074},
  "workspace": "test",
  "layer": "elevation",
  "pixel_value": [
    {
      "layer": "test:elevation",
      "value": 45.2,
      "coordinates": {"lat": 39.9042, "lon": 116.4074}
    }
  ]
}
```

### 6. 查询坐标点图层

**端点**: `GET /api/query-layers/`

**描述**: 查询指定坐标点处的图层并获取像素值

**参数**:
- `lat` (必选): 纬度，浮点数
- `lon` (必选): 经度，浮点数
- `workspace` (必选): 工作区名称

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/query-layers/?lat=39.9042&lon=116.4074&workspace=test"
```

**成功响应**:
```json
{
  "status": "success",
  "coordinates": {"lat": 39.9042, "lon": 116.4074},
  "workspace": "test",
  "layers_found": [
    {
      "layer": "test:elevation",
      "value": 45.2,
      "coordinates": {"lat": 39.9042, "lon": 116.4074}
    }
  ],
  "summary": {
    "total_layers": 5,
    "layers_with_data": 1
  }
}
```

### 7. 查询有效像素值

**端点**: `GET /api/query_values/`

**描述**: 查询指定坐标点处所有栅格图层的有效像素值

**参数**:
- `lat` (必选): 纬度，浮点数
- `lon` (必选): 经度，浮点数
- `workspace` (必选): 工作区名称

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/query_values/?lat=39.9042&lon=116.4074&workspace=test"
```

**成功响应**:
```json
{
  "status": "success",
  "coordinates": {"lat": 39.9042, "lon": 116.4074},
  "workspace": "test",
  "valid_layers": [
    {
      "layer": "test:elevation",
      "value": 45.2,
      "coordinates": {"lat": 39.9042, "lon": 116.4074}
    }
  ],
  "total_valid_layers": 1
}
```

### 8. 查询相交图层

**端点**: `GET /api/query_intersecting_layers/`

**描述**: 查询指定经纬度与工作空间下所有图层的相交情况

**参数**:
- `lat` (必选): 纬度，浮点数
- `lon` (必选): 经度，浮点数
- `workspace` (必选): 工作区名称

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/query_intersecting_layers/?lat=39.9042&lon=116.4074&workspace=test"
```

**成功响应**:
```json
{
  "status": "success",
  "coordinates": {"lat": 39.9042, "lon": 116.4074},
  "workspace": "test",
  "total_layers": 5,
  "intersecting_layers_count": 2,
  "layers": [
    {
      "layer_name": "elevation",
      "layer_id": "test:elevation",
      "intersects": true,
      "pixel_value": 45.2
    },
    {
      "layer_name": "temperature",
      "layer_id": "test:temperature",
      "intersects": false,
      "pixel_value": null
    }
  ]
}
```

### 9. 获取图层相交情况

**端点**: `GET /api/layer_intersections/`

**描述**: 查询指定栅格图层与同一工作空间其他栅格图层的边界框相交情况

**参数**:
- `workspace` (必选): 工作区名称
- `layer` (必选): 目标图层名称

**请求示例**:
```bash
curl "http://127.0.0.1:5000/api/layer_intersections/?workspace=test&layer=elevation"
```

**成功响应**:
```json
{
  "status": "success",
  "target_layer": "elevation",
  "workspace": "test",
  "target_bbox": {
    "minx": 116.0,
    "miny": 39.0,
    "maxx": 117.0,
    "maxy": 40.0
  },
  "total_other_layers": 4,
  "intersecting_layers_count": 2,
  "intersections": [
    {
      "layer_name": "temperature",
      "layer_id": "test:temperature",
      "intersects": true,
      "bbox": {
        "minx": 116.5,
        "miny": 39.5,
        "maxx": 117.5,
        "maxy": 40.5
      }
    }
  ]
}
```

## 错误处理

### 常见错误响应

**参数缺失**:
```json
{
  "status": "error",
  "message": "缺少必要参数: lat, lon, workspace"
}
```

**参数格式错误**:
```json
{
  "status": "error", 
  "message": "纬度和经度必须是有效的数字"
}
```

**工作区不存在**:
```json
{
  "status": "error",
  "message": "工作区 'test' 不存在"
}
```

**图层不存在**:
```json
{
  "status": "error",
  "message": "在工作区 test 中未找到图层 elevation"
}
```

**服务器内部错误**:
```json
{
  "status": "error",
  "message": "服务器内部错误: 连接GeoServer失败"
}
```

## 使用示例

### Python客户端示例

```python
import requests

class RasterQueryClient:
    def __init__(self, base_url):
        self.base_url = base_url
    
    def query_coordinate(self, lat, lon, workspace):
        """查询坐标点栅格值"""
        url = f"{self.base_url}/api/query/"
        params = {
            'lat': lat,
            'lon': lon,
            'workspace': workspace
        }
        response = requests.get(url, params=params)
        return response.json()
    
    def get_layers(self, workspace):
        """获取图层列表"""
        url = f"{self.base_url}/api/layers/"
        params = {'workspace': workspace}
        response = requests.get(url, params=params)
        return response.json()
    
    def test_point(self, lat, lon, workspace, layer=None):
        """测试坐标点数据"""
        url = f"{self.base_url}/api/test/"
        params = {
            'lat': lat,
            'lon': lon,
            'workspace': workspace
        }
        if layer:
            params['layer'] = layer
        response = requests.get(url, params=params)
        return response.json()

# 使用示例
client = RasterQueryClient("http://127.0.0.1:5000")

# 查询北京坐标的栅格值
result = client.query_coordinate(39.9042, 116.4074, "beijing")
print(f"查询结果: {result}")

# 获取工作区图层
layers = client.get_layers("beijing")
print(f"图层数量: {layers['count']}")

# 测试坐标点数据
test_result = client.test_point(39.9042, 116.4074, "beijing", "elevation")
print(f"有数据: {test_result['has_data']}")
```

### JavaScript客户端示例

```javascript
class RasterQueryClient {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
    }
    
    async queryCoordinate(lat, lon, workspace) {
        const url = new URL(`${this.baseUrl}/api/query/`);
        url.searchParams.append('lat', lat);
        url.searchParams.append('lon', lon);
        url.searchParams.append('workspace', workspace);
        
        const response = await fetch(url);
        return await response.json();
    }
    
    async getLayers(workspace) {
        const url = new URL(`${this.baseUrl}/api/layers/`);
        url.searchParams.append('workspace', workspace);
        
        const response = await fetch(url);
        return await response.json();
    }
    
    async testPoint(lat, lon, workspace, layer = null) {
        const url = new URL(`${this.baseUrl}/api/test/`);
        url.searchParams.append('lat', lat);
        url.searchParams.append('lon', lon);
        url.searchParams.append('workspace', workspace);
        if (layer) {
            url.searchParams.append('layer', layer);
        }
        
        const response = await fetch(url);
        return await response.json();
    }
}

// 使用示例
const client = new RasterQueryClient('http://127.0.0.1:5000');

// 查询坐标点栅格值
client.queryCoordinate(39.9042, 116.4074, 'beijing')
    .then(result => {
        console.log('查询结果:', result);
    });

// 获取图层列表
client.getLayers('beijing')
    .then(layers => {
        console.log('图层数量:', layers.count);
    });
```

## 最佳实践

### 1. 批量查询优化

```python
def batch_query_coordinates(client, coordinates, workspace):
    """批量查询多个坐标点"""
    results = []
    for lat, lon in coordinates:
        try:
            result = client.query_coordinate(lat, lon, workspace)
            if result['status'] == 'success':
                results.append({
                    'coordinates': (lat, lon),
                    'data': result['results']
                })
        except Exception as e:
            print(f"查询坐标 ({lat}, {lon}) 失败: {e}")
    return results
```

### 2. 错误处理和重试

```python
import time
from functools import wraps

def retry_on_failure(max_retries=3, delay=1):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    print(f"尝试 {attempt + 1} 失败: {e}")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator

@retry_on_failure(max_retries=3, delay=2)
def robust_query(client, lat, lon, workspace):
    """带重试机制的查询"""
    return client.query_coordinate(lat, lon, workspace)
```

### 3. 缓存机制

```python
from functools import lru_cache
import hashlib

class CachedRasterQueryClient(RasterQueryClient):
    @lru_cache(maxsize=1000)
    def get_layers_cached(self, workspace):
        """缓存图层列表"""
        return self.get_layers(workspace)
    
    def query_coordinate_with_cache(self, lat, lon, workspace):
        """带缓存的坐标查询"""
        # 创建缓存键
        cache_key = hashlib.md5(
            f"{lat}_{lon}_{workspace}".encode()
        ).hexdigest()
        
        # 这里可以集成Redis或其他缓存系统
        return self.query_coordinate(lat, lon, workspace)
```

## 性能优化

### 1. 并发查询

```python
import asyncio
import aiohttp

async def async_query_coordinate(session, lat, lon, workspace):
    """异步查询坐标点"""
    url = "http://127.0.0.1:5000/api/query/"
    params = {
        'lat': lat,
        'lon': lon,
        'workspace': workspace
    }
    async with session.get(url, params=params) as response:
        return await response.json()

async def batch_async_query(coordinates, workspace):
    """批量异步查询"""
    async with aiohttp.ClientSession() as session:
        tasks = [
            async_query_coordinate(session, lat, lon, workspace)
            for lat, lon in coordinates
        ]
        results = await asyncio.gather(*tasks)
        return results
```

### 2. 连接池配置

```python
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

def create_optimized_session():
    """创建优化的HTTP会话"""
    session = requests.Session()
    
    # 配置重试策略
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    
    # 配置适配器
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=20,
        pool_maxsize=20
    )
    
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    return session
```

## 故障排除

### 常见问题

1. **查询返回空结果**
   - 检查坐标是否在图层范围内
   - 验证工作区和图层名称是否正确
   - 确认GeoServer服务正常运行

2. **查询速度慢**
   - 检查GeoServer性能
   - 考虑使用缓存机制
   - 优化图层数据结构

3. **坐标系统问题**
   - 确认使用正确的坐标系统（默认EPSG:4326）
   - 检查坐标顺序（经度，纬度）
   - 验证坐标范围的合理性

### 调试工具

```python
def debug_query(client, lat, lon, workspace):
    """调试查询问题"""
    print(f"查询参数: lat={lat}, lon={lon}, workspace={workspace}")
    
    # 1. 检查工作区是否存在
    layers = client.get_layers(workspace)
    if layers['status'] != 'success':
        print(f"工作区问题: {layers['message']}")
        return
    
    print(f"工作区 '{workspace}' 包含 {layers['count']} 个图层")
    
    # 2. 执行查询
    result = client.query_coordinate(lat, lon, workspace)
    print(f"查询结果: {result}")
    
    # 3. 分析结果
    if result['status'] == 'success':
        print(f"找到 {result['layers_with_data']} 个有数据的图层")
        for layer_result in result['results']:
            print(f"  图层: {layer_result['layer']}, 值: {layer_result['value']}")
    else:
        print(f"查询失败: {result['message']}")
```
