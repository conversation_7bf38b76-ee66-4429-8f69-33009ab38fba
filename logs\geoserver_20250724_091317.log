2025-07-24 09:13:17,928 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250724_091317.log
2025-07-24 09:13:17,930 - geo_publisher - INFO - 加载了 18 个任务状态
2025-07-24 09:13:17,945 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-24 09:13:17,946 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-24 09:13:17,967 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-24 09:13:17,975 - root - INFO - === GeoServer REST API服务 ===
2025-07-24 09:13:17,975 - root - INFO - 主机: 0.0.0.0
2025-07-24 09:13:17,976 - root - INFO - 端口: 5083
2025-07-24 09:13:17,979 - root - INFO - 调试模式: 禁用
2025-07-24 09:13:17,979 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-24 09:13:18,072 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-24 09:13:18,073 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-24 09:45:07,298 - batch_executor - INFO - 启动任务 f6068397-37b4-4f98-b1b5-7e2af1a23e6c: D:\Drone_Project\ODM\ODM\run.bat D:/Drone_Project/nginxData/ODM/Input/20250705171602/project
2025-07-24 09:45:07,300 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:45:07] "GET /api/batch/execute?project_path=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project&fast-orthophoto HTTP/1.1" 200 -
2025-07-24 09:45:07,338 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:45:07] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:46:10,242 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:46:10] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:47:13,415 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:47:13] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:48:16,148 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:48:16] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:49:18,297 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:49:18] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:50:20,426 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:50:20] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:51:22,658 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:51:22] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:52:24,788 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:52:24] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:53:26,916 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:53:26] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:54:29,130 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:54:29] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:55:31,378 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:55:31] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:56:34,448 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:56:34] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:57:35,922 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:57:35] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:58:39,613 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:58:39] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 09:59:42,433 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 09:59:42] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 10:00:44,759 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:00:44] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 10:01:47,060 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:01:47] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 10:02:49,200 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:02:49] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 10:03:51,820 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:03:51] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 10:04:53,952 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:04:53] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 10:05:56,114 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:05:56] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 10:06:58,214 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:06:58] "GET /api/batch/status?task_id=f6068397-37b4-4f98-b1b5-7e2af1a23e6c HTTP/1.1" 200 -
2025-07-24 10:07:00,015 - tif_executor - INFO - 启动TIF处理任务 455ae717-9ef6-40de-9f54-115f95e8dd3d: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif -> D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp
2025-07-24 10:07:00,016 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - INFO - TIF处理任务 455ae717-9ef6-40de-9f54-115f95e8dd3d 开始执行
2025-07-24 10:07:00,018 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:07:00] "GET /api/tif/execute?input_tif=D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&output_shp=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-24 10:07:00,024 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - INFO - 开始时间: 2025-07-24 10:07:00
2025-07-24 10:07:00,029 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - INFO - 处理参数: {
  "input_tif": "D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 10:07:00,041 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-24 10:07:00,060 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:07:00] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:07:04,265 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (10:07:04)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-24 10:07:04,317 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-24 10:07:04,677 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:05,  3.90it/s]
2025-07-24 10:07:04,804 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 25.10it/s]
2025-07-24 10:07:04,971 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理数据块:  57%|########################################                              | 12/21 [00:00<00:00, 24.60it/s]
2025-07-24 10:07:05,095 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 24.47it/s]
2025-07-24 10:07:05,238 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理数据块:  86%|############################################################          | 18/21 [00:00<00:00, 23.27it/s]
2025-07-24 10:07:05,271 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 24.66it/s]
2025-07-24 10:07:05,274 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-24 10:07:05,275 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-24 10:07:05,317 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 514.05it/s]
2025-07-24 10:07:07,580 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.26 秒 (10:07:06)预计总处理时间: 约 6.77 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif (10:07:06)写入带有nodata值的影像...
2025-07-24 10:07:07,581 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-24 10:07:08,435 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 10:07:08,436 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:07:08,541 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:00, 29.34it/s]
2025-07-24 10:07:08,542 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:07:08,643 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:00<00:00, 63.80it/s]
2025-07-24 10:07:08,644 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:07:08,752 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 1/3 写入进度: 100%|###############################################################| 21/21 [00:00<00:00, 72.48it/s]
2025-07-24 10:07:08,753 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:07:08,757 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:08:02,187 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:08:02] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:08:42,086 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [01:34<03:08, 94.50s/it]
2025-07-24 10:08:45,131 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 10:08:45,132 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:08:45,297 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  6.16it/s]
2025-07-24 10:08:45,298 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:08:45,413 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:01, 16.12it/s]
2025-07-24 10:08:45,415 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:08:45,541 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:00, 19.34it/s]
2025-07-24 10:08:45,541 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:08:45,658 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 21.71it/s]
2025-07-24 10:08:45,659 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:08:45,781 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:00<00:00, 22.61it/s]
2025-07-24 10:08:45,782 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:08:45,902 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:00<00:00, 23.33it/s]
2025-07-24 10:08:45,903 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:08:46,023 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:00<00:00, 23.81it/s]
2025-07-24 10:08:46,024 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:08:46,111 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:09:04,323 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:09:04] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:10:07,407 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:10:07] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:11:09,487 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:11:09] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:12:11,608 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:12:11] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:13:13,707 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:13:13] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:14:15,835 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:14:15] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:15:17,977 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:15:17] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:16:20,120 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:16:20] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:17:22,282 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:17:22] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:18:24,429 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:18:24] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:19:26,550 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:19:26] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:20:28,686 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:20:28] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:21:30,865 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:21:30] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:21:42,122 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理波段:  67%|################################################6                        | 2/3 [14:34<08:17, 497.76s/it]
2025-07-24 10:21:43,125 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 10:21:43,126 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:21:43,315 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  5.44it/s]
2025-07-24 10:21:43,316 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:21:43,418 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:01, 11.69it/s]
2025-07-24 10:21:43,418 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:21:43,564 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 3/3 写入进度:  29%|##################2                                             | 6/21 [00:00<00:00, 15.82it/s]
2025-07-24 10:21:43,566 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:21:43,695 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:00, 18.52it/s]
2025-07-24 10:21:43,696 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:21:43,806 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 3/3 写入进度:  57%|####################################                           | 12/21 [00:00<00:00, 21.14it/s]
2025-07-24 10:21:43,807 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:21:43,941 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 3/3 写入进度:  71%|#############################################                  | 15/21 [00:00<00:00, 21.54it/s]
2025-07-24 10:21:43,943 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:21:44,061 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 3/3 写入进度:  86%|######################################################         | 18/21 [00:00<00:00, 22.63it/s]
2025-07-24 10:21:44,062 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:21:44,188 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
波段 3/3 写入进度: 100%|###############################################################| 21/21 [00:01<00:00, 22.90it/s]
2025-07-24 10:21:44,189 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:21:44,192 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - [A
2025-07-24 10:22:32,990 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:22:32] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:23:35,128 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:23:35] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:24:37,317 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:24:37] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:25:39,486 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:25:39] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:26:41,633 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:26:41] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:26:45,329 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [19:37<00:00, 408.92s/it]
2025-07-24 10:26:45,333 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [19:37<00:00, 392.58s/it]
2025-07-24 10:27:43,912 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:27:43] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:28:46,865 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:28:46] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:29:12,674 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - INFO - 影像保存完成，耗时: 1179.34 秒 (10:26:45)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp (10:26:45)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif创建掩码，识别有效区域...轻微平滑处理掩码，减少边缘锯齿...寻找有效区域轮廓...找到 2 个初始轮廓使用所有 2 个轮廓，不进行面积过滤转换轮廓为地理坐标...
2025-07-24 10:29:12,702 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理轮廓:   0%|                                                                                  | 0/2 [00:00<?, ?it/s]
2025-07-24 10:29:13,358 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理轮廓: 100%|##########################################################################| 2/2 [00:00<00:00,  3.12it/s]
2025-07-24 10:29:13,360 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
处理轮廓: 100%|##########################################################################| 2/2 [00:00<00:00,  3.11it/s]
2025-07-24 10:29:13,467 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - INFO - 成功创建 2 个多边形合并多边形...已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.shp写入shapefile特征...处理MultiPolygon，包含 2 个多边形
2025-07-24 10:29:13,468 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
写入多边形:   0%|                                                                                | 0/2 [00:00<?, ?it/s]
2025-07-24 10:29:13,498 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - ERROR - 
写入多边形: 100%|########################################################################| 2/2 [00:00<00:00, 93.65it/s]
2025-07-24 10:29:22,941 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - INFO - TIF处理任务 455ae717-9ef6-40de-9f54-115f95e8dd3d 执行成功
2025-07-24 10:29:22,942 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - INFO - 完成时间: 2025-07-24 10:29:22
2025-07-24 10:29:22,945 - tif_task_455ae717-9ef6-40de-9f54-115f95e8dd3d - INFO - 状态: 运行成功
2025-07-24 10:29:49,143 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:29:49] "GET /api/tif/status?task_id=455ae717-9ef6-40de-9f54-115f95e8dd3d HTTP/1.1" 200 -
2025-07-24 10:29:50,547 - geo_task_9d13d791-9a4a-41a2-9c1c-4d3b80011e28 - INFO - GeoServer发布任务 9d13d791-9a4a-41a2-9c1c-4d3b80011e28 开始执行
2025-07-24 10:29:50,545 - geo_publisher - INFO - 启动GeoTIFF发布任务 9d13d791-9a4a-41a2-9c1c-4d3b80011e28: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-24 10:29:50,550 - geo_task_9d13d791-9a4a-41a2-9c1c-4d3b80011e28 - INFO - 开始时间: 2025-07-24 10:29:50
2025-07-24 10:29:50,551 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:29:50] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif&workspace=testodm&charset=UTF-8 HTTP/1.1" 200 -
2025-07-24 10:29:50,551 - geo_task_9d13d791-9a4a-41a2-9c1c-4d3b80011e28 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif",
  "workspace": "testodm",
  "store_name": null,
  "layer_name": null
}
2025-07-24 10:29:50,554 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-24 10:29:50,556 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-24 10:29:50,576 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:29:50] "GET /api/geo/status?task_id=9d13d791-9a4a-41a2-9c1c-4d3b80011e28 HTTP/1.1" 200 -
2025-07-24 10:29:52,854 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-24 10:29:52,855 - root - INFO - 未提供存储名称，使用文件名: 20250705171602
2025-07-24 10:29:52,859 - root - INFO - 未提供图层名称，使用存储名称: 20250705171602
2025-07-24 10:29:55,350 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171602\20250705171602.tif
2025-07-24 10:30:51,573 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:30:51] "GET /api/geo/status?task_id=9d13d791-9a4a-41a2-9c1c-4d3b80011e28 HTTP/1.1" 200 -
2025-07-24 10:31:52,330 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:31:52] "GET /api/geo/status?task_id=9d13d791-9a4a-41a2-9c1c-4d3b80011e28 HTTP/1.1" 200 -
2025-07-24 10:32:52,873 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:32:52] "GET /api/geo/status?task_id=9d13d791-9a4a-41a2-9c1c-4d3b80011e28 HTTP/1.1" 200 -
2025-07-24 10:33:53,492 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:33:53] "GET /api/geo/status?task_id=9d13d791-9a4a-41a2-9c1c-4d3b80011e28 HTTP/1.1" 200 -
2025-07-24 10:34:54,020 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:34:54] "GET /api/geo/status?task_id=9d13d791-9a4a-41a2-9c1c-4d3b80011e28 HTTP/1.1" 200 -
2025-07-24 10:35:54,670 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:35:54] "GET /api/geo/status?task_id=9d13d791-9a4a-41a2-9c1c-4d3b80011e28 HTTP/1.1" 200 -
2025-07-24 10:36:56,855 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:36:56] "GET /api/geo/status?task_id=9d13d791-9a4a-41a2-9c1c-4d3b80011e28 HTTP/1.1" 200 -
2025-07-24 10:37:58,638 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:37:58] "GET /api/geo/status?task_id=9d13d791-9a4a-41a2-9c1c-4d3b80011e28 HTTP/1.1" 200 -
2025-07-24 10:39:00,544 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:39:00] "GET /api/geo/status?task_id=9d13d791-9a4a-41a2-9c1c-4d3b80011e28 HTTP/1.1" 200 -
2025-07-24 10:39:53,581 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 到 'testodm:20250705171602'
2025-07-24 10:39:53,990 - root - INFO - 图层验证成功: 直接图层路径: testodm:20250705171602
2025-07-24 10:39:53,993 - root - INFO - GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif' 成功发布为图层 'testodm:20250705171602'
2025-07-24 10:39:54,001 - geo_task_9d13d791-9a4a-41a2-9c1c-4d3b80011e28 - INFO - GeoServer发布任务 9d13d791-9a4a-41a2-9c1c-4d3b80011e28 执行成功
2025-07-24 10:39:54,004 - geo_task_9d13d791-9a4a-41a2-9c1c-4d3b80011e28 - INFO - 完成时间: 2025-07-24 10:39:53
2025-07-24 10:39:54,005 - geo_task_9d13d791-9a4a-41a2-9c1c-4d3b80011e28 - INFO - 状态: 发布成功
2025-07-24 10:40:02,476 - werkzeug - INFO - 127.0.0.1 - - [24/Jul/2025 10:40:02] "GET /api/geo/status?task_id=9d13d791-9a4a-41a2-9c1c-4d3b80011e28 HTTP/1.1" 200 -
