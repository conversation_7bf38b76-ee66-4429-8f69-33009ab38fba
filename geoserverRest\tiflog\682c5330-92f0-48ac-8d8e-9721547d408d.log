2025-07-16 15:38:18,336 - INFO - TIF处理任务 682c5330-92f0-48ac-8d8e-9721547d408d 开始执行
2025-07-16 15:38:18,336 - INFO - 开始时间: 2025-07-16 15:38:18
2025-07-16 15:38:18,340 - INFO - 处理参数: {
  "input_tif": "D:/Drone_Project/geoserverapi/data/20250701/nanning.tif",
  "output_tif": "D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.tif",
  "output_shp": "D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-16 15:38:18,793 - INFO - 开始处理影像: D:/Drone_Project/geoserverapi/data/20250701/nanning.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False影像尺寸: 3840x3328x3估计内存使用: 总计 195.00 MB, 单个处理块 58.59 MB开始创建掩码... (15:38:18)使用全图检测无效区域模式...将处理 4 个数据块...使用多线程处理 (7 线程)...
2025-07-16 15:38:18,815 - ERROR - 
处理数据块:   0%|                                                                                | 0/4 [00:00<?, ?it/s]
2025-07-16 15:38:18,822 - ERROR - 
处理数据块: 100%|######################################################################| 4/4 [00:00<00:00, 1000.01it/s]
2025-07-16 15:38:18,823 - INFO - 已完成: 4/4 块 (100.0%)合并处理结果...
2025-07-16 15:38:18,824 - ERROR - 
合并结果:   0%|                                                                                  | 0/4 [00:00<?, ?it/s]
2025-07-16 15:38:18,827 - ERROR - 
合并结果: 100%|########################################################################| 4/4 [00:00<00:00, 2000.86it/s]
2025-07-16 15:38:18,908 - INFO - 合并进度: 4/4 块 (100.0%)统计结果: 总像素 12779520, 有效像素 12779514 (100.00%), 无效像素 6 (0.00%)掩码创建完成，耗时: 0.12 秒 (15:38:18)预计总处理时间: 约 0.35 秒开始设置nodata值并保存到: D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.tif (15:38:18)写入带有nodata值的影像...
2025-07-16 15:38:18,910 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-16 15:38:18,963 - ERROR - 
波段 1/3 写入进度:   0%|                                                                         | 0/4 [00:00<?, ?it/s]
2025-07-16 15:38:18,966 - ERROR - [A
2025-07-16 15:38:19,022 - ERROR - [A
2025-07-16 15:38:19,127 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:00<00:00,  4.73it/s]
2025-07-16 15:38:19,152 - ERROR - 
波段 2/3 写入进度:   0%|                                                                         | 0/4 [00:00<?, ?it/s]
2025-07-16 15:38:19,153 - ERROR - [A
2025-07-16 15:38:19,170 - ERROR - [A
2025-07-16 15:38:19,299 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [00:00<00:00,  5.30it/s]
2025-07-16 15:38:19,322 - ERROR - 
波段 3/3 写入进度:   0%|                                                                         | 0/4 [00:00<?, ?it/s]
2025-07-16 15:38:19,323 - ERROR - [A
2025-07-16 15:38:19,340 - ERROR - [A
2025-07-16 15:38:19,468 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [00:00<00:00,  5.58it/s]
2025-07-16 15:38:19,469 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [00:00<00:00,  5.42it/s]
2025-07-16 15:38:19,896 - INFO - 影像保存完成，耗时: 0.61 秒 (15:38:19)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.shp (15:38:19)从处理后的TIF文件提取有效区域: D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.tif创建掩码，识别有效区域...轻微平滑处理掩码，减少边缘锯齿...寻找有效区域轮廓...找到 1 个初始轮廓使用所有 1 个轮廓，不进行面积过滤转换轮廓为地理坐标...
2025-07-16 15:38:19,981 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-16 15:38:20,016 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00, 44.42it/s]
2025-07-16 15:38:20,055 - INFO - 成功创建 1 个多边形合并多边形...创建新shapefile: D:/Drone_Project/geoserverapi/data/20250701/odm_orthophoto_out.shp写入shapefile特征...处理单个Polygon
2025-07-16 15:38:20,056 - ERROR - 
写入多边形:   0%|                                                                                | 0/1 [00:00<?, ?it/s]
2025-07-16 15:38:20,064 - ERROR - 
写入多边形: 100%|#######################################################################| 1/1 [00:00<00:00, 166.56it/s]
2025-07-16 15:38:20,119 - INFO - TIF处理任务 682c5330-92f0-48ac-8d8e-9721547d408d 执行成功
2025-07-16 15:38:20,121 - INFO - 完成时间: 2025-07-16 15:38:20
2025-07-16 15:38:20,123 - INFO - 状态: 运行成功
