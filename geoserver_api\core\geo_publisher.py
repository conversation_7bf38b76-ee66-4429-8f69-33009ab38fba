#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer发布器 (Django版本) - 与Flask版本完全一致
"""

import os
import sys
import logging
import threading
import time
import uuid
import json
import datetime
import traceback
from django.conf import settings

# 导入GeoServer管理器
from .geoserver_manager import GeoServerManager

# 创建日志记录器 - 与Flask版本一致
geo_logger = logging.getLogger('geo_publisher')


class GeoStatus:
    """GeoServer发布状态常量 - 与Flask版本完全一致"""

    RUNNING = "正在运行"
    SUCCESS = "发布成功"
    FAILED = "发布失败"


class GeoPublisher:
    """GeoServer发布器 - 与Flask版本完全一致"""

    def __init__(self):
        """初始化GeoServer发布器"""
        self.geo_tasks = {}
        self.tasks_lock = threading.Lock()
        
        # 创建日志目录
        self.log_dir = settings.BASE_DIR / 'geoserverRest' / 'geolog'
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 状态文件路径
        self.status_file = self.log_dir / 'geo_status.json'
        
        # 加载已有任务状态
        self._load_tasks()

    def _load_tasks(self):
        """从文件加载任务状态 - 与Flask版本一致"""
        try:
            if self.status_file.exists():
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    self.geo_tasks = json.load(f)
                geo_logger.info(f"加载了 {len(self.geo_tasks)} 个GeoServer发布任务状态")
        except Exception as e:
            geo_logger.error(f"加载GeoServer发布任务状态失败: {str(e)}")
            self.geo_tasks = {}

    def _save_tasks(self):
        """保存任务状态到文件 - 与Flask版本一致"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.geo_tasks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            geo_logger.error(f"保存GeoServer发布任务状态失败: {str(e)}")

    def execute_publish_shapefile(
        self, shapefile_path, workspace, store_name=None, layer_name=None, charset="UTF-8"
    ):
        """
        异步执行Shapefile发布任务 - 与Flask版本完全一致

        参数:
            shapefile_path: Shapefile文件路径
            workspace: 工作区名称
            store_name: 存储名称（可选）
            layer_name: 图层名称（可选）
            charset: 字符集（默认UTF-8）

        返回:
            task_id: 任务ID
        """
        task_id = str(uuid.uuid4())

        # 必需参数检查
        if not shapefile_path or not workspace:
            geo_logger.error("缺少必要参数shapefile_path或workspace")
            return None

        # 创建任务日志文件
        log_file_path = str(self.log_dir / f"{task_id}.log")

        # 记录任务信息
        task_info = {
            "task_id": task_id,
            "type": "publish_shapefile",
            "parameters": {
                "shapefile_path": shapefile_path,
                "workspace": workspace,
                "store_name": store_name,
                "layer_name": layer_name,
                "charset": charset,
            },
            "status": GeoStatus.RUNNING,
            "start_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": None,
            "log_file": log_file_path,
        }

        with self.tasks_lock:
            self.geo_tasks[task_id] = task_info
            self._save_tasks()

        # 启动执行线程
        thread = threading.Thread(
            target=self._run_publish_shapefile,
            args=(
                task_id,
                shapefile_path,
                workspace,
                store_name,
                layer_name,
                charset,
                log_file_path,
            ),
            daemon=True,
        )
        thread.start()

        geo_logger.info(
            f"启动Shapefile发布任务 {task_id}: {shapefile_path}, workspace={workspace}"
        )
        return task_id

    def execute_publish_geotiff(
        self, geotiff_path, workspace, store_name=None, layer_name=None
    ):
        """
        异步执行GeoTIFF发布任务 - 与Flask版本完全一致

        参数:
            geotiff_path: GeoTIFF文件路径
            workspace: 工作区名称
            store_name: 存储名称（可选）
            layer_name: 图层名称（可选）

        返回:
            task_id: 任务ID
        """
        task_id = str(uuid.uuid4())

        # 必需参数检查
        if not geotiff_path or not workspace:
            geo_logger.error("缺少必要参数geotiff_path或workspace")
            return None

        # 创建任务日志文件
        log_file_path = str(self.log_dir / f"{task_id}.log")

        # 记录任务信息
        task_info = {
            "task_id": task_id,
            "type": "publish_geotiff",
            "parameters": {
                "geotiff_path": geotiff_path,
                "workspace": workspace,
                "store_name": store_name,
                "layer_name": layer_name,
            },
            "status": GeoStatus.RUNNING,
            "start_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": None,
            "log_file": log_file_path,
        }

        with self.tasks_lock:
            self.geo_tasks[task_id] = task_info
            self._save_tasks()

        # 启动执行线程
        thread = threading.Thread(
            target=self._run_publish_geotiff,
            args=(
                task_id,
                geotiff_path,
                workspace,
                store_name,
                layer_name,
                log_file_path,
            ),
            daemon=True,
        )
        thread.start()

        geo_logger.info(
            f"启动GeoTIFF发布任务 {task_id}: {geotiff_path}, workspace={workspace}"
        )
        return task_id

    def get_task_status(self, task_id):
        """
        获取任务状态 - 与Flask版本完全一致

        参数:
            task_id: 任务ID

        返回:
            任务状态信息字典，如果任务不存在则返回None
        """
        with self.tasks_lock:
            return self.geo_tasks.get(task_id)

    def get_all_tasks(self):
        """
        获取所有任务状态 - 与Flask版本完全一致

        返回:
            任务状态信息字典列表
        """
        with self.tasks_lock:
            return list(self.geo_tasks.values())

    def _run_publish_shapefile(
        self, task_id, shapefile_path, workspace, store_name, layer_name, charset, log_file_path
    ):
        """
        在单独的线程中执行Shapefile发布 - 与Flask版本完全一致
        """
        try:
            # 打开日志文件
            with open(log_file_path, "w", encoding="utf-8") as log_file:
                # 记录启动信息
                log_file.write(f"开始发布Shapefile: {shapefile_path}\n")
                log_file.write(f"工作区: {workspace}\n")
                log_file.write(f"存储名称: {store_name}\n")
                log_file.write(f"图层名称: {layer_name}\n")
                log_file.write(f"字符集: {charset}\n")
                log_file.write(f"开始时间: {self.geo_tasks[task_id]['start_time']}\n")
                log_file.write("-" * 50 + "\n")
                log_file.flush()

                # 创建GeoServer管理器实例
                manager = GeoServerManager()

                # 执行发布
                success = manager.publish_shapefile(
                    shapefile_path, workspace, store_name, layer_name, charset
                )

                # 更新状态
                with self.tasks_lock:
                    self.geo_tasks[task_id]["end_time"] = datetime.datetime.now().strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )
                    self.geo_tasks[task_id]["status"] = (
                        GeoStatus.SUCCESS if success else GeoStatus.FAILED
                    )
                    self._save_tasks()

                # 记录完成信息
                log_file.write(f"\n{'Shapefile发布成功' if success else 'Shapefile发布失败'}\n")
                log_file.write(f"结束时间: {self.geo_tasks[task_id]['end_time']}\n")
                log_file.write(f"状态: {self.geo_tasks[task_id]['status']}\n")

        except Exception as e:
            geo_logger.error(f"执行Shapefile发布任务 {task_id} 时出错: {str(e)}")
            geo_logger.error(traceback.format_exc())
            
            # 更新状态为失败
            with self.tasks_lock:
                self.geo_tasks[task_id]["end_time"] = datetime.datetime.now().strftime(
                    "%Y-%m-%d %H:%M:%S"
                )
                self.geo_tasks[task_id]["status"] = GeoStatus.FAILED
                self.geo_tasks[task_id]["error"] = str(e)
                self._save_tasks()

            # 记录错误到日志文件
            try:
                with open(log_file_path, "a", encoding="utf-8") as log_file:
                    log_file.write(f"\n执行出错: {str(e)}\n")
                    log_file.write(f"完成时间: {self.geo_tasks[task_id]['end_time']}\n")
                    log_file.write(f"状态: {GeoStatus.FAILED}\n")
            except:
                pass

    def _run_publish_geotiff(
        self, task_id, geotiff_path, workspace, store_name, layer_name, log_file_path
    ):
        """
        在单独的线程中执行GeoTIFF发布 - 与Flask版本完全一致
        """
        try:
            # 打开日志文件
            with open(log_file_path, "w", encoding="utf-8") as log_file:
                # 记录启动信息
                log_file.write(f"开始发布GeoTIFF: {geotiff_path}\n")
                log_file.write(f"工作区: {workspace}\n")
                log_file.write(f"存储名称: {store_name}\n")
                log_file.write(f"图层名称: {layer_name}\n")
                log_file.write(f"开始时间: {self.geo_tasks[task_id]['start_time']}\n")
                log_file.write("-" * 50 + "\n")
                log_file.flush()

                # 创建GeoServer管理器实例
                manager = GeoServerManager()

                # 执行发布
                success = manager.publish_geotiff(
                    geotiff_path, workspace, store_name, layer_name
                )

                # 更新状态
                with self.tasks_lock:
                    self.geo_tasks[task_id]["end_time"] = datetime.datetime.now().strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )
                    self.geo_tasks[task_id]["status"] = (
                        GeoStatus.SUCCESS if success else GeoStatus.FAILED
                    )
                    self._save_tasks()

                # 记录完成信息
                log_file.write(f"\n{'GeoTIFF发布成功' if success else 'GeoTIFF发布失败'}\n")
                log_file.write(f"结束时间: {self.geo_tasks[task_id]['end_time']}\n")
                log_file.write(f"状态: {self.geo_tasks[task_id]['status']}\n")

        except Exception as e:
            geo_logger.error(f"执行GeoTIFF发布任务 {task_id} 时出错: {str(e)}")
            geo_logger.error(traceback.format_exc())

            # 更新状态为失败
            with self.tasks_lock:
                self.geo_tasks[task_id]["end_time"] = datetime.datetime.now().strftime(
                    "%Y-%m-%d %H:%M:%S"
                )
                self.geo_tasks[task_id]["status"] = GeoStatus.FAILED
                self.geo_tasks[task_id]["error"] = str(e)
                self._save_tasks()

            # 记录错误到日志文件
            try:
                with open(log_file_path, "a", encoding="utf-8") as log_file:
                    log_file.write(f"\n执行出错: {str(e)}\n")
                    log_file.write(f"完成时间: {self.geo_tasks[task_id]['end_time']}\n")
                    log_file.write(f"状态: {GeoStatus.FAILED}\n")
            except:
                pass


# 全局GeoServer发布器实例
geo_executor = GeoPublisher()
