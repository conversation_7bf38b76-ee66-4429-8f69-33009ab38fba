#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer发布器 (Django版本)
"""

import os
import sys
import logging
import threading
import time
import uuid
import json
from datetime import datetime
from django.conf import settings

from .manager import GeoServerManager

# 获取Django配置的日志记录器
logger = logging.getLogger('geoserver_api')


class GeoPublisher:
    """GeoServer异步发布器"""

    def __init__(self):
        """初始化GeoServer发布器"""
        self.tasks = {}
        self.log_dir = settings.BASE_DIR / 'geoserverRest' / 'geolog'
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.status_file = self.log_dir / 'geo_status.json'
        self.load_status()

    def load_status(self):
        """从文件加载任务状态"""
        try:
            if self.status_file.exists():
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    self.tasks = json.load(f)
                logger.info(f"加载了 {len(self.tasks)} 个GeoServer发布任务状态")
        except Exception as e:
            logger.error(f"加载GeoServer发布任务状态失败: {str(e)}")
            self.tasks = {}

    def save_status(self):
        """保存任务状态到文件"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.tasks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存GeoServer发布任务状态失败: {str(e)}")

    def publish_shapefile_async(self, file_path, workspace, **kwargs):
        """
        异步发布Shapefile

        Args:
            file_path: Shapefile文件路径
            workspace: 工作区名称
            **kwargs: 其他发布参数

        Returns:
            任务ID
        """
        task_id = str(uuid.uuid4())
        
        # 创建任务记录
        task_info = {
            'id': task_id,
            'type': 'shapefile',
            'file_path': file_path,
            'workspace': workspace,
            'params': kwargs,
            'status': 'running',
            'start_time': datetime.now().isoformat(),
            'end_time': None,
            'log_file': str(self.log_dir / f'{task_id}.log')
        }
        
        self.tasks[task_id] = task_info
        self.save_status()
        
        # 在新线程中执行发布
        thread = threading.Thread(
            target=self._publish_shapefile_thread,
            args=(task_id, file_path, workspace, kwargs)
        )
        thread.daemon = True
        thread.start()
        
        logger.info(f"启动Shapefile发布任务 {task_id}: {file_path}")
        return task_id

    def _publish_shapefile_thread(self, task_id, file_path, workspace, params):
        """在线程中发布Shapefile"""
        log_file = self.log_dir / f'{task_id}.log'
        
        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"开始发布Shapefile: {file_path}\n")
                f.write(f"工作区: {workspace}\n")
                f.write(f"参数: {params}\n")
                f.write(f"开始时间: {datetime.now().isoformat()}\n")
                f.write("-" * 50 + "\n")
                f.flush()
                
                # 创建GeoServer管理器实例
                manager = GeoServerManager()
                
                # 发布Shapefile
                result = manager.publish_shapefile(
                    file_path=file_path,
                    workspace=workspace,
                    store=params.get('store_name'),
                    layer=params.get('layer_name'),
                    charset=params.get('charset', 'UTF-8')
                )
                
                if result:
                    f.write("Shapefile发布成功\n")
                    status = 'completed'
                else:
                    f.write("Shapefile发布失败\n")
                    status = 'failed'
                
                f.write(f"结束时间: {datetime.now().isoformat()}\n")
            
            # 更新任务状态
            self.tasks[task_id].update({
                'status': status,
                'end_time': datetime.now().isoformat(),
                'result': result
            })
            
            logger.info(f"Shapefile发布任务 {task_id} {'完成' if result else '失败'}")
            
        except Exception as e:
            # 更新任务状态为失败
            self.tasks[task_id].update({
                'status': 'failed',
                'end_time': datetime.now().isoformat(),
                'error': str(e)
            })
            
            # 写入错误日志
            try:
                with open(log_file, 'a', encoding='utf-8') as f:
                    f.write(f"\n发布出错: {str(e)}\n")
            except:
                pass
            
            logger.error(f"Shapefile发布任务 {task_id} 失败: {str(e)}")
        
        finally:
            self.save_status()

    def publish_geotiff_async(self, file_path, workspace, **kwargs):
        """
        异步发布GeoTIFF

        Args:
            file_path: GeoTIFF文件路径
            workspace: 工作区名称
            **kwargs: 其他发布参数

        Returns:
            任务ID
        """
        task_id = str(uuid.uuid4())
        
        # 创建任务记录
        task_info = {
            'id': task_id,
            'type': 'geotiff',
            'file_path': file_path,
            'workspace': workspace,
            'params': kwargs,
            'status': 'running',
            'start_time': datetime.now().isoformat(),
            'end_time': None,
            'log_file': str(self.log_dir / f'{task_id}.log')
        }
        
        self.tasks[task_id] = task_info
        self.save_status()
        
        # 在新线程中执行发布
        thread = threading.Thread(
            target=self._publish_geotiff_thread,
            args=(task_id, file_path, workspace, kwargs)
        )
        thread.daemon = True
        thread.start()
        
        logger.info(f"启动GeoTIFF发布任务 {task_id}: {file_path}")
        return task_id

    def _publish_geotiff_thread(self, task_id, file_path, workspace, params):
        """在线程中发布GeoTIFF"""
        log_file = self.log_dir / f'{task_id}.log'
        
        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"开始发布GeoTIFF: {file_path}\n")
                f.write(f"工作区: {workspace}\n")
                f.write(f"参数: {params}\n")
                f.write(f"开始时间: {datetime.now().isoformat()}\n")
                f.write("-" * 50 + "\n")
                f.flush()
                
                # 创建GeoServer管理器实例
                manager = GeoServerManager()
                
                # 发布GeoTIFF
                result = manager.publish_geotiff(
                    file_path=file_path,
                    workspace=workspace,
                    store=params.get('store_name'),
                    layer=params.get('layer_name')
                )
                
                if result:
                    f.write("GeoTIFF发布成功\n")
                    status = 'completed'
                else:
                    f.write("GeoTIFF发布失败\n")
                    status = 'failed'
                
                f.write(f"结束时间: {datetime.now().isoformat()}\n")
            
            # 更新任务状态
            self.tasks[task_id].update({
                'status': status,
                'end_time': datetime.now().isoformat(),
                'result': result
            })
            
            logger.info(f"GeoTIFF发布任务 {task_id} {'完成' if result else '失败'}")
            
        except Exception as e:
            # 更新任务状态为失败
            self.tasks[task_id].update({
                'status': 'failed',
                'end_time': datetime.now().isoformat(),
                'error': str(e)
            })
            
            # 写入错误日志
            try:
                with open(log_file, 'a', encoding='utf-8') as f:
                    f.write(f"\n发布出错: {str(e)}\n")
            except:
                pass
            
            logger.error(f"GeoTIFF发布任务 {task_id} 失败: {str(e)}")
        
        finally:
            self.save_status()

    def get_task_status(self, task_id):
        """获取任务状态"""
        return self.tasks.get(task_id)

    def get_all_tasks(self):
        """获取所有任务状态"""
        return list(self.tasks.values())

    def get_task_log(self, task_id):
        """获取任务日志"""
        task = self.tasks.get(task_id)
        if not task:
            return None
        
        log_file = task.get('log_file')
        if not log_file or not os.path.exists(log_file):
            return None
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取GeoServer发布任务日志失败: {str(e)}")
            return None


# 全局GeoServer发布器实例
geo_executor = GeoPublisher()
geo_logger = logger
