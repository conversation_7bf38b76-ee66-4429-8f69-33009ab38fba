2025-07-28 09:48:50,818 - INFO - ============ TIF处理任务 fa4b06bc-66c8-4de0-a0bb-703173c8023c 开始执行 ============
2025-07-28 09:48:50,831 - INFO - 开始时间: 2025-07-28 09:48:50
2025-07-28 09:48:50,835 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 09:48:50,840 - INFO - 系统信息:
2025-07-28 09:48:50,843 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 09:48:50,856 - INFO -   Python版本: 3.8.20
2025-07-28 09:48:50,857 - INFO -   GDAL版本: 3.9.2
2025-07-28 09:48:50,860 - INFO -   GPU可用: 否
2025-07-28 09:48:50,864 - INFO - 检查参数有效性...
2025-07-28 09:48:50,865 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:48:50,868 - INFO - 开始执行TIF处理流程...
2025-07-28 09:48:50,872 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 09:48:50,874 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 09:48:50,888 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 09:48:50,900 - ERROR - C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\osgeo\gdal.py:312: FutureWarning: Neither gdal.UseExceptions() nor gdal.DontUseExceptions() has been explicitly called. In GDAL 4.0, exceptions will be enabled by default.
  warnings.warn(
2025-07-28 09:48:58,780 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (09:48:58)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 09:48:58,814 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 09:48:58,987 - ERROR - 
处理数据块:  29%|####################2                                                  | 6/21 [00:00<00:00, 44.28it/s]
2025-07-28 09:48:59,530 - ERROR - 
处理数据块:  52%|####################################6                                 | 11/21 [00:00<00:00, 14.42it/s]
2025-07-28 09:48:59,643 - ERROR - 
处理数据块:  86%|############################################################          | 18/21 [00:00<00:00, 24.02it/s]
2025-07-28 09:48:59,702 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 24.65it/s]
2025-07-28 09:48:59,709 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 09:48:59,713 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 09:48:59,794 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 276.95it/s]
2025-07-28 09:49:02,963 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 3.40 秒 (09:49:01)预计总处理时间: 约 10.20 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (09:49:01)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 09:49:02,965 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 09:50:14,754 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 09:50:14,758 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 09:50:14,767 - ERROR - [A
2025-07-28 09:50:16,843 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:02<00:41,  2.07s/it]
2025-07-28 09:50:16,844 - ERROR - [A
2025-07-28 09:50:18,724 - ERROR - 
波段 1/3 写入进度:  10%|######                                                          | 2/21 [00:03<00:37,  1.96s/it]
2025-07-28 09:50:18,736 - ERROR - [A
2025-07-28 09:50:19,174 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:04<00:22,  1.27s/it]
2025-07-28 09:50:19,175 - ERROR - [A
2025-07-28 09:50:19,293 - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:04<00:05,  2.68it/s]
2025-07-28 09:50:19,298 - ERROR - [A
2025-07-28 09:50:19,404 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:04<00:01,  5.69it/s]
2025-07-28 09:50:19,405 - ERROR - [A
2025-07-28 09:50:19,521 - ERROR - 
波段 1/3 写入进度:  86%|######################################################         | 18/21 [00:04<00:00, 10.10it/s]
2025-07-28 09:50:19,522 - ERROR - [A
2025-07-28 09:50:19,888 - ERROR - [A
2025-07-28 09:51:51,709 - ERROR - 
处理波段:  33%|########################3                                                | 1/3 [02:48<05:37, 168.73s/it]
