2025-07-24 16:50:23,410 - INFO - ============ TIF处理任务 881f151d-cf18-4740-a577-bf1832971e01 开始执行 ============
2025-07-24 16:50:23,412 - INFO - 开始时间: 2025-07-24 16:50:23
2025-07-24 16:50:23,413 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 16:50:23,437 - INFO - 系统信息:
2025-07-24 16:50:23,442 - INFO -   操作系统: Windows 10.0.19045
2025-07-24 16:50:23,447 - INFO -   Python版本: 3.8.20
2025-07-24 16:50:23,457 - INFO -   GDAL版本: 3.9.2
2025-07-24 16:50:23,462 - INFO -   GPU可用: 否
2025-07-24 16:50:23,464 - INFO - 检查参数有效性...
2025-07-24 16:50:23,466 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:50:23,469 - INFO - 开始执行TIF处理流程...
2025-07-24 16:50:23,470 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:50:23,472 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-24 16:50:23,473 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-24 16:50:27,800 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (16:50:27)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-24 16:50:27,847 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-24 16:50:28,132 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:04,  4.15it/s]
2025-07-24 16:50:28,403 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 17.53it/s]
2025-07-24 16:50:28,815 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 17.21it/s]
2025-07-24 16:50:28,828 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 22.41it/s]
2025-07-24 16:50:28,831 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-24 16:50:28,832 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-24 16:50:28,870 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 552.68it/s]
2025-07-24 16:50:30,129 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 2.34 秒 (16:50:30)预计总处理时间: 约 7.01 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (16:50:30)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-24 16:50:30,132 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-24 16:50:34,271 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-24 16:50:35,356 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:50:35,360 - ERROR - [A
2025-07-24 16:50:35,818 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:09,  2.18it/s]
2025-07-24 16:50:35,818 - ERROR - [A
2025-07-24 16:50:35,919 - ERROR - 
波段 1/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:01, 11.13it/s]
2025-07-24 16:50:35,920 - ERROR - [A
2025-07-24 16:50:36,031 - ERROR - 
波段 1/3 写入进度:  57%|####################################                           | 12/21 [00:00<00:00, 25.25it/s]
2025-07-24 16:50:36,032 - ERROR - [A
2025-07-24 16:50:36,141 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:00<00:00, 36.02it/s]
2025-07-24 16:50:36,141 - ERROR - [A
2025-07-24 16:50:36,178 - ERROR - [A
2025-07-24 16:51:29,169 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:59<01:58, 59.03s/it]
2025-07-24 16:51:34,919 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-24 16:51:34,920 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:51:34,927 - ERROR - [A
2025-07-24 16:51:35,250 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:06,  3.11it/s]
2025-07-24 16:51:35,251 - ERROR - [A
2025-07-24 16:51:35,393 - ERROR - 
波段 2/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:04,  4.61it/s]
2025-07-24 16:51:35,394 - ERROR - [A
2025-07-24 16:51:35,553 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:03,  5.23it/s]
2025-07-24 16:51:35,557 - ERROR - [A
2025-07-24 16:51:35,744 - ERROR - 
波段 2/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:03,  5.23it/s]
2025-07-24 16:51:35,745 - ERROR - [A
2025-07-24 16:51:35,930 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:01<00:03,  5.28it/s]
2025-07-24 16:51:35,934 - ERROR - [A
2025-07-24 16:51:36,147 - ERROR - 
波段 2/3 写入进度:  29%|##################2                                             | 6/21 [00:01<00:02,  5.04it/s]
2025-07-24 16:51:36,148 - ERROR - [A
2025-07-24 16:51:36,266 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:01<00:02,  5.80it/s]
2025-07-24 16:51:36,266 - ERROR - [A
2025-07-24 16:51:36,457 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  7.39it/s]
2025-07-24 16:51:36,458 - ERROR - [A
2025-07-24 16:51:36,599 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  7.30it/s]
2025-07-24 16:51:36,599 - ERROR - [A
2025-07-24 16:51:36,800 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:01<00:01,  8.23it/s]
2025-07-24 16:51:36,801 - ERROR - [A
2025-07-24 16:51:36,927 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:00,  8.14it/s]
2025-07-24 16:51:36,928 - ERROR - [A
2025-07-24 16:51:37,066 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:02<00:00,  9.84it/s]
2025-07-24 16:51:37,067 - ERROR - [A
2025-07-24 16:51:40,999 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:06<00:03,  1.29it/s]
2025-07-24 16:51:41,000 - ERROR - [A
2025-07-24 16:51:44,728 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:09<00:04,  1.39s/it]
2025-07-24 16:51:44,728 - ERROR - [A
2025-07-24 16:51:49,472 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:14<00:04,  2.15s/it]
2025-07-24 16:51:49,472 - ERROR - [A
2025-07-24 16:51:54,678 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:19<00:02,  2.90s/it]
2025-07-24 16:51:54,679 - ERROR - [A
2025-07-24 16:51:55,897 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:20<00:00,  2.46s/it]
2025-07-24 16:51:55,898 - ERROR - [A
2025-07-24 16:51:55,902 - ERROR - [A
2025-07-24 16:52:26,102 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [01:55<00:57, 57.80s/it]
2025-07-24 16:53:01,623 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-24 16:53:01,626 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:53:01,631 - ERROR - [A
2025-07-24 16:53:01,860 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:04,  4.43it/s]
2025-07-24 16:53:01,861 - ERROR - [A
2025-07-24 16:53:01,983 - ERROR - 
波段 3/3 写入进度:  43%|###########################4                                    | 9/21 [00:00<00:00, 30.90it/s]
2025-07-24 16:53:01,984 - ERROR - [A
2025-07-24 16:53:02,123 - ERROR - 
波段 3/3 写入进度:  62%|#######################################                        | 13/21 [00:00<00:00, 30.05it/s]
2025-07-24 16:53:02,127 - ERROR - [A
2025-07-24 16:53:02,385 - ERROR - 
波段 3/3 写入进度:  81%|###################################################            | 17/21 [00:00<00:00, 22.21it/s]
2025-07-24 16:53:02,386 - ERROR - [A
2025-07-24 16:53:02,519 - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [00:00<00:00, 22.23it/s]
2025-07-24 16:53:02,522 - ERROR - [A
2025-07-24 16:53:02,585 - ERROR - [A
2025-07-24 16:53:03,054 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [02:32<00:00, 48.28s/it]
2025-07-24 16:53:03,055 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [02:32<00:00, 50.97s/it]
2025-07-24 16:53:13,270 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 153.37 秒 (16:53:03)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (16:53:03)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1创建输出目录: D:/Drone_Project/nginxData/ODM/Output/20250705171602错误: 输入TIF文件不存在: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-24 16:53:13,273 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-24 16:53:13,441 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:02, 12.49it/s]
2025-07-24 16:53:13,691 - ERROR - 
处理轮廓:  13%|#########4                                                               | 4/31 [00:00<00:02,  9.42it/s]
2025-07-24 16:53:13,745 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:00<00:00, 66.89it/s]
2025-07-24 16:53:14,303 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-24 16:53:14,304 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-24 16:53:14,314 - ERROR - 
写入多边形: 100%|####################################################################| 35/35 [00:00<00:00, 4379.36it/s]
2025-07-24 16:53:14,548 - INFO - 处理完成，耗时: 171.07 秒 (2.85 分钟)
2025-07-24 16:53:14,549 - INFO - 处理结果: 成功
2025-07-24 16:53:14,550 - WARNING - 输出TIF文件不存在或未指定: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-24 16:53:14,552 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-24 16:53:14,556 - INFO - TIF处理任务 881f151d-cf18-4740-a577-bf1832971e01 执行成功
2025-07-24 16:53:14,557 - INFO - 完成时间: 2025-07-24 16:53:14
2025-07-24 16:53:14,557 - INFO - 状态: 运行成功
2025-07-24 16:53:14,558 - INFO - ============ 任务执行结束 ============
