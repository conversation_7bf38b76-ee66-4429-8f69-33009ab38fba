#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer API Django视图
"""

import os
import sys
import logging
import json
import requests
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.conf import settings
from django.utils.decorators import method_decorator
from django.views import View

# 导入核心模块
from .core.manager import GeoServerManager
from .core.raster_query import GeoServerRasterQuery
from .core.batch_executor import executor as batch_executor
from .core.tif_process import tif_executor
from .core.geo_publisher import geo_executor

# 获取日志记录器
logger = logging.getLogger('geoserver_api')

# 初始化全局实例
manager = GeoServerManager()
query = GeoServerRasterQuery(manager)


# =============================================================================
# 基础API端点
# =============================================================================

def health_check(request):
    """健康检查端点"""
    return JsonResponse({
        'status': 'ok',
        'message': 'GeoServer REST API服务正在运行'
    })


# =============================================================================
# 栅格查询API
# =============================================================================

def query_coordinate(request):
    """
    查询坐标点处的栅格图层

    查询参数:
        lat: 纬度 (浮点数)
        lon: 经度 (浮点数)
        workspace: 工作区名称 (字符串)
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')

        if not all([lat, lon, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=400)

        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)

        # 查询栅格值
        result = query.query_raster_value(lat, lon, workspace)
        return JsonResponse(result)

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_layers(request):
    """
    获取工作区中的所有图层

    查询参数:
        workspace: 工作区名称
    """
    try:
        workspace = request.GET.get('workspace')
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: workspace'
            }, status=400)

        layers = query.get_raster_layers(workspace)
        return JsonResponse({
            'status': 'success',
            'workspace': workspace,
            'count': len(layers),
            'layers': layers
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def test_coordinate(request):
    """
    测试坐标点在特定图层中是否有有效数据

    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
        layer: 图层名称
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        layer = request.GET.get('layer')

        if not all([lat, lon, workspace, layer]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace, layer'
            }, status=400)

        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)

        # 查询指定图层的栅格值
        result = query.query_raster_value(lat, lon, workspace, layer)

        # 检查是否有有效数据
        has_data = len(result.get('results', [])) > 0

        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layer': layer,
            'has_data': has_data,
            'result': result
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_pixel_value(request):
    """
    获取坐标点处的像素值

    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
        layer: 图层名称
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        layer = request.GET.get('layer')

        if not all([lat, lon, workspace, layer]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace, layer'
            }, status=400)

        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)

        # 查询指定图层的栅格值
        result = query.query_raster_value(lat, lon, workspace, layer)

        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layer': layer,
            'pixel_value': result.get('results', [])
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def query_layers_at_coordinate(request):
    """
    查询坐标点处的图层并获取像素值

    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')

        if not all([lat, lon, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=400)

        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)

        # 查询栅格值
        result = query.query_raster_value(lat, lon, workspace)

        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layers_found': result.get('results', []),
            'summary': {
                'total_layers': result.get('total_layers', 0),
                'layers_with_data': result.get('layers_with_data', 0)
            }
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


# =============================================================================
# 管理API
# =============================================================================

def get_workspaces(request):
    """获取所有工作区"""
    try:
        workspaces = manager.get_workspaces()
        return JsonResponse({
            'status': 'success',
            'count': len(workspaces),
            'workspaces': workspaces
        })
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def create_workspace(request):
    """
    创建工作区

    查询参数:
        name: 工作区名称
    """
    try:
        name = request.GET.get('name')
        if not name:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: name'
            }, status=400)

        result = manager.create_workspace(name)
        if result:
            return JsonResponse({
                'status': 'success',
                'message': f'工作区 {name} 创建成功'
            })
        else:
            return JsonResponse({
                'status': 'error',
                'message': f'工作区 {name} 创建失败'
            }, status=500)

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def delete_workspace(request):
    """
    删除工作区

    查询参数:
        name: 工作区名称
        recurse: 是否递归删除 (默认: true)
    """
    try:
        name = request.GET.get('name')
        if not name:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: name'
            }, status=400)

        recurse = request.GET.get('recurse', 'true').lower() == 'true'

        result = manager.delete_workspace(name, recurse=recurse)
        if result:
            return JsonResponse({
                'status': 'success',
                'message': f'工作区 {name} 删除成功'
            })
        else:
            return JsonResponse({
                'status': 'error',
                'message': f'工作区 {name} 删除失败'
            }, status=500)

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_management_layers(request):
    """
    获取图层列表

    查询参数:
        workspace: 工作区名称 (可选)
    """
    try:
        workspace = request.GET.get('workspace')
        layers = manager.get_layers(workspace)

        return JsonResponse({
            'status': 'success',
            'workspace': workspace,
            'count': len(layers),
            'layers': layers
        })
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_datastores(request):
    """
    获取指定工作区中的数据存储

    查询参数:
        workspace: 工作区名称
    """
    try:
        workspace = request.GET.get('workspace')
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: workspace'
            }, status=400)

        datastores = manager.get_datastores(workspace)
        return JsonResponse({
            'status': 'success',
            'count': len(datastores),
            'datastores': datastores
        })
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def create_datastore(request):
    """
    创建PostGIS数据存储

    查询参数:
        name: 数据存储名称
        workspace: 工作区名称
        host: 数据库主机地址
        port: 数据库端口
        database: 数据库名称
        username: 用户名
        password: 密码
        schema: 模式名称 (默认: public)
    """
    try:
        name = request.GET.get('name')
        workspace = request.GET.get('workspace')
        host = request.GET.get('host')
        port = request.GET.get('port')
        database = request.GET.get('database')
        username = request.GET.get('username')
        password = request.GET.get('password')
        schema = request.GET.get('schema', 'public')

        if not all([name, workspace, host, port, database, username, password]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: name, workspace, host, port, database, username, password'
            }, status=400)

        try:
            port = int(port)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '端口必须是有效的数字'
            }, status=400)

        result = manager.create_datastore(name, workspace, host, port, database, username, password, schema)

        if result:
            return JsonResponse({
                'status': 'success',
                'message': f'PostGIS数据存储 {workspace}:{name} 创建成功'
            })
        else:
            return JsonResponse({
                'status': 'error',
                'message': f'PostGIS数据存储 {workspace}:{name} 创建失败'
            }, status=500)

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def publish_shapefile(request):
    """
    发布Shapefile到GeoServer

    查询参数:
        file: Shapefile文件路径
        workspace: 工作区名称
        store: 存储名称 (可选)
        layer: 图层名称 (可选)
        charset: 字符集 (默认: UTF-8)
    """
    try:
        file_path = request.GET.get('file')
        workspace = request.GET.get('workspace')
        store = request.GET.get('store')
        layer = request.GET.get('layer')
        charset = request.GET.get('charset', 'UTF-8')

        if not all([file_path, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: file, workspace'
            }, status=400)

        if not os.path.exists(file_path):
            return JsonResponse({
                'status': 'error',
                'message': f'文件不存在: {file_path}'
            }, status=404)

        # 发布Shapefile
        result = manager.publish_shapefile(file_path, workspace, store, layer, charset)

        if result:
            return JsonResponse({
                'status': 'success',
                'message': f'Shapefile {os.path.basename(file_path)} 发布成功为 {workspace}:{layer or store or os.path.splitext(os.path.basename(file_path))[0]}'
            })
        else:
            return JsonResponse({
                'status': 'error',
                'message': f'Shapefile {file_path} 发布失败'
            }, status=500)

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


# =============================================================================
# 批处理API (简化版本，其他函数类似实现)
# =============================================================================

def execute_batch(request):
    """执行批处理文件"""
    try:
        batch_path = request.GET.get('batch_path', 'D:\\Drone_Project\\ODM\\ODM\\run.bat')

        if not os.path.exists(batch_path):
            return JsonResponse({
                'status': 'error',
                'message': f'批处理文件不存在: {batch_path}'
            }, status=404)

        # 收集所有其他参数
        args = {}
        for key, value in request.GET.items():
            if key != 'batch_path' and value:
                args[key] = value

        # 如果没有提供任何参数，使用默认参数
        if not args:
            args = {
                'project_path': 'c://user/20250714140500/project',
                'fast-orthophoto': ''
            }

        # 执行批处理文件
        task_id = batch_executor.execute_batch(batch_path, args)

        return JsonResponse({
            'status': 'success',
            'message': '批处理文件执行已启动',
            'task_id': task_id
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_batch_status(request, task_id):
    """获取批处理任务状态"""
    try:
        task = batch_executor.get_task_status(task_id)
        if not task:
            return JsonResponse({
                'status': 'error',
                'message': f'任务 {task_id} 不存在'
            }, status=404)

        return JsonResponse({
            'status': 'success',
            'task': task
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_batch_log(request, task_id):
    """获取批处理任务日志"""
    try:
        log_content = batch_executor.get_task_log(task_id)
        if log_content is None:
            return JsonResponse({
                'status': 'error',
                'message': f'任务 {task_id} 的日志不存在'
            }, status=404)

        return JsonResponse({
            'status': 'success',
            'task_id': task_id,
            'log': log_content
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_all_batch_status(request):
    """获取所有批处理任务状态"""
    try:
        tasks = batch_executor.get_all_tasks()
        return JsonResponse({
            'status': 'success',
            'count': len(tasks),
            'tasks': tasks
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


# =============================================================================
# TIF处理API (简化版本)
# =============================================================================

def process_tif(request):
    """处理TIF文件"""
    try:
        input_tif = request.GET.get('input_tif')
        if not input_tif:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: input_tif'
            }, status=400)

        if not os.path.exists(input_tif):
            return JsonResponse({
                'status': 'error',
                'message': f'TIF文件不存在: {input_tif}'
            }, status=404)

        # 收集处理参数
        params = {}
        for key, value in request.GET.items():
            if key != 'input_tif' and value:
                params[key] = value

        # 异步处理TIF文件
        task_id = tif_executor.process_tif_async(input_tif, **params)

        return JsonResponse({
            'status': 'success',
            'message': 'TIF处理任务已启动',
            'task_id': task_id
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


# =============================================================================
# 占位函数 (为了保持URL配置完整性)
# =============================================================================

def publish_shapefile_directory(request):
    """发布Shapefile目录 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def publish_geotiff(request):
    """发布GeoTIFF (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def publish_geotiff_directory(request):
    """发布GeoTIFF目录 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def publish_postgis(request):
    """发布PostGIS表 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def delete_layer(request):
    """删除图层 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def set_layer_style(request):
    """设置图层样式 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def execute_batch_file(request):
    """执行批处理文件 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def diagnose_geoserver(request):
    """诊断GeoServer (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def execute_tif_process(request):
    """执行TIF处理 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_tif_status(request, task_id):
    """获取TIF任务状态 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_tif_log(request, task_id):
    """获取TIF任务日志 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_all_tif_status(request):
    """获取所有TIF任务状态 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def execute_publish_shapefile(request):
    """异步发布Shapefile (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def execute_publish_geotiff(request):
    """异步发布GeoTIFF (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_geo_status(request, task_id):
    """获取GeoServer发布任务状态 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_geo_log(request, task_id):
    """获取GeoServer发布任务日志 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_all_geo_status(request):
    """获取所有GeoServer发布任务状态 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_base_map(request):
    """获取基础地图配置 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_base_map2(request):
    """获取基础地图配置2 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_base_map3(request):
    """获取基础地图配置3 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_base_style(request):
    """获取基础样式配置 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_base_style2(request):
    """获取基础样式配置2 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_base_style3(request):
    """获取基础样式配置3 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_map_file(request, filename):
    """获取地图文件 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)

def get_map_logs(request, log_type):
    """获取地图日志 (占位函数)"""
    return JsonResponse({'status': 'error', 'message': '功能暂未实现'}, status=501)
