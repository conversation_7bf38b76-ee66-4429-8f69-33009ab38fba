2025-07-18 11:30:34,969 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250718_113034.log
2025-07-18 11:30:34,971 - geo_publisher - INFO - 加载了 11 个任务状态
2025-07-18 11:30:34,997 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 11:30:34,998 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 11:30:35,021 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 11:30:35,030 - root - INFO - === GeoServer REST API服务 ===
2025-07-18 11:30:35,031 - root - INFO - 主机: 0.0.0.0
2025-07-18 11:30:35,032 - root - INFO - 端口: 5083
2025-07-18 11:30:35,035 - root - INFO - 调试模式: 禁用
2025-07-18 11:30:35,036 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-18 11:30:35,135 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-18 11:30:35,136 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 11:30:46,072 - geo_task_71d4f572-1811-4358-8356-1684f928fcaf - INFO - GeoServer发布任务 71d4f572-1811-4358-8356-1684f928fcaf 开始执行
2025-07-18 11:30:46,071 - geo_publisher - INFO - 启动GeoTIFF发布任务 71d4f572-1811-4358-8356-1684f928fcaf: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=tttt
2025-07-18 11:30:46,078 - geo_task_71d4f572-1811-4358-8356-1684f928fcaf - INFO - 开始时间: 2025-07-18 11:30:46
2025-07-18 11:30:46,082 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:30:46] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace=tttt HTTP/1.1" 200 -
2025-07-18 11:30:46,087 - geo_task_71d4f572-1811-4358-8356-1684f928fcaf - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "tttt",
  "store_name": null,
  "layer_name": null
}
2025-07-18 11:30:46,097 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 11:30:46,099 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 11:30:46,124 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 11:30:46,125 - root - INFO - 未提供存储名称，使用文件名: 20250705171600
2025-07-18 11:30:46,126 - root - INFO - 未提供图层名称，使用存储名称: 20250705171600
2025-07-18 11:30:46,180 - root - INFO - 使用绝对路径: D:\Drone_Project\nginxData\ODM\Output\20250705171600\20250705171600.tif
2025-07-18 11:30:55,272 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:30:55] "GET /api/geo/status?task_id=71d4f572-1811-4358-8356-1684f928fcaf HTTP/1.1" 200 -
2025-07-18 11:49:07,907 - root - INFO - 发布GeoTIFF 'D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif' 到 'tttt:20250705171600'
2025-07-18 11:58:47,656 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:58:47] "GET /api/geo/status?task_id=71d4f572-1811-4358-8356-1684f928fcaf HTTP/1.1" 200 -
2025-07-18 11:59:33,579 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:59:33] "GET /api/geo/status?task_id=71d4f572-1811-4358-8356-1684f928fcaf HTTP/1.1" 200 -
2025-07-18 12:00:39,729 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:00:39] "GET /api/geo/status?task_id=71d4f572-1811-4358-8356-1684f928fcaf HTTP/1.1" 200 -
2025-07-18 12:01:01,201 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 12:01:01] "GET /api/geo/status?task_id=71d4f572-1811-4358-8356-1684f928fcaf HTTP/1.1" 200 -
