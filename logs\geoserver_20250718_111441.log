2025-07-18 11:14:41,594 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250718_111441.log
2025-07-18 11:14:41,596 - geo_publisher - INFO - 加载了 5 个任务状态
2025-07-18 11:14:41,623 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 11:14:41,624 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 11:14:41,650 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 11:14:41,660 - root - INFO - === GeoServer REST API服务 ===
2025-07-18 11:14:41,660 - root - INFO - 主机: 0.0.0.0
2025-07-18 11:14:41,661 - root - INFO - 端口: 5083
2025-07-18 11:14:41,662 - root - INFO - 调试模式: 禁用
2025-07-18 11:14:41,663 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-18 11:14:41,787 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-18 11:14:41,789 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 11:14:42,492 - geo_publisher - INFO - 启动GeoTIFF发布任务 f104757d-4597-4d56-98a5-c7a0906b3e8d: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace="tttt"
2025-07-18 11:14:42,498 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:14:42] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1" 200 -
2025-07-18 11:14:42,520 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:14:42] "GET /api/geo/status?task_id=f104757d-4597-4d56-98a5-c7a0906b3e8d HTTP/1.1" 200 -
2025-07-18 11:14:42,592 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - INFO - GeoServer发布任务 f104757d-4597-4d56-98a5-c7a0906b3e8d 开始执行
2025-07-18 11:14:42,593 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - INFO - 开始时间: 2025-07-18 11:14:42
2025-07-18 11:14:42,597 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "\"tttt\"",
  "store_name": null,
  "layer_name": null
}
2025-07-18 11:14:42,599 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 11:14:42,600 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 11:14:42,620 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 11:14:42,621 - root - INFO - 未提供存储名称，使用文件名: 20250705171600
2025-07-18 11:14:42,621 - root - INFO - 未提供图层名称，使用存储名称: 20250705171600
2025-07-18 11:14:42,672 - root - INFO - 工作区 '"tttt"' 不存在，正在创建...
2025-07-18 11:14:42,803 - geo_publisher - ERROR - 执行任务 f104757d-4597-4d56-98a5-c7a0906b3e8d 时出错: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'
2025-07-18 11:14:42,810 - geo_publisher - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 352, in _run_with_logging
    result = func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 425, in publish_func
    return manager.publish_geotiff(path, ws, store, layer)
  File "D:\Drone_Project\geoserverapi\geoserver_manager.py", line 607, in publish_geotiff
    self.geo.create_workspace(workspace)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\geo\Geoserver.py", line 396, in create_workspace
    raise GeoserverException(r.status_code, r.content)
geo.Geoserver.GeoserverException: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'

2025-07-18 11:14:42,816 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - ERROR - 执行出错: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'
2025-07-18 11:14:42,818 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 352, in _run_with_logging
    result = func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 425, in publish_func
    return manager.publish_geotiff(path, ws, store, layer)
  File "D:\Drone_Project\geoserverapi\geoserver_manager.py", line 607, in publish_geotiff
    self.geo.create_workspace(workspace)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\geo\Geoserver.py", line 396, in create_workspace
    raise GeoserverException(r.status_code, r.content)
geo.Geoserver.GeoserverException: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'

2025-07-18 11:14:42,821 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - ERROR - 完成时间: 2025-07-18 11:14:42
2025-07-18 11:14:42,822 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - ERROR - 状态: 发布失败
2025-07-18 11:15:44,451 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:15:44] "GET /api/geo/status?task_id=f104757d-4597-4d56-98a5-c7a0906b3e8d HTTP/1.1" 200 -
2025-07-18 11:16:27,238 - geo_task_c0fee95d-a07a-42f5-9850-8b0d8ea33fb1 - INFO - GeoServer发布任务 c0fee95d-a07a-42f5-9850-8b0d8ea33fb1 开始执行
