# Map API 使用文档

Map API模块提供了一组接口，用于从127.0.0.1:81获取各种JSON配置文件并提供给用户。这些配置文件包含地图相关的设置，如底图配置和样式定义。

## 可用的API端点

### 1. 获取基础地图配置

#### 获取baseMap.json
```
GET /api/map/base-map
```
返回baseMap.json的内容。

#### 获取baseMap2.json
```
GET /api/map/base-map2
```
返回baseMap2.json的内容。

#### 获取baseMap3.json
```
GET /api/map/base-map3
```
返回baseMap3.json的内容。

### 2. 获取样式配置

#### 获取baseStyle.json
```
GET /api/map/base-style
```
返回baseStyle.json的内容。

#### 获取baseStyle2.json
```
GET /api/map/base-style2
```
返回baseStyle2.json的内容。

#### 获取baseStyle3.json
```
GET /api/map/base-style3
```
返回baseStyle3.json的内容。

### 3. 通用文件访问

#### 根据文件名获取配置
```
GET /api/map/file/{filename}
```

**参数:**
- `filename`: 文件名，可以带或不带.json后缀

**示例:**
```
GET /api/map/file/baseMap
GET /api/map/file/baseStyle.json
```

### 4. 查看可用文件

#### 列出所有可用的配置文件
```
GET /api/map/list
```

返回所有可用的配置文件列表及其对应的API端点。

### 5. ODM任务管理

#### 获取所有ODM任务列表
```
GET /api/map/odm/tasks
```

获取127.0.0.1:81/ODM/Input/目录下的所有任务信息，并按以下优先级排序：
1. 正在运行中的任务（最前面）
2. 未开始的任务（中间）
3. 已完成的任务（最后面，按开始时间倒序排序）

**查询参数:**
- `limit`: 可选，限制返回的任务数量

**示例:**
```
GET /api/map/odm/tasks
GET /api/map/odm/tasks?limit=10
```

**返回格式:**
```json
{
  "status": "success",
  "count": 2,
  "tasks": [
    {
      "task_id": "20250705171602",
      "start_time": "2025-07-24 01:45:03",
      "end_time": "2025-07-24 02:41:26",
      "status": "完成",
      "has_task_info": true,
      "details": {
        "id": "20250705171602",
        "startTime": "2025-07-24 01:45:03",
        "endTime": "2025-07-24 02:41:26",
        "status": "完成",
        "odm_process": { ... },
        "tif_process": { ... },
        "geoserver_publish": { ... },
        "map_config": { ... }
      },
      "raw_data": { ... }
    },
    {
      "task_id": "20250705000000",
      "start_time": "",
      "end_time": "",
      "status": "未开始",
      "has_task_info": false,
      "details": {}
    }
  ]
}
```

#### 删除任务信息文件
```
GET /api/map/odm/task/delete-info?task_id={task_id}
```

删除指定任务目录下的TaskInfo.json文件。

**查询参数:**
- `task_id`: 任务ID（目录名），必须是14位数字

**示例:**
```
GET /api/map/odm/task/delete-info?task_id=20250705171602
```

**返回格式:**
```json
{
  "status": "success",
  "message": "成功删除任务 20250705171602 的TaskInfo.json文件"
}
```

#### 重命名任务信息文件
```
GET /api/map/odm/task/rename-info?task_id={task_id}
```

将指定任务目录下的TaskInfo.json文件重命名为RemoveTask+时间戳.json，保留原始文件内容但标记任务已被移除。

**查询参数:**
- `task_id`: 任务ID（目录名），必须是14位数字

**示例:**
```
GET /api/map/odm/task/rename-info?task_id=20250705171602
```

**返回格式:**
```json
{
  "status": "success",
  "message": "成功将任务 20250705171602 的TaskInfo.json文件重命名为 RemoveTask20250728123456.json",
  "new_filename": "RemoveTask20250728123456.json"
}
```

#### 重置任务配置
```
GET /api/map/odm/reset-task-config
```

重置ODM/Task.cfg中的任务配置，将[TASK]部分下的id和time字段值修改为NONE。

**示例:**
```
GET /api/map/odm/reset-task-config
```

**返回格式:**
```json
{
  "status": "success",
  "message": "成功将Task.cfg中的id和time重置为NONE"
}
```

### 6. 日志查询

#### 获取任务日志内容
```
GET /api/map/logs?log_type={log_type}&log_id={log_id}
```

获取指定类型和ID的任务日志内容。

**查询参数:**
- `log_type`: 日志类型，可选值：batlog, geolog, tiflog
- `log_id`: 日志ID，通常是任务ID或包含任务ID的文件名（不含.log后缀）

**示例:**
```
GET /api/map/logs?log_type=batlog&log_id=216b7e9c-d10d-43de-844b-d046e24c2205
GET /api/map/logs?log_type=geolog&log_id=216b7e9c-d10d-43de-844b-d046e24c2205
GET /api/map/logs?log_type=tiflog&log_id=05ae2ff3-1996-4c74-a831-04ba23126c27
```

**返回格式:**
```json
{
  "status": "success",
  "log_type": "batlog",
  "log_id": "20250705171602",
  "content": "日志内容..."
}
```

**说明:**
- batlog目录包含批处理任务的日志
- geolog目录包含地理处理任务的日志
- tiflog目录包含TIFF文件处理的日志
- 如果日志内容很长，可能会占用较多带宽，请酌情使用

## 响应格式

所有API端点在成功时将返回JSON格式的响应内容，这些内容与源服务器(127.0.0.1:81)上的文件内容一致。

### 成功响应
API会将源服务器提供的JSON内容原样返回，状态码为200。

### 错误响应

如果发生错误，API将返回以下格式的JSON:

```json
{
  "status": "error",
  "message": "错误详细信息"
}
```

可能的错误情况包括:
- 源服务器(127.0.0.1:81)不可用
- 请求的文件不存在
- 服务器内部错误
- 源服务器返回的内容不是有效的JSON

## 配置

该API模块从127.0.0.1:81获取JSON文件，当前支持以下文件:
- baseMap.json
- baseMap2.json
- baseMap3.json
- baseStyle.json
- baseStyle2.json
- baseStyle3.json

如需添加更多文件支持，请修改`map_api.py`中的`AVAILABLE_JSON_FILES`列表。 