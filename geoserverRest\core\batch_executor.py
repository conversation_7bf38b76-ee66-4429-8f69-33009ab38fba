#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批处理文件执行模块 - 提供执行和监控批处理文件的功能
"""

import os
import sys
import uuid
import time
import json
import datetime
import threading
import subprocess
import logging
from logging.handlers import RotatingFileHandler

# 设置日志配置
BATLOG_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'batlog'))

# 确保日志目录存在
if not os.path.exists(BATLOG_DIR):
    os.makedirs(BATLOG_DIR)

# 创建日志处理器
batch_logger = logging.getLogger('batch_executor')
batch_logger.setLevel(logging.INFO)
log_handler = RotatingFileHandler(
    os.path.join(BATLOG_DIR, 'batch_executor.log'),
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
log_handler.setFormatter(formatter)
batch_logger.addHandler(log_handler)

class BatchStatus:
    """BAT文件执行状态常量"""
    RUNNING = "正在运行"
    SUCCESS = "运行成功"
    FAILED = "运行失败"

class BatchExecutor:
    """批处理文件执行器"""
    
    def __init__(self):
        """初始化批处理执行器"""
        self.batch_tasks = {}  # 存储任务ID和状态
        self.tasks_lock = threading.Lock()  # 用于线程安全的操作
        self.status_file = os.path.join(BATLOG_DIR, 'batch_status.json')
        self._load_tasks()
    
    def _load_tasks(self):
        """从文件加载任务状态"""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    self.batch_tasks = json.load(f)
                    batch_logger.info(f"加载了 {len(self.batch_tasks)} 个任务状态")
        except Exception as e:
            batch_logger.error(f"加载任务状态失败: {str(e)}")
    
    def _save_tasks(self):
        """保存任务状态到文件"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.batch_tasks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            batch_logger.error(f"保存任务状态失败: {str(e)}")
    
    def execute_batch(self, batch_path, args=None):
        """
        执行BAT文件
        
        参数:
            batch_path: BAT文件路径
            args: BAT文件参数字典
            
        返回:
            task_id: 任务ID
        """
        # 生成唯一任务ID
        task_id = str(uuid.uuid4())
        
        # 创建命令行
        cmd = [batch_path]
        if args:
            for key, value in args.items():
                if value:  # 有值的参数
                    cmd.extend([f"--{key}", str(value)])
                else:  # 无值的标志参数
                    cmd.append(f"--{key}")
                    
        # 特殊处理项目路径参数 (如果存在)
        if args and 'project_path' in args:
            # 确保项目路径是第一个参数且不带--前缀
            cmd = [batch_path, args['project_path']] + [arg for arg in cmd[1:] if not arg.endswith('project_path') and arg != args['project_path']]
        
        # 创建任务日志文件
        log_file_path = os.path.join(BATLOG_DIR, f"{task_id}.log")
        
        # 记录任务信息
        task_info = {
            'task_id': task_id,
            'batch_path': batch_path,
            'args': args,
            'status': BatchStatus.RUNNING,
            'start_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': None,
            'log_file': log_file_path
        }
        
        with self.tasks_lock:
            self.batch_tasks[task_id] = task_info
            self._save_tasks()
        
        # 启动执行线程
        thread = threading.Thread(
            target=self._run_batch,
            args=(task_id, cmd, log_file_path),
            daemon=True
        )
        thread.start()
        
        batch_logger.info(f"启动任务 {task_id}: {' '.join(cmd)}")
        return task_id
    
    def _run_batch(self, task_id, cmd, log_file_path):
        """
        在单独的线程中执行BAT文件
        
        参数:
            task_id: 任务ID
            cmd: 命令行列表
            log_file_path: 日志文件路径
        """
        try:
            # 打开日志文件
            with open(log_file_path, 'w', encoding='utf-8') as log_file:
                # 记录启动信息
                log_file.write(f"执行命令: {' '.join(cmd)}\n")
                log_file.write(f"开始时间: {self.batch_tasks[task_id]['start_time']}\n\n")
                log_file.flush()
                
                # 执行批处理文件
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    encoding='cp936',  # 使用中文Windows默认编码
                    errors='replace'   # 处理无法解码的字符
                )
                
                # 读取并记录输出
                for line in process.stdout:
                    log_file.write(line)
                    log_file.flush()
                
                # 等待进程完成
                return_code = process.wait()
                
                # 更新状态
                with self.tasks_lock:
                    self.batch_tasks[task_id]['end_time'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self.batch_tasks[task_id]['status'] = BatchStatus.SUCCESS if return_code == 0 else BatchStatus.FAILED
                    self.batch_tasks[task_id]['return_code'] = return_code
                    self._save_tasks()
                
                # 记录完成信息
                log_file.write(f"\n完成时间: {self.batch_tasks[task_id]['end_time']}\n")
                log_file.write(f"返回代码: {return_code}\n")
                log_file.write(f"状态: {self.batch_tasks[task_id]['status']}\n")
                
        except Exception as e:
            batch_logger.error(f"执行任务 {task_id} 时出错: {str(e)}")
            # 更新状态为失败
            with self.tasks_lock:
                self.batch_tasks[task_id]['end_time'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self.batch_tasks[task_id]['status'] = BatchStatus.FAILED
                self.batch_tasks[task_id]['error'] = str(e)
                self._save_tasks()
            
            # 记录错误到日志文件
            try:
                with open(log_file_path, 'a', encoding='utf-8') as log_file:
                    log_file.write(f"\n执行出错: {str(e)}\n")
                    log_file.write(f"完成时间: {self.batch_tasks[task_id]['end_time']}\n")
                    log_file.write(f"状态: {BatchStatus.FAILED}\n")
            except:
                pass
    
    def get_task_status(self, task_id):
        """
        获取任务状态
        
        参数:
            task_id: 任务ID
            
        返回:
            任务状态信息字典，如果任务不存在则返回None
        """
        with self.tasks_lock:
            return self.batch_tasks.get(task_id)
    
    def get_all_tasks(self):
        """
        获取所有任务状态
        
        返回:
            任务状态信息字典列表
        """
        with self.tasks_lock:
            return list(self.batch_tasks.values())
    
    def clear_completed_tasks(self, hours=24):
        """
        清理指定时间前完成的任务
        
        参数:
            hours: 清理多少小时前完成的任务
        """
        cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=hours)
        cutoff_str = cutoff_time.strftime('%Y-%m-%d %H:%M:%S')
        
        with self.tasks_lock:
            to_delete = []
            for task_id, task in self.batch_tasks.items():
                if task['status'] != BatchStatus.RUNNING and task['end_time'] < cutoff_str:
                    to_delete.append(task_id)
            
            for task_id in to_delete:
                del self.batch_tasks[task_id]
            
            if to_delete:
                self._save_tasks()
                batch_logger.info(f"清理了 {len(to_delete)} 个已完成的任务")

# 创建全局执行器实例
executor = BatchExecutor() 