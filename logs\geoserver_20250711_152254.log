2025-07-11 15:22:54,610 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250711_152254.log
2025-07-11 15:22:54,642 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-11 15:22:54,643 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-11 15:22:56,527 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-11 15:22:56,532 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-11 15:22:56,532 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-11 15:22:56,553 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-11 15:22:56,559 - root - INFO - === GeoServer REST API服务 ===
2025-07-11 15:22:56,559 - root - INFO - 主机: 0.0.0.0
2025-07-11 15:22:56,560 - root - INFO - 端口: 5083
2025-07-11 15:22:56,560 - root - INFO - 调试模式: 禁用
2025-07-11 15:22:56,560 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-11 15:22:56,580 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-11 15:22:56,585 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-11 16:21:57,795 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-11 16:21:59,758 - root - INFO - 在工作区 'test_myworkspace' 中找到 5 个矢量图层
2025-07-11 16:21:59,759 - root - INFO - 在工作区 'test_myworkspace' 中找到 16 个图层（11 个栅格，5 个矢量）
2025-07-11 16:21:59,760 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 16:21:59] "GET /api/all_layers?workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-11 16:21:59,833 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 16:21:59] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-11 16:37:01,008 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-11 16:37:01,258 - root - INFO - 在工作区 'test_myworkspace' 中找到 5 个矢量图层
2025-07-11 16:37:01,319 - root - INFO - 在工作区 'test_myworkspace' 中找到 16 个图层（11 个栅格，5 个矢量）
2025-07-11 16:37:01,322 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 16:37:01] "GET /api/all_layers?workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-11 16:37:04,792 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-11 16:37:04,993 - root - INFO - 在工作区 'test_myworkspace' 中找到 5 个矢量图层
2025-07-11 16:37:04,995 - root - INFO - 在工作区 'test_myworkspace' 中找到 16 个图层（11 个栅格，5 个矢量）
2025-07-11 16:37:04,996 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 16:37:04] "GET /api/all_layers?workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-11 16:43:34,515 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 16:43:34] "[33mGET /api/management/workspaces/create?name=ttt HTTP/1.1[0m" 404 -
2025-07-11 16:43:44,319 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-11 16:43:44,509 - root - INFO - 在工作区 'test_myworkspace' 中找到 5 个矢量图层
2025-07-11 16:43:44,510 - root - INFO - 在工作区 'test_myworkspace' 中找到 16 个图层（11 个栅格，5 个矢量）
2025-07-11 16:43:44,512 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 16:43:44] "GET /api/all_layers?workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-11 16:43:49,988 - werkzeug - INFO - 127.0.0.1 - - [11/Jul/2025 16:43:49] "[33mGET /api/management/workspaces/create?name=tttt HTTP/1.1[0m" 404 -
