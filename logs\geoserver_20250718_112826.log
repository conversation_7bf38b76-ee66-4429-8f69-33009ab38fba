2025-07-18 11:28:26,501 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250718_112826.log
2025-07-18 11:28:26,503 - geo_publisher - INFO - 加载了 10 个任务状态
2025-07-18 11:28:26,519 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 11:28:26,520 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 11:28:26,538 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 11:28:26,543 - root - INFO - === GeoServer REST API服务 ===
2025-07-18 11:28:26,544 - root - INFO - 主机: 0.0.0.0
2025-07-18 11:28:26,544 - root - INFO - 端口: 5083
2025-07-18 11:28:26,545 - root - INFO - 调试模式: 禁用
2025-07-18 11:28:26,547 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-18 11:28:26,633 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-18 11:28:26,637 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 11:29:09,471 - geo_publisher - INFO - 启动GeoTIFF发布任务 08b8bd9a-bcb4-4080-84ad-2d7c0001c225: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace="tttt"
2025-07-18 11:29:09,472 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - INFO - GeoServer发布任务 08b8bd9a-bcb4-4080-84ad-2d7c0001c225 开始执行
2025-07-18 11:29:09,480 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:29:09] "GET /api/geo/geotiff/execute?path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif&workspace="tttt"&charset=UTF-8 HTTP/1.1" 200 -
2025-07-18 11:29:09,481 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - INFO - 开始时间: 2025-07-18 11:29:09
2025-07-18 11:29:09,483 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "\"tttt\"",
  "store_name": null,
  "layer_name": null
}
2025-07-18 11:29:09,486 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-18 11:29:09,488 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-18 11:29:09,506 - werkzeug - INFO - 127.0.0.1 - - [18/Jul/2025 11:29:09] "GET /api/geo/status?task_id=08b8bd9a-bcb4-4080-84ad-2d7c0001c225 HTTP/1.1" 200 -
2025-07-18 11:29:09,518 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-18 11:29:09,519 - root - INFO - 未提供存储名称，使用文件名: 20250705171600
2025-07-18 11:29:09,520 - root - INFO - 未提供图层名称，使用存储名称: 20250705171600
2025-07-18 11:29:09,557 - root - INFO - 工作区 '"tttt"' 不存在，正在创建...
2025-07-18 11:29:10,295 - geo_publisher - ERROR - 执行任务 08b8bd9a-bcb4-4080-84ad-2d7c0001c225 时出错: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'
2025-07-18 11:29:10,306 - geo_publisher - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 352, in _run_with_logging
    result = func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 425, in publish_func
    return manager.publish_geotiff(path, ws, store, layer)
  File "D:\Drone_Project\geoserverapi\geoserver_manager.py", line 607, in publish_geotiff
    self.geo.create_workspace(workspace)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\geo\Geoserver.py", line 396, in create_workspace
    raise GeoserverException(r.status_code, r.content)
geo.Geoserver.GeoserverException: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'

2025-07-18 11:29:10,313 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - ERROR - 执行出错: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'
2025-07-18 11:29:10,579 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 352, in _run_with_logging
    result = func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 425, in publish_func
    return manager.publish_geotiff(path, ws, store, layer)
  File "D:\Drone_Project\geoserverapi\geoserver_manager.py", line 607, in publish_geotiff
    self.geo.create_workspace(workspace)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\geo\Geoserver.py", line 396, in create_workspace
    raise GeoserverException(r.status_code, r.content)
geo.Geoserver.GeoserverException: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'

2025-07-18 11:29:10,583 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - ERROR - 完成时间: 2025-07-18 11:29:10
2025-07-18 11:29:10,584 - geo_task_08b8bd9a-bcb4-4080-84ad-2d7c0001c225 - ERROR - 状态: 发布失败
