2025-07-16 09:39:42,388 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250716_093942.log
2025-07-16 09:39:42,389 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 09:39:42,390 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 09:39:42,614 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 09:39:42,665 - batch_executor - INFO - 加载了 35 个任务状态
2025-07-16 09:39:48,251 - tif_executor - INFO - 加载了 1 个任务状态
2025-07-16 09:39:48,325 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 09:39:48,326 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 09:39:48,362 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 09:39:48,369 - root - INFO - === GeoServer REST API服务 ===
2025-07-16 09:39:48,371 - root - INFO - 主机: 0.0.0.0
2025-07-16 09:39:48,372 - root - INFO - 端口: 5083
2025-07-16 09:39:48,373 - root - INFO - 调试模式: 禁用
2025-07-16 09:39:48,373 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-16 09:39:48,387 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-16 09:39:48,388 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-16 09:41:00,826 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:41:00] "GET /api/tif/status?task_id=6b4d8e21-d383-4a21-9453-2f7d34f84a85 HTTP/1.1" 200 -
2025-07-16 09:41:05,094 - tif_api - INFO - 启动异步TIF处理: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto.tif
2025-07-16 09:41:05,434 - tif_executor - INFO - 启动TIF处理任务 7e439b3c-a76f-42be-a3e7-b54099b9235b: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto.tif
2025-07-16 09:41:05,435 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - ========== TIF处理任务开始执行 ==========
2025-07-16 09:41:05,439 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:41:05] "GET /api/tif/process/async?input_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif&output_shp=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-16 09:41:05,440 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 任务ID: 7e439b3c-a76f-42be-a3e7-b54099b9235b
2025-07-16 09:41:05,443 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 输入文件: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto.tif
2025-07-16 09:41:05,447 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 输出TIF: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto_out.tif
2025-07-16 09:41:05,450 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 输出SHP: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto_out.shp
2025-07-16 09:41:05,453 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 处理参数: {"input_tif": "D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250705171600\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250705171600\\project\\odm_orthophoto\\odm_orthophoto_out.tif", "output_shp": "D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250705171600\\project\\odm_orthophoto\\odm_orthophoto_out.shp", "black_threshold": "0", "white_threshold": "255", "keep_alpha": false, "protect_interior": false, "use_gpu": false}
2025-07-16 09:41:05,456 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 开始时间: 2025-07-16 09:41:05
2025-07-16 09:41:05,472 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 处理进度: 0% - 初始化中...
2025-07-16 09:41:05,477 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 开始执行TIF处理...
2025-07-16 09:41:05,480 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 处理模式: 全图检测无效区域
2025-07-16 09:41:05,481 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 使用GPU: False, GPU数量: 全部可用
2025-07-16 09:41:05,484 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 保护内部区域: False, 保留Alpha通道: False
2025-07-16 09:41:05,532 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 处理进度: 5.0% - [读取数据] 正在读取输入影像...
2025-07-16 09:41:05,544 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 处理进度: 5.0% - [读取数据] 正在读取输入影像: D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif
2025-07-16 09:41:28,206 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 处理进度: 10.0% - [读取数据] 影像读取成功，准备处理
2025-07-16 09:41:28,207 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 处理进度: 12.0% - [处理数据] 检测到Alpha通道，正在处理
2025-07-16 09:41:28,208 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 处理进度: 15.0% - [处理数据] 已移除Alpha通道，保留RGB三波段
2025-07-16 09:41:28,212 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 处理进度: 18.0% - [处理数据] 影像尺寸: 38177x48241x3, 内存估计: 27.44 GB
2025-07-16 09:41:28,216 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 处理进度: 20.0% - [创建掩码] 开始创建掩码，识别无效区域
2025-07-16 09:41:28,227 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 处理进度: 22.0% - [创建掩码] 使用全图检测无效区域模式
2025-07-16 09:41:28,230 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 处理进度: 25.0% - [创建掩码] 正在检测RGB三波段无效区域
2025-07-16 09:41:28,251 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 处理进度: 0.0% - [错误] 处理过程中发生错误: create_full_mask_parallel() got an unexpected keyword argument 'progress_callback'
2025-07-16 09:41:29,269 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - ERROR - TIF处理失败
2025-07-16 09:41:29,272 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 完成时间: 2025-07-16 09:41:29
2025-07-16 09:41:29,274 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - 状态: 运行失败
2025-07-16 09:41:29,275 - tif_task_7e439b3c-a76f-42be-a3e7-b54099b9235b - INFO - ========== TIF处理任务结束 ==========
