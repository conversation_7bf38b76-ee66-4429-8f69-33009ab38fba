2025-07-15 16:36:30,950 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250715_163630.log
2025-07-15 16:36:30,951 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-15 16:36:30,951 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-15 16:36:30,995 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-15 16:36:31,000 - batch_executor - INFO - 加载了 35 个任务状态
2025-07-15 16:36:33,860 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-15 16:36:33,861 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-15 16:36:33,873 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-15 16:36:33,878 - root - INFO - === GeoServer REST API服务 ===
2025-07-15 16:36:33,878 - root - INFO - 主机: 0.0.0.0
2025-07-15 16:36:33,879 - root - INFO - 端口: 5083
2025-07-15 16:36:33,879 - root - INFO - 调试模式: 禁用
2025-07-15 16:36:33,880 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-15 16:36:33,900 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-15 16:36:33,901 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-15 16:36:45,448 - tif_api - WARNING - 请求使用GPU但GPU不可用，将使用CPU模式
2025-07-15 16:36:45,453 - tif_api - INFO - 处理请求 - 输入: D:\Drone_Project\geoserverapi\data\20250701\nanning.tif, 输出TIF: D:\Drone_Project\geoserverapi\data\20250701\nanning_out.tif, 输出SHP: D:\Drone_Project\geoserverapi\data\20250701\nanning_out.shp
2025-07-15 16:36:45,457 - tif_api - INFO - 参数: 黑色阈值=10, 白色阈值=245, NoData值=-9999.0
2025-07-15 16:36:45,462 - tif_api - INFO - 简化参数=0.1, 处理模式=full, 保留Alpha=False
2025-07-15 16:36:45,477 - tif_api - INFO - 保护内部区域=True, 使用GPU=False, GPU数量=0
2025-07-15 16:36:46,983 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 16:36:46] "GET /api/tif/process?input_tif=D:/Drone_Project/geoserverapi/data/20250701/nanning.tif&process_mode=full&black_threshold=10&white_threshold=245&protect_interior=true&use_gpu=true HTTP/1.1" 200 -
2025-07-15 16:45:43,962 - tif_api - WARNING - 请求使用GPU但GPU不可用，将使用CPU模式
2025-07-15 16:45:43,967 - tif_api - INFO - 处理请求 - 输入: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto.tif, 输出TIF: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto_out.tif, 输出SHP: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto_out.shp
2025-07-15 16:45:43,969 - tif_api - INFO - 参数: 黑色阈值=10, 白色阈值=245, NoData值=-9999.0
2025-07-15 16:45:43,972 - tif_api - INFO - 简化参数=0.1, 处理模式=full, 保留Alpha=False
2025-07-15 16:45:43,973 - tif_api - INFO - 保护内部区域=True, 使用GPU=False, GPU数量=0
2025-07-15 17:15:28,799 - root - INFO - 获取图层 test_myworkspace:id1 的信息
2025-07-15 17:15:35,280 - root - INFO - 成功获取图层 test_myworkspace:id1 的边界框信息
2025-07-15 17:15:35,728 - werkzeug - INFO - ************** - - [15/Jul/2025 17:15:35] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=id1 HTTP/1.1" 200 -
2025-07-15 17:50:54,202 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 17:50:54] "GET /api/tif/process?input_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&process_mode=full&black_threshold=10&white_threshold=245&protect_interior=true&use_gpu=true HTTP/1.1" 200 -
2025-07-15 18:16:17,464 - tif_api - INFO - 处理请求 - 输入: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto.tif, 输出TIF: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto_out2.tif, 输出SHP: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto_out2.shp
2025-07-15 18:16:17,468 - tif_api - INFO - 参数: 黑色阈值=0, 白色阈值=255, NoData值=-9999.0
2025-07-15 18:16:17,468 - tif_api - INFO - 简化参数=0.1, 处理模式=full, 保留Alpha=False
2025-07-15 18:16:17,469 - tif_api - INFO - 保护内部区域=False, 使用GPU=False, GPU数量=0
2025-07-15 18:16:41,443 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 18:16:41] "[35m[1mGET /api/tif/process?input_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out2.tif&output_shp=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out2.shp&black_threshold=0&white_threshold=255 HTTP/1.1[0m" 500 -
2025-07-15 18:17:32,685 - tif_api - INFO - 处理请求 - 输入: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto.tif, 输出TIF: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto_out2.tif, 输出SHP: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto_out2.shp
2025-07-15 18:17:32,694 - tif_api - INFO - 参数: 黑色阈值=0, 白色阈值=255, NoData值=-9999.0
2025-07-15 18:17:32,702 - tif_api - INFO - 简化参数=0.1, 处理模式=full, 保留Alpha=False
2025-07-15 18:17:32,707 - tif_api - INFO - 保护内部区域=False, 使用GPU=False, GPU数量=0
