# GeoServer命令行工具使用说明

本文档介绍GeoServer命令行工具的使用方法，包括各种GeoServer管理操作的命令行调用。

## 基本用法

```bash
python -m geoserverRest.geoserver_cli [命令] [选项]
```

或使用已安装的命令：

```bash
geoserver_cli [命令] [选项]
```

## 全局选项

所有命令都支持以下全局选项：

- `--url URL`: GeoServer URL (默认: http://localhost:8080/geoserver)
- `--user USER`: GeoServer用户名 (默认: admin)
- `--password PASSWORD`: GeoServer密码 (默认: geoserver)
- `--workspace WORKSPACE`: 默认工作区 (默认: geoserver)
- `--help`: 显示帮助信息
- `--verbose`: 显示详细信息

## 可用命令

### 工作区管理

#### 创建工作区

```bash
geoserver_cli workspace create --name 工作区名称
```

**示例:**
```bash
geoserver_cli workspace create --name test
```

#### 删除工作区

```bash
geoserver_cli workspace delete --name 工作区名称
```

**示例:**
```bash
geoserver_cli workspace delete --name test
```

#### 列出所有工作区

```bash
geoserver_cli workspace list
```

### 数据存储管理

#### 创建PostGIS数据存储

```bash
geoserver_cli datastore create --name 存储名称 --workspace 工作区 --host 主机地址 --port 端口 --database 数据库名 --username 用户名 --password 密码
```

**示例:**
```bash
geoserver_cli datastore create --name postgis_store --workspace test --host localhost --port 5432 --database gisdb --username postgres --password postgres
```

#### 列出数据存储

```bash
geoserver_cli datastore list --workspace 工作区
```

**示例:**
```bash
geoserver_cli datastore list --workspace test
```

### 图层管理

#### 发布Shapefile

```bash
geoserver_cli publish shapefile --file Shapefile文件路径 [--workspace 工作区] [--store 存储名称] [--layer 图层名称] [--charset 字符集]
```

**示例:**
```bash
geoserver_cli publish shapefile --file D:/data/boundary.shp --workspace test --store shapefiles --layer boundary --charset UTF-8
```

#### 发布Shapefile目录

```bash
geoserver_cli publish shapefile-directory --directory Shapefile目录路径 [--workspace 工作区] [--store 存储名称前缀] [--charset 字符集]
```

**示例:**
```bash
geoserver_cli publish shapefile-directory --directory D:/data/shapefiles --workspace test --store shp_collection --charset UTF-8
```

#### 发布GeoTIFF

```bash
geoserver_cli publish geotiff --file GeoTIFF文件路径 [--workspace 工作区] [--store 存储名称] [--layer 图层名称]
```

**示例:**
```bash
geoserver_cli publish geotiff --file D:/data/orthophoto.tif --workspace test --store geotiffs --layer ortho
```

#### 发布GeoTIFF目录

```bash
geoserver_cli publish geotiff-directory --directory GeoTIFF目录路径 [--workspace 工作区] [--store 存储名称前缀]
```

**示例:**
```bash
geoserver_cli publish geotiff-directory --directory D:/data/geotiffs --workspace test --store tif_collection
```

#### 发布PostGIS表

```bash
geoserver_cli publish postgis --table 表名 --store 数据存储名称 [--workspace 工作区] [--layer 图层名称] [--geometry 几何字段名] [--srid SRID]
```

**示例:**
```bash
geoserver_cli publish postgis --table cities --store postgis_store --workspace test --layer cities_layer --geometry geom --srid 4326
```

#### 设置图层样式

```bash
geoserver_cli style set --layer 图层名称 --style 样式名称 [--workspace 工作区]
```

**示例:**
```bash
geoserver_cli style set --layer ortho1 --style raster_style --workspace test
```

#### 删除图层

```bash
geoserver_cli layer delete --layer 图层名称 [--workspace 工作区]
```

**示例:**
```bash
geoserver_cli layer delete --layer ortho1 --workspace test
```

### 批量处理

#### 执行批处理文件

```bash
geoserver_cli batch execute --file 批处理文件路径
```

**示例:**
```bash
geoserver_cli batch execute --file D:/geoserver_batch.json
```

### 诊断

#### 诊断GeoServer

```bash
geoserver_cli diagnose [--workspace 工作区] [--detail]
```

**示例:**
```bash
geoserver_cli diagnose --workspace test --detail
```

## 批处理文件格式

批处理文件是一个JSON文件，包含一系列要执行的GeoServer操作。格式如下：

```json
{
  "operations": [
    {
      "type": "create_workspace",
      "name": "test"
    },
    {
      "type": "publish_shapefile",
      "file": "D:/data/boundary.shp",
      "workspace": "test",
      "store": "shapefiles",
      "layer": "boundary"
    },
    {
      "type": "publish_geotiff",
      "file": "D:/data/orthophoto.tif",
      "workspace": "test",
      "store": "geotiffs",
      "layer": "ortho"
    }
  ]
}
```

## 示例

### 完整工作流示例

以下是一个完整的工作流示例，创建工作区、发布Shapefile和GeoTIFF文件：

```bash
# 创建工作区
geoserver_cli workspace create --name drone_data

# 发布Shapefile
geoserver_cli publish shapefile --file D:/data/boundary.shp --workspace drone_data --layer boundary

# 发布GeoTIFF
geoserver_cli publish geotiff --file D:/data/orthophoto.tif --workspace drone_data --layer orthophoto

# 设置样式
geoserver_cli style set --layer orthophoto --style raster_style --workspace drone_data

# 检查结果
geoserver_cli diagnose --workspace drone_data --detail
```

### 使用批处理文件

创建一个批处理文件 `batch.json`：

```json
{
  "operations": [
    {
      "type": "create_workspace",
      "name": "batch_test"
    },
    {
      "type": "publish_shapefile_directory",
      "directory": "D:/data/shapefiles",
      "workspace": "batch_test"
    },
    {
      "type": "publish_geotiff_directory",
      "directory": "D:/data/geotiffs",
      "workspace": "batch_test"
    }
  ]
}
```

执行批处理文件：

```bash
geoserver_cli batch execute --file batch.json
```

## 故障排除

### 常见错误

1. **连接错误**: 确保GeoServer正在运行，并且URL、用户名和密码正确。

   ```bash
   # 检查GeoServer连接
   geoserver_cli diagnose
   ```

2. **文件路径错误**: 确保文件路径正确，并且文件存在。

3. **权限错误**: 确保用户有足够的权限执行所需操作。

### 日志

命令行工具的日志文件存储在 `logs` 目录中，可以查看日志文件了解详细错误信息。 