2025-07-16 09:15:27,778 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250716_091527.log
2025-07-16 09:15:27,779 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 09:15:27,780 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 09:15:28,627 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 09:15:28,731 - batch_executor - INFO - 加载了 35 个任务状态
2025-07-16 09:15:35,219 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 09:15:35,220 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 09:15:35,247 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 09:15:35,256 - root - INFO - === GeoServer REST API服务 ===
2025-07-16 09:15:35,256 - root - INFO - 主机: 0.0.0.0
2025-07-16 09:15:35,257 - root - INFO - 端口: 5083
2025-07-16 09:15:35,258 - root - INFO - 调试模式: 禁用
2025-07-16 09:15:35,258 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-16 09:15:35,285 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-16 09:15:35,287 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-16 09:15:46,359 - tif_api - INFO - 启动异步TIF处理: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto.tif
2025-07-16 09:15:46,373 - tif_executor - INFO - 启动TIF处理任务 6b4d8e21-d383-4a21-9453-2f7d34f84a85: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto.tif
2025-07-16 09:15:46,375 - tif_task_6b4d8e21-d383-4a21-9453-2f7d34f84a85 - INFO - TIF处理任务开始执行
2025-07-16 09:15:46,377 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:15:46] "GET /api/tif/process/async?input_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto.tif&output_tif=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif&output_shp=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.shp&black_threshold=0&white_threshold=255 HTTP/1.1" 200 -
2025-07-16 09:15:46,377 - tif_task_6b4d8e21-d383-4a21-9453-2f7d34f84a85 - INFO - 输入文件: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto.tif
2025-07-16 09:15:46,381 - tif_task_6b4d8e21-d383-4a21-9453-2f7d34f84a85 - INFO - 输出TIF: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto_out.tif
2025-07-16 09:15:46,385 - tif_task_6b4d8e21-d383-4a21-9453-2f7d34f84a85 - INFO - 输出SHP: D:\Drone_Project\dataset\DJITESTIMAGE\20250705171600\project\odm_orthophoto\odm_orthophoto_out.shp
2025-07-16 09:15:46,387 - tif_task_6b4d8e21-d383-4a21-9453-2f7d34f84a85 - INFO - 处理参数: {"input_tif": "D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250705171600\\project\\odm_orthophoto\\odm_orthophoto.tif", "output_tif": "D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250705171600\\project\\odm_orthophoto\\odm_orthophoto_out.tif", "output_shp": "D:\\Drone_Project\\dataset\\DJITESTIMAGE\\20250705171600\\project\\odm_orthophoto\\odm_orthophoto_out.shp", "black_threshold": "0", "white_threshold": "255", "keep_alpha": false, "protect_interior": false, "use_gpu": false}
2025-07-16 09:15:46,412 - tif_task_6b4d8e21-d383-4a21-9453-2f7d34f84a85 - INFO - 开始时间: 2025-07-16 09:15:46
2025-07-16 09:15:46,414 - tif_task_6b4d8e21-d383-4a21-9453-2f7d34f84a85 - INFO - 开始执行TIF处理...
2025-07-16 09:15:46,575 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:15:46] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-16 09:16:33,042 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 09:16:33] "GET /api/tif/status?task_id=6b4d8e21-d383-4a21-9453-2f7d34f84a85 HTTP/1.1" 200 -
