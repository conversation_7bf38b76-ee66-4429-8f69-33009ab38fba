2025-07-18 11:14:42,592 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - INFO - GeoServer发布任务 f104757d-4597-4d56-98a5-c7a0906b3e8d 开始执行
2025-07-18 11:14:42,593 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - INFO - 开始时间: 2025-07-18 11:14:42
2025-07-18 11:14:42,597 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - INFO - 参数: {
  "geotiff_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
  "workspace": "\"tttt\"",
  "store_name": null,
  "layer_name": null
}
2025-07-18 11:14:42,816 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - ERROR - 执行出错: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'
2025-07-18 11:14:42,818 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 352, in _run_with_logging
    result = func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 425, in publish_func
    return manager.publish_geotiff(path, ws, store, layer)
  File "D:\Drone_Project\geoserverapi\geoserver_manager.py", line 607, in publish_geotiff
    self.geo.create_workspace(workspace)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\geo\Geoserver.py", line 396, in create_workspace
    raise GeoserverException(r.status_code, r.content)
geo.Geoserver.GeoserverException: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'

2025-07-18 11:14:42,821 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - ERROR - 完成时间: 2025-07-18 11:14:42
2025-07-18 11:14:42,822 - geo_task_f104757d-4597-4d56-98a5-c7a0906b3e8d - ERROR - 状态: 发布失败
