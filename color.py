'''
Author: 吴博文 <EMAIL>
Date: 2025-07-31 08:16:59
LastEditors: 吴博文 <EMAIL>
LastEditTime: 2025-07-31 08:17:25
FilePath: \geoserverapi\color.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import os

colors = [
  ("Red", "#FF0000"), ("Green", "#008000"), ("Blue", "#0000FF"),
  ("Yellow", "#FFFF00"), ("<PERSON>an", "#00FFFF"), ("Magenta", "#FF00FF"),
  ("Orange", "#FFA500"), ("Purple", "#800080"), ("Pink", "#FFC0CB"),
  ("<PERSON>", "#A52A2A"), ("<PERSON>", "#808080"), ("<PERSON>", "#000000"),
  ("White", "#FFFFFF"), ("Lime", "#00FF00"), ("Navy", "#000080"),
  ("Teal", "#008080"), ("Olive", "#808000"), ("Gold", "#FFD700"),
  ("Coral", "#FF7F50"), ("Tomato", "#FF6347"), ("Aquamarine", "#7FFFD4"),
  ("Chartreuse", "#7FFF00"), ("Orchid", "#DA70D6"), ("Violet", "#EE82EE"),
  ("SteelBlue", "#4682B4"), ("SkyBlue", "#87CEEB"), ("Salmon", "#FA8072"),
  ("Crimson", "#DC143C"), ("DarkOrange", "#FF8C00"), ("MediumPurple", "#9370DB"),
  ("LightSeaGreen", "#20B2AA"), ("MediumSpringGreen", "#00FA9A"),
  ("SlateBlue", "#6A5ACD"), ("PaleGoldenrod", "#EEE8AA"),
  ("DarkKhaki", "#BDB76B"), ("MediumVioletRed", "#C71585"),
  ("DodgerBlue", "#1E90FF"), ("DarkTurquoise", "#00CED1"),
  ("LightCoral", "#F08080"), ("MediumAquamarine", "#66CDAA")
]

os.makedirs('styles', exist_ok=True)

template = '''<?xml version="1.0" encoding="UTF-8"?>
<StyledLayerDescriptor version="1.0.0"
  xmlns="http://www.opengis.net/sld"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.opengis.net/sld http://schemas.opengis.net/sld/1.0.0/StyledLayerDescriptor.xsd">
  <NamedLayer>
    <Name>{name}</Name>
    <UserStyle>
      <Name>{name}</Name>
      <Title>{name} polygon 0.2 opacity</Title>
      <FeatureTypeStyle>
        <Rule>
          <PolygonSymbolizer>
            <Fill>
              <CssParameter name="fill">{hex}</CssParameter>
              <CssParameter name="fill-opacity">0.2</CssParameter>
            </Fill>
            <Stroke>
              <CssParameter name="stroke">#000000</CssParameter>
              <CssParameter name="stroke-width">1</CssParameter>
              <CssParameter name="stroke-opacity">1.0</CssParameter>
            </Stroke>
          </PolygonSymbolizer>
        </Rule>
      </FeatureTypeStyle>
    </UserStyle>
  </NamedLayer>
</StyledLayerDescriptor>
'''

for name, hexcode in colors:
    fname = os.path.join('styles', f'{name}.txt')
    with open(fname, 'w', encoding='utf-8') as f:
        f.write(template.format(name=name, hex=hexcode))



print("Generated styles.zip containing 40 .txt files")