# GeoServer栅格数据坐标查询服务

该模块提供了一个REST API服务，用于查询GeoServer上发布的栅格图层在特定坐标点是否存在有效数据。

## 功能特点

- 根据经纬度坐标查询工作区内的栅格图层
- 智能过滤：自动排除坐标点处为nodata、全白或全黑的图层
- 返回完整的图层信息，包括图层名称、存储名称和边界框等
- 支持跨域请求，便于前端集成
- 提供健康检查端点，便于监控服务状态

## 安装依赖

本服务依赖以下Python包：

```bash
pip install flask flask-cors gdal requests
```

注意：GDAL库的安装可能比较复杂，建议通过conda或官方预编译包安装：

```bash
# 使用conda安装GDAL（推荐）
conda install -c conda-forge gdal

# 或者在Windows上使用预编译的wheel包
pip install gdal-[版本号]-cp[Python版本]-cp[Python版本]-win_amd64.whl
```

## 配置

本服务使用项目根目录下的`config.py`文件获取GeoServer连接信息。确保该文件中包含以下配置项：

- `GEOSERVER_URL`：GeoServer服务地址
- `GEOSERVER_USER`：GeoServer用户名
- `GEOSERVER_PASSWORD`：GeoServer密码
- `LOG_LEVEL`：日志级别（DEBUG、INFO、WARNING、ERROR）

您可以通过环境变量或`.env`文件设置这些配置项。

## 启动服务

### 方式一：直接运行

```bash
cd coordinate_query
python run_server.py --host 0.0.0.0 --port 5000 --debug
```

参数说明：
- `--host`：服务器监听地址，默认为`0.0.0.0`（所有网络接口）
- `--port`：服务器监听端口，默认为`5000`
- `--debug`：启用调试模式，生产环境中请勿使用

### 方式二：使用Gunicorn（推荐用于生产环境）

```bash
cd coordinate_query
gunicorn -w 4 -b 0.0.0.0:5000 "geoserver_query_api:app"
```

参数说明：
- `-w 4`：启动4个工作进程
- `-b 0.0.0.0:5000`：绑定到所有网络接口的5000端口

### 方式三：使用Docker

如需使用Docker部署，可创建以下Dockerfile：

```dockerfile
FROM python:3.9-slim

# 安装GDAL依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libgdal-dev \
    && rm -rf /var/lib/apt/lists/*

# 设置GDAL环境变量
ENV GDAL_VERSION=$(gdal-config --version)
ENV CPLUS_INCLUDE_PATH=/usr/include/gdal
ENV C_INCLUDE_PATH=/usr/include/gdal

# 创建工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .
COPY config.py .

# 安装依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY coordinate_query/ ./coordinate_query/

# 暴露端口
EXPOSE 5000

# 启动应用
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "coordinate_query.geoserver_query_api:app"]
```

然后构建和运行Docker容器：

```bash
docker build -t geoserver-query-api .
docker run -d -p 5000:5000 --name geoserver-query geoserver-query-api
```

## API接口说明

### 1. 健康检查

- **URL**：`/health`
- **方法**：`GET`
- **描述**：检查API服务是否正常运行
- **成功响应**：
  ```json
  {
    "status": "ok",
    "message": "GeoServer Query API is running"
  }
  ```

### 2. 坐标点查询

- **URL**：`/api/query`
- **方法**：`GET`
- **描述**：查询给定坐标点处的所有有效栅格图层
- **查询参数**：
  - `lat`：纬度（浮点数）
  - `lon`：经度（浮点数）
  - `workspace`：工作区名称（字符串）
- **示例**：
  ```
  /api/query?lat=22.8167&lon=108.3667&workspace=my_workspace
  ```
- **成功响应**：
  ```json
  {
    "status": "success",
    "count": 2,
    "layers": [
      {
        "name": "layer1",
        "store": "store1",
        "workspace": "my_workspace",
        "id": "my_workspace:layer1",
        "bbox": {
          "minx": 108.3,
          "miny": 22.7,
          "maxx": 108.4,
          "maxy": 22.9
        }
      },
      {
        "name": "layer2",
        "store": "store2",
        "workspace": "my_workspace",
        "id": "my_workspace:layer2",
        "bbox": {
          "minx": 108.2,
          "miny": 22.6,
          "maxx": 108.5,
          "maxy": 23.0
        }
      }
    ]
  }
  ```

### 3. 获取工作区中的所有栅格图层

- **URL**：`/api/layers`
- **方法**：`GET`
- **描述**：获取指定工作区中的所有栅格图层
- **查询参数**：
  - `workspace`：工作区名称
- **成功响应**：
  ```json
  {
    "status": "success",
    "count": 2,
    "layers": [
      {
        "name": "layer1",
        "store": "store1",
        "workspace": "my_workspace",
        "id": "my_workspace:layer1",
        "bbox": {
          "minx": 108.3,
          "miny": 22.7,
          "maxx": 108.4,
          "maxy": 22.9
        }
      },
      {
        "name": "layer2",
        "store": "store2",
        "workspace": "my_workspace",
        "id": "my_workspace:layer2",
        "bbox": {
          "minx": 108.2,
          "miny": 22.6,
          "maxx": 108.5,
          "maxy": 23.0
        }
      }
    ]
  }
  ```

### 4. 测试坐标点（调试用）

- **URL**：`/api/test_point`
- **方法**：`GET`
- **描述**：详细测试坐标点在各图层中的情况，包括边界框检查和数据有效性检查
- **查询参数**：
  - `lat`：纬度（浮点数）
  - `lon`：经度（浮点数）
  - `workspace`：工作区名称（字符串）
  - `layer`：图层名称（字符串，可选）
- **示例**：
  ```
  /api/test_point?lat=22.77912&lon=108.36502&workspace=test_myworkspace
  ```
- **成功响应**：
  ```json
  {
    "status": "success",
    "coordinates": {
      "lat": 22.77912,
      "lon": 108.36502
    },
    "workspace": "test_myworkspace",
    "results": [
      {
        "name": "layer1",
        "store": "store1",
        "workspace": "test_myworkspace",
        "id": "test_myworkspace:layer1",
        "bbox": {
          "minx": 108.3,
          "miny": 22.7,
          "maxx": 108.4,
          "maxy": 22.9,
          "point_inside": true
        },
        "has_data": true
      },
      {
        "name": "layer2",
        "store": "store2",
        "workspace": "test_myworkspace",
        "id": "test_myworkspace:layer2",
        "bbox": {
          "minx": 108.2,
          "miny": 22.6,
          "maxx": 108.5,
          "maxy": 23.0,
          "point_inside": true
        },
        "has_data": false
      }
    ]
  }
  ```

### 5. 获取像素值

- **URL**：`/api/pixel_value`
- **方法**：`GET`
- **描述**：获取栅格图层在指定坐标点的像素值
- **查询参数**：
  - `lat`：纬度（浮点数）
  - `lon`：经度（浮点数）
  - `workspace`：工作区名称（字符串）
  - `layer`：图层名称（字符串，必需）
- **示例**：
  ```
  /api/pixel_value?lat=22.77912&lon=108.36502&workspace=test_myworkspace&layer=nanning
  ```
- **成功响应**：
  ```json
  {
    "status": "success",
    "coordinates": {
      "lat": 22.77912,
      "lon": 108.36502
    },
    "layer": "test_myworkspace:nanning",
    "pixel_values": {
      "band1": 127.0,
      "band2": 86.5,
      "band3": 42.0
    }
  }
  ```
- **无数据响应**：
  ```json
  {
    "status": "success",
    "coordinates": {
      "lat": 22.77912,
      "lon": 108.36502
    },
    "layer": "test_myworkspace:nanning",
    "pixel_values": {},
    "message": "该坐标点无有效数据"
  }
  ```

### 6. 查询栅格图层并获取有效像素值

- **URL**：`/api/query_values`
- **方法**：`GET`
- **描述**：查询坐标点处的栅格图层并获取有效像素值，自动过滤满足以下任一条件的图层：
  - 没有像素值
  - 像素值为全黑(0,0,0)
  - 像素值为全白(255,255,255)
  - 存在ALPHA_BAND且值为0（完全透明）
- **查询参数**：
  - `lat`：纬度（浮点数）
  - `lon`：经度（浮点数）
  - `workspace`：工作区名称（字符串）
- **示例**：
  ```
  /api/query_values?lat=22.77912&lon=108.36502&workspace=test_myworkspace
  ```
- **成功响应**：
  ```json
  {
    "status": "success",
    "count": 2,
    "coordinates": {
      "lat": 22.77912,
      "lon": 108.36502
    },
    "workspace": "test_myworkspace",
    "layers": [
      {
        "name": "layer1",
        "store": "store1",
        "workspace": "test_myworkspace",
        "id": "test_myworkspace:layer1",
        "bbox": {
          "minx": 108.3,
          "miny": 22.7,
          "maxx": 108.4,
          "maxy": 22.9
        },
        "pixel_values": {
          "band1": 127.5,
          "band2": 86.3,
          "band3": 42.1
        }
      },
      {
        "name": "layer3",
        "store": "store3",
        "workspace": "test_myworkspace",
        "id": "test_myworkspace:layer3",
        "bbox": {
          "minx": 108.2,
          "miny": 22.6,
          "maxx": 108.5,
          "maxy": 23.0
        },
        "pixel_values": {
          "Red": 120.0,
          "Green": 75.0,
          "Blue": 30.0
        }
      }
    ]
  }
  ```
- **无有效图层响应**：
  ```json
  {
    "status": "success",
    "message": "在坐标点 (22.77912, 108.36502) 的工作区 test_myworkspace 中未找到有有效数据的栅格图层",
    "count": 0,
    "layers": []
  }
  ```

### 7. 获取工作区中的所有图层（栅格和矢量）

- **URL**：`/api/all_layers`
- **方法**：`GET`
- **描述**：获取指定工作区中的所有图层（包括栅格和矢量），并按照特定格式返回
- **查询参数**：
  - `workspace`：工作区名称（字符串）
- **示例**：
  ```
  /api/all_layers?workspace=test_myworkspace
  ```
- **成功响应**：
  ```json
  {
    "status": "success",
    "count": 2,
    "layers": [
      {
        "id": "test_myworkspace:nanning",
        "name": "nanning",
        "type": "raster",
        "initialLoad": false,
        "protocol": "WMTS",
        "workspace": "test_myworkspace",
        "layerName": "nanning",
        "theme": ""
      },
      {
        "id": "yz:种植区",
        "name": "种植区",
        "type": "vector",
        "protocol": "WFS",
        "workspace": "yz",
        "layerName": "种植区",
        "initialLoad": false,
        "defaultStyle": "default_polygon",
        "geometryType": "MultiPolygon",
        "theme": ""
      }
    ]
  }
  ```

## 浏览器直接访问示例

以下是可以直接在浏览器地址栏输入的示例URL：

### 健康检查

```
http://localhost:5000/health
```

### 获取工作区中的所有栅格图层

```
http://localhost:5000/api/layers?workspace=my_workspace
```

### 坐标点查询

```
http://localhost:5000/api/query?lat=22.8167&lon=108.3667&workspace=my_workspace
```

### 测试坐标点（调试用）

```
http://localhost:5000/api/test_point?lat=22.77912&lon=108.36502&workspace=test_myworkspace
```

### 获取像素值

```
http://localhost:5000/api/pixel_value?lat=22.77912&lon=108.36502&workspace=test_myworkspace&layer=nanning
```

### 获取有效图层及像素值

```
http://localhost:5000/api/query_values?lat=22.77912&lon=108.36502&workspace=test_myworkspace
```

### 获取所有图层（栅格和矢量）

```
http://localhost:5000/api/all_layers?workspace=test_myworkspace
```

## 示例代码

### Python客户端示例

```python
import requests
import json

# API基础URL
base_url = "http://localhost:5000"

# 示例1：健康检查
health_response = requests.get(f"{base_url}/health")
print("健康检查:", health_response.json())

# 示例2：获取工作区中的栅格图层
workspace = "my_workspace"
layers_response = requests.get(f"{base_url}/api/layers?workspace={workspace}")
print(f"工作区 {workspace} 中的图层:", json.dumps(layers_response.json(), indent=2, ensure_ascii=False))

# 示例3：查询坐标点
lat = 22.8167
lon = 108.3667
query_response = requests.get(f"{base_url}/api/query?lat={lat}&lon={lon}&workspace={workspace}")
print(f"坐标点 ({lat}, {lon}) 查询结果:", 
      json.dumps(query_response.json(), indent=2, ensure_ascii=False))

# 示例4：获取像素值
layer = "nanning"
pixel_response = requests.get(f"{base_url}/api/pixel_value?lat={lat}&lon={lon}&workspace={workspace}&layer={layer}")
print(f"坐标点 ({lat}, {lon}) 在图层 {layer} 的像素值:", 
      json.dumps(pixel_response.json(), indent=2, ensure_ascii=False))

# 示例5：查询有效图层及像素值
query_values_response = requests.get(f"{base_url}/api/query_values?lat={lat}&lon={lon}&workspace={workspace}")
print(f"坐标点 ({lat}, {lon}) 的有效图层及像素值:", 
      json.dumps(query_values_response.json(), indent=2, ensure_ascii=False))

# 示例6：获取所有图层（栅格和矢量）
all_layers_response = requests.get(f"{base_url}/api/all_layers?workspace={workspace}")
print(f"工作区 {workspace} 中的所有图层:", 
      json.dumps(all_layers_response.json(), indent=2, ensure_ascii=False))
```

### JavaScript客户端示例

```javascript
// API基础URL
const baseUrl = "http://localhost:5000";

// 示例1：健康检查
fetch(`${baseUrl}/health`)
  .then(response => response.json())
  .then(data => console.log("健康检查:", data));

// 示例2：获取工作区中的栅格图层
const workspace = "my_workspace";
fetch(`${baseUrl}/api/layers?workspace=${workspace}`)
  .then(response => response.json())
  .then(data => console.log(`工作区 ${workspace} 中的图层:`, data));

// 示例3：查询坐标点
const lat = 22.8167;
const lon = 108.3667;
fetch(`${baseUrl}/api/query?lat=${lat}&lon=${lon}&workspace=${workspace}`)
  .then(response => response.json())
  .then(data => console.log(`坐标点 (${lat}, ${lon}) 查询结果:`, data));

// 示例4：获取像素值
const layer = "nanning";
fetch(`${baseUrl}/api/pixel_value?lat=${lat}&lon=${lon}&workspace=${workspace}&layer=${layer}`)
  .then(response => response.json())
  .then(data => console.log(`坐标点 (${lat}, ${lon}) 在图层 ${layer} 的像素值:`, data));

// 示例5：查询有效图层及像素值
fetch(`${baseUrl}/api/query_values?lat=${lat}&lon=${lon}&workspace=${workspace}`)
  .then(response => response.json())
  .then(data => console.log(`坐标点 (${lat}, ${lon}) 的有效图层及像素值:`, data));

// 示例6：获取所有图层（栅格和矢量）
fetch(`${baseUrl}/api/all_layers?workspace=${workspace}`)
  .then(response => response.json())
  .then(data => console.log(`工作区 ${workspace} 中的所有图层:`, data));
```

## 注意事项

1. **性能考虑**：对于大量图层或高频率查询，请考虑增加缓存机制或优化查询算法。
2. **坐标系统**：本服务默认使用EPSG:4326坐标系（WGS84，经纬度），确保输入的坐标符合此坐标系。
3. **GeoServer配置**：确保GeoServer已正确配置并启用CORS支持。
4. **安全性**：在生产环境中部署时，请考虑添加适当的身份验证和授权机制。

## 故障排除

### 常见问题

1. **坐标点查询返回空结果**：
   - 检查坐标点是否在图层边界框内（使用`/api/test_point`端点）
   - 确保GeoServer图层配置正确，特别是坐标系统设置
   - 检查栅格数据在该点是否有有效值（非NoData）
   - 尝试调整坐标点位置，看是否能获取到结果

2. **连接错误**：检查GeoServer连接配置是否正确。
3. **CORS错误**：确保GeoServer和API服务都已启用CORS支持。
4. **性能问题**：对于大型栅格数据，考虑使用金字塔层或概览层优化GeoServer性能。

### 调试技巧

1. **使用测试端点**：`/api/test_point`端点提供详细的测试结果，可以帮助排查问题。
2. **启用调试模式**：启动服务时使用`--debug`参数，可以查看更详细的日志信息。
3. **检查日志文件**：查看`logs`目录下的日志文件，了解请求处理过程中的详细信息。
4. **直接查看GeoServer WMS响应**：可以直接在浏览器中访问GeoServer的GetFeatureInfo请求，检查返回的数据。

## 许可证

该项目采用MIT许可证。详见LICENSE文件。