2025-07-24 16:54:27,250 - INFO - ============ TIF处理任务 6c8aba6f-9089-4964-9803-e9f3d639868d 开始执行 ============
2025-07-24 16:54:27,251 - INFO - 开始时间: 2025-07-24 16:54:27
2025-07-24 16:54:27,252 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-24 16:54:27,254 - INFO - 系统信息:
2025-07-24 16:54:27,257 - INFO -   操作系统: Windows 10.0.19045
2025-07-24 16:54:27,278 - INFO -   Python版本: 3.8.20
2025-07-24 16:54:27,279 - INFO -   GDAL版本: 3.9.2
2025-07-24 16:54:27,280 - INFO -   GPU可用: 否
2025-07-24 16:54:27,282 - INFO - 检查参数有效性...
2025-07-24 16:54:27,284 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:54:27,290 - INFO - 开始执行TIF处理流程...
2025-07-24 16:54:27,307 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-24 16:54:27,309 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-24 16:54:27,310 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-24 16:54:31,237 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (16:54:31)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-24 16:54:31,264 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-24 16:54:31,586 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:06,  3.14it/s]
2025-07-24 16:54:31,847 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 15.93it/s]
2025-07-24 16:54:32,079 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 21.75it/s]
2025-07-24 16:54:32,100 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 25.26it/s]
2025-07-24 16:54:32,102 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-24 16:54:32,103 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-24 16:54:32,139 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 587.29it/s]
2025-07-24 16:54:33,234 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 1.97 秒 (16:54:33)预计总处理时间: 约 5.92 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (16:54:33)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-24 16:54:33,236 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-24 16:54:37,180 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-24 16:54:37,181 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:54:37,185 - ERROR - [A
2025-07-24 16:54:37,293 - ERROR - 
波段 1/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:00, 28.26it/s]
2025-07-24 16:54:37,294 - ERROR - [A
2025-07-24 16:54:37,399 - ERROR - 
波段 1/3 写入进度:  52%|#################################                              | 11/21 [00:00<00:00, 56.12it/s]
2025-07-24 16:54:37,400 - ERROR - [A
2025-07-24 16:54:37,505 - ERROR - 
波段 1/3 写入进度:  90%|#########################################################      | 19/21 [00:00<00:00, 64.96it/s]
2025-07-24 16:54:37,506 - ERROR - [A
2025-07-24 16:54:37,537 - ERROR - [A
2025-07-24 16:55:27,321 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:54<01:48, 54.08s/it]
2025-07-24 16:55:55,633 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-24 16:55:55,635 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:55:55,637 - ERROR - [A
2025-07-24 16:55:55,825 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:03,  5.42it/s]
2025-07-24 16:55:55,826 - ERROR - [A
2025-07-24 16:55:55,959 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:01, 10.22it/s]
2025-07-24 16:55:55,959 - ERROR - [A
2025-07-24 16:55:56,138 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:01, 10.72it/s]
2025-07-24 16:55:56,138 - ERROR - [A
2025-07-24 16:55:56,448 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:01,  8.38it/s]
2025-07-24 16:55:56,449 - ERROR - [A
2025-07-24 16:55:56,605 - ERROR - 
波段 2/3 写入进度:  38%|########################3                                       | 8/21 [00:00<00:01,  7.80it/s]
2025-07-24 16:55:56,605 - ERROR - [A
2025-07-24 16:55:56,732 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:01<00:01,  7.82it/s]
2025-07-24 16:55:56,734 - ERROR - [A
2025-07-24 16:55:56,887 - ERROR - 
波段 2/3 写入进度:  48%|##############################                                 | 10/21 [00:01<00:01,  7.40it/s]
2025-07-24 16:55:56,929 - ERROR - [A
2025-07-24 16:55:57,077 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:01<00:01,  6.69it/s]
2025-07-24 16:55:57,085 - ERROR - [A
2025-07-24 16:55:57,265 - ERROR - 
波段 2/3 写入进度:  57%|####################################                           | 12/21 [00:01<00:01,  6.23it/s]
2025-07-24 16:55:57,266 - ERROR - [A
2025-07-24 16:55:57,421 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:01<00:01,  6.27it/s]
2025-07-24 16:55:57,425 - ERROR - [A
2025-07-24 16:55:57,566 - ERROR - 
波段 2/3 写入进度:  67%|##########################################                     | 14/21 [00:01<00:01,  6.45it/s]
2025-07-24 16:55:57,568 - ERROR - [A
2025-07-24 16:55:59,314 - ERROR - 
波段 2/3 写入进度:  76%|################################################               | 16/21 [00:03<00:02,  2.08it/s]
2025-07-24 16:55:59,315 - ERROR - [A
2025-07-24 16:56:03,263 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:07<00:05,  1.33s/it]
2025-07-24 16:56:03,263 - ERROR - [A
2025-07-24 16:56:07,483 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:11<00:06,  2.08s/it]
2025-07-24 16:56:07,484 - ERROR - [A
2025-07-24 16:56:11,870 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:16<00:05,  2.70s/it]
2025-07-24 16:56:11,870 - ERROR - [A
2025-07-24 16:56:16,506 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:20<00:03,  3.24s/it]
2025-07-24 16:56:16,507 - ERROR - [A
2025-07-24 16:56:17,671 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:22<00:00,  2.65s/it]
2025-07-24 16:56:17,672 - ERROR - [A
2025-07-24 16:56:17,675 - ERROR - [A
2025-07-24 16:57:19,433 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [02:46<01:28, 88.22s/it]
2025-07-24 16:57:30,446 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-24 16:57:30,449 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-24 16:57:30,453 - ERROR - [A
2025-07-24 16:57:30,710 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:05,  3.93it/s]
2025-07-24 16:57:30,710 - ERROR - [A
2025-07-24 16:57:30,918 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  7.02it/s]
2025-07-24 16:57:30,918 - ERROR - [A
2025-07-24 16:57:31,043 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:00<00:02,  7.31it/s]
2025-07-24 16:57:31,044 - ERROR - [A
2025-07-24 16:57:31,171 - ERROR - 
波段 3/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:02,  7.47it/s]
2025-07-24 16:57:31,172 - ERROR - [A
2025-07-24 16:57:31,276 - ERROR - 
波段 3/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:01, 10.60it/s]
2025-07-24 16:57:31,277 - ERROR - [A
2025-07-24 16:57:31,380 - ERROR - 
波段 3/3 写入进度:  48%|##############################                                 | 10/21 [00:00<00:00, 15.65it/s]
2025-07-24 16:57:31,380 - ERROR - [A
2025-07-24 16:57:31,494 - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:01<00:00, 21.59it/s]
2025-07-24 16:57:31,494 - ERROR - [A
2025-07-24 16:57:31,601 - ERROR - 
波段 3/3 写入进度:  90%|#########################################################      | 19/21 [00:01<00:00, 28.78it/s]
2025-07-24 16:57:31,603 - ERROR - [A
2025-07-24 16:57:31,646 - ERROR - [A
2025-07-24 16:59:12,280 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:39<00:00, 99.46s/it]
2025-07-24 16:59:12,281 - ERROR - 
处理波段: 100%|##########################################################################| 3/3 [04:39<00:00, 93.01s/it]
2025-07-24 16:59:55,966 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 279.55 秒 (16:59:12)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (16:59:12)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-24 16:59:55,991 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-24 16:59:56,317 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  3.14it/s]
2025-07-24 16:59:56,318 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  3.12it/s]
2025-07-24 16:59:56,331 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-24 17:00:09,718 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-24 17:00:09,719 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-24 17:00:09,995 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:00<00:03,  7.35it/s]
2025-07-24 17:00:11,308 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:17,  1.60it/s]
2025-07-24 17:00:11,352 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:01<00:00, 19.04it/s]
2025-07-24 17:00:12,046 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-24 17:00:12,048 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-24 17:00:12,068 - ERROR - 
写入多边形: 100%|####################################################################| 35/35 [00:00<00:00, 2273.12it/s]
2025-07-24 17:00:12,302 - INFO - 处理完成，耗时: 344.99 秒 (5.75 分钟)
2025-07-24 17:00:12,303 - INFO - 处理结果: 成功
2025-07-24 17:00:12,306 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-24 17:00:12,309 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-24 17:00:12,313 - INFO - TIF处理任务 6c8aba6f-9089-4964-9803-e9f3d639868d 执行成功
2025-07-24 17:00:12,314 - INFO - 完成时间: 2025-07-24 17:00:12
2025-07-24 17:00:12,314 - INFO - 状态: 运行成功
2025-07-24 17:00:12,315 - INFO - ============ 任务执行结束 ============
