2025-07-01 16:38:04,504 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250701_163804.log
2025-07-01 16:38:04,521 - root - INFO - 开始执行命令: publish-shapefile-directory
2025-07-01 16:38:04,522 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-01 16:38:04,522 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-01 16:38:04,539 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-01 16:38:04,541 - root - INFO - 在目录 'data/20250701/testshp' 中找到 5 个Shapefile文件
2025-07-01 16:38:04,542 - root - INFO - 使用字符集: UTF-8
2025-07-01 16:38:04,543 - root - INFO - 未提供存储名称，使用目录名称作为前缀: testshp
2025-07-01 16:38:04,565 - root - INFO - 在工作区 'test_workspace' 中找到 0 个图层
2025-07-01 16:38:04,565 - root - INFO - 发布前工作区 'test_workspace' 中有 0 个图层
2025-07-01 16:38:04,566 - root - INFO - 正在发布Shapefile: 招商引资片区.shp, 存储名: testshp
2025-07-01 16:38:04,578 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区.shp
2025-07-01 16:38:04,579 - root - INFO - 添加文件到ZIP: 招商引资片区.shp
2025-07-01 16:38:04,739 - root - INFO - 添加文件到ZIP: 招商引资片区.dbf
2025-07-01 16:38:04,741 - root - INFO - 添加文件到ZIP: 招商引资片区.shx
2025-07-01 16:38:04,742 - root - INFO - 添加文件到ZIP: 招商引资片区.prj
2025-07-01 16:38:04,743 - root - INFO - 添加文件到ZIP: 招商引资片区.qix
2025-07-01 16:38:04,744 - root - INFO - 添加文件到ZIP: 招商引资片区.cpg
2025-07-01 16:38:04,744 - root - INFO - 添加文件到ZIP: 招商引资片区.qmd
2025-07-01 16:38:04,745 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:38:04,746 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:38:04,881 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:38:04,960 - root - INFO - 成功创建图层 '招商引资片区'
2025-07-01 16:38:04,978 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/招商引资片区
2025-07-01 16:38:04,978 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区.shp' 成功发布为图层 'test_workspace:招商引资片区'
2025-07-01 16:38:04,979 - root - INFO - 正在发布Shapefile: 招商引资片区交种植区.shp, 存储名: testshp
2025-07-01 16:38:04,996 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区交种植区.shp
2025-07-01 16:38:04,997 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shp
2025-07-01 16:38:05,624 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.dbf
2025-07-01 16:38:05,800 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.shx
2025-07-01 16:38:05,817 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.prj
2025-07-01 16:38:05,818 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qix
2025-07-01 16:38:05,833 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.cpg
2025-07-01 16:38:05,834 - root - INFO - 添加文件到ZIP: 招商引资片区交种植区.qmd
2025-07-01 16:38:05,835 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:38:05,836 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:38:06,025 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:38:06,036 - root - INFO - 图层 '招商引资片区交种植区' 已存在
2025-07-01 16:38:06,051 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/招商引资片区交种植区
2025-07-01 16:38:06,052 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区交种植区.shp' 成功发布为图层 'test_workspace:招商引资片区交种植区'
2025-07-01 16:38:06,073 - root - INFO - 正在发布Shapefile: 招商引资片区交设施农用地.shp, 存储名: testshp
2025-07-01 16:38:06,086 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区交设施农用地.shp
2025-07-01 16:38:06,087 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shp
2025-07-01 16:38:06,234 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.dbf
2025-07-01 16:38:06,251 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.shx
2025-07-01 16:38:06,252 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.prj
2025-07-01 16:38:06,253 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qix
2025-07-01 16:38:06,256 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.cpg
2025-07-01 16:38:06,257 - root - INFO - 添加文件到ZIP: 招商引资片区交设施农用地.qmd
2025-07-01 16:38:06,258 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:38:06,259 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:38:06,363 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:38:06,444 - root - INFO - 成功创建图层 '招商引资片区交设施农用地'
2025-07-01 16:38:06,459 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/招商引资片区交设施农用地
2025-07-01 16:38:06,459 - root - INFO - Shapefile 'data/20250701/testshp\招商引资片区交设施农用地.shp' 成功发布为图层 'test_workspace:招商引资片区交设施农用地'
2025-07-01 16:38:06,461 - root - INFO - 正在发布Shapefile: 种植区.shp, 存储名: testshp
2025-07-01 16:38:06,478 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\种植区.shp
2025-07-01 16:38:06,479 - root - INFO - 添加文件到ZIP: 种植区.shp
2025-07-01 16:38:07,889 - root - INFO - 添加文件到ZIP: 种植区.dbf
2025-07-01 16:38:08,108 - root - INFO - 添加文件到ZIP: 种植区.shx
2025-07-01 16:38:08,133 - root - INFO - 添加文件到ZIP: 种植区.prj
2025-07-01 16:38:08,134 - root - INFO - 添加文件到ZIP: 种植区.qix
2025-07-01 16:38:08,172 - root - INFO - 添加文件到ZIP: 种植区.cpg
2025-07-01 16:38:08,173 - root - INFO - 添加文件到ZIP: 种植区.qmd
2025-07-01 16:38:08,174 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:38:08,175 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:38:08,448 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:38:08,509 - root - INFO - 成功创建图层 '种植区'
2025-07-01 16:38:08,520 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/种植区
2025-07-01 16:38:08,522 - root - INFO - Shapefile 'data/20250701/testshp\种植区.shp' 成功发布为图层 'test_workspace:种植区'
2025-07-01 16:38:08,524 - root - INFO - 正在发布Shapefile: 设施农用地潜力.shp, 存储名: testshp
2025-07-01 16:38:08,537 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\设施农用地潜力.shp
2025-07-01 16:38:08,538 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shp
2025-07-01 16:38:09,653 - root - INFO - 添加文件到ZIP: 设施农用地潜力.dbf
2025-07-01 16:38:09,714 - root - INFO - 添加文件到ZIP: 设施农用地潜力.shx
2025-07-01 16:38:09,723 - root - INFO - 添加文件到ZIP: 设施农用地潜力.prj
2025-07-01 16:38:09,724 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qix
2025-07-01 16:38:09,735 - root - INFO - 添加文件到ZIP: 设施农用地潜力.cpg
2025-07-01 16:38:09,736 - root - INFO - 添加文件到ZIP: 设施农用地潜力.qmd
2025-07-01 16:38:09,736 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_workspace/datastores/testshp/file.shp
2025-07-01 16:38:09,832 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:38:10,025 - root - INFO - 成功创建数据存储 'testshp'
2025-07-01 16:38:10,089 - root - INFO - 成功创建图层 '设施农用地潜力'
2025-07-01 16:38:10,109 - root - INFO - 图层验证成功: 完整路径 - 工作区/存储/图层: test_workspace/testshp/设施农用地潜力
2025-07-01 16:38:10,110 - root - INFO - Shapefile 'data/20250701/testshp\设施农用地潜力.shp' 成功发布为图层 'test_workspace:设施农用地潜力'
2025-07-01 16:38:10,129 - root - INFO - 在工作区 'test_workspace' 中找到 5 个图层
2025-07-01 16:38:10,130 - root - INFO - 发布后工作区 'test_workspace' 中有 5 个图层
2025-07-01 16:38:10,131 - root - INFO - 新增了 5 个图层: 招商引资片区交设施农用地, 种植区, 招商引资片区交种植区, 设施农用地潜力, 招商引资片区
2025-07-01 16:38:10,131 - root - INFO - 成功发布了 5 个Shapefile文件
2025-07-01 16:38:10,132 - root - INFO - 成功发布: 5, 失败: 0
2025-07-01 16:38:10,132 - root - INFO - 命令执行完成: publish-shapefile-directory
