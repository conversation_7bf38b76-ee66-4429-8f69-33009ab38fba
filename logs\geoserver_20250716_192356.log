2025-07-16 19:23:56,569 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250716_192356.log
2025-07-16 19:23:56,570 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 19:23:56,571 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 19:23:57,192 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 19:23:57,593 - batch_executor - INFO - 加载了 35 个任务状态
2025-07-16 19:24:04,231 - tif_executor - INFO - 加载了 4 个任务状态
2025-07-16 19:24:04,287 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-16 19:24:04,288 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-16 19:24:04,304 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-16 19:24:04,310 - root - INFO - === GeoServer REST API服务 ===
2025-07-16 19:24:04,310 - root - INFO - 主机: 0.0.0.0
2025-07-16 19:24:04,311 - root - INFO - 端口: 5083
2025-07-16 19:24:04,311 - root - INFO - 调试模式: 禁用
2025-07-16 19:24:04,313 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-16 19:24:04,331 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-16 19:24:04,332 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-16 19:25:58,251 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/nanning/file.geotiff
2025-07-16 19:26:33,463 - root - ERROR - REST API发布失败: 500 - Error auto-configuring coverage
2025-07-16 19:26:33,477 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 19:26:33] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/geoserverapi/data/20250701/nanning.tif&workspace=tttt&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-16 19:26:33,563 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 19:26:33] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-16 19:26:53,344 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/nanning/file.geotiff
2025-07-16 19:26:54,159 - root - ERROR - REST API发布失败: 500 - Error auto-configuring coverage
2025-07-16 19:26:54,165 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 19:26:54] "[35m[1mGET /api/management/geotiffs/publish?file=D:/Drone_Project/geoserverapi/data/20250701/nanning.tif&workspace=tttt&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-16 19:28:28,251 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/nanning/file.geotiff
2025-07-16 19:28:29,462 - root - INFO - 成功通过REST API发布GeoTIFF: 201
2025-07-16 19:28:29,463 - root - INFO - 成功发布GeoTIFF: tttt:nanning:nanning
2025-07-16 19:28:29,514 - root - INFO - 图层验证成功: tttt:nanning
2025-07-16 19:28:29,535 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 19:28:29] "GET /api/management/geotiffs/publish?file=D:/Drone_Project/geoserverapi/data/20250701/nanning.tif&workspace=tttt&charset=UTF-8 HTTP/1.1" 200 -
2025-07-16 19:29:54,636 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/odm_orthophoto_out/file.geotiff
2025-07-16 19:37:02,757 - root - INFO - 成功通过REST API发布GeoTIFF: 201
2025-07-16 19:37:02,772 - root - INFO - 成功发布GeoTIFF: tttt:odm_orthophoto_out:odm_orthophoto_out
2025-07-16 19:38:57,190 - root - INFO - 图层验证成功: tttt:odm_orthophoto_out
2025-07-16 19:39:01,376 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 19:39:01] "GET /api/management/geotiffs/publish?file=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif&workspace=tttt&charset=UTF-8 HTTP/1.1" 200 -
2025-07-16 19:41:11,977 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/odm_orthophoto_out/file.geotiff
2025-07-16 19:45:30,398 - root - INFO - 通过REST API发布GeoTIFF: http://localhost:8083/geoserver/rest/workspaces/tttt/coveragestores/odm_orthophoto_out/file.geotiff
2025-07-16 19:47:09,737 - root - INFO - 成功通过REST API发布GeoTIFF: 201
2025-07-16 19:47:09,756 - root - INFO - 成功发布GeoTIFF: tttt:odm_orthophoto_out:odm_orthophoto_out
2025-07-16 19:48:42,292 - root - INFO - 成功通过REST API发布GeoTIFF: 201
2025-07-16 19:48:42,294 - root - INFO - 成功发布GeoTIFF: tttt:odm_orthophoto_out:odm_orthophoto_out
2025-07-16 19:48:42,358 - root - INFO - 图层验证成功: tttt:odm_orthophoto_out
2025-07-16 19:48:42,358 - root - INFO - 图层验证成功: tttt:odm_orthophoto_out
2025-07-16 19:48:45,168 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 19:48:45] "GET /api/management/geotiffs/publish?file=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif&workspace=tttt&charset=UTF-8 HTTP/1.1" 200 -
2025-07-16 19:48:45,169 - werkzeug - INFO - 127.0.0.1 - - [16/Jul/2025 19:48:45] "GET /api/management/geotiffs/publish?file=D:/Drone_Project/dataset/DJITESTIMAGE/20250705171600/project/odm_orthophoto/odm_orthophoto_out.tif&workspace=tttt&charset=UTF-8 HTTP/1.1" 200 -
