2025-07-12 10:26:48,338 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250712_102648.log
2025-07-12 10:26:48,341 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 10:26:48,341 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 10:26:48,402 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 10:26:48,412 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 10:26:48,413 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 10:26:48,432 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 10:26:48,437 - root - INFO - === GeoServer REST API服务 ===
2025-07-12 10:26:48,437 - root - INFO - 主机: 0.0.0.0
2025-07-12 10:26:48,438 - root - INFO - 端口: 5083
2025-07-12 10:26:48,438 - root - INFO - 调试模式: 禁用
2025-07-12 10:26:48,438 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-12 10:26:48,453 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-12 10:26:48,455 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 10:27:04,939 - root - ERROR - 发布Shapefile时出错: create_shp_datastore() got an unexpected keyword argument 'charset'
2025-07-12 10:27:04,941 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 10:27:04] "[35m[1mGET /api/management/shapefiles/publish?file=D:/Drone_Project/geoserverapi/data/20250701/testshp/设施农用地潜力.shp&workspace=tttt&store=20250717&layer=设施农用地潜力&charset=UTF-8 HTTP/1.1[0m" 500 -
2025-07-12 10:27:07,968 - root - ERROR - 发布Shapefile时出错: create_shp_datastore() got an unexpected keyword argument 'charset'
2025-07-12 10:27:07,969 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 10:27:07] "[35m[1mGET /api/management/shapefiles/publish?file=D:/Drone_Project/geoserverapi/data/20250701/testshp/设施农用地潜力.shp&workspace=tttt&store=20250717&layer=设施农用地潜力&charset=UTF-8 HTTP/1.1[0m" 500 -
