2025-07-01 16:23:28,267 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250701_162328.log
2025-07-01 16:23:28,287 - root - INFO - 开始执行命令: publish-shapefile
2025-07-01 16:23:28,288 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-01 16:23:28,288 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-01 16:23:28,304 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-01 16:23:28,322 - root - INFO - 工作区 'test_options' 不存在，正在创建...
2025-07-01 16:23:28,364 - root - INFO - 成功创建工作区 'test_options'
2025-07-01 16:23:28,365 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\testshp\招商引资片区.shp
2025-07-01 16:23:28,366 - root - INFO - 添加文件到ZIP: 招商引资片区.shp
2025-07-01 16:23:28,486 - root - INFO - 添加文件到ZIP: 招商引资片区.dbf
2025-07-01 16:23:28,488 - root - INFO - 添加文件到ZIP: 招商引资片区.shx
2025-07-01 16:23:28,488 - root - INFO - 添加文件到ZIP: 招商引资片区.prj
2025-07-01 16:23:28,489 - root - INFO - 添加文件到ZIP: 招商引资片区.qix
2025-07-01 16:23:28,490 - root - INFO - 添加文件到ZIP: 招商引资片区.cpg
2025-07-01 16:23:28,490 - root - INFO - 添加文件到ZIP: 招商引资片区.qmd
2025-07-01 16:23:28,491 - root - INFO - 使用URL: http://localhost:8083/geoserver/rest/workspaces/test_options/datastores/None/file.shp
2025-07-01 16:23:28,492 - root - INFO - 发送ZIP数据到GeoServer...
2025-07-01 16:23:28,492 - root - INFO - 请求URL: http://localhost:8083/geoserver/rest/workspaces/test_options/datastores/None/file.shp
2025-07-01 16:23:28,493 - root - INFO - 请求参数: {'charset': 'UTF-8', 'create spatial index': 'true', 'memory mapped buffer': 'true', 'cache and reuse memory maps': 'true'}
2025-07-01 16:23:28,623 - root - INFO - 成功创建数据存储 'None'
2025-07-01 16:23:28,623 - root - INFO - 检查数据存储 'None' 的设置...
2025-07-01 16:23:28,635 - root - INFO - 数据存储设置:
2025-07-01 16:23:28,636 - root - INFO -   - 字符集: 未设置
2025-07-01 16:23:28,636 - root - INFO -   - 空间索引: 未设置
2025-07-01 16:23:28,636 - root - INFO -   - 内存映射缓冲区: 未设置
2025-07-01 16:23:28,637 - root - INFO -   - 缓存和重用内存映射: 未设置
2025-07-01 16:23:28,650 - root - INFO - 图层 '招商引资片区' 已存在
2025-07-01 16:23:28,662 - root - INFO - 图层验证成功: 直接图层路径: test_options:招商引资片区
2025-07-01 16:23:28,663 - root - INFO - Shapefile 'data/20250701/testshp/招商引资片区.shp' 成功发布为图层 'test_options:招商引资片区'
2025-07-01 16:23:28,664 - root - INFO - 命令执行完成: publish-shapefile
