2025-07-14 12:35:52,764 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250714_123552.log
2025-07-14 12:35:52,765 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 12:35:52,767 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 12:35:52,814 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 12:35:52,824 - batch_executor - INFO - 加载了 26 个任务状态
2025-07-14 12:35:52,835 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-14 12:35:52,836 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-14 12:35:52,853 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-14 12:35:52,860 - root - INFO - === GeoServer REST API服务 ===
2025-07-14 12:35:52,860 - root - INFO - 主机: 0.0.0.0
2025-07-14 12:35:52,861 - root - INFO - 端口: 5083
2025-07-14 12:35:52,861 - root - INFO - 调试模式: 禁用
2025-07-14 12:35:52,861 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-14 12:35:52,879 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-14 12:35:52,883 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-14 12:36:01,784 - batch_executor - INFO - 启动任务 2bf1ea13-8bd3-43fa-99cb-bc4dbcdedc8d: D:\Drone_Project\ODM\ODM\run.bat D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project
2025-07-14 12:36:01,786 - werkzeug - INFO - ************** - - [14/Jul/2025 12:36:01] "GET /api/batch/execute?project_path=D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project&fast-orthophoto HTTP/1.1" 200 -
2025-07-14 15:04:04,093 - batch_executor - INFO - 启动任务 dc27d05e-fc6b-457e-bbd4-5129d14033b5: D:\Drone_Project\ODM\ODM\run.bat D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project
2025-07-14 15:04:04,094 - werkzeug - INFO - ************** - - [14/Jul/2025 15:04:04] "GET /api/batch/execute?project_path=D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project&fast-orthophoto HTTP/1.1" 200 -
2025-07-14 15:05:09,619 - batch_executor - INFO - 启动任务 b9fdf65d-9fbb-4db8-9a25-8f62eade5fda: D:\Drone_Project\ODM\ODM\run.bat D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project
2025-07-14 15:05:09,621 - werkzeug - INFO - ************** - - [14/Jul/2025 15:05:09] "GET /api/batch/execute?project_path=D://Drone_Project//dataset//DJITESTIMAGE//20250714140500//project&fast-orthophoto HTTP/1.1" 200 -
