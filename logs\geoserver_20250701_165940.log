2025-07-01 16:59:40,715 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250701_165940.log
2025-07-01 16:59:40,732 - root - INFO - 开始执行命令: publish-geotiff
2025-07-01 16:59:40,732 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-01 16:59:40,732 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-01 16:59:40,749 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-01 16:59:40,750 - root - INFO - 未提供存储名称，使用文件名: nanning
2025-07-01 16:59:40,750 - root - INFO - 未提供图层名称，使用文件名: nanning
2025-07-01 16:59:40,766 - root - INFO - 工作区 'tif_workspace' 不存在，正在创建...
2025-07-01 16:59:40,807 - root - INFO - 成功创建工作区 'tif_workspace'
2025-07-01 16:59:40,808 - root - INFO - 使用绝对路径: D:\Drone_Project\geoserverapi\data\20250701\nanning.tif
2025-07-01 16:59:40,808 - root - ERROR - 发布GeoTIFF 'data/20250701/nanning.tif' 失败: create_coveragestore() got an unexpected keyword argument 'store_name'
2025-07-01 16:59:40,809 - root - INFO - 命令执行完成: publish-geotiff
