2025-07-12 16:15:01,201 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250712_161501.log
2025-07-12 16:15:01,203 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 16:15:01,204 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 16:15:01,225 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 16:15:01,231 - batch_executor - INFO - 加载了 6 个任务状态
2025-07-12 16:15:01,243 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-12 16:15:01,243 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-12 16:15:01,260 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-12 16:15:01,268 - root - INFO - === GeoServer REST API服务 ===
2025-07-12 16:15:01,269 - root - INFO - 主机: 0.0.0.0
2025-07-12 16:15:01,269 - root - INFO - 端口: 5083
2025-07-12 16:15:01,269 - root - INFO - 调试模式: 禁用
2025-07-12 16:15:01,270 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-12 16:15:01,293 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://************:5083
2025-07-12 16:15:01,294 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 16:15:31,042 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:15:31] "GET /health HTTP/1.1" 200 -
2025-07-12 16:15:41,336 - batch_executor - INFO - 启动任务 4fe5d6d7-d5fc-4790-9a44-7e3b11ec209b: d:\Drone_Project\testdata\test_script.bat --arg1 v1 --arg2 v2
2025-07-12 16:15:41,339 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:15:41] "GET /api/batch/execute?arg1=v1&arg2=v2 HTTP/1.1" 200 -
2025-07-12 16:15:41,369 - batch_executor - ERROR - 执行任务 4fe5d6d7-d5fc-4790-9a44-7e3b11ec209b 时出错: 'gbk' codec can't decode byte 0x80 in position 11: illegal multibyte sequence
2025-07-12 16:15:49,094 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:15:49] "GET /api/batch/status?task_id=4fe5d6d7-d5fc-4790-9a44-7e3b11ec209b HTTP/1.1" 200 -
