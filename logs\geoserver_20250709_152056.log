2025-07-09 15:20:56,104 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\geoserverRest\core\..\..\logs\geoserver_20250709_152056.log
2025-07-09 15:20:56,107 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-09 15:20:56,107 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-09 15:20:56,127 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-09 15:20:56,140 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-09 15:20:56,140 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-09 15:20:56,152 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-09 15:20:56,159 - root - INFO - === GeoServer REST API服务 ===
2025-07-09 15:20:56,159 - root - INFO - 主机: 0.0.0.0
2025-07-09 15:20:56,159 - root - INFO - 端口: 5083
2025-07-09 15:20:56,160 - root - INFO - 调试模式: 禁用
2025-07-09 15:20:56,160 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-09 15:20:56,174 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**************:5083
2025-07-09 15:20:56,174 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 15:21:43,106 - root - INFO - 开始查询坐标点 (22.834372754198128, 108.19687725970199) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-09 15:21:43,367 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-07-09 15:21:43,390 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-09 15:21:43,422 - root - INFO - 图层 'id2' 在该坐标点有有效数据，添加到结果中
2025-07-09 15:21:43,422 - root - INFO - 在坐标点 (22.834372754198128, 108.19687725970199) 处找到 2 个有有效数据的栅格图层
2025-07-09 15:21:43,424 - werkzeug - INFO - ************** - - [09/Jul/2025 15:21:43] "GET /api/query_values?lat=22.834372754198128&lon=108.19687725970199&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-09 15:22:27,433 - root - ERROR - 删除图层时出错: Status : 404 - b'test_myworkspace:testshp'
2025-07-09 15:22:27,434 - werkzeug - INFO - ************** - - [09/Jul/2025 15:22:27] "GET /api/management/layers/delete?workspace=test_myworkspace&layer=testshp HTTP/1.1" 200 -
