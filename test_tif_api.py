#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TIF API测试脚本 - 演示如何使用TIF处理API
"""

import os
import requests
import json

# API基础URL
BASE_URL = "http://localhost:5000/api/tif"

def print_response(resp):
    """打印API响应结果"""
    print("-" * 50)
    print(f"状态码: {resp.status_code}")
    try:
        data = resp.json()
        print(json.dumps(data, ensure_ascii=False, indent=2))
    except:
        print(resp.text)
    print("-" * 50)

def test_get_tif_info(tif_path):
    """测试获取TIF文件信息"""
    print("\n获取TIF文件信息:")
    url = f"{BASE_URL}/info"
    params = {
        "path": tif_path
    }
    response = requests.get(url, params=params)
    print_response(response)
    return response.json() if response.status_code == 200 else None

def test_set_nodata(input_tif, output_tif, black_threshold=0, white_threshold=255, nodata_value=-9999, edge_width=100):
    """测试设置NoData值"""
    print("\n设置NoData值:")
    url = f"{BASE_URL}/set-nodata"
    params = {
        "input_tif": input_tif,
        "output_tif": output_tif,
        "black_threshold": black_threshold,
        "white_threshold": white_threshold,
        "nodata_value": nodata_value,
        "edge_width": edge_width
    }
    response = requests.get(url, params=params)
    print_response(response)
    return response.json() if response.status_code == 200 else None

def test_create_shapefile(input_tif, output_shp, nodata_value=-9999, simplify_tolerance=0.1):
    """测试创建Shapefile"""
    print("\n创建Shapefile:")
    url = f"{BASE_URL}/create-shapefile"
    params = {
        "input_tif": input_tif,
        "output_shp": output_shp,
        "nodata_value": nodata_value,
        "simplify_tolerance": simplify_tolerance
    }
    response = requests.get(url, params=params)
    print_response(response)
    return response.json() if response.status_code == 200 else None

def test_process_tif(input_tif, output_tif, output_shp, process_mode="full", use_gpu=False):
    """测试完整处理流程"""
    print("\n完整处理流程:")
    url = f"{BASE_URL}/process"
    params = {
        "input_tif": input_tif,
        "output_tif": output_tif,
        "output_shp": output_shp,
        "black_threshold": 0,
        "white_threshold": 255,
        "nodata_value": -9999,
        "simplify_tolerance": 0.1,
        "edge_width": 100,
        "process_mode": process_mode,
        "keep_alpha": "false",
        "protect_interior": "false",
        "use_gpu": "true" if use_gpu else "false",
        "num_gpus": 0
    }
    response = requests.get(url, params=params)
    print_response(response)
    return response.json() if response.status_code == 200 else None

if __name__ == "__main__":
    print("TIF API测试脚本")
    
    # 修改为您的实际TIF文件路径
    input_tif = "D:/Drone_Project/geoserverapi/data/20250701/nanning.tif"
    output_tif = "D:/Drone_Project/geoserverapi/data/20250701/nanning_processed.tif"
    output_shp = "D:/Drone_Project/geoserverapi/data/20250701/nanning_valid_area.shp"
    
    # 检查文件是否存在
    if not os.path.exists(input_tif):
        print(f"错误: 输入文件不存在: {input_tif}")
        print("请修改脚本中的文件路径为您系统上的实际TIF文件")
        exit(1)
    
    # 1. 获取TIF文件信息
    tif_info = test_get_tif_info(input_tif)
    
    # 2. 测试设置NoData (仅演示 - 此步骤可省略，因为完整处理流程会包含此步骤)
    # test_set_nodata(input_tif, output_tif)
    
    # 3. 测试创建Shapefile (仅演示 - 此步骤可省略，因为完整处理流程会包含此步骤)
    # test_create_shapefile(output_tif, output_shp)
    
    # 4. 测试完整处理流程 (包含上述所有步骤)
    process_result = test_process_tif(input_tif, output_tif, output_shp, process_mode="full", use_gpu=False)
    
    if process_result and process_result.get("status") == "success":
        print("\n处理成功!")
        print(f"输出TIF: {process_result.get('output_tif')}")
        print(f"输出SHP: {process_result.get('output_shp')}")
        print(f"处理时间: {process_result.get('processing_time')}")
    else:
        print("\n处理失败，请检查API响应和日志文件") 