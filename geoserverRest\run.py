#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer REST API服务启动脚本
"""

import os
import sys
import argparse
import logging

# 导入API服务模块
from geoserverRest.api.server import start_server, logger

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='启动GeoServer REST API服务'
    )
    parser.add_argument(
        '--host', 
        type=str, 
        default='0.0.0.0',
        help='服务器主机地址 (默认: 0.0.0.0)'
    )
    parser.add_argument(
        '--port', 
        type=int, 
        default=5000,
        help='服务器端口 (默认: 5000)'
    )
    parser.add_argument(
        '--debug', 
        action='store_true',
        help='启用调试模式'
    )
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 打印启动信息
    logger.info("=== GeoServer REST API服务 ===")
    logger.info(f"主机: {args.host}")
    logger.info(f"端口: {args.port}")
    logger.info(f"调试模式: {'启用' if args.debug else '禁用'}")
    
    # 启动服务器
    try:
        start_server(
            host=args.host, 
            port=args.port, 
            debug=args.debug
        )
    except Exception as e:
        logger.error(f"启动服务器时出错: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main() 