2025-07-28 11:34:14,737 - INFO - ============ TIF处理任务 da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd 开始执行 ============
2025-07-28 11:34:14,737 - INFO - 开始时间: 2025-07-28 11:34:14
2025-07-28 11:34:14,738 - INFO - 处理参数: {
  "input_tif": "D:\\Drone_Project\\nginxData\\ODM\\Input\\20250705171602\\project\\odm_orthophoto\\odm_orthophoto.tif",
  "output_tif": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif",
  "output_shp": "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp",
  "black_threshold": 0,
  "white_threshold": 255
}
2025-07-28 11:34:14,741 - INFO - 系统信息:
2025-07-28 11:34:14,742 - INFO -   操作系统: Windows 10.0.19045
2025-07-28 11:34:14,743 - INFO -   Python版本: 3.8.20
2025-07-28 11:34:14,745 - INFO -   GDAL版本: 3.9.2
2025-07-28 11:34:14,745 - INFO -   GPU可用: 否
2025-07-28 11:34:14,746 - INFO - 检查参数有效性...
2025-07-28 11:34:14,746 - INFO - 输入文件存在: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 11:34:14,748 - INFO - 开始执行TIF处理流程...
2025-07-28 11:34:14,748 - INFO - 输入: D:\Drone_Project\nginxData\ODM\Input\20250705171602\project\odm_orthophoto\odm_orthophoto.tif
2025-07-28 11:34:14,749 - INFO - 输出TIF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif
2025-07-28 11:34:14,749 - INFO - 输出SHP: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp
2025-07-28 11:34:18,146 - INFO - 开始处理影像: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif可用GPU数量: 0, 使用GPU: False处理模式: 全图检测无效区域保护内部区域: False保留Alpha通道: False尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tifTIF文件信息: 宽=17976, 高=20856, 波段数=4开始读取影像数据，大小约: 5720.63 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Input/20250705171602/project/odm_orthophoto/odm_orthophoto.tif检测到4波段影像(可能包含Alpha通道)将移除Alpha通道，只保留RGB三波段处理后波段数: 3影像尺寸: 17976x20856x3估计内存使用: 总计 5.59 GB, 单个处理块 274.29 MB开始创建掩码... (11:34:18)使用全图检测无效区域模式...将处理 21 个数据块...使用多线程处理 (7 线程)...
2025-07-28 11:34:18,152 - ERROR - 
处理数据块:   0%|                                                                               | 0/21 [00:00<?, ?it/s]
2025-07-28 11:34:18,455 - ERROR - 
处理数据块:   5%|###3                                                                   | 1/21 [00:00<00:05,  3.57it/s]
2025-07-28 11:34:18,714 - ERROR - 
处理数据块:  38%|###########################                                            | 8/21 [00:00<00:00, 16.90it/s]
2025-07-28 11:34:18,973 - ERROR - 
处理数据块:  71%|##################################################                    | 15/21 [00:00<00:00, 21.44it/s]
2025-07-28 11:34:18,998 - ERROR - 
处理数据块: 100%|######################################################################| 21/21 [00:00<00:00, 25.48it/s]
2025-07-28 11:34:18,999 - INFO - 已完成: 10/21 块 (47.6%)已完成: 20/21 块 (95.2%)已完成: 21/21 块 (100.0%)合并处理结果...
2025-07-28 11:34:18,999 - ERROR - 
合并结果:   0%|                                                                                 | 0/21 [00:00<?, ?it/s]
2025-07-28 11:34:19,029 - ERROR - 
合并结果: 100%|#######################################################################| 21/21 [00:00<00:00, 732.94it/s]
2025-07-28 11:34:20,623 - INFO - 合并进度: 20/21 块 (95.2%)合并进度: 21/21 块 (100.0%)统计结果: 总像素 374907456, 有效像素 130792446 (34.89%), 无效像素 244115010 (65.11%)掩码创建完成，耗时: 1.92 秒 (11:34:19)预计总处理时间: 约 5.77 秒开始设置nodata值并保存到: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif (11:34:19)开始设置NoData值并保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif使用已处理过的影像信息影像信息: 宽=17976, 高=20856, 波段数=3掩码形状: (20856, 17976), 数据类型: uint8警告: 输出文件已存在，将被覆盖: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif获取GDAL GTiff驱动...创建输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 尺寸: 17976x20856x3使用GDAL创建选项: ['COMPRESS=DEFLATE', 'PREDICTOR=2', 'TILED=YES', 'BIGTIFF=IF_SAFER']设置地理信息...写入带有nodata值的影像...
2025-07-28 11:34:20,625 - ERROR - 
处理波段:   0%|                                                                                  | 0/3 [00:00<?, ?it/s]
2025-07-28 11:34:31,317 - INFO - 波段 1 所有像素值有效波段 1 统计信息: min=0.0, max=255.0, mean=38.99, std=61.51应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 1...开始分块写入波段 1, 总共 21 个数据块
2025-07-28 11:34:31,318 - ERROR - 
波段 1/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 11:34:31,318 - ERROR - [A
2025-07-28 11:34:31,574 - ERROR - 
波段 1/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:05,  3.92it/s]
2025-07-28 11:34:31,575 - ERROR - [A
2025-07-28 11:34:31,679 - ERROR - 
波段 1/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:00, 23.58it/s]
2025-07-28 11:34:31,680 - ERROR - [A
2025-07-28 11:34:31,781 - ERROR - 
波段 1/3 写入进度:  76%|################################################               | 16/21 [00:00<00:00, 45.51it/s]
2025-07-28 11:34:31,782 - ERROR - [A
2025-07-28 11:34:31,841 - ERROR - [A
2025-07-28 11:35:16,393 - ERROR - 
处理波段:  33%|########################6                                                 | 1/3 [00:55<01:51, 55.77s/it]
2025-07-28 11:35:43,795 - INFO - 设置波段 1 的NoData值为 -9999波段 1/3 处理完成波段 2 所有像素值有效波段 2 统计信息: min=0.0, max=255.0, mean=42.02, std=63.05应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 2...开始分块写入波段 2, 总共 21 个数据块
2025-07-28 11:35:43,796 - ERROR - 
波段 2/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 11:35:43,797 - ERROR - [A
2025-07-28 11:35:44,020 - ERROR - 
波段 2/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:04,  4.50it/s]
2025-07-28 11:35:44,021 - ERROR - [A
2025-07-28 11:35:44,206 - ERROR - 
波段 2/3 写入进度:  14%|#########1                                                      | 3/21 [00:00<00:02,  7.89it/s]
2025-07-28 11:35:44,207 - ERROR - [A
2025-07-28 11:35:44,371 - ERROR - 
波段 2/3 写入进度:  24%|###############2                                                | 5/21 [00:00<00:01,  9.62it/s]
2025-07-28 11:35:44,372 - ERROR - [A
2025-07-28 11:35:44,541 - ERROR - 
波段 2/3 写入进度:  33%|#####################3                                          | 7/21 [00:00<00:01, 10.46it/s]
2025-07-28 11:35:46,250 - ERROR - [A
2025-07-28 11:35:46,432 - ERROR - 
波段 2/3 写入进度:  43%|###########################4                                    | 9/21 [00:02<00:04,  2.40it/s]
2025-07-28 11:35:46,432 - ERROR - [A
2025-07-28 11:35:46,582 - ERROR - 
波段 2/3 写入进度:  52%|#################################                              | 11/21 [00:02<00:02,  3.37it/s]
2025-07-28 11:35:46,583 - ERROR - [A
2025-07-28 11:35:46,735 - ERROR - 
波段 2/3 写入进度:  62%|#######################################                        | 13/21 [00:02<00:01,  4.48it/s]
2025-07-28 11:35:46,736 - ERROR - [A
2025-07-28 11:35:46,860 - ERROR - 
波段 2/3 写入进度:  71%|#############################################                  | 15/21 [00:03<00:01,  5.83it/s]
2025-07-28 11:35:46,861 - ERROR - [A
2025-07-28 11:35:54,017 - ERROR - 
波段 2/3 写入进度:  81%|###################################################            | 17/21 [00:10<00:04,  1.25s/it]
2025-07-28 11:35:54,017 - ERROR - [A
2025-07-28 11:35:57,363 - ERROR - 
波段 2/3 写入进度:  86%|######################################################         | 18/21 [00:13<00:04,  1.63s/it]
2025-07-28 11:35:57,363 - ERROR - [A
2025-07-28 11:36:07,239 - ERROR - 
波段 2/3 写入进度:  90%|#########################################################      | 19/21 [00:23<00:06,  3.35s/it]
2025-07-28 11:36:07,240 - ERROR - [A
2025-07-28 11:36:11,631 - ERROR - 
波段 2/3 写入进度:  95%|############################################################   | 20/21 [00:27<00:03,  3.59s/it]
2025-07-28 11:36:11,631 - ERROR - [A
2025-07-28 11:36:12,762 - ERROR - 
波段 2/3 写入进度: 100%|###############################################################| 21/21 [00:28<00:00,  2.98s/it]
2025-07-28 11:36:12,762 - ERROR - [A
2025-07-28 11:36:12,763 - ERROR - [A
2025-07-28 11:37:10,027 - ERROR - 
处理波段:  67%|#################################################3                        | 2/3 [02:49<01:29, 89.81s/it]
2025-07-28 11:37:47,745 - INFO - 设置波段 2 的NoData值为 -9999波段 2/3 处理完成波段 3 所有像素值有效波段 3 统计信息: min=0.0, max=255.0, mean=33.88, std=52.73应用掩码: 244115010 个像素将被设置为NoData值 -9999获取输出波段 3...开始分块写入波段 3, 总共 21 个数据块
2025-07-28 11:37:47,747 - ERROR - 
波段 3/3 写入进度:   0%|                                                                        | 0/21 [00:00<?, ?it/s]
2025-07-28 11:37:47,747 - ERROR - [A
2025-07-28 11:37:48,249 - ERROR - 
波段 3/3 写入进度:   5%|###                                                             | 1/21 [00:00<00:10,  2.00it/s]
2025-07-28 11:37:48,250 - ERROR - [A
2025-07-28 11:37:48,694 - ERROR - 
波段 3/3 写入进度:  10%|######                                                          | 2/21 [00:00<00:08,  2.14it/s]
2025-07-28 11:37:48,694 - ERROR - [A
2025-07-28 11:37:49,224 - ERROR - 
波段 3/3 写入进度:  14%|#########1                                                      | 3/21 [00:01<00:08,  2.01it/s]
2025-07-28 11:37:49,224 - ERROR - [A
2025-07-28 11:37:49,779 - ERROR - 
波段 3/3 写入进度:  19%|############1                                                   | 4/21 [00:02<00:08,  1.93it/s]
2025-07-28 11:37:49,779 - ERROR - [A
2025-07-28 11:37:49,887 - ERROR - 
波段 3/3 写入进度:  29%|##################2                                             | 6/21 [00:02<00:04,  3.67it/s]
2025-07-28 11:37:49,888 - ERROR - [A
2025-07-28 11:37:49,990 - ERROR - 
波段 3/3 写入进度:  48%|##############################                                 | 10/21 [00:02<00:01,  8.06it/s]
2025-07-28 11:37:49,991 - ERROR - [A
2025-07-28 11:37:50,096 - ERROR - 
波段 3/3 写入进度:  67%|##########################################                     | 14/21 [00:02<00:00, 12.67it/s]
2025-07-28 11:37:50,097 - ERROR - [A
2025-07-28 11:37:50,209 - ERROR - 
波段 3/3 写入进度:  95%|############################################################   | 20/21 [00:02<00:00, 20.38it/s]
2025-07-28 11:37:50,210 - ERROR - [A
2025-07-28 11:37:50,224 - ERROR - [A
2025-07-28 11:39:59,184 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [05:38<00:00, 126.04s/it]
2025-07-28 11:39:59,184 - ERROR - 
处理波段: 100%|#########################################################################| 3/3 [05:38<00:00, 112.85s/it]
2025-07-28 11:40:55,722 - INFO - 设置波段 3 的NoData值为 -9999波段 3/3 处理完成刷新缓存并关闭输出文件...成功保存输出文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif影像保存完成，耗时: 339.58 秒 (11:39:59)开始从处理后的影像提取有效区域shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp (11:39:59)从处理后的TIF文件提取有效区域: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif输出Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp使用NoData值: -9999, 简化容差: 0.1警告: 以下Shapefile相关文件已存在并将被覆盖:  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf  - D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj读取TIF文件...尝试打开TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tifTIF文件信息: 宽=17976, 高=20856, 波段数=3开始读取影像数据，大小约: 4290.48 MB获取地理参照信息...投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...地理变换参数: (760459.2609127441, 0.07273482865108821, 0.0, 2487350.7407215266, 0.0, -0.07273421304104036)成功读取TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif提取TIF文件信息...TIF文件信息: 宽=17976, 高=20856, 波段数=3创建掩码，识别有效区域...处理多波段数据 (3 个波段)...检查波段 1...波段 1 检测到 244115010 个无效像素检查波段 2...波段 2 检测到 244115010 个无效像素检查波段 3...波段 3 检测到 244115010 个无效像素掩码统计: 总像素数 374907456, 有效像素数 130792446 (34.89%), 无效像素数 244115010 (65.11%)轻微平滑处理掩码，减少边缘锯齿...执行形态学闭操作 (kernel=3x3)...执行形态学开操作 (kernel=3x3)...寻找有效区域轮廓...尝试使用OpenCV查找轮廓...OpenCV找到 2 个初始轮廓轮廓面积统计: 最小=4.00, 最大=130769827.50, 平均=65384915.75, 总数=2过滤后剩余 1/2 个轮廓 (最小面积阈值: 37490.75)将 1 个轮廓转换为地理坐标...
2025-07-28 11:40:55,756 - ERROR - 
处理轮廓:   0%|                                                                                  | 0/1 [00:00<?, ?it/s]
2025-07-28 11:40:56,084 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  3.06it/s]
2025-07-28 11:40:56,085 - ERROR - 
处理轮廓: 100%|##########################################################################| 1/1 [00:00<00:00,  3.04it/s]
2025-07-28 11:40:56,092 - INFO - 成功创建 1 个有效多边形合并和简化多边形...只有一个多边形，无需合并使用简化容差 0.1 简化多边形...处理多边形时发生错误: name 'count_points' is not definedTraceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\tif_process.py", line 1997, in create_shapefile_from_output_tif
    original_points = count_points(unified_polygon)
NameError: name 'count_points' is not defined
2025-07-28 11:41:07,984 - INFO - 从输出TIF生成shapefile失败，尝试使用内存中的掩码生成预处理掩码以提高轮廓查找质量...寻找有效区域轮廓...找到 31 个轮廓，转换为地理坐标...
2025-07-28 11:41:07,985 - ERROR - 
处理轮廓:   0%|                                                                                 | 0/31 [00:00<?, ?it/s]
2025-07-28 11:41:09,180 - ERROR - 
处理轮廓:   6%|####7                                                                    | 2/31 [00:01<00:17,  1.68it/s]
2025-07-28 11:41:09,306 - ERROR - 
处理轮廓:  10%|#######                                                                  | 3/31 [00:01<00:11,  2.50it/s]
2025-07-28 11:41:11,972 - ERROR - 
处理轮廓:  13%|#########4                                                               | 4/31 [00:03<00:33,  1.25s/it]
2025-07-28 11:41:12,014 - ERROR - 
处理轮廓: 100%|########################################################################| 31/31 [00:04<00:00,  7.70it/s]
2025-07-28 11:41:13,418 - INFO - 处理了 85262 个坐标点合并并简化 31 个多边形...多边形简化完成，类型: <class 'shapely.geometry.multipolygon.MultiPolygon'>已删除现有文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.dbf已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.prj已删除辅助文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shx创建新shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp写入shapefile特征...处理MultiPolygon，包含 35 个多边形
2025-07-28 11:41:13,420 - ERROR - 
写入多边形:   0%|                                                                               | 0/35 [00:00<?, ?it/s]
2025-07-28 11:41:14,967 - ERROR - 
写入多边形:   6%|####                                                                   | 2/35 [00:01<00:25,  1.29it/s]
2025-07-28 11:41:14,985 - ERROR - 
写入多边形: 100%|######################################################################| 35/35 [00:01<00:00, 22.38it/s]
2025-07-28 11:41:15,216 - INFO - 处理完成，耗时: 420.47 秒 (7.01 分钟)
2025-07-28 11:41:15,217 - INFO - 处理结果: 成功
2025-07-28 11:41:15,223 - INFO - 输出TIF文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif, 大小: 781.83 MB
2025-07-28 11:41:15,225 - INFO - 输出SHP文件: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp, 大小: 21.20 KB
2025-07-28 11:41:15,231 - INFO - TIF处理任务 da5d9f2e-3ba3-4530-8c23-4f6a5d3111fd 执行成功
2025-07-28 11:41:15,232 - INFO - 完成时间: 2025-07-28 11:41:15
2025-07-28 11:41:15,233 - INFO - 状态: 运行成功
2025-07-28 11:41:15,233 - INFO - ============ 任务执行结束 ============
