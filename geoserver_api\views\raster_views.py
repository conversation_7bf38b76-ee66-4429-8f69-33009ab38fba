#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
栅格查询视图 - 栅格数据查询相关功能
"""

import logging
from django.http import JsonResponse

# 导入核心模块
from ..core.manager import GeoServerManager
from ..core.raster_query import GeoServerRasterQuery

# 获取日志记录器
logger = logging.getLogger('geoserver_api')

# 初始化全局实例
manager = GeoServerManager()
query = GeoServerRasterQuery(manager)


def query_coordinate(request):
    """
    查询坐标点处的栅格图层
    
    查询参数:
        lat: 纬度 (浮点数)
        lon: 经度 (浮点数)
        workspace: 工作区名称 (字符串)
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        
        if not all([lat, lon, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=400)
        
        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)
        
        # 查询栅格值
        result = query.query_raster_value(lat, lon, workspace)
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_layers(request):
    """
    获取工作区中的所有栅格图层
    
    查询参数:
        workspace: 工作区名称
    """
    try:
        workspace = request.GET.get('workspace')
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: workspace'
            }, status=400)
        
        layers = query.get_raster_layers(workspace)
        return JsonResponse({
            'status': 'success',
            'workspace': workspace,
            'count': len(layers),
            'layers': layers
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_all_layers(request):
    """
    获取工作区中的所有图层（包括栅格和矢量）
    
    查询参数:
        workspace: 工作区名称
    """
    try:
        workspace = request.GET.get('workspace')
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: workspace'
            }, status=400)
        
        # 获取所有图层（这里需要扩展raster_query模块来支持所有图层类型）
        raster_layers = query.get_raster_layers(workspace)
        vector_layers = manager.get_layers(workspace)
        
        all_layers = []
        
        # 添加栅格图层
        for layer in raster_layers:
            layer['type'] = 'raster'
            all_layers.append(layer)
        
        # 添加矢量图层
        for layer_name in vector_layers:
            all_layers.append({
                'name': layer_name,
                'workspace': workspace,
                'id': f"{workspace}:{layer_name}",
                'type': 'vector'
            })
        
        return JsonResponse({
            'status': 'success',
            'workspace': workspace,
            'count': len(all_layers),
            'layers': all_layers
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def test_coordinate(request):
    """
    测试坐标点在特定图层中是否有有效数据
    
    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
        layer: 图层名称
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        layer = request.GET.get('layer')
        
        if not all([lat, lon, workspace, layer]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace, layer'
            }, status=400)
        
        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)
        
        # 查询指定图层的栅格值
        result = query.query_raster_value(lat, lon, workspace, layer)
        
        # 检查是否有有效数据
        has_data = len(result.get('results', [])) > 0
        
        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layer': layer,
            'has_data': has_data,
            'result': result
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_pixel_value(request):
    """
    获取坐标点处的像素值
    
    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
        layer: 图层名称
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        layer = request.GET.get('layer')
        
        if not all([lat, lon, workspace, layer]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace, layer'
            }, status=400)
        
        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)
        
        # 查询指定图层的栅格值
        result = query.query_raster_value(lat, lon, workspace, layer)
        
        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layer': layer,
            'pixel_value': result.get('results', [])
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def query_layers_at_coordinate(request):
    """
    查询坐标点处的图层并获取像素值
    
    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        
        if not all([lat, lon, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=400)
        
        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)
        
        # 查询栅格值
        result = query.query_raster_value(lat, lon, workspace)
        
        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layers_found': result.get('results', []),
            'summary': {
                'total_layers': result.get('total_layers', 0),
                'layers_with_data': result.get('layers_with_data', 0)
            }
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)
