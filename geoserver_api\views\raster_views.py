#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
栅格查询视图 - 栅格数据查询相关功能
"""

import logging
from django.http import JsonResponse

# 导入核心模块
from ..core.manager import GeoServerManager
from ..core.raster_query import GeoServerRasterQuery

# 获取日志记录器
logger = logging.getLogger('geoserver_api')

# 初始化全局实例
manager = GeoServerManager()
query = GeoServerRasterQuery(manager)


def query_coordinate(request):
    """
    查询坐标点处的栅格图层
    
    查询参数:
        lat: 纬度 (浮点数)
        lon: 经度 (浮点数)
        workspace: 工作区名称 (字符串)
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        
        if not all([lat, lon, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=400)
        
        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)
        
        # 执行查询 - 调用与Flask版本相同的方法
        layers = query.query_point(lat, lon, workspace)
        return JsonResponse({"status": "success", "count": len(layers), "layers": layers})
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_layers(request):
    """
    获取工作区中的所有栅格图层
    
    查询参数:
        workspace: 工作区名称
    """
    try:
        workspace = request.GET.get('workspace')
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: workspace'
            }, status=400)
        
        # 获取图层列表 - 与Flask版本相同的调用
        layers = query.get_raster_layers(workspace)

        return JsonResponse({"status": "success", "count": len(layers), "layers": layers})
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_all_layers(request):
    """
    获取工作区中的所有图层（包括栅格和矢量）
    
    查询参数:
        workspace: 工作区名称
    """
    try:
        workspace = request.GET.get('workspace')
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: workspace'
            }, status=400)
        
        # 获取图层列表 - 与Flask版本相同的调用
        layers = query.get_all_layers(workspace)

        return JsonResponse({"status": "success", "count": len(layers), "layers": layers})
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def test_coordinate(request):
    """
    测试坐标点在图层中是否有有效数据 - 与Flask版本完全一致

    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
        layer: 图层名称（可选）
    """
    try:
        # 获取查询参数
        lat_str = request.GET.get("lat")
        lon_str = request.GET.get("lon")
        workspace = request.GET.get("workspace")
        layer_name = request.GET.get("layer")

        # 验证必要参数
        if not all([lat_str, lon_str, workspace]):
            missing_params = []
            if not lat_str:
                missing_params.append("lat")
            if not lon_str:
                missing_params.append("lon")
            if not workspace:
                missing_params.append("workspace")

            return JsonResponse({
                "status": "error",
                "message": f'缺少必要参数: {", ".join(missing_params)}',
            }, status=400)

        # 转换经纬度为浮点数
        try:
            lat = float(lat_str)
            lon = float(lon_str)
        except (ValueError, TypeError):
            return JsonResponse({
                "status": "error",
                "message": "经纬度必须是有效的数值"
            }, status=400)

        # 获取工作区中的所有栅格图层
        layers = query.get_raster_layers(workspace)

        if not layers:
            return JsonResponse({
                "status": "error",
                "message": f"工作区 {workspace} 中没有找到栅格图层",
            }, status=404)

        results = []

        # 如果指定了特定图层，只测试该图层
        if layer_name:
            target_layers = [layer for layer in layers if layer["name"] == layer_name]
            if not target_layers:
                return JsonResponse({
                    "status": "error",
                    "message": f"在工作区 {workspace} 中未找到图层 {layer_name}",
                }, status=404)
        else:
            target_layers = layers

        # 测试每个图层
        for layer in target_layers:
            layer_info = {
                "name": layer["name"],
                "store": layer["store"],
                "workspace": layer["workspace"],
                "id": layer["id"],
            }

            # 检查边界框
            bbox = layer.get("bbox", {})
            if bbox:
                try:
                    minx = float(bbox.get("minx", 0))
                    miny = float(bbox.get("miny", 0))
                    maxx = float(bbox.get("maxx", 0))
                    maxy = float(bbox.get("maxy", 0))

                    is_in_bbox = minx <= lon <= maxx and miny <= lat <= maxy
                    layer_info["bbox"] = {
                        "minx": minx,
                        "miny": miny,
                        "maxx": maxx,
                        "maxy": maxy,
                        "point_inside": is_in_bbox,
                    }

                    if is_in_bbox:
                        # 测试点是否有有效数据
                        has_data = query._check_point_data(
                            lat, lon, workspace, layer["name"]
                        )
                        layer_info["has_data"] = has_data
                    else:
                        layer_info["has_data"] = False
                        layer_info["reason"] = "坐标点不在图层边界框内"
                except (ValueError, TypeError) as e:
                    layer_info["error"] = f"处理边界框时出错: {str(e)}"
            else:
                layer_info["error"] = "图层没有边界框信息"

            results.append(layer_info)

        return JsonResponse({
            "status": "success",
            "coordinates": {"lat": lat, "lon": lon},
            "count": len(results),
            "results": results,
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            "status": "error",
            "message": f"服务器内部错误: {str(e)}"
        }, status=500)


def get_pixel_value(request):
    """
    获取指定坐标点在特定图层中的像素值 - 与Flask版本完全一致

    查询参数:
        lat: 纬度 (浮点数)
        lon: 经度 (浮点数)
        workspace: 工作区名称 (字符串)
        layer: 图层名称 (字符串)

    返回:
        像素值信息
    """
    try:
        # 获取查询参数
        lat_str = request.GET.get("lat")
        lon_str = request.GET.get("lon")
        workspace = request.GET.get("workspace")
        layer_name = request.GET.get("layer")

        # 验证必要参数
        if not all([lat_str, lon_str, workspace, layer_name]):
            missing_params = []
            if not lat_str:
                missing_params.append("lat")
            if not lon_str:
                missing_params.append("lon")
            if not workspace:
                missing_params.append("workspace")
            if not layer_name:
                missing_params.append("layer")

            return JsonResponse({
                "status": "error",
                "message": f'缺少必要参数: {", ".join(missing_params)}',
            }, status=400)

        # 转换经纬度为浮点数
        try:
            lat = float(lat_str)
            lon = float(lon_str)
        except (ValueError, TypeError):
            return JsonResponse({
                "status": "error",
                "message": "经纬度必须是有效的数值"
            }, status=400)

        # 检查图层是否存在
        layers = query.get_raster_layers(workspace)
        layer_exists = any(layer["name"] == layer_name for layer in layers)

        if not layer_exists:
            return JsonResponse({
                "status": "error",
                "message": f"在工作区 {workspace} 中未找到图层 {layer_name}",
            }, status=404)

        # 获取像素值
        pixel_data = query.get_pixel_value(lat, lon, workspace, layer_name)

        if pixel_data is None:
            return JsonResponse({
                "status": "error",
                "message": f"无法获取坐标点 ({lat}, {lon}) 在图层 {workspace}:{layer_name} 的像素值",
            }, status=500)

        # 直接返回Flask版本格式的数据
        return JsonResponse(pixel_data)

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            "status": "error",
            "message": f"服务器内部错误: {str(e)}"
        }, status=500)


def query_layers_at_coordinate(request):
    """
    查询坐标点处的图层并获取像素值
    
    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        
        if not all([lat, lon, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=400)
        
        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)
        
        # 查询栅格值
        result = query.query_raster_value(lat, lon, workspace)
        
        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layers_found': result.get('results', []),
            'summary': {
                'total_layers': result.get('total_layers', 0),
                'layers_with_data': result.get('layers_with_data', 0)
            }
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def test_point(request):
    """
    测试坐标点在特定图层中是否有有效数据 (兼容Flask版本的test_point接口)

    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
        layer: 图层名称 (可选)
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        layer = request.GET.get('layer')

        if not all([lat, lon, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=400)

        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)

        # 查询指定图层或所有图层的栅格值
        result = query.query_raster_value(lat, lon, workspace, layer)

        # 检查是否有有效数据
        has_data = len(result.get('results', [])) > 0

        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layer': layer,
            'has_data': has_data,
            'result': result
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def query_values(request):
    """
    查询坐标点处的所有栅格图层并获取有效像素值 - 与Flask版本完全一致

    查询参数:
        lat: 纬度 (浮点数)
        lon: 经度 (浮点数)
        workspace: 工作区名称 (字符串)

    返回:
        包含有效数据的图层及其像素值
    """
    try:
        # 获取查询参数
        lat_str = request.GET.get("lat")
        lon_str = request.GET.get("lon")
        workspace = request.GET.get("workspace")

        # 验证必要参数
        if not all([lat_str, lon_str, workspace]):
            missing_params = []
            if not lat_str:
                missing_params.append("lat")
            if not lon_str:
                missing_params.append("lon")
            if not workspace:
                missing_params.append("workspace")

            return JsonResponse({
                "status": "error",
                "message": f'缺少必要参数: {", ".join(missing_params)}',
            }, status=400)

        # 转换经纬度为浮点数
        try:
            lat = float(lat_str)
            lon = float(lon_str)
        except (ValueError, TypeError):
            return JsonResponse({
                "status": "error",
                "message": "经纬度必须是有效的数值"
            }, status=400)

        # 执行查询 - 调用与Flask版本相同的方法
        result_layers = query.query_point_with_values(lat, lon, workspace)

        # 检查是否找到有效图层
        if not result_layers:
            return JsonResponse({
                "status": "success",
                "message": f"在坐标点 ({lat}, {lon}) 的工作区 {workspace} 中未找到有有效数据的栅格图层",
                "count": 0,
                "layers": [],
            })

        return JsonResponse({
            "status": "success",
            "count": len(result_layers),
            "coordinates": {"lat": lat, "lon": lon},
            "workspace": workspace,
            "layers": result_layers,
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            "status": "error",
            "message": f"服务器内部错误: {str(e)}"
        }, status=500)


def query_intersecting_layers(request):
    """
    查询指定经纬度与工作空间下所有图层的相交情况 - 与Flask版本完全一致

    查询参数:
        lat: 纬度 (浮点数)
        lon: 经度 (浮点数)
        workspace: 工作区名称 (字符串)

    返回:
        与指定坐标点相交的所有图层信息，包括边界框信息（只要坐标点在边界框内就返回）
    """
    try:
        # 获取查询参数
        lat_str = request.GET.get("lat")
        lon_str = request.GET.get("lon")
        workspace = request.GET.get("workspace")

        # 验证必要参数
        if not all([lat_str, lon_str, workspace]):
            missing_params = []
            if not lat_str:
                missing_params.append("lat")
            if not lon_str:
                missing_params.append("lon")
            if not workspace:
                missing_params.append("workspace")

            return JsonResponse({
                "status": "error",
                "message": f'缺少必要参数: {", ".join(missing_params)}',
            }, status=400)

        # 转换经纬度为浮点数
        try:
            lat = float(lat_str)
            lon = float(lon_str)
        except (ValueError, TypeError):
            return JsonResponse({
                "status": "error",
                "message": "经纬度必须是有效的数值"
            }, status=400)

        # 获取工作区中的所有栅格图层
        raster_layers = query.get_raster_layers(workspace)

        if not raster_layers:
            return JsonResponse({
                "status": "success",
                "message": f"工作区 {workspace} 中没有找到栅格图层",
                "count": 0,
                "coordinates": {"lat": lat, "lon": lon},
                "workspace": workspace,
                "layers": [],
            })

        # 查找与坐标点相交的图层
        intersecting_layers = []

        for layer in raster_layers:
            layer_info = {
                "name": str(layer["name"]),
                "store": layer["store"],
                "workspace": layer["workspace"],
                "type": "raster",
            }

            # 添加图层ID（如果存在）
            if "id" in layer:
                layer_info["id"] = layer["id"]

            # 检查边界框
            bbox = layer.get("bbox", {})
            if bbox:
                try:
                    minx = float(bbox.get("minx", 0))
                    miny = float(bbox.get("miny", 0))
                    maxx = float(bbox.get("maxx", 0))
                    maxy = float(bbox.get("maxy", 0))

                    # 检查坐标点是否在边界框内
                    is_in_bbox = minx <= lon <= maxx and miny <= lat <= maxy

                    layer_info["bbox"] = {
                        "minx": minx,
                        "miny": miny,
                        "maxx": maxx,
                        "maxy": maxy,
                    }
                    layer_info["point_inside_bbox"] = is_in_bbox

                    # 只有在边界框内的图层才添加到结果中
                    if is_in_bbox:
                        intersecting_layers.append(layer_info)

                except (ValueError, TypeError) as e:
                    logger.warning(f"处理图层 {layer['name']} 的边界框时出错: {str(e)}")
                    layer_info["error"] = f"处理边界框时出错: {str(e)}"
            else:
                layer_info["error"] = "图层没有边界框信息"

        return JsonResponse({
            "status": "success",
            "count": len(intersecting_layers),
            "coordinates": {"lat": lat, "lon": lon},
            "workspace": workspace,
            "layers": intersecting_layers,
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_layer_intersections(request):
    """
    查询指定栅格图层与同一工作空间其他栅格图层的边界框相交情况 - 与Flask版本完全一致

    查询参数:
        workspace: 工作区名称 (必选)
        layer: 图层名称 (必选)

    返回:
        与指定图层边界框相交的其他栅格图层信息列表
    """
    try:
        # 获取查询参数
        workspace = request.GET.get("workspace")
        layer_name = request.GET.get("layer")

        # 验证必要参数
        if not workspace:
            return JsonResponse({
                "status": "error",
                "message": "缺少必要参数: workspace"
            }, status=400)

        if not layer_name:
            return JsonResponse({
                "status": "error",
                "message": "缺少必要参数: layer"
            }, status=400)

        # 只获取工作区中的栅格图层
        raster_layers = query.get_raster_layers(workspace)

        logger.info(f"在工作区 {workspace} 中找到 {len(raster_layers)} 个栅格图层")

        if not raster_layers:
            return JsonResponse({
                "status": "error",
                "message": f"工作区 {workspace} 中没有找到栅格图层",
            }, status=404)

        # 查找目标图层
        target_layer = None
        for layer in raster_layers:
            layer_name_str = str(layer.get("name", ""))
            logger.debug(
                f"检查图层: {layer_name_str} (类型: {type(layer.get('name'))})"
            )
            # 比较时都转换为字符串
            if layer_name_str == str(layer_name):
                target_layer = layer
                break

        if not target_layer:
            # 记录所有可用的图层名称以便调试
            available_layers = [str(layer["name"]) for layer in raster_layers]
            logger.warning(f"未找到图层 {layer_name}，可用图层: {available_layers}")
            return JsonResponse({
                "status": "error",
                "message": f"在工作区 {workspace} 中未找到栅格图层 {layer_name}",
                "available_layers": available_layers,
            }, status=404)

        # 检查目标图层是否有边界框信息
        target_bbox = target_layer.get("bbox", {})
        if not target_bbox:
            return JsonResponse({
                "status": "error",
                "message": f"图层 {layer_name} 没有边界框信息"
            }, status=400)

        try:
            target_minx = float(target_bbox.get("minx", 0))
            target_miny = float(target_bbox.get("miny", 0))
            target_maxx = float(target_bbox.get("maxx", 0))
            target_maxy = float(target_bbox.get("maxy", 0))
        except (ValueError, TypeError):
            return JsonResponse({
                "status": "error",
                "message": f"图层 {layer_name} 的边界框数据格式错误",
            }, status=400)

        # 查找与目标图层相交的其他栅格图层
        intersecting_layers = []

        for layer in raster_layers:
            # 跳过目标图层本身
            if str(layer["name"]) == str(layer_name):
                continue

            layer_bbox = layer.get("bbox", {})
            if not layer_bbox:
                # 如果图层没有边界框信息，跳过
                logger.debug(f"图层 {layer['name']} 没有边界框信息，跳过")
                continue

            try:
                layer_minx = float(layer_bbox.get("minx", 0))
                layer_miny = float(layer_bbox.get("miny", 0))
                layer_maxx = float(layer_bbox.get("maxx", 0))
                layer_maxy = float(layer_bbox.get("maxy", 0))

                # 检查边界框是否相交
                # 两个矩形相交的条件：
                # 1. target_minx <= layer_maxx 且 layer_minx <= target_maxx (X轴重叠)
                # 2. target_miny <= layer_maxy 且 layer_miny <= target_maxy (Y轴重叠)
                intersects = (
                    target_minx <= layer_maxx
                    and layer_minx <= target_maxx
                    and target_miny <= layer_maxy
                    and layer_miny <= target_maxy
                )

                if intersects:
                    # 计算相交区域
                    intersection_minx = max(target_minx, layer_minx)
                    intersection_miny = max(target_miny, layer_miny)
                    intersection_maxx = min(target_maxx, layer_maxx)
                    intersection_maxy = min(target_maxy, layer_maxy)

                    # 计算相交面积
                    intersection_area = (intersection_maxx - intersection_minx) * (
                        intersection_maxy - intersection_miny
                    )
                    target_area = (target_maxx - target_minx) * (
                        target_maxy - target_miny
                    )
                    layer_area = (layer_maxx - layer_minx) * (layer_maxy - layer_miny)

                    # 计算重叠百分比
                    overlap_percentage_target = (
                        (intersection_area / target_area * 100)
                        if target_area > 0
                        else 0
                    )
                    overlap_percentage_layer = (
                        (intersection_area / layer_area * 100) if layer_area > 0 else 0
                    )

                    layer_info = {
                        "name": layer["name"],
                        "store": layer["store"],
                        "workspace": layer["workspace"],
                        "type": "raster",
                        "bbox": {
                            "minx": layer_minx,
                            "miny": layer_miny,
                            "maxx": layer_maxx,
                            "maxy": layer_maxy,
                        },
                        "intersection": {
                            "bbox": {
                                "minx": intersection_minx,
                                "miny": intersection_miny,
                                "maxx": intersection_maxx,
                                "maxy": intersection_maxy,
                            },
                            "area": intersection_area,
                            "overlap_percentage_with_target": round(
                                overlap_percentage_target, 2
                            ),
                            "overlap_percentage_with_self": round(
                                overlap_percentage_layer, 2
                            ),
                        },
                    }

                    # 添加图层ID（如果存在）
                    if "id" in layer:
                        layer_info["id"] = layer["id"]

                    intersecting_layers.append(layer_info)

            except (ValueError, TypeError) as e:
                logger.warning(f"处理图层 {layer['name']} 的边界框时出错: {str(e)}")
                continue

        # 按重叠面积降序排序
        intersecting_layers.sort(key=lambda x: x["intersection"]["area"], reverse=True)

        return JsonResponse({
            "status": "success",
            "target_layer": {
                "name": target_layer["name"],
                "store": target_layer["store"],
                "workspace": target_layer["workspace"],
                "type": "raster",
                "bbox": {
                    "minx": target_minx,
                    "miny": target_miny,
                    "maxx": target_maxx,
                    "maxy": target_maxy,
                },
            },
            "intersecting_layers_count": len(intersecting_layers),
            "intersecting_layers": intersecting_layers,
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            "status": "error",
            "message": f"服务器内部错误: {str(e)}"
        }, status=500)
