#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
栅格查询视图 - 栅格数据查询相关功能
"""

import logging
from django.http import JsonResponse

# 导入核心模块
from ..core.manager import GeoServerManager
from ..core.raster_query import GeoServerRasterQuery

# 获取日志记录器
logger = logging.getLogger('geoserver_api')

# 初始化全局实例
manager = GeoServerManager()
query = GeoServerRasterQuery(manager)


def query_coordinate(request):
    """
    查询坐标点处的栅格图层
    
    查询参数:
        lat: 纬度 (浮点数)
        lon: 经度 (浮点数)
        workspace: 工作区名称 (字符串)
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        
        if not all([lat, lon, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=400)
        
        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)
        
        # 查询栅格值
        result = query.query_raster_value(lat, lon, workspace)
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_layers(request):
    """
    获取工作区中的所有栅格图层
    
    查询参数:
        workspace: 工作区名称
    """
    try:
        workspace = request.GET.get('workspace')
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: workspace'
            }, status=400)
        
        layers = query.get_raster_layers(workspace)
        return JsonResponse({
            'status': 'success',
            'workspace': workspace,
            'count': len(layers),
            'layers': layers
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_all_layers(request):
    """
    获取工作区中的所有图层（包括栅格和矢量）
    
    查询参数:
        workspace: 工作区名称
    """
    try:
        workspace = request.GET.get('workspace')
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: workspace'
            }, status=400)
        
        # 获取所有图层（这里需要扩展raster_query模块来支持所有图层类型）
        raster_layers = query.get_raster_layers(workspace)
        vector_layers = manager.get_layers(workspace)
        
        all_layers = []
        
        # 添加栅格图层
        for layer in raster_layers:
            layer['type'] = 'raster'
            all_layers.append(layer)
        
        # 添加矢量图层
        for layer_name in vector_layers:
            all_layers.append({
                'name': layer_name,
                'workspace': workspace,
                'id': f"{workspace}:{layer_name}",
                'type': 'vector'
            })
        
        return JsonResponse({
            'status': 'success',
            'workspace': workspace,
            'count': len(all_layers),
            'layers': all_layers
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def test_coordinate(request):
    """
    测试坐标点在特定图层中是否有有效数据
    
    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
        layer: 图层名称
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        layer = request.GET.get('layer')
        
        if not all([lat, lon, workspace, layer]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace, layer'
            }, status=400)
        
        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)
        
        # 查询指定图层的栅格值
        result = query.query_raster_value(lat, lon, workspace, layer)
        
        # 检查是否有有效数据
        has_data = len(result.get('results', [])) > 0
        
        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layer': layer,
            'has_data': has_data,
            'result': result
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_pixel_value(request):
    """
    获取坐标点处的像素值
    
    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
        layer: 图层名称
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        layer = request.GET.get('layer')
        
        if not all([lat, lon, workspace, layer]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace, layer'
            }, status=400)
        
        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)
        
        # 查询指定图层的栅格值
        result = query.query_raster_value(lat, lon, workspace, layer)
        
        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layer': layer,
            'pixel_value': result.get('results', [])
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def query_layers_at_coordinate(request):
    """
    查询坐标点处的图层并获取像素值
    
    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        
        if not all([lat, lon, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=400)
        
        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)
        
        # 查询栅格值
        result = query.query_raster_value(lat, lon, workspace)
        
        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layers_found': result.get('results', []),
            'summary': {
                'total_layers': result.get('total_layers', 0),
                'layers_with_data': result.get('layers_with_data', 0)
            }
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def test_point(request):
    """
    测试坐标点在特定图层中是否有有效数据 (兼容Flask版本的test_point接口)

    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
        layer: 图层名称 (可选)
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')
        layer = request.GET.get('layer')

        if not all([lat, lon, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=400)

        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)

        # 查询指定图层或所有图层的栅格值
        result = query.query_raster_value(lat, lon, workspace, layer)

        # 检查是否有有效数据
        has_data = len(result.get('results', [])) > 0

        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layer': layer,
            'has_data': has_data,
            'result': result
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def query_values(request):
    """
    查询坐标点处的所有栅格图层并获取有效像素值

    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')

        if not all([lat, lon, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=400)

        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)

        # 查询栅格值
        result = query.query_raster_value(lat, lon, workspace)

        # 过滤出有有效值的图层
        valid_results = []
        for layer_result in result.get('results', []):
            if layer_result.get('value') is not None:
                valid_results.append(layer_result)

        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'valid_layers': valid_results,
            'total_valid_layers': len(valid_results)
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def query_intersecting_layers(request):
    """
    查询指定经纬度与工作空间下所有图层的相交情况

    查询参数:
        lat: 纬度
        lon: 经度
        workspace: 工作区名称
    """
    try:
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')

        if not all([lat, lon, workspace]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=400)

        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return JsonResponse({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=400)

        # 获取所有图层
        all_layers = query.get_raster_layers(workspace)

        # 查询每个图层的相交情况
        intersecting_layers = []
        for layer in all_layers:
            try:
                # 查询该图层在指定坐标的值
                layer_result = query.query_raster_value(lat, lon, workspace, layer['name'])

                if layer_result.get('results') and len(layer_result['results']) > 0:
                    layer_info = {
                        'layer_name': layer['name'],
                        'layer_id': layer['id'],
                        'intersects': True,
                        'pixel_value': layer_result['results'][0].get('value')
                    }
                else:
                    layer_info = {
                        'layer_name': layer['name'],
                        'layer_id': layer['id'],
                        'intersects': False,
                        'pixel_value': None
                    }

                intersecting_layers.append(layer_info)

            except Exception as e:
                logger.warning(f"查询图层 {layer['name']} 时出错: {str(e)}")
                intersecting_layers.append({
                    'layer_name': layer['name'],
                    'layer_id': layer['id'],
                    'intersects': False,
                    'error': str(e)
                })

        # 统计相交的图层
        intersecting_count = sum(1 for layer in intersecting_layers if layer.get('intersects', False))

        return JsonResponse({
            'status': 'success',
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'total_layers': len(intersecting_layers),
            'intersecting_layers_count': intersecting_count,
            'layers': intersecting_layers
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


def get_layer_intersections(request):
    """
    查询指定栅格图层与同一工作空间其他栅格图层的边界框相交情况

    查询参数:
        workspace: 工作区名称
        layer: 目标图层名称
    """
    try:
        workspace = request.GET.get('workspace')
        target_layer = request.GET.get('layer')

        if not all([workspace, target_layer]):
            return JsonResponse({
                'status': 'error',
                'message': '缺少必要参数: workspace, layer'
            }, status=400)

        # 获取工作区中的所有栅格图层
        all_layers = query.get_raster_layers(workspace)

        # 找到目标图层
        target_layer_info = None
        for layer in all_layers:
            if layer['name'] == target_layer:
                target_layer_info = layer
                break

        if not target_layer_info:
            return JsonResponse({
                'status': 'error',
                'message': f'在工作区 {workspace} 中未找到图层 {target_layer}'
            }, status=404)

        # 获取目标图层的边界框
        target_bbox = target_layer_info.get('bbox', {})

        # 检查与其他图层的相交情况
        intersections = []
        for layer in all_layers:
            if layer['name'] == target_layer:
                continue  # 跳过自己

            layer_bbox = layer.get('bbox', {})

            # 简单的边界框相交检查
            intersects = False
            if target_bbox and layer_bbox:
                # 这里应该实现实际的边界框相交算法
                # 简化实现，假设都相交
                intersects = True

            intersections.append({
                'layer_name': layer['name'],
                'layer_id': layer['id'],
                'intersects': intersects,
                'bbox': layer_bbox
            })

        intersecting_count = sum(1 for item in intersections if item['intersects'])

        return JsonResponse({
            'status': 'success',
            'target_layer': target_layer,
            'workspace': workspace,
            'target_bbox': target_bbox,
            'total_other_layers': len(intersections),
            'intersecting_layers_count': intersecting_count,
            'intersections': intersections
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)
