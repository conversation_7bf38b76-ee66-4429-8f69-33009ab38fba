2025-07-07 16:49:33,072 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250707_164933.log
2025-07-07 16:49:35,016 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-07 16:49:35,016 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-07 16:49:35,554 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-07 16:49:35,558 - geoserver_query_api - INFO - === GeoServer栅格数据坐标查询API服务 ===
2025-07-07 16:49:35,558 - geoserver_query_api - INFO - 主机: 0.0.0.0
2025-07-07 16:49:35,558 - geoserver_query_api - INFO - 端口: 5000
2025-07-07 16:49:35,559 - geoserver_query_api - INFO - 调试模式: 禁用
2025-07-07 16:49:35,577 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-07 16:49:35,578 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-07 16:49:50,269 - root - INFO - 开始查询坐标点 (22.879460261319736, 108.27837897069966) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-07 16:49:50,685 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 16:49:54,109 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 16:49:54,110 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 16:49:54,114 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 16:49:54,116 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 16:49:54,117 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 16:49:54,119 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 16:49:54,121 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 16:49:54,123 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 16:49:54,124 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 16:49:54,126 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 16:49:54,136 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 16:49:54,705 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-07 16:49:54,781 - root - INFO - 图层 'id1' 在该坐标点有有效数据，添加到结果中
2025-07-07 16:49:54,782 - root - INFO - 在坐标点 (22.879460261319736, 108.27837897069966) 处找到 2 个有有效数据的栅格图层
2025-07-07 16:49:54,786 - werkzeug - INFO - ************** - - [07/Jul/2025 16:49:54] "GET /api/query_values?lat=22.879460261319736&lon=108.27837897069966&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 16:56:15,561 - root - INFO - 开始查询坐标点 (22.81930819286177, 108.37060989439017) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-07 16:56:15,781 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 16:56:16,193 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 16:56:16,194 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 16:56:16,196 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 16:56:16,197 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 16:56:16,200 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 16:56:16,202 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 16:56:16,203 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 16:56:16,205 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 16:56:16,207 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 16:56:16,208 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 16:56:16,210 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 16:56:16,232 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-07 16:56:16,272 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-07 16:56:16,272 - root - INFO - 在坐标点 (22.81930819286177, 108.37060989439017) 处找到 2 个有有效数据的栅格图层
2025-07-07 16:56:16,273 - werkzeug - INFO - ************** - - [07/Jul/2025 16:56:16] "GET /api/query_values?lat=22.81930819286177&lon=108.37060989439017&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 16:56:34,458 - root - INFO - 开始查询坐标点 (22.823527006389853, 108.39272044205234) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-07 16:56:34,654 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 16:56:35,001 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 16:56:35,002 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 16:56:35,003 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 16:56:35,004 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 16:56:35,005 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 16:56:35,006 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 16:56:35,007 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 16:56:35,008 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 16:56:35,009 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 16:56:35,011 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 16:56:35,013 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 16:56:35,036 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-07 16:56:35,067 - root - INFO - 图层 'id8' 在该坐标点有有效数据，添加到结果中
2025-07-07 16:56:35,068 - root - INFO - 在坐标点 (22.823527006389853, 108.39272044205234) 处找到 2 个有有效数据的栅格图层
2025-07-07 16:56:35,069 - werkzeug - INFO - ************** - - [07/Jul/2025 16:56:35] "GET /api/query_values?lat=22.823527006389853&lon=108.39272044205234&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 16:56:40,250 - root - INFO - 开始查询坐标点 (22.80867480576427, 108.32165810028282) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-07 16:56:40,407 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 16:56:40,767 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 16:56:40,768 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 16:56:40,770 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 16:56:40,771 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 16:56:40,773 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 16:56:40,774 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 16:56:40,775 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 16:56:40,778 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 16:56:40,780 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 16:56:40,782 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 16:56:40,784 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 16:56:40,807 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-07 16:56:40,834 - root - INFO - 图层 'id5' 在该坐标点有有效数据，添加到结果中
2025-07-07 16:56:40,835 - root - INFO - 在坐标点 (22.80867480576427, 108.32165810028282) 处找到 2 个有有效数据的栅格图层
2025-07-07 16:56:40,836 - werkzeug - INFO - ************** - - [07/Jul/2025 16:56:40] "GET /api/query_values?lat=22.80867480576427&lon=108.32165810028282&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 16:56:43,860 - root - INFO - 开始查询坐标点 (22.866724193612185, 108.42348970343706) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-07 16:56:44,019 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 16:56:44,380 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 16:56:44,382 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 16:56:44,382 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 16:56:44,385 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 16:56:44,386 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 16:56:44,387 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 16:56:44,389 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 16:56:44,390 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 16:56:44,391 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 16:56:44,392 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 16:56:44,393 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 16:56:44,413 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-07 16:56:44,446 - root - INFO - 图层 'id7' 在该坐标点有有效数据，添加到结果中
2025-07-07 16:56:44,448 - root - INFO - 在坐标点 (22.866724193612185, 108.42348970343706) 处找到 2 个有有效数据的栅格图层
2025-07-07 16:56:44,449 - werkzeug - INFO - ************** - - [07/Jul/2025 16:56:44] "GET /api/query_values?lat=22.866724193612185&lon=108.42348970343706&workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-07 16:57:54,961 - root - INFO - 开始查询坐标点 (22.893551071154874, 108.35555406778445) 在工作区 'test_myworkspace' 中的图层及像素值
2025-07-07 16:57:55,149 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-07 16:57:55,489 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-07 16:57:55,491 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-07 16:57:55,492 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-07 16:57:55,493 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-07 16:57:55,495 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-07 16:57:55,498 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-07 16:57:55,498 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-07 16:57:55,499 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-07 16:57:55,500 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-07 16:57:55,501 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-07 16:57:55,503 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-07 16:57:55,524 - root - INFO - 图层 'nanning' 在该坐标点有有效数据，添加到结果中
2025-07-07 16:57:55,564 - root - INFO - 图层 'id4' 在该坐标点有有效数据，添加到结果中
2025-07-07 16:57:55,564 - root - INFO - 在坐标点 (22.893551071154874, 108.35555406778445) 处找到 2 个有有效数据的栅格图层
2025-07-07 16:57:55,567 - werkzeug - INFO - ************** - - [07/Jul/2025 16:57:55] "GET /api/query_values?lat=22.893551071154874&lon=108.35555406778445&workspace=test_myworkspace HTTP/1.1" 200 -
