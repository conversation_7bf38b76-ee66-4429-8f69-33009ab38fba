#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试GeoTIFF发布接口
"""

import requests
import json
import time

def test_django_geotiff_publish():
    """测试Django版本的GeoTIFF发布接口"""
    try:
        url = "http://127.0.0.1:5000/api/geo/geotiff/execute/"
        params = {
            "path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project/odm_orthophoto/odm_orthophoto.tif",
            "workspace": "tttt"
        }
        
        print("发送GeoTIFF发布请求...")
        print(f"URL: {url}")
        print(f"参数: {params}")
        
        response = requests.get(url, params=params, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("发布请求成功!")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 如果获得了task_id，查询任务状态
            if "task_id" in data:
                task_id = data["task_id"]
                print(f"\n获得任务ID: {task_id}")
                
                # 等待一段时间后查询状态
                print("等待5秒后查询任务状态...")
                time.sleep(5)
                
                status_url = "http://127.0.0.1:5000/api/geo/status/"
                status_response = requests.get(status_url, params={"task_id": task_id}, timeout=10)
                
                print(f"状态查询响应: {status_response.status_code}")
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    print("任务状态:")
                    print(json.dumps(status_data, indent=2, ensure_ascii=False))
                    
                    # 如果有日志文件，尝试读取日志
                    if "task" in status_data and "log_file" in status_data["task"]:
                        log_file = status_data["task"]["log_file"]
                        print(f"\n日志文件路径: {log_file}")
                        
                        try:
                            with open(log_file, 'r', encoding='utf-8') as f:
                                log_content = f.read()
                                print("日志内容:")
                                print("-" * 50)
                                print(log_content)
                                print("-" * 50)
                        except Exception as e:
                            print(f"读取日志文件失败: {e}")
                else:
                    print(f"状态查询失败: {status_response.text}")
            
            return data
        else:
            print(f"发布请求失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return None

def test_flask_geotiff_publish():
    """测试Flask版本的GeoTIFF发布接口"""
    try:
        url = "http://127.0.0.1:5000/api/geo/geotiff/execute"
        params = {
            "path": "D:/Drone_Project/nginxData/ODM/Input/20250705171599/project/odm_orthophoto/odm_orthophoto.tif",
            "workspace": "tttt"
        }
        
        print("发送Flask GeoTIFF发布请求...")
        print(f"URL: {url}")
        print(f"参数: {params}")
        
        response = requests.get(url, params=params, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("Flask发布请求成功!")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            return data
        else:
            print(f"Flask发布请求失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"Flask测试失败: {str(e)}")
        return None

if __name__ == "__main__":
    print("开始测试GeoTIFF发布接口...")
    
    print("="*60)
    print("测试Django版本")
    print("="*60)
    django_result = test_django_geotiff_publish()
    
    print("\n" + "="*60)
    print("测试Flask版本")
    print("="*60)
    flask_result = test_flask_geotiff_publish()
    
    print("\n" + "="*60)
    print("测试完成")
    print("="*60)
