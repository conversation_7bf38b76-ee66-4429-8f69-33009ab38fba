2025-07-08 08:55:50,484 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250708_085550.log
2025-07-08 08:55:52,453 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-08 08:55:52,453 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-08 08:55:52,988 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-08 08:55:52,993 - geoserver_query_api - INFO - === GeoServer栅格数据坐标查询API服务 ===
2025-07-08 08:55:52,993 - geoserver_query_api - INFO - 主机: 0.0.0.0
2025-07-08 08:55:52,993 - geoserver_query_api - INFO - 端口: 5000
2025-07-08 08:55:52,994 - geoserver_query_api - INFO - 调试模式: 禁用
2025-07-08 08:55:53,005 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-08 08:55:53,005 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 08:56:02,991 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 08:56:06,202 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 08:56:06,203 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 08:56:06,206 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 08:56:06,207 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 08:56:06,209 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 08:56:06,210 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 08:56:06,211 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 08:56:06,213 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 08:56:06,214 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 08:56:06,215 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 08:56:06,217 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 08:56:06,889 - root - INFO - 在工作区 'test_myworkspace' 中找到 5 个矢量图层
2025-07-08 08:56:06,891 - root - INFO - 在工作区 'test_myworkspace' 中找到 15 个图层（10 个栅格，5 个矢量）
2025-07-08 08:56:06,895 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 08:56:06] "GET /api/all_layers?workspace=test_myworkspace HTTP/1.1" 200 -
2025-07-08 08:58:59,287 - root - INFO - 尝试从WMS GetCapabilities获取 10 个图层的边界框
2025-07-08 08:58:59,985 - root - INFO - 成功从WMS获取图层 'nanning' 的边界框: (108.17138671875, 22.65380859375) - (108.5009765625, 22.939453125)
2025-07-08 08:58:59,986 - root - INFO - 成功从WMS获取图层 'id1' 的边界框: (108.171472549, 22.844266891) - (108.281164169, 22.939453125)
2025-07-08 08:58:59,988 - root - INFO - 成功从WMS获取图层 'id2' 的边界框: (108.171472549, 22.749080658) - (108.281164169, 22.844181061)
2025-07-08 08:58:59,989 - root - INFO - 成功从WMS获取图层 'id3' 的边界框: (108.171472549, 22.653894424) - (108.281164169, 22.748994827)
2025-07-08 08:58:59,991 - root - INFO - 成功从WMS获取图层 'id4' 的边界框: (108.28125, 22.844266891) - (108.391027451, 22.939453125)
2025-07-08 08:58:59,992 - root - INFO - 成功从WMS获取图层 'id5' 的边界框: (108.28125, 22.749080658) - (108.391027451, 22.844181061)
2025-07-08 08:58:59,994 - root - INFO - 成功从WMS获取图层 'id6' 的边界框: (108.28125, 22.653894424) - (108.391027451, 22.748994827)
2025-07-08 08:58:59,995 - root - INFO - 成功从WMS获取图层 'id7' 的边界框: (108.391113281, 22.844266891) - (108.500890732, 22.939453125)
2025-07-08 08:58:59,997 - root - INFO - 成功从WMS获取图层 'id8' 的边界框: (108.391113281, 22.749080658) - (108.500890732, 22.844181061)
2025-07-08 08:58:59,998 - root - INFO - 成功从WMS获取图层 'id9' 的边界框: (108.391113281, 22.653894424) - (108.500890732, 22.748994827)
2025-07-08 08:59:00,000 - root - INFO - 在工作区 'test_myworkspace' 中找到 10 个栅格图层
2025-07-08 08:59:00,236 - root - INFO - 在工作区 'test_myworkspace' 中找到 5 个矢量图层
2025-07-08 08:59:00,238 - root - INFO - 在工作区 'test_myworkspace' 中找到 15 个图层（10 个栅格，5 个矢量）
2025-07-08 08:59:00,240 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 08:59:00] "GET /api/all_layers?workspace=test_myworkspace HTTP/1.1" 200 -
